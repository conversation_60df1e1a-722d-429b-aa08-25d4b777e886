path:

	ftp anonymous server : github link for repository have git encrypted password  

	login smb using decrypted password (admin rapport for 5 CVEs found )

	exploit CVE-2025 for admin login on network analyzer web based "deepeseek suppose an service web vulnerable for admin bypass cve 2025 "

	analyze traffic and find a backdoor php on the web server 

	exploit backdoor and get www-data 

	find cloud passwords on the machine with week permission but encrypted password 

	use the pasword to login low privileged user

	in  mails dir find a mail have link for source code website listen in local for the developer to make chnages , need to analyze and find the weekness to dump database and crack the passwords and login to seconde user 

	then test the glibc and find vulenrabilty loonytunables and get root ,, 

	what you think about it ? ,, rate it , if it's hard or medium or easy or peace of cake ?




requirements : 

ftp server anonymous login (*)

repository have encrypted password with leaked private key (-)

smb server (*)

pdf rapport (*)

apache2 server (*)

long subdomain for the network analyzer tool (-)

static web site (-)

backdoor with long path e.g. /app/app/paa/c0nfig/json/js/html/public/f/i/ms/bac/k/d/ooo/rr/1d6b2f1b31d4733116fb5960d65f38f8.php

cloud.passwd (-) encrypted password 

create user developer

pdf mail with permission for only user developer 

github : websource code 

local web site sqlinjection 

mysql database

glibc loonytunable 

 

