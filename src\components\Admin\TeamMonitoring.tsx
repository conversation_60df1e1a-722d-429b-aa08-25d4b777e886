import React, { useState, useEffect } from 'react';
import { AdminService, AdminTeamListItem, AdminPaginationParams } from '../../services/admin';
import { Edit, Trash2, Search, ChevronLeft, ChevronRight, Eye, EyeOff, Users } from 'lucide-react';

export function TeamMonitoring() {
  const [teams, setTeams] = useState<AdminTeamListItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalTeams, setTotalTeams] = useState<number>(0);
  const [limit] = useState<number>(10);

  const fetchTeams = async (params: AdminPaginationParams = {}) => {
    setLoading(true);
    try {
      const response = await AdminService.getTeams({
        page: params.page || currentPage,
        limit,
        search: params.search || searchTerm,
        sortBy: 'teamScore',
        sortDirection: 'desc'
      });
      setTeams(response.teams);
      setTotalPages(response.pages);
      setTotalTeams(response.total);
      setCurrentPage(response.page);
    } catch (err) {
      setError(err.message || 'Failed to fetch teams');
      console.error('Error fetching teams:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTeams();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchTeams({ page: 1, search: searchTerm });
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      fetchTeams({ page: newPage });
    }
  };
  const handleToggleTeamStatus = async (teamId: string, currentStatus: boolean) => {
    if (!teamId) {
      setError('Invalid team ID');
      return;
    }
    
    try {
      await AdminService.updateTeamStatus(teamId, !currentStatus);
      fetchTeams();
    } catch (err: any) {
      console.error('Error toggling team status:', err);
      setError(err.message || 'Failed to update team status');
    }
  };

  const handleDeleteTeam = async (teamId: string) => {
    if (confirm('Are you sure you want to delete this team? This action cannot be undone. Team members will be removed from the team.')) {
      try {
        await AdminService.deleteTeam(teamId);
        fetchTeams();
      } catch (err: any) {
        console.error('Error deleting team:', err);
        setError(err.message || 'Failed to delete team');
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold text-white">Team Management</h3>
        <div className="text-sm text-slate-400">
          {totalTeams} total teams
        </div>
      </div>
      
      <form onSubmit={handleSearchSubmit} className="relative">
        <Search className="absolute left-3 top-3 w-5 h-5 text-slate-400" />
        <input
          type="text"
          placeholder="Search teams by name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-3 bg-slate-900/50 border border-slate-800 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400 backdrop-blur-sm"
        />
      </form>
      
      {error && (
        <div className="p-4 bg-red-900/30 border border-red-800 rounded-xl text-red-400">
          {error}
        </div>
      )}
      
      <div className="bg-slate-800/50 rounded-xl border border-slate-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-slate-900/50">
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Team</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Visibility</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Captain</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Members</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Score</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Rank</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Created</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Status</th>
                <th className="px-4 py-3 text-right text-xs font-medium text-slate-400 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-slate-700">
              {loading && Array(5).fill(0).map((_, i) => (
                <tr key={`skeleton-${i}`} className="animate-pulse">
                  <td colSpan={9} className="px-4 py-4">
                    <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                  </td>
                </tr>
              ))}
              
              {!loading && teams.length === 0 && (
                <tr>
                  <td colSpan={9} className="px-4 py-6 text-center text-slate-400">
                    No teams found. Try adjusting your search.
                  </td>
                </tr>
              )}
              
              {!loading && teams.map((team) => (
                <tr key={team.id} className="hover:bg-slate-800/50 transition-colors">
                  <td className="px-4 py-4">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-white">{team.name}</div>
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      team.isPublic 
                        ? 'bg-blue-500/20 text-blue-400' 
                        : 'bg-slate-500/20 text-slate-400'
                    }`}>
                      {team.isPublic ? 'Public' : 'Private'}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">{team.captainName}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300 flex items-center">
                      <Users className="w-3 h-3 mr-1" />
                      {team.memberCount}
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-emerald-400 font-medium">{team.teamScore}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">#{team.rank}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">{formatDate(team.createdAt)}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      team.isActive 
                        ? 'bg-emerald-500/20 text-emerald-400' 
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      {team.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <button 
                        onClick={() => handleToggleTeamStatus(team.id, team.isActive)}
                        className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-white transition-colors"
                        title={team.isActive ? "Deactivate Team" : "Activate Team"}
                      >
                        {team.isActive ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                      <button 
                        className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-blue-400 transition-colors"
                        title="View Team Details"
                      >
                        <Users className="w-4 h-4" />
                      </button>
                      <button 
                        onClick={() => handleDeleteTeam(team.id)}
                        className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-red-400 transition-colors"
                        title="Delete Team"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {totalPages > 1 && (
          <div className="px-4 py-3 flex items-center justify-between border-t border-slate-700">
            <div className="flex-1 flex justify-between items-center">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`flex items-center px-4 py-2 border rounded-md ${
                  currentPage === 1
                    ? 'border-slate-700 bg-slate-800/30 text-slate-500 cursor-not-allowed'
                    : 'border-slate-700 bg-slate-800 text-slate-300 hover:bg-slate-700'
                }`}
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Previous
              </button>
              <div className="text-sm text-slate-400">
                Page {currentPage} of {totalPages}
              </div>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`flex items-center px-4 py-2 border rounded-md ${
                  currentPage === totalPages
                    ? 'border-slate-700 bg-slate-800/30 text-slate-500 cursor-not-allowed'
                    : 'border-slate-700 bg-slate-800 text-slate-300 hover:bg-slate-700'
                }`}
              >
                Next
                <ChevronRight className="w-4 h-4 ml-2" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
