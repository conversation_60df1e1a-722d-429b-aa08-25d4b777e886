import { Injectable, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Category } from '../schemas/category.schema';
import { Challenge } from '../schemas/challenge.schema';
import { CreateCategoryDto, UpdateCategoryDto } from '../admin/dto/category.dto';

@Injectable()
export class CategoriesService {
  constructor(
    @InjectModel(Category.name) private categoryModel: Model<Category>,
    @InjectModel(Challenge.name) private challengeModel: Model<Challenge>,
  ) {}

  async createCategory(createCategoryDto: CreateCategoryDto): Promise<Category> {
    // Check if category name already exists
    const existingCategory = await this.categoryModel.findOne({ 
      name: createCategoryDto.name.toLowerCase() 
    });
    
    if (existingCategory) {
      throw new ConflictException('Category name already exists');
    }

    const category = new this.categoryModel({
      ...createCategoryDto,
      name: createCategoryDto.name.toLowerCase(),
      isActive: createCategoryDto.isActive ?? true,
      sortOrder: createCategoryDto.sortOrder ?? 0,
    });

    return category.save();
  }

  async getAllCategories(includeInactive = false): Promise<Category[]> {
    const filter = includeInactive ? {} : { isActive: true };
    return this.categoryModel
      .find(filter)
      .sort({ sortOrder: 1, name: 1 })
      .exec();
  }

  async getCategoryById(id: string): Promise<Category> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid category ID');
    }

    const category = await this.categoryModel.findById(id);
    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async getCategoryByName(name: string): Promise<Category | null> {
    return this.categoryModel.findOne({ name: name.toLowerCase() });
  }

  async updateCategory(id: string, updateCategoryDto: UpdateCategoryDto): Promise<Category> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid category ID');
    }

    // If updating name, check for conflicts
    if (updateCategoryDto.name) {
      const existingCategory = await this.categoryModel.findOne({ 
        name: updateCategoryDto.name.toLowerCase(),
        _id: { $ne: id }
      });
      
      if (existingCategory) {
        throw new ConflictException('Category name already exists');
      }
      
      updateCategoryDto.name = updateCategoryDto.name.toLowerCase();
    }

    const category = await this.categoryModel.findByIdAndUpdate(
      id,
      updateCategoryDto,
      { new: true }
    );

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async deleteCategory(id: string): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid category ID');
    }

    const category = await this.categoryModel.findById(id);
    if (!category) {
      throw new NotFoundException('Category not found');
    }

    // Check if any challenges use this category
    const challengeCount = await this.challengeModel.countDocuments({ 
      category: category.name 
    });

    if (challengeCount > 0) {
      throw new BadRequestException(
        `Cannot delete category. ${challengeCount} challenge(s) are using this category. ` +
        'Please reassign or delete those challenges first.'
      );
    }

    await this.categoryModel.findByIdAndDelete(id);
  }

  async getCategoryStats(): Promise<any[]> {
    const categories = await this.getAllCategories();
    
    const stats = await Promise.all(
      categories.map(async (category) => {
        const totalChallenges = await this.challengeModel.countDocuments({
          category: category.name,
          isActive: true
        });

        return {
          id: (category._id as any).toString(),
          name: category.name,
          displayName: category.displayName,
          color: category.color,
          icon: category.icon,
          totalChallenges,
          isActive: category.isActive,
          sortOrder: category.sortOrder
        };
      })
    );

    return stats.sort((a, b) => a.sortOrder - b.sortOrder);
  }

  async initializeDefaultCategories(): Promise<void> {
    const existingCategories = await this.categoryModel.countDocuments();
    
    if (existingCategories === 0) {
      const defaultCategories = [
        {
          name: 'web',
          displayName: 'Web Security',
          description: 'Web application security challenges including XSS, SQL injection, and more',
          color: '#3B82F6',
          icon: 'Shield',
          sortOrder: 1
        },
        {
          name: 'crypto',
          displayName: 'Cryptography',
          description: 'Cryptographic challenges involving encryption, hashing, and mathematical puzzles',
          color: '#8B5CF6',
          icon: 'Lock',
          sortOrder: 2
        },
        {
          name: 'pwn',
          displayName: 'Binary Exploitation',
          description: 'Binary exploitation and reverse engineering challenges',
          color: '#EF4444',
          icon: 'Zap',
          sortOrder: 3
        },
        {
          name: 'reverse',
          displayName: 'Reverse Engineering',
          description: 'Reverse engineering and malware analysis challenges',
          color: '#10B981',
          icon: 'RefreshCw',
          sortOrder: 4
        },
        {
          name: 'forensics',
          displayName: 'Digital Forensics',
          description: 'Digital forensics and steganography challenges',
          color: '#F59E0B',
          icon: 'Search',
          sortOrder: 5
        },
        {
          name: 'misc',
          displayName: 'Miscellaneous',
          description: 'Various challenges that don\'t fit into other categories',
          color: '#6366F1',
          icon: 'Star',
          sortOrder: 6
        }
      ];

      await this.categoryModel.insertMany(defaultCategories);
      console.log('✅ Default categories initialized');
    }
  }
}
