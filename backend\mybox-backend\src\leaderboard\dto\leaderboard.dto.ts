import { IsN<PERSON>ber, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class LeaderboardQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 20;
}

export class UserLeaderboardEntryDto {
  userId: string;
  username: string;
  email: string;
  avatar?: string;
  score: number;
  rank: number;
  solvedChallenges: number;
  lastSolved?: Date;
  totalSubmissions: number;
}

export class TeamLeaderboardEntryDto {
  teamId: string;
  teamName: string;
  teamScore: number;
  rank: number;
  memberCount: number;
  solvedChallenges: number;
  lastSolved?: Date;
  captain: {
    id: string;
    username: string;
  };
}

export class UserLeaderboardResponseDto {
  users: UserLeaderboardEntryDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class TeamLeaderboardResponseDto {
  teams: TeamLeaderboardEntryDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class UserRankingDto {
  userId: string;
  username: string;
  score: number;
  rank: number;
  solvedChallenges: number;
  lastSolved?: Date;
  percentile: number;
}

export class TeamRankingDto {
  teamId: string;
  teamName: string;
  teamScore: number;
  rank: number;
  memberCount: number;
  solvedChallenges: number;
  lastSolved?: Date;
  percentile: number;
}
