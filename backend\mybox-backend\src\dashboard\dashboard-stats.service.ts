import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from '../schemas/user.schema';
import { Challenge } from '../schemas/challenge.schema';
import { MachineInstance } from '../schemas/machine-instance.schema';
import { ChallengeSubmission } from '../schemas/challenge-submission.schema';
import { MachineSubmission, MachineSubmissionDocument } from '../schemas/machine-submission.schema';
import { Team } from '../schemas/team.schema';

export interface DashboardStats {
  totalUsers: number;
  totalChallenges: number;
  activeVMs: number;
  totalSolves: number;
  onlineUsers: number;
  recentActivity: ActivityItem[];
  platformStats: {
    totalTeams: number;
    totalSubmissions: number;
    averageScore: number;
    topPerformers: number;
  };
}

export interface ActivityItem {
  id: string;
  type: 'challenge_solved' | 'vm_started' | 'rank_up' | 'first_blood' | 'team_joined';
  title: string;
  description: string;
  timestamp: string;
  points?: number;
  userId: string;
  username: string;
}

export interface UserActivityStats {
  recentActivity: ActivityItem[];
  totalActivities: number;
}

@Injectable()
export class DashboardStatsService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Challenge.name) private challengeModel: Model<Challenge>,
    @InjectModel(MachineInstance.name) private machineInstanceModel: Model<MachineInstance>,
    @InjectModel(ChallengeSubmission.name) private challengeSubmissionModel: Model<ChallengeSubmission>,
    @InjectModel(MachineSubmission.name) private machineSubmissionModel: Model<MachineSubmissionDocument>,
    @InjectModel(Team.name) private teamModel: Model<Team>,
  ) {}

  async getDashboardStats(): Promise<DashboardStats> {
    const [
      totalUsers,
      totalChallenges,
      activeVMs,
      totalSolves,
      onlineUsers,
      recentActivity,
      platformStats
    ] = await Promise.all([
      this.getTotalUsers(),
      this.getTotalChallenges(),
      this.getActiveVMs(),
      this.getTotalSolves(),
      this.getOnlineUsers(),
      this.getRecentActivity(10),
      this.getPlatformStats()
    ]);

    return {
      totalUsers,
      totalChallenges,
      activeVMs,
      totalSolves,
      onlineUsers,
      recentActivity,
      platformStats
    };
  }

  async getUserActivityStats(userId: string, limit: number = 20): Promise<UserActivityStats> {
    const recentActivity = await this.getUserRecentActivity(userId, limit);
    const totalActivities = await this.getUserTotalActivities(userId);

    return {
      recentActivity,
      totalActivities
    };
  }

  private async getTotalUsers(): Promise<number> {
    return this.userModel.countDocuments({ isActive: true });
  }

  private async getTotalChallenges(): Promise<number> {
    return this.challengeModel.countDocuments({ isActive: true });
  }

  private async getActiveVMs(): Promise<number> {
    return this.machineInstanceModel.countDocuments({ 
      status: { $in: ['running', 'starting'] } 
    });
  }

  private async getTotalSolves(): Promise<number> {
    const [challengeSolves, machineSolves] = await Promise.all([
      this.challengeSubmissionModel.countDocuments({ isCorrect: true }),
      this.machineSubmissionModel.countDocuments({ isCorrect: true })
    ]);
    return challengeSolves + machineSolves;
  }

  private async getOnlineUsers(): Promise<number> {
    // Users active in the last 15 minutes
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    return this.userModel.countDocuments({ 
      lastActive: { $gte: fifteenMinutesAgo },
      isActive: true 
    });
  }

  private async getPlatformStats() {
    const [totalTeams, totalSubmissions, avgScore] = await Promise.all([
      this.teamModel.countDocuments({ isActive: true }),
      this.getTotalSubmissions(),
      this.getAverageScore()
    ]);

    const topPerformers = await this.userModel.countDocuments({ 
      score: { $gte: 1000 },
      isActive: true 
    });

    return {
      totalTeams,
      totalSubmissions,
      averageScore: Math.round(avgScore),
      topPerformers
    };
  }

  private async getTotalSubmissions(): Promise<number> {
    const [challengeSubmissions, machineSubmissions] = await Promise.all([
      this.challengeSubmissionModel.countDocuments(),
      this.machineSubmissionModel.countDocuments()
    ]);
    return challengeSubmissions + machineSubmissions;
  }

  private async getAverageScore(): Promise<number> {
    const result = await this.userModel.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: null, avgScore: { $avg: '$score' } } }
    ]);
    return result[0]?.avgScore || 0;
  }

  private async getRecentActivity(limit: number = 10): Promise<ActivityItem[]> {
    const activities: ActivityItem[] = [];

    try {
      // Get recent challenge solves
      const challengeSolves = await this.challengeSubmissionModel
        .find({ isCorrect: true })
        .populate('userId', 'username')
        .populate('challengeId', 'title points')
        .sort({ submittedAt: -1 })
        .limit(limit)
        .lean();

      challengeSolves.forEach(solve => {
        if (solve.userId && solve.challengeId) {
          activities.push({
            id: solve._id.toString(),
            type: 'challenge_solved',
            title: (solve.challengeId as any)?.title || 'Unknown Challenge',
            description: `Solved by ${(solve.userId as any)?.username || 'Unknown User'}`,
            timestamp: this.formatTimestamp(solve.submittedAt),
            points: (solve.challengeId as any)?.points || solve.pointsAwarded || 0,
            userId: (solve.userId as any)?._id?.toString() || '',
            username: (solve.userId as any)?.username || 'Unknown User'
          });
        }
      });

      // Get recent VM starts
      const vmStarts = await this.machineInstanceModel
        .find({ status: 'running' })
        .populate('ownerId', 'username')
        .populate('templateId', 'name')
        .sort({ startedAt: -1 })
        .limit(limit)
        .lean();

      vmStarts.forEach(vm => {
        if (vm.ownerId && vm.templateId) {
          activities.push({
            id: vm._id.toString(),
            type: 'vm_started',
            title: (vm.templateId as any)?.name || 'Unknown Machine',
            description: `Started by ${(vm.ownerId as any)?.username || 'Unknown User'}`,
            timestamp: this.formatTimestamp(vm.startedAt),
            userId: (vm.ownerId as any)?._id?.toString() || '',
            username: (vm.ownerId as any)?.username || 'Unknown User'
          });
        }
      });
    } catch (error) {
      console.error('Error fetching recent activity:', error);
    }

    // Sort all activities by timestamp and return top results
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  private async getUserRecentActivity(userId: string, limit: number = 20): Promise<ActivityItem[]> {
    const activities: ActivityItem[] = [];

    try {
      // Get user's recent challenge solves
      const challengeSolves = await this.challengeSubmissionModel
        .find({ userId, isCorrect: true })
        .populate('challengeId', 'title points')
        .sort({ submittedAt: -1 })
        .limit(limit)
        .lean();

      challengeSolves.forEach(solve => {
        if (solve.challengeId) {
          activities.push({
            id: solve._id.toString(),
            type: 'challenge_solved',
            title: (solve.challengeId as any)?.title || 'Unknown Challenge',
            description: `Solved in ${this.calculateSolveTime(solve.submittedAt)}`,
            timestamp: this.formatTimestamp(solve.submittedAt),
            points: (solve.challengeId as any)?.points || solve.pointsAwarded || 0,
            userId,
            username: 'You'
          });
        }
      });

      // Get user's recent VM starts
      const vmStarts = await this.machineInstanceModel
        .find({ ownerId: userId })
        .populate('templateId', 'name')
        .sort({ startedAt: -1 })
        .limit(limit)
        .lean();

      vmStarts.forEach(vm => {
        if (vm.templateId) {
          activities.push({
            id: vm._id.toString(),
            type: 'vm_started',
            title: (vm.templateId as any)?.name || 'Unknown Machine',
            description: 'Started machine instance',
            timestamp: this.formatTimestamp(vm.startedAt),
            userId,
            username: 'You'
          });
        }
      });

      // Get user's machine flag solves
      const machineSolves = await this.machineSubmissionModel
        .find({ userId, isCorrect: true })
        .populate('templateId', 'name')
        .sort({ submittedAt: -1 })
        .limit(limit)
        .lean();

      machineSolves.forEach(solve => {
        if (solve.templateId) {
          activities.push({
            id: solve._id.toString(),
            type: 'challenge_solved',
            title: `${(solve.templateId as any)?.name || 'Unknown Machine'} - ${solve.flagName}`,
            description: `Captured flag: ${solve.flagName}`,
            timestamp: this.formatTimestamp(solve.submittedAt),
            points: solve.pointsAwarded || 0,
            userId,
            username: 'You'
          });
        }
      });
    } catch (error) {
      console.error('Error fetching user activity:', error);
    }

    // Sort all activities by timestamp and return top results
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  private async getUserTotalActivities(userId: string): Promise<number> {
    try {
      const [challengeSolves, machineSolves, vmStarts] = await Promise.all([
        this.challengeSubmissionModel.countDocuments({ userId, isCorrect: true }),
        this.machineSubmissionModel.countDocuments({ userId, isCorrect: true }),
        this.machineInstanceModel.countDocuments({ ownerId: userId })
      ]);

      return challengeSolves + machineSolves + vmStarts;
    } catch (error) {
      console.error('Error fetching user total activities:', error);
      return 0;
    }
  }

  private formatTimestamp(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return date.toLocaleDateString();
  }

  private calculateSolveTime(solveDate: Date): string {
    // This is a simplified calculation - in a real implementation,
    // you might track when the user first accessed the challenge
    return 'some time';
  }
}