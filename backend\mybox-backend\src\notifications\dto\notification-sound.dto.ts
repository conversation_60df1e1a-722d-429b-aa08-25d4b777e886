import { IsS<PERSON>, <PERSON>Optional, IsBoolean, Is<PERSON><PERSON>ber, <PERSON>, <PERSON> } from 'class-validator';

export class CreateNotificationSoundDto {
  @IsString()
  name: string;

  @IsString()
  displayName: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}

export class UpdateNotificationSoundDto {
  @IsOptional()
  @IsString()
  displayName?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateUserNotificationSettingsDto {
  @IsOptional()
  @IsBoolean()
  soundEnabled?: boolean;

  @IsOptional()
  @IsString()
  selectedSoundId?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  volume?: number;

  @IsOptional()
  @IsBoolean()
  desktopNotifications?: boolean;

  @IsOptional()
  @IsBoolean()
  emailNotifications?: boolean;

  @IsOptional()
  notificationTypes?: {
    admin_message?: boolean;
    first_blood?: boolean;
    challenge_solved?: boolean;
    new_challenge?: boolean;
    new_machine?: boolean;
    machine_first_blood?: boolean;
    system_announcement?: boolean;
    team_invitation?: boolean;
    competition_update?: boolean;
  };
}