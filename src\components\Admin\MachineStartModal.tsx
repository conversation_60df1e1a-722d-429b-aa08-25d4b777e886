import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  Play,
  Flag,
  Crown,
  User,
  Globe,
  BookOpen,
  Monitor,
  Cpu,
  HardDrive,
  Clock,
  Award,
  ChevronDown,
  ChevronRight,
  AlertCircle
} from 'lucide-react';
import { MachineTemplate } from '../../services/machines';
import { getImageUrl } from '../../utils/imageUtils';

interface MachineStartModalProps {
  machine: MachineTemplate;
  isOpen: boolean;
  onClose: () => void;
  onStart: (machineId: string) => Promise<void>;
}

export function MachineStartModal({ machine, isOpen, onClose, onStart }: MachineStartModalProps) {
  const [expandedTopics, setExpandedTopics] = useState<Set<number>>(new Set());
  const [isStarting, setIsStarting] = useState(false);

  const flagTypeIcons = {
    root: Crown,
    user: User,
    'www-data': Globe,
    custom: Flag
  };

  const flagTypeColors = {
    root: 'text-red-400 bg-red-500/20 border-red-500/30',
    user: 'text-blue-400 bg-blue-500/20 border-blue-500/30',
    'www-data': 'text-green-400 bg-green-500/20 border-green-500/30',
    custom: 'text-purple-400 bg-purple-500/20 border-purple-500/30'
  };

  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      easy: 'text-green-400 bg-green-500/20',
      medium: 'text-yellow-400 bg-yellow-500/20',
      hard: 'text-orange-400 bg-orange-500/20',
      insane: 'text-red-400 bg-red-500/20'
    };
    return colors[difficulty as keyof typeof colors] || 'text-gray-400 bg-gray-500/20';
  };

  const toggleTopic = (index: number) => {
    const newExpanded = new Set(expandedTopics);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedTopics(newExpanded);
  };

  const handleStart = async () => {
    try {
      setIsStarting(true);
      await onStart(machine.id || machine._id);
      onClose();
    } catch (error) {
      console.error('Error starting machine:', error);
    } finally {
      setIsStarting(false);
    }
  };

  const sortedTopics = [...(machine.topics || [])].sort((a, b) => a.order - b.order);
  const sortedFlags = [...(machine.flags || [])].sort((a, b) => {
    const typePriority = { root: 4, user: 3, 'www-data': 2, custom: 1 };
    const aPriority = typePriority[a.type] || 0;
    const bPriority = typePriority[b.type] || 0;
    
    if (aPriority !== bPriority) {
      return bPriority - aPriority;
    }
    return b.points - a.points;
  });

  if (!isOpen) return null;

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-purple-600 rounded-lg">
                <Monitor className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">{machine.name}</h2>
                <div className="flex items-center space-x-4 mt-1">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(machine.difficulty)}`}>
                    {machine.difficulty?.toUpperCase()}
                  </span>
                  <span className="text-gray-400 text-sm">{machine.category}</span>
                  <span className="text-gray-400 text-sm">{machine.os}</span>
                </div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Machine Description */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-white mb-3">Description</h3>
            <p className="text-gray-300 leading-relaxed">{machine.description}</p>
          </div>

          {/* Machine Specs */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-white mb-3">Specifications</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-700/50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <HardDrive className="w-4 h-4 text-blue-400" />
                  <span className="text-sm font-medium text-gray-300">Memory</span>
                </div>
                <span className="text-white font-semibold">{machine.requiredRAM || 512} MB</span>
              </div>
              <div className="bg-gray-700/50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Cpu className="w-4 h-4 text-green-400" />
                  <span className="text-sm font-medium text-gray-300">CPU Cores</span>
                </div>
                <span className="text-white font-semibold">{machine.requiredCPU || 1}</span>
              </div>
              <div className="bg-gray-700/50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Clock className="w-4 h-4 text-purple-400" />
                  <span className="text-sm font-medium text-gray-300">Max Instances</span>
                </div>
                <span className="text-white font-semibold">{machine.maxInstances || 10}</span>
              </div>
            </div>
          </div>

          {/* Topics Section */}
          {sortedTopics.length > 0 && (
            <div className="mb-6">
              <div className="flex items-center space-x-2 mb-4">
                <BookOpen className="w-5 h-5 text-purple-400" />
                <h3 className="text-lg font-semibold text-white">Topics & Hints</h3>
              </div>
              
              <div className="space-y-3">
                {sortedTopics.map((topic, index) => (
                  <div
                    key={index}
                    className="border border-gray-700 rounded-lg overflow-hidden"
                  >
                    <button
                      onClick={() => toggleTopic(index)}
                      className="w-full p-4 text-left hover:bg-gray-700/50 transition-colors flex items-center justify-between"
                    >
                      <div className="flex items-center space-x-3">
                        {topic.imageUrl && (
                          <img
                            src={getImageUrl(topic.imageUrl)}
                            alt={topic.title}
                            className="w-8 h-8 object-cover rounded"
                          />
                        )}
                        <div>
                          <h4 className="font-medium text-white">{topic.title}</h4>
                          <p className="text-sm text-gray-400 mt-1 line-clamp-1">
                            {topic.description}
                          </p>
                        </div>
                      </div>
                      {expandedTopics.has(index) ? (
                        <ChevronDown className="w-5 h-5 text-gray-400" />
                      ) : (
                        <ChevronRight className="w-5 h-5 text-gray-400" />
                      )}
                    </button>
                    
                    <AnimatePresence>
                      {expandedTopics.has(index) && (
                        <motion.div
                          className="border-t border-gray-700 p-4 bg-gray-700/30"
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          {topic.imageUrl && (
                            <div className="mb-4">
                              <img
                                src={getImageUrl(topic.imageUrl)}
                                alt={topic.title}
                                className="max-w-full h-auto rounded-lg border border-gray-600"
                              />
                            </div>
                          )}
                          <p className="text-gray-300 whitespace-pre-wrap">
                            {topic.description}
                          </p>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Flags Section */}
          {sortedFlags.length > 0 && (
            <div className="mb-6">
              <div className="flex items-center space-x-2 mb-4">
                <Flag className="w-5 h-5 text-purple-400" />
                <h3 className="text-lg font-semibold text-white">Flags</h3>
              </div>
              
              <div className="grid gap-3">
                {sortedFlags.map((flag, index) => {
                  const IconComponent = flagTypeIcons[flag.type];
                  
                  return (
                    <div
                      key={index}
                      className="border border-gray-700 bg-gray-700/30 rounded-lg p-4"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg border ${flagTypeColors[flag.type]}`}>
                            <IconComponent className="w-4 h-4" />
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-white">{flag.name}</h4>
                              {flag.isRequired && (
                                <span className="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full">
                                  Required
                                </span>
                              )}
                            </div>
                            <div className="flex items-center space-x-4 mt-1">
                              <div className="flex items-center space-x-1">
                                <Award className="w-3 h-3 text-purple-400" />
                                <span className="text-purple-400 text-sm font-medium">{flag.points} points</span>
                              </div>
                              <span className="text-gray-400 text-sm capitalize">{flag.type} flag</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {flag.description && (
                        <p className="text-gray-300 text-sm mt-3">{flag.description}</p>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Warning */}
          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-6">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-yellow-400 mt-0.5" />
              <div>
                <h4 className="text-yellow-400 font-medium">Before Starting</h4>
                <p className="text-yellow-300 text-sm mt-1">
                  Make sure you have reviewed the topics and understand the objectives. 
                  The machine instance will be created and you'll have access to it for solving.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-700 flex justify-end space-x-4">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleStart}
            disabled={isStarting}
            className="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center space-x-2"
          >
            <Play className="w-4 h-4" />
            <span>{isStarting ? 'Starting...' : 'Start Machine'}</span>
          </button>
        </div>
      </motion.div>
    </motion.div>
  );
}
