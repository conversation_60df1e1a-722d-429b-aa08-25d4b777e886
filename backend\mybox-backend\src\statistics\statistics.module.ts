import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { StatisticsController } from './statistics.controller';
import { StatisticsService } from './statistics.service';
import { Challenge, ChallengeSchema } from '../schemas/challenge.schema';
import { ChallengeSubmission, ChallengeSubmissionSchema } from '../schemas/challenge-submission.schema';
import { TeamChallengeSolve, TeamChallengeSolveSchema } from '../schemas/team-challenge-solve.schema';
import { User, UserSchema } from '../schemas/user.schema';
import { Team, TeamSchema } from '../schemas/team.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Challenge.name, schema: ChallengeSchema },
      { name: ChallengeSubmission.name, schema: ChallengeSubmissionSchema },
      { name: TeamChallengeSolve.name, schema: TeamChallengeSolveSchema },
      { name: User.name, schema: UserSchema },
      { name: Team.name, schema: TeamSchema },
    ]),
  ],
  controllers: [StatisticsController],
  providers: [StatisticsService],
  exports: [StatisticsService],
})
export class StatisticsModule {}