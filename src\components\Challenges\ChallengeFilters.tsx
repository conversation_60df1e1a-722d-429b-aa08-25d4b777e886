import React from 'react';
import { Filter } from 'lucide-react';
import { Category } from '../../types';

interface ChallengeFiltersProps {
  categories: Category[];
  selectedCategory: string;
  selectedDifficulty: string;
  showSolved: boolean;
  onCategoryChange: (category: string) => void;
  onDifficultyChange: (difficulty: string) => void;
  onShowSolvedChange: (showSolved: boolean) => void;
}

export function ChallengeFilters({
  categories,
  selectedCategory,
  selectedDifficulty,
  showSolved,
  onCategoryChange,
  onDifficultyChange,
  onShowSolvedChange
}: ChallengeFiltersProps) {
  const difficulties = ['all', 'easy', 'medium', 'hard', 'insane'];

  // Create category options with 'all' first, then sorted categories
  const categoryOptions = [
    { value: 'all', label: 'All Categories' },
    ...categories
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .map(cat => ({ value: cat.name, label: cat.displayName }))
  ];

  return (
    <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6 mb-8">
      <div className="flex items-center space-x-2 mb-4">
        <Filter className="w-5 h-5 text-slate-400" />
        <h3 className="text-lg font-semibold text-white">Filters</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">Category</label>
          <select
            value={selectedCategory}
            onChange={(e) => onCategoryChange(e.target.value)}
            className="w-full px-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400 capitalize"
          >
            {categoryOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">Difficulty</label>
          <select
            value={selectedDifficulty}
            onChange={(e) => onDifficultyChange(e.target.value)}
            className="w-full px-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400 capitalize"
          >
            {difficulties.map(difficulty => (
              <option key={difficulty} value={difficulty}>
                {difficulty === 'all' ? 'All Difficulties' : difficulty}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">Status</label>
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={showSolved}
                onChange={(e) => onShowSolvedChange(e.target.checked)}
                className="w-4 h-4 text-emerald-600 bg-slate-800 border-slate-700 rounded focus:ring-emerald-500 focus:ring-2"
              />
              <span className="text-sm text-slate-300">Show solved</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}