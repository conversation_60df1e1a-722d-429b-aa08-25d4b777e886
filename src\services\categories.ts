import { API_BASE_URL } from './api';

export interface Category {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  color: string;
  icon: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface CategoryStats {
  id: string;
  name: string;
  displayName: string;
  color: string;
  icon: string;
  totalChallenges: number;
  isActive: boolean;
  sortOrder: number;
}

export interface CategoriesResponse {
  categories: Category[];
}

export interface CategoryStatsResponse {
  stats: CategoryStats[];
}

export class CategoriesService {
  private static getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('mybox_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  private static async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  static async getCategories(): Promise<Category[]> {
    const response = await fetch(`${API_BASE_URL}/categories`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<Category[]>(response);
  }

  static async getCategoryStats(): Promise<CategoryStats[]> {
    const response = await fetch(`${API_BASE_URL}/categories/stats`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<CategoryStats[]>(response);
  }

  // Admin methods
  static async createCategory(categoryData: {
    name: string;
    displayName: string;
    description?: string;
    color: string;
    icon: string;
    isActive?: boolean;
    sortOrder?: number;
  }): Promise<Category> {
    const response = await fetch(`${API_BASE_URL}/admin/categories`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(categoryData),
    });

    return this.handleResponse<Category>(response);
  }

  static async updateCategory(id: string, categoryData: Partial<{
    name: string;
    displayName: string;
    description?: string;
    color: string;
    icon: string;
    isActive: boolean;
    sortOrder: number;
  }>): Promise<Category> {
    const response = await fetch(`${API_BASE_URL}/admin/categories/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(categoryData),
    });

    return this.handleResponse<Category>(response);
  }

  static async deleteCategory(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/admin/categories/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
  }

  static async getAllCategories(includeInactive = false): Promise<Category[]> {
    const params = includeInactive ? '?includeInactive=true' : '';
    const response = await fetch(`${API_BASE_URL}/admin/categories${params}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<Category[]>(response);
  }
}
