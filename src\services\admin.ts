// Admin API Service
import { API_BASE_URL } from './api';

export interface AdminUserListItem {
  id: string;
  username: string;
  email: string;
  role: 'user' | 'admin' | 'moderator';
  score: number;
  rank: number;
  isActive: boolean;
  isEmailVerified: boolean;
  lastActive: string;
  createdAt: string;
  teamId?: string | null;
}

export interface AdminTeamListItem {
  id: string;
  name: string;
  description: string;
  isPublic: boolean;
  captainId: string;
  captainName: string;
  memberCount: number;
  teamScore: number;
  rank: number;
  isActive: boolean;
  createdAt: string;
}

export interface Flag {
  value: string;
  isCaseSensitive: boolean;
  points: number;
  description?: string;
}

export interface ChallengeFile {
  name: string;
  path: string;
  size?: number;
  type?: string;
  description?: string;
}

export interface FirstBlood {
  userId: string;
  username: string;
  timestamp: string;
  teamId?: string;
  teamName?: string;
}

export interface AdminChallengeListItem {
  id: string;
  title: string;
  category: 'web' | 'crypto' | 'pwn' | 'reverse' | 'forensics' | 'misc';
  difficulty: 'easy' | 'medium' | 'hard' | 'insane';
  points: number;
  solveCount: number;
  isActive: boolean;
  releaseDate: string;
  expiryDate?: string;
  createdAt: string;
  authorId: string;
  authorName?: string;
  description?: string;
  hints?: string[];
  tags?: string[];
  flag?: string;
  flags?: Flag[];
  files?: ChallengeFile[];
  firstBlood?: FirstBlood;
  dockerImage?: string;
  requiresServer?: boolean;
  serverConfig?: any;
}

export interface AdminSystemStats {
  totalUsers: number;
  activeUsers: number;
  totalChallenges: number;
  activeChallenges: number;
  totalTeams: number;
  activeTeams: number;
  totalVMs: number;
  runningVMs: number;
  totalSolves: number;
  todaysSolves: number;
}

export interface AdminRecentActivity {
  id: string;
  type: 'user_registered' | 'challenge_solved' | 'vm_started' | 'vm_stopped' | 'flag_submitted' | 'team_created';
  userId: string;
  username: string;
  details: string;
  metadata?: any;
  timestamp: string;
}

export interface AdminPaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

export interface AdminUserPaginationResponse {
  users: AdminUserListItem[];
  total: number;
  page: number;
  pages: number;
  limit: number;
}

export interface AdminTeamPaginationResponse {
  teams: AdminTeamListItem[];
  total: number;
  page: number;
  pages: number;
  limit: number;
}

export interface AdminChallengePaginationResponse {
  challenges: AdminChallengeListItem[];
  total: number;
  page: number;
  pages: number;
  limit: number;
}

export interface AdminActivityPaginationResponse {
  activities: AdminRecentActivity[];
  total: number;
  page: number;
  pages: number;
  limit: number;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  role?: 'user' | 'admin' | 'moderator';
  avatarUrl?: string;
  country?: string;
  bio?: string;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  role?: 'user' | 'admin' | 'moderator';
  avatarUrl?: string;
  country?: string;
  bio?: string;
}

export interface CreateChallengeRequest {
  title: string;
  description: string;
  category: 'web' | 'crypto' | 'pwn' | 'reverse' | 'forensics' | 'misc';
  difficulty: 'easy' | 'medium' | 'hard' | 'insane';
  points?: number;
  // Legacy flag support
  flag?: string;
  // Multiple flags support
  flags?: Flag[];
  hints?: string[];
  tags?: string[];
  authorId?: string;
  authorName?: string;
  files?: ChallengeFile[];
  isActive?: boolean;
  releaseDate?: string;
  expiryDate?: string;
  dockerImage?: string;
  requiresServer?: boolean;
  serverConfig?: any;
}

export interface UpdateChallengeRequest {
  title?: string;
  description?: string;
  category?: 'web' | 'crypto' | 'pwn' | 'reverse' | 'forensics' | 'misc';
  difficulty?: 'easy' | 'medium' | 'hard' | 'insane';
  points?: number;
  // Legacy flag support
  flag?: string;
  // Multiple flags support
  flags?: Flag[];
  hints?: string[];
  tags?: string[];
  authorId?: string;
  authorName?: string;
  files?: ChallengeFile[];
  isActive?: boolean;
  releaseDate?: string;
  expiryDate?: string;
  dockerImage?: string;
  requiresServer?: boolean;
  serverConfig?: any;
}

export class AdminService {
  private static getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('mybox_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  private static async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Request failed' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Helper method to transform user objects from MongoDB format to frontend format
  private static transformUser(user: any): AdminUserListItem {
    return {
      id: user._id || user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      score: user.score,
      rank: user.rank,
      isActive: user.isActive,
      isEmailVerified: user.isEmailVerified || false,
      lastActive: user.lastActive,
      createdAt: user.createdAt,
      teamId: user.teamId
    };
  }

  // Helper method to transform team objects from MongoDB format to frontend format
  private static transformTeam(team: any): AdminTeamListItem {
    return {
      id: team._id || team.id,
      name: team.name,
      description: team.description,
      isPublic: team.isPublic,
      captainId: team.captainId,
      captainName: team.captainName || 'Unknown',
      memberCount: team.memberCount || 0,
      teamScore: team.teamScore || 0,
      rank: team.rank || 0,
      isActive: team.isActive,
      createdAt: team.createdAt
    };
  }

  // Helper method to transform challenge objects from MongoDB format to frontend format
  private static transformChallenge(challenge: any): AdminChallengeListItem {
    return {
      id: challenge._id || challenge.id,
      title: challenge.title,
      category: challenge.category,
      difficulty: challenge.difficulty,
      points: challenge.points,
      solveCount: challenge.solveCount || 0,
      isActive: challenge.isActive,
      releaseDate: challenge.releaseDate || challenge.createdAt,
      createdAt: challenge.createdAt,
      authorId: challenge.authorId,
      authorName: challenge.authorName || 'Unknown'
    };
  }

  static async getSystemStats(): Promise<AdminSystemStats> {
    const response = await fetch(`${API_BASE_URL}/admin/stats`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<AdminSystemStats>(response);
  }

  static async getRecentActivity(params?: AdminPaginationParams): Promise<AdminActivityPaginationResponse> {
    const queryParams = new URLSearchParams();
    if (params) {
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortDirection) queryParams.append('sortDirection', params.sortDirection);
    }

    const response = await fetch(`${API_BASE_URL}/admin/activities?${queryParams}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<AdminActivityPaginationResponse>(response);
  }  static async getUsers(params?: AdminPaginationParams): Promise<AdminUserPaginationResponse> {
    const queryParams = new URLSearchParams();
    if (params) {
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.search) queryParams.append('search', params.search);
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortDirection) queryParams.append('sortDirection', params.sortDirection);
    }

    const response = await fetch(`${API_BASE_URL}/admin/users?${queryParams}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    const data = await this.handleResponse<any>(response);
    
    // Transform _id to id in each user
    const transformedUsers = data.users.map((user: any) => this.transformUser(user));
    
    return {
      users: transformedUsers,
      total: data.total,
      page: data.page,
      pages: data.pages,
      limit: data.limit
    };
  }
  static async createUser(userData: CreateUserRequest): Promise<AdminUserListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/users`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(userData),
    });

    const data = await this.handleResponse<any>(response);
    return {
      id: data._id,
      username: data.username,
      email: data.email,
      role: data.role,
      score: data.score,
      rank: data.rank,
      isActive: data.isActive,
      lastActive: data.lastActive,
      createdAt: data.createdAt,
      teamId: data.teamId
    };
  }
  static async getUser(userId: string): Promise<AdminUserListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    const data = await this.handleResponse<any>(response);
    return {
      id: data._id,
      username: data.username,
      email: data.email,
      role: data.role,
      score: data.score,
      rank: data.rank,
      isActive: data.isActive,
      lastActive: data.lastActive,
      createdAt: data.createdAt,
      teamId: data.teamId
    };
  }
  static async updateUser(userId: string, userData: UpdateUserRequest): Promise<AdminUserListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(userData),
    });

    const data = await this.handleResponse<any>(response);
    return {
      id: data._id,
      username: data.username,
      email: data.email,
      role: data.role,
      score: data.score,
      rank: data.rank,
      isActive: data.isActive,
      lastActive: data.lastActive,
      createdAt: data.createdAt,
      teamId: data.teamId
    };
  }
  static async updateUserStatus(userId: string, isActive: boolean): Promise<AdminUserListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/status`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ isActive }),
    });

    const data = await this.handleResponse<any>(response);
    return {
      id: data._id,
      username: data.username,
      email: data.email,
      role: data.role,
      score: data.score,
      rank: data.rank,
      isActive: data.isActive,
      lastActive: data.lastActive,
      createdAt: data.createdAt,
      teamId: data.teamId
    };
  }
  static async updateUserRole(userId: string, role: 'user' | 'admin' | 'moderator'): Promise<AdminUserListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/role`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ role }),
    });

    const data = await this.handleResponse<any>(response);
    return {
      id: data._id,
      username: data.username,
      email: data.email,
      role: data.role,
      score: data.score,
      rank: data.rank,
      isActive: data.isActive,
      lastActive: data.lastActive,
      createdAt: data.createdAt,
      teamId: data.teamId
    };
  }

  static async deleteUser(userId: string): Promise<{ message: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ message: string }>(response);
  }

  static async verifyUserEmail(userId: string): Promise<AdminUserListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/verify-email`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to verify user email');
    }

    const data = await response.json();
    return {
      id: data._id || data.id,
      username: data.username,
      email: data.email,
      role: data.role,
      score: data.score,
      rank: data.rank,
      isActive: data.isActive,
      isEmailVerified: data.isEmailVerified,
      lastActive: data.lastActive,
      createdAt: data.createdAt,
      teamId: data.teamId
    };
  }
  static async getTeams(params?: AdminPaginationParams): Promise<AdminTeamPaginationResponse> {
    const queryParams = new URLSearchParams();
    if (params) {
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.search) queryParams.append('search', params.search);
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortDirection) queryParams.append('sortDirection', params.sortDirection);
    }

    const response = await fetch(`${API_BASE_URL}/admin/teams?${queryParams}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    const data = await this.handleResponse<any>(response);
    
    // Transform _id to id in each team
    const transformedTeams = data.teams.map((team: any) => ({
      id: team._id,
      name: team.name,
      description: team.description,
      isPublic: team.isPublic,
      captainId: team.captainId,
      captainName: team.captainName || 'Unknown',
      memberCount: team.memberCount,
      teamScore: team.teamScore,
      rank: team.rank,
      isActive: team.isActive,
      createdAt: team.createdAt
    }));
    
    return {
      teams: transformedTeams,
      total: data.total,
      page: data.page,
      pages: data.pages,
      limit: data.limit
    };
  }

  static async getTeam(teamId: string): Promise<AdminTeamListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/teams/${teamId}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<AdminTeamListItem>(response);
  }

  static async updateTeamStatus(teamId: string, isActive: boolean): Promise<AdminTeamListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/teams/${teamId}/status`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ isActive }),
    });

    return this.handleResponse<AdminTeamListItem>(response);
  }

  static async deleteTeam(teamId: string): Promise<{ message: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/teams/${teamId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ message: string }>(response);
  }
  static async getChallenges(params?: AdminPaginationParams): Promise<AdminChallengePaginationResponse> {
    const queryParams = new URLSearchParams();
    if (params) {
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.search) queryParams.append('search', params.search);
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortDirection) queryParams.append('sortDirection', params.sortDirection);
    }

    const response = await fetch(`${API_BASE_URL}/admin/challenges?${queryParams}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    const data = await this.handleResponse<any>(response);
    
    // Transform _id to id in each challenge
    const transformedChallenges = data.challenges.map((challenge: any) => ({
      id: challenge._id,
      title: challenge.title,
      category: challenge.category,
      difficulty: challenge.difficulty,
      points: challenge.points,
      solveCount: challenge.solveCount || 0,
      isActive: challenge.isActive,
      releaseDate: challenge.releaseDate,
      createdAt: challenge.createdAt,
      authorId: challenge.authorId,
      authorName: challenge.authorName || 'Unknown'
    }));
    
    return {
      challenges: transformedChallenges,
      total: data.total,
      page: data.page,
      pages: data.pages,
      limit: data.limit
    };
  }

  static async getChallenge(challengeId: string): Promise<AdminChallengeListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/challenges/${challengeId}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<AdminChallengeListItem>(response);
  }
  static async updateChallengeStatus(challengeId: string, isActive: boolean): Promise<AdminChallengeListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/challenges/${challengeId}/status`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ isActive }),
    });

    return this.handleResponse<AdminChallengeListItem>(response);
  }

  static async deleteChallenge(challengeId: string): Promise<{ message: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/challenges/${challengeId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ message: string }>(response);
  }
  static async createChallenge(challenge: CreateChallengeRequest): Promise<AdminChallengeListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/challenges`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(challenge),
    });

    const data = await this.handleResponse<any>(response);
    
    // Transform the response using our enhanced transformation function
    return this.transformFullChallenge(data);
  }

  static async updateChallenge(challengeId: string, challenge: UpdateChallengeRequest): Promise<AdminChallengeListItem> {
    const response = await fetch(`${API_BASE_URL}/admin/challenges/${challengeId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(challenge),
    });

    const data = await this.handleResponse<any>(response);
    
    // Transform the response using our enhanced transformation function
    return this.transformFullChallenge(data);
  }
  
  // Helper method to transform challenge objects with all the new fields
  private static transformFullChallenge(challenge: any): AdminChallengeListItem {
    return {
      id: challenge._id || challenge.id,
      title: challenge.title,
      description: challenge.description,
      category: challenge.category,
      difficulty: challenge.difficulty,
      points: challenge.points,
      solveCount: challenge.solveCount || 0,
      isActive: challenge.isActive,
      releaseDate: challenge.releaseDate,
      expiryDate: challenge.expiryDate,
      createdAt: challenge.createdAt,
      authorId: challenge.authorId || '',
      authorName: challenge.authorName || 'Unknown',
      hints: challenge.hints || [],
      tags: challenge.tags || [],
      flag: challenge.flag,
      flags: challenge.flags || [],
      files: challenge.files || [],
      firstBlood: challenge.firstBlood || null,
      dockerImage: challenge.dockerImage,
      requiresServer: challenge.requiresServer || false,
      serverConfig: challenge.serverConfig
    };
  }
}
