import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Send, 
  Users, 
  MessageSquare, 
  AlertCircle, 
  Bell,
  Plus,
  Trash2,
  Edit,
  Eye,
  Filter,
  RefreshCw
} from 'lucide-react';
import { notificationsService, Notification, CreateNotificationDto } from '../../services/notifications';

interface NotificationForm {
  title: string;
  message: string;
  type: string;
  priority: string;
  recipients: string[];
  isGlobal: boolean;
  expiresAt: string;
}

const initialForm: NotificationForm = {
  title: '',
  message: '',
  type: 'admin_message',
  priority: 'medium',
  recipients: [],
  isGlobal: true,
  expiresAt: ''
};

const notificationTypes = [
  { value: 'admin_message', label: 'Admin Message', icon: MessageSquare },
  { value: 'system_announcement', label: 'System Announcement', icon: AlertCircle },
  { value: 'competition_update', label: 'Competition Update', icon: Bell },
];

const priorityLevels = [
  { value: 'low', label: 'Low', color: 'text-gray-400' },
  { value: 'medium', label: 'Medium', color: 'text-blue-400' },
  { value: 'high', label: 'High', color: 'text-orange-400' },
  { value: 'urgent', label: 'Urgent', color: 'text-red-400' },
];

export function NotificationManager() {
  const [form, setForm] = useState<NotificationForm>(initialForm);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [filter, setFilter] = useState({ type: '', priority: '' });
  const [stats, setStats] = useState({ connectedUsers: 0, connectedUsersList: [] });

  useEffect(() => {
    loadNotifications();
    loadStats();
  }, []);

  const loadNotifications = async () => {
    setLoading(true);
    try {
      const response = await notificationsService.getAdminNotifications(filter);
      setNotifications(response.notifications);
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await notificationsService.getNotificationStats();
      setStats(response);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSending(true);

    try {
      const notificationData: CreateNotificationDto = {
        title: form.title,
        message: form.message,
        type: form.type,
        priority: form.priority,
        recipients: form.isGlobal ? undefined : form.recipients,
        isGlobal: form.isGlobal,
        expiresAt: form.expiresAt || undefined,
      };

      if (editingId) {
        await notificationsService.updateNotification(editingId, notificationData);
      } else {
        if (form.type === 'system_announcement') {
          await notificationsService.createSystemAnnouncement(notificationData);
        } else {
          await notificationsService.sendAdminNotification(notificationData);
        }
      }

      setForm(initialForm);
      setShowForm(false);
      setEditingId(null);
      loadNotifications();
    } catch (error) {
      console.error('Failed to send notification:', error);
    } finally {
      setSending(false);
    }
  };

  const handleEdit = (notification: Notification) => {
    setForm({
      title: notification.title,
      message: notification.message,
      type: notification.type,
      priority: notification.priority,
      recipients: notification.recipients.map(r => r.toString()),
      isGlobal: notification.isGlobal,
      expiresAt: notification.expiresAt ? new Date(notification.expiresAt).toISOString().slice(0, 16) : '',
    });
    setEditingId(notification._id);
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this notification?')) {
      try {
        await notificationsService.deleteNotification(id);
        loadNotifications();
      } catch (error) {
        console.error('Failed to delete notification:', error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Notification Manager</h2>
          <p className="text-slate-400">Send notifications to users and manage announcements</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 px-3 py-2 bg-green-500/20 border border-green-500/30 rounded-lg">
            <Users className="w-4 h-4 text-green-400" />
            <span className="text-green-400 font-medium">
              {stats.connectedUsers} online
            </span>
          </div>
          
          <button
            onClick={() => {
              setForm(initialForm);
              setEditingId(null);
              setShowForm(!showForm);
            }}
            className="flex items-center space-x-2 px-4 py-2 bg-purple-600/20 text-purple-400 border border-purple-600/30 rounded-lg hover:bg-purple-600/30 transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>New Notification</span>
          </button>
        </div>
      </div>

      {/* Notification Form */}
      {showForm && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6"
        >
          <h3 className="text-lg font-semibold text-white mb-4">
            {editingId ? 'Edit Notification' : 'Create New Notification'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Title
                </label>
                <input
                  type="text"
                  value={form.title}
                  onChange={(e) => setForm(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-purple-400"
                  placeholder="Notification title"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Type
                </label>
                <select
                  value={form.type}
                  onChange={(e) => setForm(prev => ({ ...prev, type: e.target.value }))}
                  className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-purple-400"
                >
                  {notificationTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Priority
                </label>
                <select
                  value={form.priority}
                  onChange={(e) => setForm(prev => ({ ...prev, priority: e.target.value }))}
                  className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-purple-400"
                >
                  {priorityLevels.map(priority => (
                    <option key={priority.value} value={priority.value}>
                      {priority.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Expires At (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={form.expiresAt}
                  onChange={(e) => setForm(prev => ({ ...prev, expiresAt: e.target.value }))}
                  className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-purple-400"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Message
              </label>
              <textarea
                value={form.message}
                onChange={(e) => setForm(prev => ({ ...prev, message: e.target.value }))}
                rows={4}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-purple-400"
                placeholder="Notification message"
                required
              />
            </div>

            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={form.isGlobal}
                  onChange={(e) => setForm(prev => ({ ...prev, isGlobal: e.target.checked }))}
                  className="w-4 h-4 text-purple-600 bg-slate-800 border-slate-600 rounded focus:ring-purple-500"
                />
                <span className="text-slate-300">Send to all users</span>
              </label>
            </div>

            <div className="flex items-center space-x-4 pt-4">
              <button
                type="submit"
                disabled={sending}
                className="flex items-center space-x-2 px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
              >
                <Send className="w-4 h-4" />
                <span>{sending ? 'Sending...' : editingId ? 'Update' : 'Send'}</span>
              </button>
              
              <button
                type="button"
                onClick={() => {
                  setShowForm(false);
                  setForm(initialForm);
                  setEditingId(null);
                }}
                className="px-6 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </motion.div>
      )}

      {/* Filters */}
      <div className="flex items-center space-x-4 p-4 bg-slate-800/30 backdrop-blur-sm border border-slate-700/50 rounded-xl">
        <Filter className="w-5 h-5 text-slate-400" />
        
        <select
          value={filter.type}
          onChange={(e) => setFilter(prev => ({ ...prev, type: e.target.value }))}
          className="px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-purple-400"
        >
          <option value="">All Types</option>
          {notificationTypes.map(type => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>
        
        <select
          value={filter.priority}
          onChange={(e) => setFilter(prev => ({ ...prev, priority: e.target.value }))}
          className="px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-purple-400"
        >
          <option value="">All Priorities</option>
          {priorityLevels.map(priority => (
            <option key={priority.value} value={priority.value}>
              {priority.label}
            </option>
          ))}
        </select>
        
        <button
          onClick={loadNotifications}
          disabled={loading}
          className="flex items-center space-x-2 px-3 py-2 bg-blue-600/20 text-blue-400 border border-blue-600/30 rounded-lg hover:bg-blue-600/30 transition-colors"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Notifications List */}
      <div className="space-y-4">
        {notifications.length === 0 ? (
          <div className="text-center py-12">
            <Bell className="w-16 h-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-slate-400 mb-2">No notifications found</h3>
            <p className="text-slate-500">Create your first notification to get started</p>
          </div>
        ) : (
          notifications.map((notification) => (
            <motion.div
              key={notification._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="p-6 bg-slate-800/30 backdrop-blur-sm border border-slate-700/50 rounded-xl"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="font-semibold text-white">{notification.title}</h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      notification.priority === 'urgent' ? 'bg-red-500/20 text-red-400' :
                      notification.priority === 'high' ? 'bg-orange-500/20 text-orange-400' :
                      notification.priority === 'medium' ? 'bg-blue-500/20 text-blue-400' :
                      'bg-gray-500/20 text-gray-400'
                    }`}>
                      {notification.priority.toUpperCase()}
                    </span>
                    <span className="px-2 py-1 text-xs font-medium bg-purple-500/20 text-purple-400 rounded-full">
                      {notification.type.replace('_', ' ').toUpperCase()}
                    </span>
                    {notification.isGlobal && (
                      <span className="px-2 py-1 text-xs font-medium bg-green-500/20 text-green-400 rounded-full">
                        GLOBAL
                      </span>
                    )}
                  </div>
                  
                  <p className="text-slate-300 mb-3">{notification.message}</p>
                  
                  <div className="flex items-center space-x-4 text-sm text-slate-400">
                    <span>Created: {formatDate(notification.createdAt)}</span>
                    {notification.expiresAt && (
                      <span>Expires: {formatDate(notification.expiresAt)}</span>
                    )}
                    {notification.sender && (
                      <span>By: {notification.sender.username}</span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(notification)}
                    className="p-2 text-blue-400 hover:bg-blue-500/20 rounded-lg transition-colors"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  
                  <button
                    onClick={() => handleDelete(notification._id)}
                    className="p-2 text-red-400 hover:bg-red-500/20 rounded-lg transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>
    </div>
  );
}