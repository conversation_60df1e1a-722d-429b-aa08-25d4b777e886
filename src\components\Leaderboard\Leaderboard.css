/* Leaderboard Page - Creative Design */

/* Custom scrollbar for leaderboard page */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(139, 92, 246, 0.1);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(139, 92, 246, 0.1);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #8B5CF6, #EC4899);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #A78BFA, #F472B6);
}

/* Glass card effects - Always visible */
.glass-card {
  backdrop-filter: blur(20px) !important;
  background: rgba(139, 92, 246, 0.1) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(139, 92, 246, 0.1),
    inset 0 0 20px rgba(139, 92, 246, 0.05) !important;
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(139, 92, 246, 0.15) !important;
  border: 1px solid rgba(139, 92, 246, 0.4) !important;
  transform: translateY(-2px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(139, 92, 246, 0.2),
    inset 0 0 30px rgba(139, 92, 246, 0.1) !important;
}

.glass-card-dark {
  backdrop-filter: blur(20px) !important;
  background: rgba(139, 92, 246, 0.08) !important;
  border: 1px solid rgba(139, 92, 246, 0.25) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(139, 92, 246, 0.1),
    inset 0 0 20px rgba(139, 92, 246, 0.05) !important;
  transition: all 0.3s ease;
}

.glass-card-dark:hover {
  background: rgba(139, 92, 246, 0.12) !important;
  border: 1px solid rgba(139, 92, 246, 0.35) !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(139, 92, 246, 0.15),
    inset 0 0 30px rgba(139, 92, 246, 0.08) !important;
}

/* Leaderboard entry hover effects */
.leaderboard-entry {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.leaderboard-entry:hover {
  z-index: 10;
  transform: translateY(-2px);
}

.leaderboard-entry::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(236, 72, 153, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.leaderboard-entry:hover::before {
  opacity: 1;
}

/* Rank badge animations */
.rank-badge {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.rank-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.rank-badge:hover::before {
  left: 100%;
}

/* Trophy animations */
.trophy-glow {
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
  animation: trophy-pulse 2s ease-in-out infinite;
}

@keyframes trophy-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
  }
}

.medal-glow {
  filter: drop-shadow(0 0 8px rgba(192, 192, 192, 0.5));
  animation: medal-pulse 2s ease-in-out infinite;
}

@keyframes medal-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 8px rgba(192, 192, 192, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 16px rgba(192, 192, 192, 0.8));
  }
}

.bronze-glow {
  filter: drop-shadow(0 0 8px rgba(205, 127, 50, 0.5));
  animation: bronze-pulse 2s ease-in-out infinite;
}

@keyframes bronze-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 8px rgba(205, 127, 50, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 16px rgba(205, 127, 50, 0.8));
  }
}

/* Enhanced avatar animations */
.avatar-enhanced {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.avatar-enhanced::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.avatar-enhanced:hover::before {
  opacity: 1;
  animation: avatar-shimmer 1.5s ease-in-out infinite;
}

@keyframes avatar-shimmer {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Top performer special effects */
.top-performer-glow {
  position: relative;
  overflow: visible;
}

.top-performer-glow::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #fbbf24);
  background-size: 400% 400%;
  border-radius: inherit;
  z-index: -1;
  animation: top-performer-pulse 3s ease-in-out infinite;
  opacity: 0.6;
}

@keyframes top-performer-pulse {
  0%, 100% {
    background-position: 0% 50%;
    opacity: 0.6;
  }
  50% {
    background-position: 100% 50%;
    opacity: 0.8;
  }
}

/* Enhanced download button */
.download-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.download-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.download-button:hover::before {
  left: 100%;
}

.download-button:active {
  transform: scale(0.95);
}

/* Leaderboard entry enhancements */
.leaderboard-entry-enhanced {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  overflow: hidden;
}

.leaderboard-entry-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.leaderboard-entry-enhanced:hover::before {
  opacity: 1;
}

/* Rank badge enhancements */
.rank-badge-enhanced {
  position: relative;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(236, 72, 153, 0.2));
  border: 1px solid rgba(139, 92, 246, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.rank-badge-enhanced:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
}

/* Score animation */
.score-animated {
  position: relative;
  background: linear-gradient(135deg, #8B5CF6, #EC4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: score-glow 2s ease-in-out infinite alternate;
}

@keyframes score-glow {
  0% {
    filter: brightness(1);
  }
  100% {
    filter: brightness(1.2);
  }
}

/* Team avatar special styling */
.team-avatar-enhanced {
  position: relative;
  background: linear-gradient(135deg, var(--team-color-start), var(--team-color-end));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.team-avatar-enhanced::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.team-avatar-enhanced:hover::before {
  width: 100%;
  height: 100%;
}

/* Sparkle animation for top performers */
.sparkle-effect {
  position: relative;
}

.sparkle-effect::after {
  content: '✨';
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 12px;
  animation: sparkle-twinkle 2s ease-in-out infinite;
  opacity: 0;
}

.sparkle-effect:hover::after {
  opacity: 1;
}

@keyframes sparkle-twinkle {
  0%, 100% {
    transform: scale(0.8) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 1;
  }
}

/* Enhanced pagination */
.pagination-enhanced {
  backdrop-filter: blur(20px);
  background: rgba(139, 92, 246, 0.05);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

/* Loading animation enhancement */
.loading-enhanced {
  position: relative;
}

.loading-enhanced::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  margin: -30px 0 0 -30px;
  border: 3px solid rgba(139, 92, 246, 0.2);
  border-top: 3px solid #8B5CF6;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .avatar-enhanced {
    width: 40px;
    height: 40px;
  }
  
  .rank-badge-enhanced {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }
  
  .score-animated {
    font-size: 18px;
  }
}

/* Dark theme specific enhancements */
@media (prefers-color-scheme: dark) {
  .glass-card,
  .glass-card-dark {
    backdrop-filter: blur(25px);
    background: rgba(139, 92, 246, 0.08);
    border: 1px solid rgba(139, 92, 246, 0.25);
  }
  
  .leaderboard-entry-enhanced:hover {
    background: rgba(139, 92, 246, 0.1);
  }
}

/* Floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Morph shapes */
.morph-shape {
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: translate3d(0, 0, 0) rotateZ(0.01deg);
  }
  34% {
    border-radius: 70% 60% 70% 30% / 50% 60% 30% 60%;
    transform: translate3d(5px, -10px, 0) rotateZ(0.01deg);
  }
  67% {
    border-radius: 100% 60% 60% 100% / 100% 100% 60% 60%;
    transform: translate3d(-5px, 10px, 0) rotateZ(0.01deg);
  }
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text {
  background: linear-gradient(-45deg, #8B5CF6, #EC4899, #06B6D4, #10B981);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Score counter animation */
.score-counter {
  position: relative;
  overflow: hidden;
}

.score-counter::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.3), transparent);
  transform: translateX(-100%);
  animation: score-shine 3s ease-in-out infinite;
}

@keyframes score-shine {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Tab button enhancements */
.tab-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.tab-button:hover::before {
  left: 100%;
}

.tab-button.active {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(236, 72, 153, 0.2)) !important;
  border: 1px solid rgba(139, 92, 246, 0.4) !important;
  box-shadow: 
    0 4px 16px rgba(139, 92, 246, 0.3),
    0 0 20px rgba(139, 92, 246, 0.2) !important;
}

/* Avatar glow effects */
.avatar-glow {
  position: relative;
}

.avatar-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #8B5CF6, #EC4899, #06B6D4, #10B981);
  border-radius: 50%;
  z-index: -1;
  animation: avatar-rotate 3s linear infinite;
}

@keyframes avatar-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Pagination enhancements */
.pagination-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.pagination-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.3), transparent);
  transition: left 0.5s;
}

.pagination-button:hover::before {
  left: 100%;
}

.pagination-button.active {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(236, 72, 153, 0.2)) !important;
  border: 1px solid rgba(139, 92, 246, 0.4) !important;
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.3) !important;
}

/* Loading animation */
.loading-spinner {
  border: 3px solid rgba(139, 92, 246, 0.3);
  border-top: 3px solid #8B5CF6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Stagger animation delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }
.stagger-7 { animation-delay: 0.7s; }
.stagger-8 { animation-delay: 0.8s; }

/* Hover lift effect */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .glass-card-dark {
    padding: 1rem;
  }
  
  .leaderboard-entry {
    margin-bottom: 0.5rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .glass-card,
  .glass-card-dark {
    backdrop-filter: blur(25px);
  }
}

/* Text clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}