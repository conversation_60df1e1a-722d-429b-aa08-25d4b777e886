# Server configuration
dev tun
proto udp
port 1194
server ******** *************

# Security
cipher AES-256-CBC
auth SHA256
comp-lzo

# Certificates
ca /etc/openvpn/pki/ca.crt
cert /etc/openvpn/pki/issued/server.crt
key /etc/openvpn/pki/private/server.key
dh /etc/openvpn/pki/dh.pem
tls-auth /etc/openvpn/pki/ta.key 0
key-direction 0

# Security settings
user nobody
group nogroup
persist-key
persist-tun

# Network settings
keepalive 10 120
verb 3

# Push routes to client
push "redirect-gateway def1 bypass-dhcp"
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# TLS settings
tls-version-min 1.2
tls-cipher "DEFAULT:@SECLEVEL=0"
data-ciphers AES-256-CBC:AES-256-CFB:AES-256-CFB1:AES-256-CFB8:AES-256-OFB:AES-256-GCM
data-ciphers-fallback AES-128-CBC

# Client configuration
client-config-dir /etc/openvpn/ccd
script-security 2
up "/etc/openvpn/up.sh"
down "/etc/openvpn/down.sh"

# Logging
log-append /var/log/openvpn/openvpn.log
status /var/log/openvpn/status.log

# Performance
tun-mtu 1500
mssfix 1400
sndbuf 0
rcvbuf 0

# Security
reneg-sec 0
remote-cert-tls client

# Compression
compress lz4-v2
push "compress lz4-v2"
status /tmp/openvpn-status.log 30
status-version 3

# Security
cipher AES-256-GCM
auth SHA256
tls-version-min 1.2
tls-cipher TLS-ECDHE-ECDSA-WITH-AES-128-GCM-SHA256:TLS-ECDHE-RSA-WITH-AES-128-GCM-SHA256:TLS-ECDHE-ECDSA-WITH-AES-256-GCM-SHA384:TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384

# User/Group
user nobody
group nogroup

# Compression
compress lz4-v2
push "compress lz4-v2"

# Network Configuration
# Route all client traffic through the VPN
push "redirect-gateway def1 bypass-dhcp"

# Prevent DNS leaks
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# Prevent IPv6 leaks
push "block-ipv6"

# Allow client-to-client communication
client-to-client

# Enable NAT
script-security 2
up "/etc/openvpn/up.sh"
down "/etc/openvpn/down.sh"

# Push routes to the client
push "route 0.0.0.0 0.0.0.0 vpn_gateway 500"
push "block-outside-dns"

# Route Configuration
route ************* *************

# Security Enhancements
reneg-sec 0
remote-cert-tls client
tls-server

# Performance
tun-mtu 1500
mssfix 1450
sndbuf 0
rcvbuf 0
