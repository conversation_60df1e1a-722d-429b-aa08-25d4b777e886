import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Volume2,
  VolumeX,
  Bell,
  BellOff,
  Monitor,
  Mail,
  Play,
  Pause,
  Upload,
  Download,
  Settings,
  Save,
  RefreshCw,
  Check,
  X
} from 'lucide-react';
import { useNotifications } from '../../contexts/NotificationsContext';
import { NotificationSound, notificationSoundsService } from '../../services/notification-sounds';

export function NotificationSettings() {
  const { notificationSettings, loadNotificationSettings, updateNotificationSettings } = useNotifications();
  const [availableSounds, setAvailableSounds] = useState<NotificationSound[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [playingSound, setPlayingSound] = useState<string | null>(null);
  const [localSettings, setLocalSettings] = useState(notificationSettings);

  // Load available notification sounds
  useEffect(() => {
    const loadSounds = async () => {
      try {
        const sounds = await notificationSoundsService.getAllNotificationSounds();
        setAvailableSounds(sounds);
      } catch (error) {
        console.error('Failed to load notification sounds:', error);
      }
    };

    loadSounds();
  }, []);

  // Update local settings when context settings change
  useEffect(() => {
    setLocalSettings(notificationSettings);
  }, [notificationSettings]);

  const handleSaveSettings = async () => {
    if (!localSettings) return;

    setSaving(true);
    try {
      await updateNotificationSettings({
        soundEnabled: localSettings.soundEnabled,
        selectedSoundId: localSettings.selectedSoundId?._id,
        volume: localSettings.volume,
        desktopNotifications: localSettings.desktopNotifications,
        emailNotifications: localSettings.emailNotifications,
        notificationTypes: localSettings.notificationTypes,
      });
    } catch (error) {
      console.error('Failed to save notification settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const handlePlaySound = async (soundId: string) => {
    if (playingSound === soundId) {
      setPlayingSound(null);
      return;
    }

    setPlayingSound(soundId);
    try {
      await notificationSoundsService.playNotificationSound(soundId, localSettings?.volume || 0.7);
    } catch (error) {
      console.error('Failed to play sound:', error);
    } finally {
      setPlayingSound(null);
    }
  };

  const handleTestNotification = async () => {
    if (!localSettings?.selectedSoundId) return;

    try {
      // Request notification permission if needed
      await notificationSoundsService.requestNotificationPermission();
      
      // Play sound
      await notificationSoundsService.playNotificationSound(
        localSettings.selectedSoundId._id,
        localSettings.volume
      );
      
      // Show desktop notification if enabled
      if (localSettings.desktopNotifications) {
        await notificationSoundsService.showDesktopNotification(
          '🔔 Test Notification',
          {
            body: 'This is how your notifications will appear!',
            icon: '/favicon.ico',
          }
        );
      }
    } catch (error) {
      console.error('Failed to test notification:', error);
    }
  };

  if (!localSettings) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="flex items-center space-x-3 text-purple-200/80">
          <RefreshCw className="w-6 h-6 animate-spin" />
          <span>Loading notification settings...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-4">
          Notification Settings
        </h2>
        <p className="text-slate-400">
          Customize how you receive notifications and choose your preferred sound
        </p>
      </motion.div>

      {/* Main Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="glass-card p-6 border border-purple-500/30"
      >
        <h3 className="text-xl font-semibold text-white mb-6 flex items-center space-x-2">
          <Settings className="w-5 h-5 text-purple-400" />
          <span>General Settings</span>
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Sound Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {localSettings.soundEnabled ? (
                  <Volume2 className="w-5 h-5 text-green-400" />
                ) : (
                  <VolumeX className="w-5 h-5 text-red-400" />
                )}
                <span className="text-white font-medium">Sound Notifications</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localSettings.soundEnabled}
                  onChange={(e) =>
                    setLocalSettings(prev => prev ? { ...prev, soundEnabled: e.target.checked } : null)
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
              </label>
            </div>

            {/* Volume Control */}
            {localSettings.soundEnabled && (
              <div className="space-y-2">
                <label className="text-sm text-slate-300">Volume: {Math.round(localSettings.volume * 100)}%</label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={localSettings.volume}
                  onChange={(e) =>
                    setLocalSettings(prev => prev ? { ...prev, volume: parseFloat(e.target.value) } : null)
                  }
                  className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>
            )}
          </div>

          {/* Desktop & Email Notifications */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Monitor className="w-5 h-5 text-blue-400" />
                <span className="text-white font-medium">Desktop Notifications</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localSettings.desktopNotifications}
                  onChange={(e) =>
                    setLocalSettings(prev => prev ? { ...prev, desktopNotifications: e.target.checked } : null)
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-green-400" />
                <span className="text-white font-medium">Email Notifications</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localSettings.emailNotifications}
                  onChange={(e) =>
                    setLocalSettings(prev => prev ? { ...prev, emailNotifications: e.target.checked } : null)
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
              </label>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Sound Selection */}
      {localSettings.soundEnabled && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="glass-card p-6 border border-purple-500/30"
        >
          <h3 className="text-xl font-semibold text-white mb-6 flex items-center space-x-2">
            <Volume2 className="w-5 h-5 text-purple-400" />
            <span>Notification Sound</span>
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableSounds.map((sound) => (
              <motion.div
                key={sound._id}
                className={`p-4 rounded-lg border transition-all duration-200 cursor-pointer ${
                  localSettings.selectedSoundId?._id === sound._id
                    ? 'border-purple-400 bg-purple-500/20'
                    : 'border-slate-600 hover:border-slate-500 hover:bg-slate-800/50'
                }`}
                onClick={() =>
                  setLocalSettings(prev => prev ? { ...prev, selectedSoundId: sound } : null)
                }
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-white">{sound.displayName}</h4>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePlaySound(sound._id);
                    }}
                    className="p-1 rounded hover:bg-slate-700 transition-colors"
                  >
                    {playingSound === sound._id ? (
                      <Pause className="w-4 h-4 text-purple-400" />
                    ) : (
                      <Play className="w-4 h-4 text-purple-400" />
                    )}
                  </button>
                </div>
                
                {sound.description && (
                  <p className="text-sm text-slate-400 mb-2">{sound.description}</p>
                )}
                
                <div className="flex items-center justify-between text-xs text-slate-500">
                  <span>{sound.duration}s</span>
                  <span>{(sound.fileSize / 1024).toFixed(1)}KB</span>
                </div>
                
                {sound.isDefault && (
                  <div className="mt-2">
                    <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded text-xs">
                      Default
                    </span>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Notification Types */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="glass-card p-6 border border-purple-500/30"
      >
        <h3 className="text-xl font-semibold text-white mb-6 flex items-center space-x-2">
          <Bell className="w-5 h-5 text-purple-400" />
          <span>Notification Types</span>
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(localSettings.notificationTypes).map(([type, enabled]) => (
            <div key={type} className="flex items-center justify-between p-3 rounded-lg bg-slate-800/50">
              <span className="text-white capitalize">
                {type.replace(/_/g, ' ')}
              </span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={enabled}
                  onChange={(e) =>
                    setLocalSettings(prev => prev ? {
                      ...prev,
                      notificationTypes: {
                        ...prev.notificationTypes,
                        [type]: e.target.checked
                      }
                    } : null)
                  }
                  className="sr-only peer"
                />
                <div className="w-9 h-5 bg-slate-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-purple-600"></div>
              </label>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="flex items-center justify-between"
      >
        <button
          onClick={handleTestNotification}
          disabled={!localSettings.selectedSoundId}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600/20 text-blue-400 border border-blue-600/30 rounded-lg hover:bg-blue-600/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Play className="w-4 h-4" />
          <span>Test Notification</span>
        </button>

        <div className="flex items-center space-x-3">
          <button
            onClick={() => setLocalSettings(notificationSettings)}
            className="flex items-center space-x-2 px-4 py-2 bg-slate-600/20 text-slate-400 border border-slate-600/30 rounded-lg hover:bg-slate-600/30 transition-colors"
          >
            <X className="w-4 h-4" />
            <span>Reset</span>
          </button>

          <button
            onClick={handleSaveSettings}
            disabled={saving}
            className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-colors disabled:opacity-50"
          >
            {saving ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>{saving ? 'Saving...' : 'Save Settings'}</span>
          </button>
        </div>
      </motion.div>
    </div>
  );
}