// API configuration and types
export const API_BASE_URL = 'http://localhost:3001/api';

// Error types
export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
}

// Generic API client using fetch
class ApiClient {
  private getAuthHeaders(isFormData: boolean = false): HeadersInit {
    const token = localStorage.getItem('mybox_token');
    const headers: HeadersInit = {};
    
    // Don't set Content-Type for FormData - let browser set it with boundary
    if (!isFormData) {
      headers['Content-Type'] = 'application/json';
    }
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    return headers;
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData: ApiError = await response.json().catch(() => ({ message: 'Unknown error', statusCode: response.status }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  async get<T>(endpoint: string): Promise<{ data: T }> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });
    const data = await this.handleResponse<T>(response);
    return { data };
  }

  async post<T>(endpoint: string, body?: any): Promise<{ data: T }> {
    const isFormData = body instanceof FormData;
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(isFormData),
      body: isFormData ? body : (body ? JSON.stringify(body) : undefined),
    });
    const data = await this.handleResponse<T>(response);
    return { data };
  }
  async put<T>(endpoint: string, body?: any): Promise<{ data: T }> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: body ? JSON.stringify(body) : undefined,
    });
    const data = await this.handleResponse<T>(response);
    return { data };
  }

  async patch<T>(endpoint: string, body?: any): Promise<{ data: T }> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: body ? JSON.stringify(body) : undefined,
    });
    const data = await this.handleResponse<T>(response);
    return { data };
  }

  async delete<T>(endpoint: string): Promise<{ data: T }> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    const data = await this.handleResponse<T>(response);
    return { data };
  }
}

export const api = new ApiClient();

export interface AuthResponse {
  access_token?: string;
  user?: {
    id: string;
    username: string;
    email: string;
    role: 'user' | 'admin' | 'moderator';
    score: number;
    rank: number;
    teamId?: string | null;
    isEmailVerified: boolean;
  };
  requiresEmailVerification?: boolean;
  message?: string;
}

export interface UserProfile {
  id: string;
  username: string;
  email: string;
  role: 'user' | 'admin' | 'moderator';
  score: number;
  rank: number;
  teamId?: string | null;
  avatarUrl?: string | null;
  country?: string | null;
  bio?: string | null;
  isActive: boolean;
  lastActive: string;
  createdAt: string;
  updatedAt: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface UpdateProfileRequest {
  username?: string;
  email?: string;
  avatarUrl?: string | null;
  country?: string | null;  bio?: string | null;
}

export class AuthService {
  private static getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('mybox_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  private static async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData: ApiError = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  static async register(data: RegisterRequest): Promise<AuthResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    const result = await this.handleResponse<AuthResponse>(response);

    // Store token in localStorage if present
    if (result.access_token) {
      localStorage.setItem('mybox_token', result.access_token);
    }

    return result;
  }

  static async login(data: LoginRequest): Promise<AuthResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    const result = await this.handleResponse<AuthResponse>(response);

    // Store token in localStorage if present
    if (result.access_token) {
      localStorage.setItem('mybox_token', result.access_token);
    }

    return result;
  }

  static async logout(): Promise<void> {
    try {
      await fetch(`${API_BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });
    } catch (error) {
      console.warn('Logout request failed:', error);
    } finally {
      // Always remove token from localStorage
      localStorage.removeItem('mybox_token');
      localStorage.removeItem('mybox_user');
    }
  }

  static async getProfile(): Promise<UserProfile> {
    const response = await fetch(`${API_BASE_URL}/users/profile`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<UserProfile>(response);
  }

  static async updateProfile(data: UpdateProfileRequest): Promise<UserProfile> {
    const response = await fetch(`${API_BASE_URL}/users/profile`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse<UserProfile>(response);
  }

  static async regenerateApiToken(): Promise<{ apiToken: string }> {
    const response = await fetch(`${API_BASE_URL}/users/regenerate-api-token`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ apiToken: string }>(response);
  }

  static async refreshToken(): Promise<AuthResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    const result = await this.handleResponse<AuthResponse>(response);

    // Update token in localStorage if present
    if (result.access_token) {
      localStorage.setItem('mybox_token', result.access_token);
    }

    return result;
  }

  static isAuthenticated(): boolean {
    return !!localStorage.getItem('mybox_token');
  }  static getToken(): string | null {
    return localStorage.getItem('mybox_token');
  }
}
