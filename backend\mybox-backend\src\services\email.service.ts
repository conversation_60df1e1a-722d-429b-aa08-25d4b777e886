import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as nodemailer from 'nodemailer';
import { AdminSettings } from '../schemas/admin-settings.schema';

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

@Injectable()
export class EmailService {
  constructor(
    @InjectModel(AdminSettings.name) private adminSettingsModel: Model<AdminSettings>,
  ) {}

  private async createTransporter() {
    const settings = await this.adminSettingsModel.findOne({ configId: 'default' });

    if (!settings || !settings.smtpHost || !settings.fromEmail) {
      throw new Error('Email settings not configured');
    }

    console.log('Creating SMTP transporter with settings:', {
      host: settings.smtpHost,
      port: settings.smtpPort,
      secure: settings.smtpSecure,
      hasAuth: !!(settings.smtpUsername && settings.smtpPassword)
    });

    const transportConfig: any = {
      host: settings.smtpHost,
      port: settings.smtpPort,
      secure: settings.smtpSecure, // true for 465, false for other ports
      connectionTimeout: 60000, // 60 seconds
      greetingTimeout: 30000, // 30 seconds
      socketTimeout: 60000, // 60 seconds
    };

    // Add authentication if provided
    if (settings.smtpUsername && settings.smtpPassword) {
      transportConfig.auth = {
        user: settings.smtpUsername,
        pass: settings.smtpPassword,
      };
    }

    // Add TLS options for better compatibility
    if (!settings.smtpSecure && settings.smtpPort !== 25) {
      transportConfig.requireTLS = true;
      transportConfig.tls = {
        ciphers: 'SSLv3',
        rejectUnauthorized: false
      };
    }

    return nodemailer.createTransport(transportConfig);
  }

  async sendEmail(options: EmailOptions): Promise<void> {
    const settings = await this.adminSettingsModel.findOne({ configId: 'default' });

    if (!settings || !settings.fromEmail) {
      throw new Error('Email settings not configured');
    }

    try {
      const transporter = await this.createTransporter();

      const mailOptions = {
        from: `"Rakcha Pentest V2 Platform" <${settings.fromEmail}>`,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
      };

      console.log('Attempting to send email to:', options.to);
      const result = await transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
    } catch (error) {
      console.error('Email sending failed:', error);

      // Provide more specific error messages
      if (error.code === 'ETIMEDOUT') {
        throw new Error('Email server connection timeout. Please check SMTP host and port settings.');
      } else if (error.code === 'EAUTH') {
        throw new Error('Email authentication failed. Please check SMTP username and password.');
      } else if (error.code === 'ECONNREFUSED') {
        throw new Error('Email server connection refused. Please check SMTP host and port settings.');
      } else {
        throw new Error(`Email sending failed: ${error.message}`);
      }
    }
  }

  async sendVerificationEmail(email: string, username: string, verificationCode: string): Promise<void> {
    const subject = 'Verify Your Email - Rakcha Pentest V2 Platform';
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
          }
          .container {
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            font-size: 24px;
            font-weight: bold;
            color: #10b981;
            margin-bottom: 10px;
          }
          .verification-code {
            background: #f1f5f9;
            border: 2px dashed #64748b;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 30px 0;
          }
          .code {
            font-size: 32px;
            font-weight: bold;
            color: #10b981;
            letter-spacing: 4px;
            font-family: 'Courier New', monospace;
          }
          .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            font-size: 14px;
            color: #64748b;
            text-align: center;
          }
          .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #92400e;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🔒 Rakcha Pentest V2 Platform</div>
            <h1>Email Verification Required</h1>
          </div>
          
          <p>Hello <strong>${username}</strong>,</p>
          
          <p>Welcome to Rakcha Pentest V2 Platform! To complete your registration and secure your account, please verify your email address using the verification code below:</p>
          
          <div class="verification-code">
            <div class="code">${verificationCode}</div>
            <p style="margin: 10px 0 0 0; color: #64748b;">Enter this code in the verification form</p>
          </div>
          
          <div class="warning">
            <strong>⚠️ Important:</strong> This verification code will expire in 15 minutes for security reasons. If you didn't request this verification, please ignore this email.
          </div>
          
          <p>If you're having trouble with verification, please contact our support team.</p>
          
          <div class="footer">
            <p>This email was sent to <strong>${email}</strong></p>
            <p>© ${new Date().getFullYear()} Rakcha Pentest V2 Platform. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Rakcha Pentest V2 Platform - Email Verification
      
      Hello ${username},
      
      Welcome to Rakcha Pentest V2 Platform! Please verify your email address using this code:
      
      Verification Code: ${verificationCode}
      
      This code will expire in 15 minutes.
      
      If you didn't request this verification, please ignore this email.
    `;

    await this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  async testEmailConfiguration(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Testing email configuration...');
      const transporter = await this.createTransporter();

      console.log('Verifying SMTP connection...');
      await transporter.verify();

      console.log('Email configuration test successful');
      return {
        success: true,
        message: 'Email configuration is valid and connection successful'
      };
    } catch (error) {
      console.error('Email configuration test failed:', error);

      let message = 'Email configuration test failed';
      if (error.code === 'ETIMEDOUT') {
        message = 'Connection timeout. Check SMTP host and port settings.';
      } else if (error.code === 'EAUTH') {
        message = 'Authentication failed. Check SMTP username and password.';
      } else if (error.code === 'ECONNREFUSED') {
        message = 'Connection refused. Check SMTP host and port settings.';
      } else {
        message = `Configuration error: ${error.message}`;
      }

      return {
        success: false,
        message
      };
    }
  }

  generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
}
