import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class AdminSettings extends Document {
  @Prop({ required: true, unique: true, default: 'default' })
  configId: string;

  // Platform Settings
  @Prop({ default: true })
  allowRegistration: boolean;

  @Prop({ default: false })
  emailVerification: boolean;

  @Prop({ default: false })
  maintenanceMode: boolean;

  @Prop({ default: true })
  enableTeams: boolean;

  @Prop({ default: 5, min: 1, max: 20 })
  maxTeamSize: number;

  @Prop({ default: true })
  autoBackup: boolean;

  @Prop({ default: 24, min: 1, max: 168 })
  backupInterval: number;

  // VM Settings
  @Prop({ default: 2, min: 1, max: 24 })
  defaultSessionTime: number;

  @Prop({ default: 1, min: 1, max: 10 })
  maxConcurrentVMs: number;

  // Security Settings
  @Prop({ default: 3 })
  maxLoginAttempts: number;

  @Prop({ default: 15 })
  lockoutDuration: number;

  @Prop({ default: 8 })
  minPasswordLength: number;

  @Prop({ default: true })
  requirePasswordComplexity: boolean;

  // Email Settings
  @Prop({ default: '' })
  smtpHost: string;

  @Prop({ default: 587 })
  smtpPort: number;

  @Prop({ default: '' })
  smtpUsername: string;

  @Prop({ default: '' })
  smtpPassword: string;

  @Prop({ default: false })
  smtpSecure: boolean;

  @Prop({ default: '' })
  fromEmail: string;

  // System Settings
  @Prop({ default: 1000 })
  maxFileUploadSize: number; // in MB

  @Prop({ default: 30 })
  sessionTimeout: number; // in minutes

  @Prop({ default: true })
  enableLogging: boolean;

  @Prop({ default: 'info' })
  logLevel: string;

  // Rate Limiting
  @Prop({ default: 100 })
  apiRateLimit: number; // requests per minute

  @Prop({ default: 10 })
  loginRateLimit: number; // login attempts per minute

  // Maintenance
  @Prop({ default: new Date() })
  lastBackup: Date;

  @Prop({ default: 0 })
  totalBackups: number;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  updatedBy: Types.ObjectId;

  createdAt: Date;
  updatedAt: Date;
}

export const AdminSettingsSchema = SchemaFactory.createForClass(AdminSettings);

// Create indexes
AdminSettingsSchema.index({ configId: 1 }, { unique: true });
AdminSettingsSchema.index({ updatedAt: -1 });