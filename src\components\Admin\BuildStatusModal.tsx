import { useState, useEffect } from 'react';
import { X, Play, Trash2, <PERSON>, <PERSON>Off, RefreshC<PERSON>, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import machineService, { MachineTemplate } from '../../services/machines';

interface BuildStatusModalProps {
  template: MachineTemplate;
  isOpen: boolean;
  onClose: () => void;
  onStatusUpdate?: () => void;
}

interface ImageStatus {
  imageExists: boolean;
  hasExtractedFiles: boolean;
  imageName: string;
  canBuild: boolean;
}

export function BuildStatusModal({ template, isOpen, onClose, onStatusUpdate }: BuildStatusModalProps) {
  const [imageStatus, setImageStatus] = useState<ImageStatus | null>(null);
  const [buildLogs, setBuildLogs] = useState<string[]>([]);
  const [showLogs, setShowLogs] = useState(false);
  const [loading, setLoading] = useState(false);
  const [building, setBuilding] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [buildProgress, setBuildProgress] = useState<string>('');

  useEffect(() => {
    if (isOpen && template.id) {
      loadImageStatus();
      loadBuildLogs();
    }
  }, [isOpen, template.id]);

  const loadImageStatus = async () => {
    try {
      setLoading(true);
      const status = await machineService.getImageStatus(template.id);
      setImageStatus(status);
    } catch (error: any) {
      console.error('Error loading image status:', error);
      setError(error.response?.data?.message || 'Failed to load image status');
    } finally {
      setLoading(false);
    }
  };

  const loadBuildLogs = async () => {
    try {
      const logs = await machineService.getBuildLogs(template.id);
      setBuildLogs(logs);
    } catch (error) {
      console.error('Error loading build logs:', error);
      setBuildLogs(['No build logs available']);
    }
  };

  const buildImage = async () => {
    try {
      setBuilding(true);
      setError(null);
      setBuildLogs([]);
      setBuildProgress('Starting build...');

      // Start the build
      const buildPromise = machineService.buildMachineImage(template.id);

      // Poll for logs while building
      const pollInterval = setInterval(async () => {
        try {
          const logs = await machineService.getBuildLogs(template.id);
          setBuildLogs(logs);

          // Update progress based on logs
          const lastLog = logs[logs.length - 1] || '';
          if (lastLog.includes('Step ')) {
            const stepMatch = lastLog.match(/Step (\d+)\/(\d+)/);
            if (stepMatch) {
              const current = parseInt(stepMatch[1]);
              const total = parseInt(stepMatch[2]);
              setBuildProgress(`Building... Step ${current}/${total} (${Math.round((current/total)*100)}%)`);
            }
          } else if (lastLog.includes('npm install')) {
            setBuildProgress('Installing dependencies...');
          } else if (lastLog.includes('npm run build')) {
            setBuildProgress('Building application...');
          } else if (lastLog.includes('Successfully built')) {
            setBuildProgress('Build completed successfully!');
          }
        } catch (pollError) {
          // Ignore polling errors
        }
      }, 2000); // Poll every 2 seconds

      const result = await buildPromise;
      clearInterval(pollInterval);

      setBuildLogs(result.buildLogs || ['Build completed successfully']);
      setBuildProgress('Build completed successfully!');
      setShowLogs(true);

      // Refresh status
      await loadImageStatus();
      onStatusUpdate?.();
    } catch (error: any) {
      console.error('Error building image:', error);
      setError(error.response?.data?.message || 'Failed to build image');
      setBuildLogs([`Build failed: ${error.response?.data?.message || error.message}`]);
      setBuildProgress('Build failed');
      setShowLogs(true);
    } finally {
      setBuilding(false);
    }
  };

  const removeImage = async () => {
    try {
      setLoading(true);
      await machineService.removeImage(template.id);
      await loadImageStatus();
      onStatusUpdate?.();
    } catch (error: any) {
      console.error('Error removing image:', error);
      setError(error.response?.data?.message || 'Failed to remove image');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-900 rounded-xl border border-slate-700 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-700">
          <div>
            <h2 className="text-xl font-semibold text-white">Build Status</h2>
            <p className="text-slate-400 text-sm">{template.name}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-800 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {loading && !imageStatus ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="w-6 h-6 text-blue-400 animate-spin" />
              <span className="ml-2 text-slate-400">Loading status...</span>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Image Status */}
              {imageStatus && (
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-white mb-4">Docker Image Status</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        imageStatus.hasExtractedFiles ? 'bg-green-500' : 'bg-gray-500'
                      }`}></div>
                      <span className="text-slate-300">
                        Files Extracted: {imageStatus.hasExtractedFiles ? 'Yes' : 'No'}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        imageStatus.imageExists ? 'bg-green-500' : 'bg-gray-500'
                      }`}></div>
                      <span className="text-slate-300">
                        Image Built: {imageStatus.imageExists ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <span className="text-sm text-slate-400">Image Name:</span>
                    <code className="ml-2 px-2 py-1 bg-slate-900 rounded text-green-400 text-sm">
                      {imageStatus.imageName}
                    </code>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-3">
                    {imageStatus.canBuild && (
                      <div className="space-y-2">
                        <button
                          onClick={buildImage}
                          disabled={building}
                          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {building ? (
                            <>
                              <RefreshCw className="w-4 h-4 animate-spin" />
                              <span>Building...</span>
                            </>
                          ) : (
                            <>
                              <Play className="w-4 h-4" />
                              <span>Build Image</span>
                            </>
                          )}
                        </button>

                        {building && buildProgress && (
                          <div className="text-sm text-blue-400 bg-blue-900/20 px-3 py-2 rounded">
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                              <span>{buildProgress}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {imageStatus.imageExists && (
                      <button
                        onClick={removeImage}
                        disabled={loading}
                        className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Trash2 className="w-4 h-4" />
                        <span>Remove Image</span>
                      </button>
                    )}
                    
                    <button
                      onClick={loadImageStatus}
                      disabled={loading}
                      className="flex items-center space-x-2 px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                      <span>Refresh</span>
                    </button>
                  </div>
                </div>
              )}

              {/* Build Logs */}
              {buildLogs.length > 0 && (
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-white">Build Logs</h3>
                    <button
                      onClick={() => setShowLogs(!showLogs)}
                      className="flex items-center space-x-2 px-3 py-1 bg-slate-700 text-white rounded hover:bg-slate-600"
                    >
                      {showLogs ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      <span>{showLogs ? 'Hide' : 'Show'}</span>
                    </button>
                  </div>
                  
                  {showLogs && (
                    <div className="bg-black/50 rounded-lg p-4 max-h-64 overflow-y-auto">
                      <pre className="text-xs text-green-400 font-mono whitespace-pre-wrap">
                        {buildLogs.join('\n')}
                      </pre>
                    </div>
                  )}
                </div>
              )}

              {/* Error Display */}
              {error && (
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="w-5 h-5 text-red-400" />
                    <span className="text-red-400 font-medium">Error</span>
                  </div>
                  <p className="text-red-300 mt-2">{error}</p>
                </div>
              )}

              {/* Status Summary */}
              <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-700">
                <h4 className="text-sm font-medium text-slate-300 mb-2">Status Summary</h4>
                <div className="space-y-2 text-sm">
                  {imageStatus?.hasExtractedFiles ? (
                    <div className="flex items-center space-x-2 text-green-400">
                      <CheckCircle className="w-4 h-4" />
                      <span>Template files are extracted and ready</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2 text-yellow-400">
                      <Clock className="w-4 h-4" />
                      <span>Upload a template archive to get started</span>
                    </div>
                  )}
                  
                  {imageStatus?.imageExists ? (
                    <div className="flex items-center space-x-2 text-green-400">
                      <CheckCircle className="w-4 h-4" />
                      <span>Docker image is built and ready for deployment</span>
                    </div>
                  ) : imageStatus?.canBuild ? (
                    <div className="flex items-center space-x-2 text-blue-400">
                      <Play className="w-4 h-4" />
                      <span>Ready to build Docker image</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2 text-slate-400">
                      <AlertCircle className="w-4 h-4" />
                      <span>Cannot build image - missing template files</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
