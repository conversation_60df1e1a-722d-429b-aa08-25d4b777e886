#!/bin/bash

chmod 755 /var/public/ftp

# User setup
if ! id developer &>/dev/null; then
    useradd -M -s /usr/sbin/nologin developer
    echo -e "hardwork112\nhardwork112" | smbpasswd -a -s developer
fi

if ! id admin &>/dev/null; then
    useradd -M -s /usr/sbin/nologin admin
    echo -e "admin\nadmin" | smbpasswd -a -s admin
fi

mkdir -p /srv/samba/guest /srv/samba/dev /srv/samba/admin
chmod -R 777 /srv/samba/guest
chown -R developer /srv/samba/dev
chown -R admin /srv/samba/admin

# Apache
a2enmod proxy proxy_http rewrite headers
a2ensite hardwork.kybs.conf
a2ensite networkManagementlocalAdministrationP4nel.hardwork.kybs.conf
a2ensite uploadsSubdomain.hardwork.kybs
echo "ServerName hardwork.kybs" >> /etc/apache2/apache2.conf
apachectl -DFOREGROUND &

# FTP
echo "[*] Starting FTP server..."
/usr/sbin/vsftpd /etc/vsftpd.conf &

# NextJS App
cd /opt/nextjs-app
npm run build
npm run start-local &

# Flask
echo "[*] Starting Flask app as developer..."
su - developer -c 'cd /home/<USER>/app && FLASK_APP=app.py nohup flask run --host=127.0.0.1 --port=9980 > /home/<USER>/flask.log 2>&1 &' 

# Wait forever
wait
