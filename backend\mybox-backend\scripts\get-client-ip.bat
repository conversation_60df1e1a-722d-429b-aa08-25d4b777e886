@echo off
if "%~1"=="" (
    echo Usage: %~n0 ^<username^>
    exit /b 1
)

set USERNAME=%~1
set STATUS_FILE=C:\Program Files\OpenVPN\log\status.log

:: Get client IP from OpenVPN status log
for /f "tokens=1,2 delims=," %%a in ('findstr /i "%USERNAME%" "%STATUS_FILE%"') do (
    for %%i in (%%b) do (
        echo %%i | findstr /r "^[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*$" >nul
        if not errorlevel 1 (
            echo %%i
            exit /b 0
        )
    )
)

echo ""
