#!/bin/bash

# Ensure the FTP directory has correct permissions
chmod 755 /var/public/ftp

cron

# SMB config
# Create users with no shell access
useradd -M -s /usr/sbin/nologin developer
(echo "hardwork112"; echo "hardwork112") | smbpasswd -s -a developer

useradd -M -s /usr/sbin/nologin admin
echo -e "admin\admin" | smbpasswd -a -s admin

# Create shared folders
mkdir -p /srv/samba/guest /srv/samba/dev /srv/samba/admin
chmod -R 777 /srv/samba/guest
chown -R developer /srv/samba/dev
chown -R admin /srv/samba/admin


a2enmod proxy proxy_http rewrite headers
a2ensite hardwork.kybs.conf
a2ensite networkManagementlocalAdministrationP4nel.hardwork.kybs.conf
a2ensite uploadsSubdomain.hardwork.kybs
echo "ServerName hardwork.kybs" >> /etc/apache2/apache2.conf
apachectl -DFOREGROUND &
 
	


echo "[*] Starting FTP server..."
/usr/sbin/vsftpd /etc/vsftpd.conf &
exec smbd -F --no-process-group

cd /opt/network-app
npm run dev && cd /opt/nextjs-app
npm run build
npm run start-local &
npm run dev

# 🟤 Start Flask app as developer
echo "[*] Starting Flask app as developer..."
su - developer -c 'cd /home/<USER>/app && FLASK_APP=app.py nohup flask run --host=127.0.0.1 --port=5000 > /home/<USER>/flask.log 2>&1 &'

# Wait forever
wait

