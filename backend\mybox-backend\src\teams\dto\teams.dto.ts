import { IsString, <PERSON><PERSON>ptional, Is<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateTeamDto {
  @ApiProperty({ description: 'Team name', minLength: 3, maxLength: 50 })
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  name: string;

  @ApiPropertyOptional({ description: 'Team description' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({ description: 'Whether the team is public', default: true })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: 'Maximum number of members', default: 5, minimum: 2, maximum: 10 })
  @IsOptional()
  @IsNumber()
  @Min(2)
  @Max(10)
  maxMembers?: number;
}

export class UpdateTeamDto {
  @ApiPropertyOptional({ description: 'Team name', minLength: 3, maxLength: 50 })
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  name?: string;

  @ApiPropertyOptional({ description: 'Team description' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({ description: 'Whether the team is public' })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: 'Maximum number of members', minimum: 2, maximum: 10 })
  @IsOptional()
  @IsNumber()
  @Min(2)
  @Max(10)
  maxMembers?: number;
}

export class JoinTeamDto {
  @ApiProperty({ description: 'Team invite code or team ID' })
  @IsString()
  teamIdentifier: string;
}

export class InviteMemberDto {
  @ApiProperty({ description: 'Username or email of the user to invite' })
  @IsString()
  username: string;
}

export class TransferCaptainshipDto {
  @ApiProperty({ description: 'User ID of the new captain' })
  @IsString()
  newCaptainId: string;
}

export class TeamInvitationResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  teamId: string;

  @ApiProperty()
  teamName: string;

  @ApiProperty()
  invitedUserId: string;

  @ApiProperty()
  invitedUserEmail: string;

  @ApiProperty()
  invitedByUserId: string;

  @ApiProperty()
  invitedByUsername: string;

  @ApiProperty({ enum: ['pending', 'accepted', 'declined', 'cancelled'] })
  status: string;

  @ApiProperty()
  createdAt: Date;

  @ApiPropertyOptional()
  respondedAt?: Date;

  @ApiProperty()
  expiresAt: Date;
}

export class TeamSolveDto {
  @ApiProperty()
  challengeId: string;

  @ApiProperty()
  challengeTitle: string;

  @ApiProperty()
  challengeCategory: string;

  @ApiProperty()
  challengeDifficulty: string;

  @ApiProperty()
  solvedBy: string;

  @ApiProperty()
  solvedByUsername: string;

  @ApiProperty()
  pointsAwarded: number;

  @ApiProperty()
  solvedAt: Date;

  @ApiPropertyOptional()
  isFirstBlood?: boolean;

  @ApiPropertyOptional()
  flagIndex?: number;
}

export class TeamMemberDto {
  @ApiProperty()
  userId: string;

  @ApiProperty()
  username: string;

  @ApiProperty()
  email: string;

  @ApiProperty({ enum: ['captain', 'member'] })
  role: string;

  @ApiProperty({ enum: ['active', 'pending', 'left'] })
  status: string;

  @ApiProperty()
  joinedAt: Date;

  @ApiPropertyOptional()
  score?: number;

  @ApiPropertyOptional()
  solvedChallenges?: number;
}

export class TeamLeaderboardEntryDto {
  @ApiProperty()
  teamId: string;

  @ApiProperty()
  teamName: string;

  @ApiProperty()
  teamScore: number;

  @ApiProperty()
  rank: number;

  @ApiProperty()
  memberCount: number;

  @ApiProperty()
  solvedChallenges: number;

  @ApiPropertyOptional()
  lastSolved?: Date;

  @ApiProperty()
  captain: {
    id: string;
    username: string;
  };
}

export class TeamResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiProperty()
  isPublic: boolean;

  @ApiProperty()
  captainId: string;

  @ApiProperty()
  captain: {
    id: string;
    username: string;
    email: string;
  };

  @ApiProperty()
  members: {
    userId: string;
    username: string;
    email: string;
    role: 'captain' | 'member';
    status: 'active' | 'pending' | 'left';
    joinedAt: Date;
  }[];

  @ApiProperty()
  maxMembers: number;

  @ApiProperty()
  teamScore: number;

  @ApiProperty()
  rank: number;

  @ApiPropertyOptional()
  inviteCode?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class TeamListResponseDto {
  @ApiProperty({ type: [TeamResponseDto] })
  teams: TeamResponseDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;
}

export class TeamStatsDto {
  @ApiProperty()
  totalMembers: number;

  @ApiProperty()
  activeChallenges: number;

  @ApiProperty()
  solvedChallenges: number;

  @ApiProperty()
  totalScore: number;

  @ApiProperty()
  rank: number;

  @ApiProperty()
  recentActivity: {
    type: 'challenge_solved' | 'member_joined' | 'member_left';
    description: string;
    timestamp: Date;
    points?: number;
  }[];
}
