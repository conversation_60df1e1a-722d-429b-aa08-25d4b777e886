import { Modu<PERSON> } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { User, UserSchema } from '../schemas/user.schema';
import { AdminSettings, AdminSettingsSchema } from '../schemas/admin-settings.schema';
import { JwtStrategy } from './jwt.strategy';
import { LocalStrategy } from './local.strategy';
import { EmailService } from '../services/email.service';

@Module({
  imports: [
    PassportModule,
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: AdminSettings.name, schema: AdminSettingsSchema },
    ]),
  ],
  controllers: [AuthController],
  providers: [AuthService, UsersService, EmailService, JwtStrategy, LocalStrategy],
  exports: [AuthService, UsersService],
})
export class AuthModule {}
