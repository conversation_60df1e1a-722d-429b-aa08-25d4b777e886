import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TeamsController } from './teams.controller';
import { TeamsService } from './teams.service';
import { Team, TeamSchema } from '../schemas/team.schema';
import { User, UserSchema } from '../schemas/user.schema';
import { Challenge, ChallengeSchema } from '../schemas/challenge.schema';
import { ChallengeSubmission, ChallengeSubmissionSchema } from '../schemas/challenge-submission.schema';
import { TeamChallengeSolve, TeamChallengeSolveSchema } from '../schemas/team-challenge-solve.schema';
import { TeamInvitation, TeamInvitationSchema } from '../schemas/team-invitation.schema';
import { AdminSettings, AdminSettingsSchema } from '../schemas/admin-settings.schema';
import { UsersService } from '../users/users.service';
import { TeamsEnabledGuard } from '../guards/teams-enabled.guard';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Team.name, schema: TeamSchema },
      { name: User.name, schema: UserSchema },
      { name: Challenge.name, schema: ChallengeSchema },
      { name: ChallengeSubmission.name, schema: ChallengeSubmissionSchema },
      { name: TeamChallengeSolve.name, schema: TeamChallengeSolveSchema },
      { name: TeamInvitation.name, schema: TeamInvitationSchema },
      { name: AdminSettings.name, schema: AdminSettingsSchema }
    ]),
  ],
  controllers: [TeamsController],
  providers: [TeamsService, UsersService, TeamsEnabledGuard],
  exports: [TeamsService],
})
export class TeamsModule {}