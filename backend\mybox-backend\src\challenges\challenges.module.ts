import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChallengesController } from './challenges.controller';
import { ChallengesService } from './challenges.service';
import { Challenge, ChallengeSchema } from '../schemas/challenge.schema';
import { Category, CategorySchema } from '../schemas/category.schema';
import { ChallengeSubmission, ChallengeSubmissionSchema } from '../schemas/challenge-submission.schema';
import { TeamChallengeSolve, TeamChallengeSolveSchema } from '../schemas/team-challenge-solve.schema';
import { UserSchema } from '../schemas/user.schema';
import { TeamSchema } from '../schemas/team.schema';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Challenge.name, schema: ChallengeSchema },
      { name: Category.name, schema: CategorySchema },
      { name: ChallengeSubmission.name, schema: ChallengeSubmissionSchema },
      { name: TeamChallengeSolve.name, schema: TeamChallengeSolveSchema },
      { name: 'User', schema: UserSchema },
      { name: 'Team', schema: TeamSchema },
    ]),
    NotificationsModule,
  ],
  controllers: [ChallengesController],
  providers: [ChallengesService],
  exports: [ChallengesService],
})
export class ChallengesModule {}