import { Injectable, NotFoundException, ConflictException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Team, TeamMember } from '../schemas/team.schema';
import { User } from '../schemas/user.schema';
import { Challenge } from '../schemas/challenge.schema';
import { ChallengeSubmission } from '../schemas/challenge-submission.schema';
import { TeamChallengeSolve } from '../schemas/team-challenge-solve.schema';
import { TeamInvitation } from '../schemas/team-invitation.schema';
import { 
  CreateTeamDto, 
  UpdateTeamDto, 
  JoinTeamDto, 
  InviteMemberDto, 
  TransferCaptainshipDto,
  TeamResponseDto, 
  TeamStatsDto,
  TeamInvitationResponseDto,
  TeamSolveDto,
  TeamMemberDto,
  TeamLeaderboardEntryDto
} from './dto/teams.dto';
import { UsersService } from '../users/users.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class TeamsService {
  constructor(
    @InjectModel(Team.name) private teamModel: Model<Team>,
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Challenge.name) private challengeModel: Model<Challenge>,
    @InjectModel(ChallengeSubmission.name) private challengeSubmissionModel: Model<ChallengeSubmission>,
    @InjectModel(TeamChallengeSolve.name) private teamChallengeSolveModel: Model<TeamChallengeSolve>,
    @InjectModel(TeamInvitation.name) private teamInvitationModel: Model<TeamInvitation>,
    private usersService: UsersService,
  ) {}

  async create(userId: string, createTeamDto: CreateTeamDto): Promise<TeamResponseDto> {
    // Check if user is already in a team
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.teamId) {
      throw new ConflictException('User is already a member of a team');
    }

    // Check if team name already exists
    const existingTeam = await this.teamModel.findOne({ name: createTeamDto.name });
    if (existingTeam) {
      throw new ConflictException('Team name already exists');
    }

    // Generate unique invite code
    const inviteCode = this.generateInviteCode();

    // Create team
    const team = new this.teamModel({
      name: createTeamDto.name,
      description: createTeamDto.description || '',
      isPublic: createTeamDto.isPublic ?? true,
      captainId: new Types.ObjectId(userId),
      maxMembers: createTeamDto.maxMembers || 5,
      inviteCode,
      members: [{
        userId: new Types.ObjectId(userId),
        role: 'captain',
        status: 'active',
        joinedAt: new Date()
      }]
    });    const savedTeam = await team.save();

    // Update user's teamId
    await this.usersService.joinTeam(userId, String(savedTeam._id));

    return this.formatTeamResponse(savedTeam);
  }

  async findAll(page: number = 1, limit: number = 20, isPublic?: boolean): Promise<{ teams: TeamResponseDto[], total: number, page: number, limit: number }> {
    const query: any = {};
    if (isPublic !== undefined) {
      query.isPublic = isPublic;
    }

    const skip = (page - 1) * limit;
    const [teams, total] = await Promise.all([
      this.teamModel
        .find(query)
        .populate('captainId', 'username email')
        .populate('members.userId', 'username email')
        .sort({ teamScore: -1, createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.teamModel.countDocuments(query)
    ]);

    const formattedTeams = await Promise.all(
      teams.map(team => this.formatTeamResponse(team))
    );

    return {
      teams: formattedTeams,
      total,
      page,
      limit
    };
  }

  async findById(teamId: string): Promise<TeamResponseDto> {
    if (!Types.ObjectId.isValid(teamId)) {
      throw new NotFoundException('Invalid team ID');
    }

    const team = await this.teamModel
      .findById(teamId)
      .populate('captainId', 'username email')
      .populate('members.userId', 'username email')
      .exec();

    if (!team) {
      throw new NotFoundException('Team not found');
    }

    return this.formatTeamResponse(team);
  }

  async findByUser(userId: string): Promise<TeamResponseDto | null> {
    const user = await this.usersService.findById(userId);
    if (!user || !user.teamId) {
      return null;
    }

    return this.findById(user.teamId.toString());
  }

  async update(teamId: string, userId: string, updateTeamDto: UpdateTeamDto): Promise<TeamResponseDto> {
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    // Check if user is the captain
    if (team.captainId.toString() !== userId) {
      throw new ForbiddenException('Only team captain can update team settings');
    }

    // Check if team name already exists (if being changed)
    if (updateTeamDto.name && updateTeamDto.name !== team.name) {
      const existingTeam = await this.teamModel.findOne({ 
        name: updateTeamDto.name,
        _id: { $ne: teamId }
      });
      if (existingTeam) {
        throw new ConflictException('Team name already exists');
      }
    }

    // Update team
    const updatedTeam = await this.teamModel
      .findByIdAndUpdate(teamId, updateTeamDto, { new: true })
      .populate('captainId', 'username email')
      .populate('members.userId', 'username email')
      .exec();

    return this.formatTeamResponse(updatedTeam!);
  }

  async joinTeam(userId: string, joinTeamDto: JoinTeamDto): Promise<TeamResponseDto> {
    // Check if user is already in a team
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.teamId) {
      throw new ConflictException('User is already a member of a team');
    }

    // Find team by invite code or ID
    let team;
    if (Types.ObjectId.isValid(joinTeamDto.teamIdentifier)) {
      team = await this.teamModel.findById(joinTeamDto.teamIdentifier);
    } else {
      team = await this.teamModel.findOne({ inviteCode: joinTeamDto.teamIdentifier });
    }

    if (!team) {
      throw new NotFoundException('Team not found');
    }    // Check if team is full
    const activeMembers = team.members.filter(member => member.status === 'active');
    console.log(`DEBUG: Team ${team.name} has ${activeMembers.length} active members, max is ${team.maxMembers}`);
    console.log(`DEBUG: All team members:`, team.members.map(m => ({
      userId: m.userId.toString(),
      status: m.status,
      role: m.role,
      joinedAt: m.joinedAt
    })));
    console.log(`DEBUG: Active members:`, activeMembers.map(m => ({
      userId: m.userId.toString(),
      status: m.status,
      role: m.role,
      joinedAt: m.joinedAt
    })));
    
    if (activeMembers.length >= team.maxMembers) {
      throw new ConflictException('Team is full');
    }    // Check if team is public or user has invite code
    if (!team.isPublic && team.inviteCode !== joinTeamDto.teamIdentifier) {
      throw new ForbiddenException('Team is private and requires valid invite code');
    }

    // Check if user already has an entry in the team (reactivate instead of adding new)
    const existingMemberIndex = team.members.findIndex(m => m.userId.toString() === userId);
    
    if (existingMemberIndex !== -1) {
      // User was previously in this team, reactivate them
      team.members[existingMemberIndex].status = 'active';
      team.members[existingMemberIndex].role = 'member';
      team.members[existingMemberIndex].joinedAt = new Date();
    } else {
      // Add user to team as a new member
      team.members.push({
        userId: new Types.ObjectId(userId),
        role: 'member',
        status: 'active',
        joinedAt: new Date()
      } as TeamMember);
    }await team.save();

    // Update user's teamId
    await this.usersService.joinTeam(userId, String(team._id));

    // Transfer points from user's individual solves to team
    await this.transferUserPointsToTeam(userId, String(team._id));

    return this.formatTeamResponse(team);
  }

  async leaveTeam(userId: string): Promise<void> {
    const user = await this.usersService.findById(userId);
    if (!user || !user.teamId) {
      throw new NotFoundException('User is not in any team');
    }

    const team = await this.teamModel.findById(user.teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    // Check if user is captain
    if (team.captainId.toString() === userId) {
      // If captain is leaving and there are other members, transfer captaincy
      const activeMembers = team.members.filter(
        member => member.status === 'active' && member.userId.toString() !== userId
      );

      if (activeMembers.length > 0) {
        // Transfer captaincy to the oldest member
        const newCaptain = activeMembers.sort((a, b) => a.joinedAt.getTime() - b.joinedAt.getTime())[0];
        team.captainId = newCaptain.userId;
        
        // Update new captain's role
        const captainMember = team.members.find(m => m.userId.toString() === newCaptain.userId.toString());
        if (captainMember) {
          captainMember.role = 'captain';
        }
      } else {        // If no other members, delete the team
        await this.teamModel.findByIdAndDelete(String(team._id));
        await this.usersService.leaveTeam(userId);
        return;
      }
    }    // Remove user from team members array
    const memberIndex = team.members.findIndex(m => m.userId.toString() === userId);
    if (memberIndex !== -1) {
      team.members.splice(memberIndex, 1); // Actually remove the member from the array
    }

    await team.save();
    await this.usersService.leaveTeam(userId);
  }

  async removeMember(teamId: string, captainId: string, memberUserId: string): Promise<TeamResponseDto> {
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    // Check if user is the captain
    if (team.captainId.toString() !== captainId) {
      throw new ForbiddenException('Only team captain can remove members');
    }

    // Check if trying to remove self
    if (captainId === memberUserId) {
      throw new BadRequestException('Captain cannot remove themselves. Use leave team instead');
    }    // Find and remove member
    const memberIndex = team.members.findIndex(
      m => m.userId.toString() === memberUserId && m.status === 'active'
    );

    if (memberIndex === -1) {
      throw new NotFoundException('Member not found in team');
    }

    // Actually remove the member from the array
    team.members.splice(memberIndex, 1);
    await team.save();

    // Update user's teamId
    await this.usersService.leaveTeam(memberUserId);

    return this.formatTeamResponse(team);
  }

  async getTeamStats(teamId: string): Promise<TeamStatsDto> {
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    const activeMembers = team.members.filter(member => member.status === 'active');
    
    // TODO: Once challenges service is implemented, get actual challenge stats
    const stats: TeamStatsDto = {
      totalMembers: activeMembers.length,
      activeChallenges: 0, // Will be calculated from challenges service
      solvedChallenges: 0, // Will be calculated from challenges service
      totalScore: team.teamScore,
      rank: team.rank,
      recentActivity: [] // Will be populated from activity logs
    };

    return stats;
  }
  async regenerateInviteCode(teamId: string, userId: string): Promise<{ inviteCode: string }> {
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    // Check if user is the captain
    if (team.captainId.toString() !== userId) {
      throw new ForbiddenException('Only team captain can regenerate invite code');
    }

    const newInviteCode = this.generateInviteCode();
    team.inviteCode = newInviteCode;
    await team.save();

    return { inviteCode: newInviteCode };
  }

  // Transfer captainship
  async transferCaptainship(teamId: string, currentCaptainId: string, transferDto: TransferCaptainshipDto): Promise<TeamResponseDto> {
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    // Check if user is the current captain
    if (team.captainId.toString() !== currentCaptainId) {
      throw new ForbiddenException('Only team captain can transfer captainship');
    }

    // Check if new captain is a member of the team
    const newCaptainMember = team.members.find(
      m => m.userId.toString() === transferDto.newCaptainId && m.status === 'active'
    );

    if (!newCaptainMember) {
      throw new NotFoundException('New captain must be an active member of the team');
    }

    // Update roles
    const currentCaptainMember = team.members.find(m => m.userId.toString() === currentCaptainId);
    if (currentCaptainMember) {
      currentCaptainMember.role = 'member';
    }
    newCaptainMember.role = 'captain';
    team.captainId = new Types.ObjectId(transferDto.newCaptainId);

    await team.save();
    return this.formatTeamResponse(team);
  }

  // Team invitation methods
  async inviteMember(teamId: string, captainId: string, inviteDto: InviteMemberDto): Promise<TeamInvitationResponseDto> {
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    // Check if user is the captain
    if (team.captainId.toString() !== captainId) {
      throw new ForbiddenException('Only team captain can invite members');
    }

    // Check if team is full
    const activeMembers = team.members.filter(member => member.status === 'active');
    if (activeMembers.length >= team.maxMembers) {
      throw new ConflictException('Team is full');
    }    // Find user to invite
    const userToInvite = await this.userModel.findOne({
      $or: [
        { username: inviteDto.username },
        { email: inviteDto.username }
      ]
    });

    if (!userToInvite) {
      throw new NotFoundException('User not found');
    }

    // Check if user is already in a team
    if (userToInvite.teamId) {
      throw new ConflictException('User is already in a team');
    }

    // Check if user is already invited to this team
    const existingInvitation = await this.teamInvitationModel.findOne({
      teamId: new Types.ObjectId(teamId),
      invitedUserId: userToInvite._id,
      status: 'pending',
      expiresAt: { $gt: new Date() }
    });

    if (existingInvitation) {
      throw new ConflictException('User already has a pending invitation to this team');
    }

    // Create invitation
    const invitation = new this.teamInvitationModel({
      teamId: new Types.ObjectId(teamId),
      invitedUserId: userToInvite._id,
      invitedByUserId: new Types.ObjectId(captainId),
      invitedUserEmail: userToInvite.email,
      status: 'pending'
    });

    const savedInvitation = await invitation.save();

    // Get captain info for response
    const captain = await this.userModel.findById(captainId).select('username').lean();

    return {
      id: savedInvitation._id.toString(),
      teamId: teamId,
      teamName: team.name,
      invitedUserId: (userToInvite._id as any).toString(),
      invitedUserEmail: userToInvite.email,
      invitedByUserId: captainId,
      invitedByUsername: captain?.username || 'Unknown',
      status: savedInvitation.status,
      createdAt: savedInvitation.createdAt,
      expiresAt: savedInvitation.expiresAt
    };
  }

  async getTeamInvitations(teamId: string, captainId: string): Promise<TeamInvitationResponseDto[]> {
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    // Check if user is the captain
    if (team.captainId.toString() !== captainId) {
      throw new ForbiddenException('Only team captain can view invitations');
    }

    const invitations = await this.teamInvitationModel
      .find({ teamId: new Types.ObjectId(teamId) })
      .populate('invitedUserId', 'username email')
      .populate('invitedByUserId', 'username')
      .sort({ createdAt: -1 })
      .lean();

    return invitations.map(inv => ({
      id: inv._id.toString(),
      teamId: teamId,
      teamName: team.name,
      invitedUserId: (inv.invitedUserId as any)._id.toString(),
      invitedUserEmail: (inv.invitedUserId as any).email,
      invitedByUserId: (inv.invitedByUserId as any)._id.toString(),
      invitedByUsername: (inv.invitedByUserId as any).username,
      status: inv.status,
      createdAt: inv.createdAt,
      respondedAt: inv.respondedAt,
      expiresAt: inv.expiresAt
    }));
  }

  async acceptInvitation(invitationId: string, userId: string): Promise<TeamResponseDto> {
    const invitation = await this.teamInvitationModel.findById(invitationId);
    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    // Check if user is the invited user
    if (invitation.invitedUserId.toString() !== userId) {
      throw new ForbiddenException('You can only accept your own invitations');
    }

    // Check if invitation is still valid
    if (invitation.status !== 'pending') {
      throw new BadRequestException('Invitation has already been responded to');
    }

    if (invitation.expiresAt < new Date()) {
      throw new BadRequestException('Invitation has expired');
    }    // Check if user is already in a team
    const user = await this.usersService.findById(userId);
    if (user && user.teamId) {
      throw new ConflictException('You are already in a team');
    }

    // Get team and check if it's full
    const team = await this.teamModel.findById(invitation.teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    const activeMembers = team.members.filter(member => member.status === 'active');
    if (activeMembers.length >= team.maxMembers) {
      throw new ConflictException('Team is full');
    }

    // Add user to team
    team.members.push({
      userId: new Types.ObjectId(userId),
      role: 'member',
      status: 'active',
      joinedAt: new Date()
    } as TeamMember);

    await team.save();

    // Update invitation status
    invitation.status = 'accepted';
    invitation.respondedAt = new Date();
    await invitation.save();    // Update user's teamId
    await this.usersService.joinTeam(userId, (team._id as any).toString());

    // Transfer points from user's individual solves to team
    await this.transferUserPointsToTeam(userId, (team._id as any).toString());

    return this.formatTeamResponse(team);
  }

  async declineInvitation(invitationId: string, userId: string): Promise<void> {
    const invitation = await this.teamInvitationModel.findById(invitationId);
    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    // Check if user is the invited user
    if (invitation.invitedUserId.toString() !== userId) {
      throw new ForbiddenException('You can only decline your own invitations');
    }

    // Check if invitation is still valid
    if (invitation.status !== 'pending') {
      throw new BadRequestException('Invitation has already been responded to');
    }

    // Update invitation status
    invitation.status = 'declined';
    invitation.respondedAt = new Date();
    await invitation.save();
  }

  async cancelInvitation(invitationId: string, captainId: string): Promise<void> {
    const invitation = await this.teamInvitationModel.findById(invitationId);
    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    // Check if user is the captain who sent the invitation
    if (invitation.invitedByUserId.toString() !== captainId) {
      throw new ForbiddenException('Only the captain who sent the invitation can cancel it');
    }

    // Check if invitation is still pending
    if (invitation.status !== 'pending') {
      throw new BadRequestException('Can only cancel pending invitations');
    }

    // Update invitation status
    invitation.status = 'cancelled';
    invitation.respondedAt = new Date();
    await invitation.save();
  }

  // Get team challenge solves
  async getTeamSolves(teamId: string): Promise<TeamSolveDto[]> {
    if (!Types.ObjectId.isValid(teamId)) {
      throw new NotFoundException('Invalid team ID');
    }

    const teamSolves = await this.teamChallengeSolveModel
      .find({ teamId: new Types.ObjectId(teamId) })
      .populate('challengeId', 'title category difficulty')
      .populate('solvedBy', 'username')
      .sort({ solvedAt: -1 })
      .lean();

    return teamSolves.map(solve => ({
      challengeId: (solve.challengeId as any)._id.toString(),
      challengeTitle: (solve.challengeId as any).title,
      challengeCategory: (solve.challengeId as any).category,
      challengeDifficulty: (solve.challengeId as any).difficulty,
      solvedBy: (solve.solvedBy as any)._id.toString(),
      solvedByUsername: (solve.solvedBy as any).username,
      pointsAwarded: solve.pointsAwarded,
      solvedAt: solve.solvedAt,
      isFirstBlood: solve.isFirstBlood,
      flagIndex: solve.flagIndex
    }));
  }

  // Get team members with detailed info
  async getTeamMembers(teamId: string): Promise<TeamMemberDto[]> {
    if (!Types.ObjectId.isValid(teamId)) {
      throw new NotFoundException('Invalid team ID');
    }

    const team = await this.teamModel
      .findById(teamId)
      .populate('members.userId', 'username email score')
      .lean();

    if (!team) {
      throw new NotFoundException('Team not found');
    }

    const memberStats = await Promise.all(
      team.members
        .filter(member => member.status === 'active')
        .map(async (member) => {
          const user = member.userId as any;
          
          // Get solved challenges count for this user
          const solvedCount = await this.challengeSubmissionModel.countDocuments({
            userId: user._id,
            isCorrect: true
          });

          return {
            userId: user._id.toString(),
            username: user.username,
            email: user.email,
            role: member.role,
            status: member.status,
            joinedAt: member.joinedAt,
            score: user.score || 0,
            solvedChallenges: solvedCount
          };
        })
    );

    return memberStats;
  }

  // Get team leaderboard
  async getTeamLeaderboard(page: number = 1, limit: number = 20): Promise<{
    teams: TeamLeaderboardEntryDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    const skip = (page - 1) * limit;

    const [teams, total] = await Promise.all([
      this.teamModel.aggregate([
        { $match: { 'members.status': 'active' } },
        {
          $lookup: {
            from: 'teamchallengesolves',
            localField: '_id',
            foreignField: 'teamId',
            as: 'solves'
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'captainId',
            foreignField: '_id',
            as: 'captain'
          }
        },
        {
          $addFields: {
            solvedChallenges: { $size: '$solves' },
            lastSolved: { $max: '$solves.solvedAt' },
            memberCount: {
              $size: {
                $filter: {
                  input: '$members',
                  cond: { $eq: ['$$this.status', 'active'] }
                }
              }
            }
          }
        },
        { $sort: { teamScore: -1, lastSolved: 1 } },
        { $skip: skip },
        { $limit: limit },
        {
          $project: {
            _id: 1,
            name: 1,
            teamScore: 1,
            rank: 1,
            memberCount: 1,
            solvedChallenges: 1,
            lastSolved: 1,
            captain: { $arrayElemAt: ['$captain', 0] }
          }
        }
      ]),
      this.teamModel.countDocuments({ 'members.status': 'active' })
    ]);

    const formattedTeams = teams.map((team, index) => ({
      teamId: team._id.toString(),
      teamName: team.name,
      teamScore: team.teamScore || 0,
      rank: skip + index + 1, // Calculate rank based on position
      memberCount: team.memberCount,
      solvedChallenges: team.solvedChallenges,
      lastSolved: team.lastSolved,
      captain: {
        id: team.captain._id.toString(),
        username: team.captain.username
      }
    }));

    return {
      teams: formattedTeams,
      total,
      page,
      limit
    };
  }

  // Point calculation methods (no-redundancy logic)
  async updateTeamScore(teamId: string, challengeId: string, points: number): Promise<void> {
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    // Check if team has already solved this challenge
    const alreadySolved = team.solvedChallenges.some(
      solved => solved.challengeId.toString() === challengeId
    );

    if (!alreadySolved) {
      // Add points only if team hasn't solved this challenge before
      team.teamScore += points;
      team.solvedChallenges.push({
        challengeId: new Types.ObjectId(challengeId),
        solvedAt: new Date(),
        points
      });

      await team.save();
      
      // Update team rankings
      await this.updateTeamRankings();
    }
  }

  async updateTeamRankings(): Promise<void> {
    const teams = await this.teamModel
      .find({ 'members.status': 'active' })
      .sort({ teamScore: -1, createdAt: 1 })
      .exec();    const bulkOps = teams.map((team, index) => ({
      updateOne: {
        filter: { _id: String(team._id) },
        update: { rank: index + 1 }
      }
    }));

    if (bulkOps.length > 0) {
      await this.teamModel.bulkWrite(bulkOps);
    }
  }
  private generateInviteCode(): string {
    return `team_${uuidv4().replace(/-/g, '').substring(0, 12)}`;
  }  /**
   * Transfers points from user's individual challenge solves to team when joining
   * Only awards points for challenges that the team hasn't solved yet
   */  private async transferUserPointsToTeam(userId: string, teamId: string): Promise<void> {
    try {
      console.log(`\n=== DEBUGGING POINT TRANSFER ===`);
      console.log(`User ID: ${userId}, Team ID: ${teamId}`);
      
      // First, let's check if the user actually exists and get their current score
      const user = await this.userModel.findById(userId).lean();
      console.log(`User found:`, user ? `Yes - Current score: ${user.score || 0}` : 'No');
      
      // Let's see ALL submissions in the database to understand the data structure
      const allSubmissions = await this.challengeSubmissionModel.find({}).limit(5).lean();
      console.log(`\nSample submissions in database (first 5):`);
      allSubmissions.forEach((sub, index) => {
        console.log(`Sample ${index + 1}:`, {
          _id: sub._id,
          userId: sub.userId,
          challengeId: sub.challengeId,
          isCorrect: sub.isCorrect,
          teamId: sub.teamId,
          submittedAt: sub.submittedAt,
          points: sub.pointsAwarded || 'N/A',
          flagIndex: sub.flagIndex || 'N/A'
        });
      });
        // Check how many total submissions exist for this specific user
      // Try both string and ObjectId formats since the debug shows userId as string in DB
      const userSubmissionCount = await this.challengeSubmissionModel.countDocuments({
        $or: [
          { userId: userId }, // Try as string first
          { userId: new Types.ObjectId(userId) } // Then as ObjectId
        ]
      });
      console.log(`\nTotal submissions for user ${userId}: ${userSubmissionCount}`);
      
      // Check correct submissions for this user
      const correctSubmissionCount = await this.challengeSubmissionModel.countDocuments({
        $or: [
          { userId: userId, isCorrect: true },
          { userId: new Types.ObjectId(userId), isCorrect: true }
        ]
      });
      console.log(`Correct submissions for user ${userId}: ${correctSubmissionCount}`);
      
      // Let's see ALL submissions for this user (not just correct ones)
      const allUserSubmissions = await this.challengeSubmissionModel
        .find({
          $or: [
            { userId: userId },
            { userId: new Types.ObjectId(userId) }
          ]
        })
        .populate('challengeId')
        .lean();

      console.log(`\nAll submissions for user ${userId}: ${allUserSubmissions.length}`);
      allUserSubmissions.forEach((sub, index) => {
        console.log(`Submission ${index + 1}:`, {
          _id: sub._id,
          isCorrect: sub.isCorrect,
          teamId: sub.teamId,
          challengeId: sub.challengeId ? (sub.challengeId as any)._id : 'Not populated',
          challengeName: sub.challengeId ? (sub.challengeId as any).title : 'Not populated',
          points: sub.pointsAwarded || (sub.challengeId ? (sub.challengeId as any).points : 'N/A'),
          flagIndex: sub.flagIndex,
          submittedAt: sub.submittedAt
        });
      });      // Now let's see ALL submissions for this user to debug
      const allUserSolves = await this.challengeSubmissionModel
        .find({
          $or: [
            { userId: userId, isCorrect: true },
            { userId: new Types.ObjectId(userId), isCorrect: true }
          ]
        })
        .populate('challengeId')
        .lean();

      console.log(`\nFound ${allUserSolves.length} total correct solves for user ${userId}`);
      
      // Log the teamId values to see what they look like
      allUserSolves.forEach((solve, index) => {
        console.log(`Correct solve ${index + 1}:`, {
          challengeId: solve.challengeId ? (solve.challengeId as any)._id : 'Not populated',
          challengeName: solve.challengeId ? (solve.challengeId as any).title : 'Not populated',
          teamId: solve.teamId,
          teamIdType: typeof solve.teamId,
          teamIdExists: solve.teamId !== undefined,
          teamIdNull: solve.teamId === null,
          points: solve.pointsAwarded || (solve.challengeId ? (solve.challengeId as any).points : 'N/A'),
          flagIndex: solve.flagIndex,
          submittedAt: solve.submittedAt
        });
      });      // Get all challenges the user has solved individually (correct submissions)
      // Let's be more flexible about what constitutes an "individual" solve
      // We'll include ALL correct solves and then exclude only those that clearly belong to other teams
      const userSolves = await this.challengeSubmissionModel
        .find({
          $and: [
            {
              $or: [
                { userId: userId, isCorrect: true },
                { userId: new Types.ObjectId(userId), isCorrect: true }
              ]
            },
            {
              $or: [
                { teamId: { $exists: false } },
                { teamId: null },
                { teamId: userId }, // Sometimes individual solves have userId as teamId (string format)
                { teamId: new Types.ObjectId(userId) }, // Sometimes individual solves have userId as teamId (ObjectId format)
                { teamId: { $ne: new Types.ObjectId(teamId) } } // Any solves not for the current team
              ]
            }
          ]
        })
        .populate('challengeId')
        .lean();

      console.log(`Found ${userSolves.length} individual solves for user ${userId} after filtering`);

      if (!userSolves.length) {
        console.log('User has no individual solves to transfer');
        return; // User has no individual solves
      }

      // Get challenges already solved by the team
      const teamSolves = await this.teamChallengeSolveModel
        .find({ teamId: new Types.ObjectId(teamId) })
        .select('challengeId')
        .lean();

      const teamSolvedChallengeIds = new Set(
        teamSolves.map(solve => solve.challengeId.toString())
      );

      let totalPointsToAdd = 0;
      const challengesToProcess: Array<{
        challengeId: Types.ObjectId;
        points: number;
        userSolve: any;
      }> = [];

      // Process each user solve to see if team can get points
      for (const userSolve of userSolves) {
        const challenge = userSolve.challengeId as any;
        if (!challenge) continue;

        const challengeIdStr = challenge._id.toString();

        // Only process if team hasn't solved this challenge yet
        if (!teamSolvedChallengeIds.has(challengeIdStr)) {
          let points = 0;

          // Calculate points based on challenge structure
          if (challenge.flags && challenge.flags.length > 0 && userSolve.flagIndex >= 0) {
            // Multi-flag challenge - get points for specific flag
            const flag = challenge.flags[userSolve.flagIndex];
            points = flag?.points || challenge.points;
          } else {
            // Single flag challenge - use challenge points
            points = challenge.points;
          }

          totalPointsToAdd += points;
          challengesToProcess.push({
            challengeId: challenge._id,
            points: points,
            userSolve: userSolve
          });
        }
      }

      if (challengesToProcess.length === 0) {
        return; // No new challenges to award points for
      }

      // Create team solve records for each qualifying challenge
      const teamSolveRecords = challengesToProcess.map(item => ({
        teamId: new Types.ObjectId(teamId),
        challengeId: item.challengeId,
        solvedBy: new Types.ObjectId(userId),
        pointsAwarded: item.points,
        solvedAt: item.userSolve.submittedAt,
        isFirstBlood: item.userSolve.isFirstBlood || false,
        flagIndex: item.userSolve.flagIndex || -1
      }));      // Insert team solve records (batch insert for performance)
      if (teamSolveRecords.length > 0) {
        await this.teamChallengeSolveModel.insertMany(teamSolveRecords);
        console.log(`Created ${teamSolveRecords.length} team solve records`);
      }      // Update the individual submissions to mark them as team submissions
      const submissionIds = challengesToProcess.map(item => item.userSolve._id);
      if (submissionIds.length > 0) {
        await this.challengeSubmissionModel.updateMany(
          { _id: { $in: submissionIds } },
          { 
            teamId: new Types.ObjectId(teamId),
            teamPointsAwarded: totalPointsToAdd / challengesToProcess.length // Average points per challenge
          }
        );
        console.log(`Updated ${submissionIds.length} individual submissions with team info`);
      }

      // Update team score
      if (totalPointsToAdd > 0) {
        await this.teamModel.findByIdAndUpdate(
          teamId,
          { $inc: { teamScore: totalPointsToAdd } }
        );
        console.log(`Updated team score by ${totalPointsToAdd} points`);

        // Update team rankings after score change
        await this.updateTeamRankings();
        console.log('Updated team rankings');
      }

      console.log(`Successfully transferred ${totalPointsToAdd} points to team ${teamId} for user ${userId} from ${challengesToProcess.length} challenges`);
      
    } catch (error) {
      console.error('Error transferring user points to team:', error);
      // Don't throw error to prevent team join from failing
      // The user can still join the team, just without the point transfer
    }
  }

  /**
   * Clean up duplicate team member entries
   * This method removes duplicate entries for the same user in team members array
   */
  async cleanupDuplicateMembers(teamId: string): Promise<void> {
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    const uniqueMembers: TeamMember[] = [];
    const seenUserIds = new Set<string>();

    // Keep only the latest entry for each user (based on joinedAt)
    team.members
      .sort((a, b) => b.joinedAt.getTime() - a.joinedAt.getTime()) // Sort by joinedAt descending (latest first)
      .forEach(member => {
        const userId = member.userId.toString();
        if (!seenUserIds.has(userId)) {
          seenUserIds.add(userId);
          uniqueMembers.push(member);
        }
      });

    team.members = uniqueMembers;
    await team.save();
    
    console.log(`Cleaned up duplicate members for team ${team.name}. Kept ${uniqueMembers.length} unique members.`);
  }

  private async formatTeamResponse(team: any): Promise<TeamResponseDto> {
    // Ensure captain is populated
    let captain;
    if (typeof team.captainId === 'object' && team.captainId.username) {
      captain = team.captainId;
    } else {
      captain = await this.userModel.findById(team.captainId, 'username email').exec();
    }

    // Format members with user details
    const formattedMembers = await Promise.all(
      team.members
        .filter((member: any) => member.status === 'active')
        .map(async (member: any) => {
          let user;
          if (typeof member.userId === 'object' && member.userId.username) {
            user = member.userId;
          } else {
            user = await this.userModel.findById(member.userId, 'username email').exec();
          }          return {
            userId: typeof member.userId === 'object' ? String(member.userId._id) : String(member.userId),
            username: user?.username || 'Unknown',
            email: user?.email || 'Unknown',
            role: member.role,
            status: member.status,
            joinedAt: member.joinedAt
          };
        })
    );    return {
      id: String(team._id),
      name: team.name,
      description: team.description,
      isPublic: team.isPublic,      captainId: typeof team.captainId === 'object' ? String(team.captainId._id) : String(team.captainId),
      captain: {
        id: typeof team.captainId === 'object' ? String(team.captainId._id) : String(team.captainId),
        username: captain?.username || 'Unknown',
        email: captain?.email || 'Unknown'
      },
      members: formattedMembers,
      maxMembers: team.maxMembers,
      teamScore: team.teamScore,
      rank: team.rank,
      inviteCode: team.inviteCode,
      createdAt: team.createdAt,
      updatedAt: team.updatedAt
    };
  }
}
