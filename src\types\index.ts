export interface User {
  id: string;
  username: string;
  email: string;
  role: 'user' | 'admin' | 'moderator';
  score: number;
  rank: number;
  teamId?: string | null;
  avatarUrl?: string | null;
  country?: string | null;
  bio?: string | null;
  isActive?: boolean;
  lastActive?: string;
  createdAt?: string;
  updatedAt?: string;
  apiToken?: string;
  // Legacy fields for backward compatibility
  avatar?: string;
  joinedDate?: string;
}

export interface Flag {
  value: string;
  isCaseSensitive: boolean;
  points: number;
  description?: string;
}

export interface ChallengeFile {
  name: string;
  path: string;
  size?: number;
  type?: string;
  description?: string;
}

export interface FirstBlood {
  userId: string;
  username: string;
  timestamp: string;
  teamId?: string;
  teamName?: string;
  flagIndex?: number;
}

export interface Challenge {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard' | 'insane';
  points: number;
  solves: number;
  hints: string[];
  isSolved: boolean;
  releaseDate: string;
  author: string;
  authorId?: string;
  authorName?: string;
  tags: string[];
  // Multi-flag support
  flags?: Flag[];
  // File attachments
  files?: ChallengeFile[];
  // First blood tracking
  firstBlood?: FirstBlood;
  flagsFirstBlood?: Record<string, FirstBlood>;
  // Server configuration
  dockerImage?: string;
  requiresServer?: boolean;
  serverConfig?: any;
  // Team solve information
  solvedByTeammate?: {
    userId: string;
    username: string;
    solvedAt: string;
    isStillInTeam: boolean;
  };
}

export interface VM {
  id: string;
  name: string;
  ipAddress: string;
  status: 'running' | 'stopped' | 'starting' | 'stopping';
  timeRemaining: number;
  maxTime: number;
  ports: number[];
  os: string;
  difficulty: 'easy' | 'medium' | 'hard' | 'insane';
  category: string;
}

export interface LeaderboardEntry {
  rank: number;
  user: User;
  score: number;
  solvedChallenges: number;
  lastSolved: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface Category {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  color: string;
  icon: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface AppState {
  auth: AuthState;
  challenges: Challenge[];
  categories: Category[];
  vms: VM[];
  leaderboard: LeaderboardEntry[];
  userLeaderboard: UserLeaderboardResponse | null;
  teamLeaderboard: TeamLeaderboardResponse | null;
  topUsers: UserLeaderboardEntry[];
  topTeams: TeamLeaderboardEntry[];
  userRanking: UserRanking | null;
  teamRanking: TeamRanking | null;
  stats: {
    totalUsers: number;
    totalChallenges: number;
    activeVMs: number;
    totalSolves: number;
  };
}

// Team-related interfaces
export interface TeamMember {
  userId: string;
  username: string;
  email: string;
  role: 'captain' | 'member';
  status: string;
  joinedAt: string;
}

export interface Team {
  id: string;
  name: string;
  description: string;
  isPublic: boolean;
  captainId: string;
  captain: {
    id: string;
    username: string;
    email: string;
  };
  members: TeamMember[];
  maxMembers: number;
  teamScore: number;
  rank: number;
  inviteCode: string;
  createdAt: string;
  updatedAt: string;
}

export interface TeamInvitation {
  id: string;
  teamId: string;
  teamName: string;
  invitedUserId: string;
  invitedUserEmail: string;
  invitedByUserId: string;
  invitedByUsername: string;
  status: 'pending' | 'accepted' | 'declined' | 'cancelled';
  createdAt: string;
  respondedAt?: string;
  expiresAt: string;
}

export interface TeamSolve {
  challengeId: string;
  challengeTitle: string;
  challengeCategory: string;
  challengeDifficulty: string;
  solvedBy: string;
  solvedByUsername: string;
  pointsAwarded: number;
  solvedAt: string;
  isFirstBlood: boolean;
  flagIndex: number;
}

export interface TeamMemberDetailed {
  userId: string;
  username: string;
  email: string;
  role: 'captain' | 'member';
  status: string;
  joinedAt: string;
  score: number;
  solvedChallenges: number;
}

// Leaderboard interfaces
export interface UserLeaderboardEntry {
  userId: string;
  username: string;
  email: string;
  avatar?: string;
  score: number;
  rank: number;
  solvedChallenges: number;
  lastSolved?: string;
  totalSubmissions: number;
}

export interface TeamLeaderboardEntry {
  teamId: string;
  teamName: string;
  teamScore: number;
  rank: number;
  memberCount: number;
  solvedChallenges: number;
  lastSolved?: string;
  captain: {
    id: string;
    username: string;
  };
}

export interface UserLeaderboardResponse {
  users: UserLeaderboardEntry[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface TeamLeaderboardResponse {
  teams: TeamLeaderboardEntry[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface UserRanking {
  userId: string;
  username: string;
  score: number;
  rank: number;
  solvedChallenges: number;
  lastSolved?: string;
  percentile: number;
}

export interface TeamRanking {
  teamId: string;
  teamName: string;
  teamScore: number;
  rank: number;
  memberCount: number;
  solvedChallenges: number;
  lastSolved?: string;
  percentile: number;
}

export interface LeaderboardQuery {
  page?: number;
  limit?: number;
}