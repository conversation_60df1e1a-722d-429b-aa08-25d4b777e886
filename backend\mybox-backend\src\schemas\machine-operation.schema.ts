import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type MachineOperationDocument = MachineOperation & Document;

@Schema({ timestamps: true })
export class MachineOperation {
  @Prop({ type: Types.ObjectId, ref: 'MachineInstance', required: true })
  instanceId: Types.ObjectId;

  @Prop({ 
    required: true, 
    enum: ['stop', 'restart', 'extend', 'cleanup', 'auto_shutdown'] 
  })
  operationType: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  scheduledBy: Types.ObjectId;

  @Prop({ required: true })
  scheduledAt: Date;

  @Prop({ required: true })
  executeAt: Date;

  @Prop({ 
    required: true, 
    enum: ['pending', 'executing', 'completed', 'cancelled', 'failed'],
    default: 'pending'
  })
  status: string;

  @Prop({ default: true })
  cancellable: boolean;

  @Prop({ required: true })
  jobId: string;

  @Prop({ default: 0 })
  attempts: number;

  @Prop({ required: false })
  lastError?: string;

  @Prop({ required: false })
  completedAt?: Date;

  @Prop({ required: false })
  cancelledAt?: Date;

  @Prop({ required: false })
  cancelledBy?: Types.ObjectId;

  @Prop({ type: Object, default: {} })
  operationData: Record<string, any>;
}

export const MachineOperationSchema = SchemaFactory.createForClass(MachineOperation);
