import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { Notification, notificationsService } from '../services/notifications';
import { UserNotificationSettings, notificationSoundsService } from '../services/notification-sounds';
import { useApp } from './AppContext';
import { AuthService } from '../services/api';

interface NotificationsContextType {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  loading: boolean;
  markAsRead: (notificationIds: string[]) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  loadNotifications: (query?: any) => Promise<void>;
  showFirstBloodAnimation: boolean;
  setShowFirstBloodAnimation: (show: boolean) => void;
  latestFirstBlood: Notification | null;
  notificationSettings: UserNotificationSettings | null;
  loadNotificationSettings: () => Promise<void>;
  updateNotificationSettings: (settings: Partial<UserNotificationSettings>) => Promise<void>;
}

const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationsContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationsProvider');
  }
  return context;
};

interface NotificationsProviderProps {
  children: React.ReactNode;
}

export const NotificationsProvider: React.FC<NotificationsProviderProps> = ({ children }) => {
  const { state } = useApp();
  const user = state.auth.user;
  
  const [socket, setSocket] = useState<Socket | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showFirstBloodAnimation, setShowFirstBloodAnimation] = useState(false);
  const [latestFirstBlood, setLatestFirstBlood] = useState<Notification | null>(null);
  const [notificationSettings, setNotificationSettings] = useState<UserNotificationSettings | null>(null);

  // Load notification settings
  const loadNotificationSettings = useCallback(async () => {
    if (!user) return;
    
    try {
      const settings = await notificationSoundsService.getUserNotificationSettings();
      setNotificationSettings(settings);
      
      // Initialize notification sounds
      await notificationSoundsService.initializeNotificationSounds(settings);
    } catch (error) {
      console.error('Failed to load notification settings:', error);
    }
  }, [user]);

  // Update notification settings
  const updateNotificationSettings = useCallback(async (updates: Partial<UserNotificationSettings>) => {
    if (!user) return;
    
    try {
      const response = await notificationSoundsService.updateUserNotificationSettings(updates);
      setNotificationSettings(response.settings);
      
      // Re-initialize notification sounds with new settings
      await notificationSoundsService.initializeNotificationSounds(response.settings);
    } catch (error) {
      console.error('Failed to update notification settings:', error);
    }
  }, [user]);

  // Load notification settings on mount
  useEffect(() => {
    if (user) {
      loadNotificationSettings();
    }
  }, [user, loadNotificationSettings]);

  // Play notification sound for incoming notifications
  const playNotificationSound = useCallback(async (notification: Notification) => {
    if (!notificationSettings) return;

    try {
      // Always get fresh settings to ensure we use the latest sound selection
      const freshSettings = await notificationSoundsService.getUserNotificationSettings();
      const notificationType = notification.type as keyof UserNotificationSettings['notificationTypes'];
      await notificationSoundsService.playNotificationForType(notificationType, freshSettings);
    } catch (error) {
      console.error('Failed to play notification sound:', error);
    }
  }, [notificationSettings]);

  // Initialize socket connection
  useEffect(() => {
    // Wait for auth to finish loading
    if (state.auth.isLoading) {
      return;
    }

    if (!state.auth.isAuthenticated || !user) {
      return;
    }

    // Get token from AuthService
    const token = AuthService.getToken();
    if (!token) {
      return;
    }

    const apiUrl = 'http://localhost:3001';
    const socketUrl = `${apiUrl}/notifications`;
    
    const newSocket = io(socketUrl, {
      auth: {
        token: token,
      },
      transports: ['websocket'],
      forceNew: true,
      timeout: 20000,
    });

    newSocket.on('connect', () => {
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      setIsConnected(false);
    });

    newSocket.on('connect_error', () => {
      setIsConnected(false);
    });

    newSocket.on('unread_count', (data: { count: number }) => {
      setUnreadCount(data.count);
    });

    newSocket.on('new_notification', async (data: { notification: Notification; timestamp: string }) => {
      setNotifications(prev => [data.notification, ...prev]);
      setUnreadCount(prev => prev + 1);
      
      // Play notification sound
      await playNotificationSound(data.notification);
      
      // Dispatch custom event for toast notification
      window.dispatchEvent(new CustomEvent('new-notification-toast', { 
        detail: data.notification 
      }));
      
      // Show browser notification if permission is granted and enabled
      if (notificationSettings?.desktopNotifications && 
          'Notification' in window && 
          window.Notification.permission === 'granted') {
        new window.Notification(data.notification.title, {
          body: data.notification.message,
          icon: '/favicon.ico',
          tag: data.notification._id,
        });
      }
    });

    newSocket.on('first_blood_notification', async (data: { 
      notification: Notification; 
      timestamp: string; 
      animation: string 
    }) => {
      setNotifications(prev => [data.notification, ...prev]);
      setUnreadCount(prev => prev + 1);
      setLatestFirstBlood(data.notification);
      setShowFirstBloodAnimation(true);
      
      // Play notification sound for first blood
      await playNotificationSound(data.notification);
      
      // Dispatch custom event for toast notification
      window.dispatchEvent(new CustomEvent('first-blood-notification-toast', { 
        detail: data.notification 
      }));
      
      // Show special browser notification for first blood
      if (notificationSettings?.desktopNotifications && 
          'Notification' in window && 
          window.Notification.permission === 'granted') {
        new window.Notification('🏆 First Blood!', {
          body: data.notification.message,
          icon: '/favicon.ico',
          tag: data.notification._id,
        });
      }
      
      // Auto-hide animation after 5 seconds
      setTimeout(() => {
        setShowFirstBloodAnimation(false);
      }, 5000);
    });

    newSocket.on('notifications_marked_read', (data: { notificationIds: string[] }) => {
      setNotifications(prev => 
        prev.map(notification => 
          data.notificationIds.includes(notification._id)
            ? { ...notification, isRead: true }
            : notification
        )
      );
    });

    newSocket.on('all_notifications_marked_read', () => {
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, isRead: true }))
      );
      setUnreadCount(0);
    });

    setSocket(newSocket);

    return () => {
      newSocket.disconnect();
    };
  }, [state.auth.isLoading, state.auth.isAuthenticated, user]);

  // Load initial notifications
  const loadNotifications = useCallback(async (query: any = {}) => {
    if (!user) return;
    
    setLoading(true);
    try {
      const response = await notificationsService.getUserNotifications(query);
      setNotifications(response.notifications);
      setUnreadCount(response.unreadCount);
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Load notifications on mount
  useEffect(() => {
    if (user) {
      loadNotifications();
    }
  }, [user, loadNotifications]);

  const markAsRead = useCallback(async (notificationIds: string[]) => {
    try {
      const response = await notificationsService.markAsRead(notificationIds);
      setUnreadCount(response.unreadCount);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notificationIds.includes(notification._id)
            ? { ...notification, isRead: true }
            : notification
        )
      );
    } catch (error) {
      console.error('Failed to mark notifications as read:', error);
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      await notificationsService.markAllAsRead();
      setUnreadCount(0);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, isRead: true }))
      );
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }, []);

  const value: NotificationsContextType = {
    notifications,
    unreadCount,
    isConnected,
    loading,
    markAsRead,
    markAllAsRead,
    loadNotifications,
    showFirstBloodAnimation,
    setShowFirstBloodAnimation,
    latestFirstBlood,
    notificationSettings,
    loadNotificationSettings,
    updateNotificationSettings,
  };

  return (
    <NotificationsContext.Provider value={value}>
      {children}
    </NotificationsContext.Provider>
  );
};