import { useState } from 'react';
import { VM } from '../../types';
import machineService, { MachineTemplate } from '../../services/machines';
import {
  Play,
  Square,
  RotateCcw,
  Monitor,
  Clock,
  Wifi,
  Terminal,
  Eye,
  Flag,
  AlertCircle
} from 'lucide-react';
import './Machines.css';

interface MachineCardProps {
  vm: VM;
  templateId?: string; // Add template ID for spawning
  template?: MachineTemplate; // Add full template data for modal
  onRefresh?: () => void;
  onShowStartModal?: (template: MachineTemplate) => void; // Callback to show modal
}

const statusColors = {
  running: 'text-emerald-400 bg-emerald-500/20',
  stopped: 'text-red-400 bg-red-500/20',
  starting: 'text-yellow-400 bg-yellow-500/20',
  stopping: 'text-orange-400 bg-orange-500/20',
  error: 'text-red-400 bg-red-500/20'
};

const difficultyColors = {
  easy: 'text-emerald-400',
  medium: 'text-yellow-400',
  hard: 'text-orange-400',
  insane: 'text-red-400'
};

function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m ${secs}s`;
}

export function MachineCard({ vm, templateId, template, onRefresh, onShowStartModal }: MachineCardProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [flagValue, setFlagValue] = useState('');
  const [flagSubmitting, setFlagSubmitting] = useState(false);
  const [flagMessage, setFlagMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleStart = () => {
    if (vm.status === 'running') return;

    // If we have template data and callback, show the modal
    if (template && onShowStartModal) {
      onShowStartModal(template);
    } else {
      // Fallback to direct start if no template data or callback
      handleDirectStart();
    }
  };

  const handleDirectStart = async () => {
    try {
      setLoading(true);
      setError(null);
      // Use templateId for spawning new instances, fallback to vm.id if not provided
      const idToUse = templateId || vm.id;
      await machineService.spawnMachine(idToUse);
      onRefresh?.();
    } catch (err) {
      setError('Failed to start machine');
      console.error('Error starting machine:', err);
    } finally {
      setLoading(false);
    }
  };



  const handleStop = async () => {
    if (vm.status !== 'running') return;
    
    try {
      setLoading(true);
      setError(null);
      await machineService.terminateInstance(vm.id);
      onRefresh?.();
    } catch (err) {
      setError('Failed to stop machine');
      console.error('Error stopping machine:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    if (vm.status !== 'running') return;
    
    try {
      setLoading(true);
      setError(null);
      await machineService.restartInstance(vm.id);
      onRefresh?.();
    } catch (err) {
      setError('Failed to restart machine');
      console.error('Error restarting machine:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFlagSubmit = async () => {
    if (!flagValue.trim()) return;
    
    try {
      setFlagSubmitting(true);
      setFlagMessage(null);
      
      const result = await machineService.submitFlag(vm.id, {
        flagName: 'root', // Default flag name, could be made dynamic
        flag: flagValue.trim()
      });
      
      if (result.isCorrect) {
        setFlagMessage({ type: 'success', text: `Correct! +${result.pointsAwarded} points` });
        setFlagValue('');
        onRefresh?.();
      } else {
        setFlagMessage({ type: 'error', text: 'Incorrect flag' });
      }
    } catch (err) {
      setFlagMessage({ type: 'error', text: 'Failed to submit flag' });
      console.error('Error submitting flag:', err);
    } finally {
      setFlagSubmitting(false);
    }
  };

  const isActionDisabled = loading || vm.status === 'starting' || vm.status === 'stopping';

  return (
    <div className="glass-card rounded-xl p-6 group transition-all duration-300">
      {error && (
        <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center space-x-2">
          <AlertCircle className="w-4 h-4 text-red-400" />
          <span className="text-red-400 text-sm">{error}</span>
        </div>
      )}
      
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-3 bg-slate-800/50 rounded-lg">
            <Monitor className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">{vm.name}</h3>
            <p className="text-sm text-slate-400 capitalize">{vm.category}</p>
          </div>
        </div>
        
        <div className={`px-3 py-1 rounded-full text-xs font-medium ${statusColors[vm.status]}`}>
          {vm.status}
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-slate-400">Difficulty</span>
          <span className={`text-sm font-medium capitalize ${difficultyColors[vm.difficulty]}`}>
            {vm.difficulty}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-slate-400">Operating System</span>
          <span className="text-sm font-medium text-white">{vm.os}</span>
        </div>

        {vm.status === 'running' && (
          <>
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">IP Address</span>
              <div className="flex items-center space-x-2">
                <Wifi className="w-4 h-4 text-emerald-400" />
                <span className="text-sm font-mono text-emerald-400">{vm.ipAddress}</span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Time Remaining</span>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-orange-400" />
                <span className="text-sm font-medium text-orange-400">
                  {formatTime(vm.timeRemaining)}
                </span>
              </div>
            </div>

            <div className="w-full bg-slate-800 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-emerald-500 to-orange-500 h-2 rounded-full transition-all duration-1000"
                style={{ width: `${(vm.timeRemaining / vm.maxTime) * 100}%` }}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Open Ports</span>
              <div className="flex space-x-1">
                {vm.ports.map(port => (
                  <span key={port} className="px-2 py-1 bg-slate-800 text-slate-300 rounded text-xs font-mono">
                    {port}
                  </span>
                ))}
              </div>
            </div>
          </>
        )}

        <div className="flex space-x-2 pt-4">
          {vm.status === 'stopped' && (
            <button
              onClick={handleStart}
              disabled={isActionDisabled}
              className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-300 shadow-lg hover:shadow-purple-500/25"
            >
              <Play className="w-4 h-4" />
              <span>Start</span>
            </button>
          )}

          {vm.status === 'running' && (
            <>
              <button
                onClick={handleStop}
                disabled={isActionDisabled}
                className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors"
              >
                <Square className="w-4 h-4" />
                <span>Stop</span>
              </button>
              
              <button
                onClick={handleReset}
                disabled={isActionDisabled}
                className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-orange-600 hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
                <span>Reset</span>
              </button>
            </>
          )}

          {(vm.status === 'starting' || vm.status === 'stopping') && (
            <button disabled className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-slate-600 text-white font-medium rounded-lg cursor-not-allowed">
              <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
              <span className="capitalize">{vm.status}...</span>
            </button>
          )}
        </div>

        {vm.status === 'running' && (
          <>
            <div className="flex space-x-2">
              <button className="flex-1 flex items-center justify-center space-x-2 py-2 px-4 bg-blue-600/20 text-blue-400 border border-blue-600/30 rounded-lg hover:bg-blue-600/30 transition-colors">
                <Terminal className="w-4 h-4" />
                <span>SSH</span>
              </button>
              <button className="flex-1 flex items-center justify-center space-x-2 py-2 px-4 bg-purple-600/20 text-purple-400 border border-purple-600/30 rounded-lg hover:bg-purple-600/30 transition-colors">
                <Eye className="w-4 h-4" />
                <span>VNC</span>
              </button>
            </div>
            
            <div className="mt-4 p-4 bg-slate-800/30 rounded-lg border border-slate-700">
              <div className="flex items-center space-x-2 mb-3">
                <Flag className="w-4 h-4 text-green-400" />
                <span className="text-sm font-medium text-white">Submit Flag</span>
              </div>
              
              {flagMessage && (
                <div className={`mb-3 p-2 rounded text-sm ${
                  flagMessage.type === 'success' 
                    ? 'bg-green-500/10 text-green-400 border border-green-500/20' 
                    : 'bg-red-500/10 text-red-400 border border-red-500/20'
                }`}>
                  {flagMessage.text}
                </div>
              )}
              
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Enter flag..."
                  value={flagValue}
                  onChange={(e) => setFlagValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleFlagSubmit()}
                  disabled={flagSubmitting}
                  className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white text-sm placeholder-slate-400 focus:outline-none focus:border-blue-500 disabled:opacity-50"
                />
                <button
                  onClick={handleFlagSubmit}
                  disabled={!flagValue.trim() || flagSubmitting}
                  className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed text-white text-sm font-medium rounded transition-all duration-300 shadow-lg hover:shadow-purple-500/25"
                >
                  {flagSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                  ) : (
                    'Submit'
                  )}
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}