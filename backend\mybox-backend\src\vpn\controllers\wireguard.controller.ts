import { 
  Controller, 
  Get, 
  Post, 
  Delete, 
  Put,
  Req, 
  Res, 
  UseGuards, 
  HttpException, 
  HttpStatus,
  Logger
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Response, Request } from 'express';

import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { VPNConfigService } from '../services/vpn-config.service';
import { NetworkIsolationService } from '../services/network-isolation.service';
import { User } from '../../schemas/user.schema';

interface AuthenticatedRequest extends Request {
  user: User & { _id: string };
}

@ApiTags('VPN - WireGuard')
@Controller('vpn')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class WireGuardController {
  private readonly logger = new Logger(WireGuardController.name);

  constructor(
    private readonly vpnConfigService: VPNConfigService,
    private readonly networkIsolationService: NetworkIsolationService,
  ) {}

  /**
   * Generate VPN configuration for the authenticated user
   */
  @Post('generate')
  @ApiOperation({ summary: 'Generate VPN configuration for user' })
  @ApiResponse({ status: 201, description: 'VPN configuration created successfully' })
  @ApiResponse({ status: 400, description: 'User already has VPN configuration' })
  async generateVPNConfig(@Req() req: AuthenticatedRequest) {
    try {
      const userId = req.user._id;
      const vpnConfig = await this.vpnConfigService.createVPNConfig(userId);
      
      this.logger.log(`Generated VPN configuration for user ${userId}`);
      
      return {
        success: true,
        message: 'VPN configuration created successfully',
        data: {
          ipAddress: vpnConfig.ipAddress,
          subnet: vpnConfig.subnet,
          hasConfig: true
        }
      };
    } catch (error) {
      this.logger.error(`Failed to generate VPN config: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Get VPN configuration file for download
   */
  @Get('config')
  @ApiOperation({ summary: 'Download VPN configuration file' })
  @ApiResponse({ status: 200, description: 'VPN configuration file' })
  @ApiResponse({ status: 404, description: 'VPN configuration not found' })
  async downloadVPNConfig(@Req() req: AuthenticatedRequest, @Res() res: Response) {
    try {
      const userId = req.user._id;
      const username = req.user.username || 'user';
      
      const vpnConfig = await this.vpnConfigService.getVPNConfig(userId);
      
      if (!vpnConfig) {
        throw new HttpException('VPN configuration not found. Please generate one first.', HttpStatus.NOT_FOUND);
      }

      // Set headers for file download
      res.set({
        'Content-Type': 'application/x-wireguard-profile',
        'Content-Disposition': `attachment; filename="${username}-mybox.conf"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });

      this.logger.log(`User ${userId} downloaded VPN configuration`);

      // Log the download activity (indicates potential connection)
      await this.vpnConfigService.logVPNActivity(
        userId,
        'connect',
        req.ip || req.connection?.remoteAddress,
        vpnConfig.ipAddress
      );

      res.send(vpnConfig.clientConfig);
    } catch (error) {
      this.logger.error(`Failed to get VPN config: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Get VPN status and information
   */
  @Get('status')
  @ApiOperation({ summary: 'Get VPN status and configuration info' })
  @ApiResponse({ status: 200, description: 'VPN status retrieved' })
  async getVPNStatus(@Req() req: AuthenticatedRequest) {
    try {
      const userId = req.user._id;
      
      const vpnConfig = await this.vpnConfigService.getVPNConfig(userId);
      const serverStatus = await this.vpnConfigService.getVPNServerStatus();
      const userNetworks = await this.networkIsolationService.getUserNetworks(userId);

      // Check if user is likely connected (heuristic based on server status and user config)
      const isUserConnected = vpnConfig && serverStatus.isRunning && serverStatus.activeConnections > 0;

      return {
        success: true,
        data: {
          hasVPNConfig: !!vpnConfig,
          isConnected: isUserConnected, // Add connection status
          vpnInfo: vpnConfig ? {
            ipAddress: vpnConfig.ipAddress,
            subnet: vpnConfig.subnet,
            serverEndpoint: serverStatus.serverEndpoint,
            serverPort: serverStatus.serverPort,
            createdAt: vpnConfig.createdAt,
            lastUsed: vpnConfig.lastUsed
          } : null,
          serverStatus: {
            isRunning: serverStatus.isRunning,
            totalUsers: serverStatus.totalUsers,
            activeConnections: serverStatus.activeConnections
          },
          userNetworks: userNetworks.map(network => ({
            machineId: network.machineId,
            internalIP: network.internalIP,
            exposedPorts: network.exposedPorts,
            status: network.status
          }))
        }
      };
    } catch (error) {
      this.logger.error(`Failed to get VPN status: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Regenerate VPN configuration (new keys)
   */
  @Put('regenerate')
  @ApiOperation({ summary: 'Regenerate VPN configuration with new keys' })
  @ApiResponse({ status: 200, description: 'VPN configuration regenerated' })
  async regenerateVPNConfig(@Req() req: AuthenticatedRequest) {
    try {
      const userId = req.user._id;
      
      const vpnConfig = await this.vpnConfigService.regenerateVPNConfig(userId);
      
      this.logger.log(`Regenerated VPN configuration for user ${userId}`);
      
      return {
        success: true,
        message: 'VPN configuration regenerated successfully',
        data: {
          ipAddress: vpnConfig.ipAddress,
          subnet: vpnConfig.subnet
        }
      };
    } catch (error) {
      this.logger.error(`Failed to regenerate VPN config: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Revoke VPN access
   */
  @Delete('revoke')
  @ApiOperation({ summary: 'Revoke VPN access and delete configuration' })
  @ApiResponse({ status: 200, description: 'VPN access revoked' })
  async revokeVPNAccess(@Req() req: AuthenticatedRequest) {
    try {
      const userId = req.user._id;
      
      await this.vpnConfigService.revokeVPNConfig(userId);
      await this.networkIsolationService.removeUserNetwork(userId);
      
      this.logger.log(`Revoked VPN access for user ${userId}`);
      
      return {
        success: true,
        message: 'VPN access revoked successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to revoke VPN access: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Get user's connection logs
   */
  @Get('logs')
  @ApiOperation({ summary: 'Get user VPN connection logs' })
  @ApiResponse({ status: 200, description: 'Connection logs retrieved' })
  async getConnectionLogs(@Req() req: AuthenticatedRequest) {
    try {
      const userId = req.user._id;
      
      const logs = await this.vpnConfigService.getUserConnectionLogs(userId, 20);
      
      return {
        success: true,
        data: logs.map(log => ({
          action: log.action,
          timestamp: log.timestamp,
          clientIP: log.clientIP,
          vpnIP: log.vpnIP,
          disconnectedAt: log.disconnectedAt,
          connectionStats: log.connectionStats
        }))
      };
    } catch (error) {
      this.logger.error(`Failed to get connection logs: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get setup instructions for different operating systems
   */
  @Get('setup-instructions')
  @ApiOperation({ summary: 'Get VPN setup instructions for different OS' })
  @ApiResponse({ status: 200, description: 'Setup instructions retrieved' })
  async getSetupInstructions() {
    return {
      success: true,
      data: {
        windows: {
          title: 'Windows Setup',
          steps: [
            'Download and install WireGuard from https://www.wireguard.com/install/',
            'Download your configuration file from the VPN section',
            'Open WireGuard application',
            'Click "Add Tunnel" and select your .conf file',
            'Click "Activate" to connect to the VPN'
          ]
        },
        macos: {
          title: 'macOS Setup',
          steps: [
            'Download WireGuard from the Mac App Store',
            'Download your configuration file from the VPN section',
            'Open WireGuard application',
            'Click "Import tunnel(s) from file" and select your .conf file',
            'Toggle the switch to connect to the VPN'
          ]
        },
        linux: {
          title: 'Linux Setup',
          steps: [
            'Install WireGuard: sudo apt install wireguard (Ubuntu/Debian)',
            'Download your configuration file from the VPN section',
            'Copy the file to /etc/wireguard/mybox.conf',
            'Start the VPN: sudo wg-quick up mybox',
            'Stop the VPN: sudo wg-quick down mybox'
          ]
        },
        android: {
          title: 'Android Setup',
          steps: [
            'Install WireGuard from Google Play Store',
            'Download your configuration file from the VPN section',
            'Open WireGuard app and tap the "+" button',
            'Select "Import from file or archive"',
            'Select your .conf file and tap the toggle to connect'
          ]
        },
        ios: {
          title: 'iOS Setup',
          steps: [
            'Install WireGuard from the App Store',
            'Download your configuration file from the VPN section',
            'Open WireGuard app and tap the "+" button',
            'Select "Create from file or archive"',
            'Select your .conf file and tap the toggle to connect'
          ]
        }
      }
    };
  }
}
