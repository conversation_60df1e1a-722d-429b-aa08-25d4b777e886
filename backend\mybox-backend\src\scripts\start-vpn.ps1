# PowerShell script to start OpenVPN infrastructure
Write-Host "Starting OpenVPN infrastructure..." -ForegroundColor Green

# Check if Dock<PERSON> is running
try {
    docker version | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Navigate to backend directory
$backendPath = Split-Path -Parent $PSScriptRoot
Set-Location $backendPath

# Create openvpn-data directory if it doesn't exist
if (!(Test-Path "openvpn-data")) {
    New-Item -ItemType Directory -Path "openvpn-data"
    Write-Host "✓ Created openvpn-data directory" -ForegroundColor Green
}

# Start VPN services
Write-Host "Starting OpenVPN and ovpn-admin containers..." -ForegroundColor Yellow
docker compose -f docker-compose.vpn.yml up -d

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ VPN infrastructure started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "VPN Admin Web UI: http://localhost:8080" -ForegroundColor Cyan
    Write-Host "Default credentials: admin/admin" -ForegroundColor Cyan
    Write-Host "OpenVPN Port: 1194/UDP" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Waiting for services to be ready..." -ForegroundColor Yellow
    
    # Wait for services to be ready
    $maxAttempts = 30
    $attempt = 0
    
    do {
        Start-Sleep -Seconds 2
        $attempt++
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 5 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Write-Host "✓ VPN Admin interface is ready!" -ForegroundColor Green
                break
            }
        } catch {
            # Service not ready yet
        }
        
        if ($attempt -eq $maxAttempts) {
            Write-Host "⚠ Services may still be starting. Check manually at http://localhost:8080" -ForegroundColor Yellow
        }
    } while ($attempt -lt $maxAttempts)
    
} else {
    Write-Host "✗ Failed to start VPN infrastructure" -ForegroundColor Red
    exit 1
}