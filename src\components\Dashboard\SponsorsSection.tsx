import React from 'react';
import { motion } from 'framer-motion';
import { Sponsor, dashboardService } from '../../services/dashboard';
import { Award, Star, Crown, Shield, ExternalLink } from 'lucide-react';
import { getImageUrl } from '../../utils/imageUtils';

interface SponsorsSectionProps {
  sponsors: Sponsor[];
}

export function SponsorsSection({ sponsors }: SponsorsSectionProps) {
  const getTierIcon = (tier: Sponsor['tier']) => {
    const iconProps = { className: "w-5 h-5" };
    
    switch (tier) {
      case 'platinum':
        return <Crown {...iconProps} />;
      case 'gold':
        return <Award {...iconProps} />;
      case 'silver':
        return <Shield {...iconProps} />;
      case 'bronze':
        return <Star {...iconProps} />;
      default:
        return <Star {...iconProps} />;
    }
  };

  const getTierTitle = (tier: Sponsor['tier']) => {
    switch (tier) {
      case 'platinum':
        return 'Platinum Sponsors';
      case 'gold':
        return 'Gold Sponsors';
      case 'silver':
        return 'Silver Sponsors';
      case 'bronze':
        return 'Bronze Sponsors';
      default:
        return 'Sponsors';
    }
  };

  const getTierGradient = (tier: Sponsor['tier']) => {
    switch (tier) {
      case 'platinum':
        return 'from-gray-200 to-gray-400';
      case 'gold':
        return 'from-yellow-300 to-yellow-500';
      case 'silver':
        return 'from-gray-300 to-gray-500';
      case 'bronze':
        return 'from-amber-400 to-amber-600';
      default:
        return 'from-purple-400 to-pink-400';
    }
  };

  const getTierSize = (tier: Sponsor['tier']) => {
    switch (tier) {
      case 'platinum':
        return 'h-24 md:h-32';
      case 'gold':
        return 'h-20 md:h-24';
      case 'silver':
        return 'h-16 md:h-20';
      case 'bronze':
        return 'h-12 md:h-16';
      default:
        return 'h-16 md:h-20';
    }
  };

  const activeSponsors = sponsors.filter(sponsor => sponsor.isActive);
  
  if (activeSponsors.length === 0) {
    return null;
  }

  const sponsorsByTier = {
    platinum: dashboardService.getSponsorsByTier(activeSponsors, 'platinum'),
    gold: dashboardService.getSponsorsByTier(activeSponsors, 'gold'),
    silver: dashboardService.getSponsorsByTier(activeSponsors, 'silver'),
    bronze: dashboardService.getSponsorsByTier(activeSponsors, 'bronze'),
  };

  const tiers = ['platinum', 'gold', 'silver', 'bronze'] as const;

  return (
    <motion.div
      className="animate-slide-in-up stagger-5"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1.1, duration: 0.6 }}
    >
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2">
          Our Sponsors
        </h2>
        <p className="text-purple-200/70">Thank you to our amazing sponsors who make this event possible</p>
      </div>

      <div className="space-y-12">
        {tiers.map((tier, tierIndex) => {
          const tierSponsors = sponsorsByTier[tier];
          
          if (tierSponsors.length === 0) return null;

          return (
            <motion.div
              key={tier}
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 + tierIndex * 0.1, duration: 0.5 }}
            >
              {/* Tier Header */}
              <div className="text-center">
                <div className="flex items-center justify-center space-x-3 mb-4">
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${getTierGradient(tier)}`}>
                    {getTierIcon(tier)}
                  </div>
                  <h3 className={`text-2xl font-bold bg-gradient-to-r ${getTierGradient(tier)} bg-clip-text text-transparent`}>
                    {getTierTitle(tier)}
                  </h3>
                </div>
              </div>

              {/* Sponsors Grid */}
              <div className={`grid gap-6 ${
                tier === 'platinum' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                tier === 'gold' ? 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4' :
                'grid-cols-2 md:grid-cols-4 lg:grid-cols-6'
              }`}>
                {tierSponsors.map((sponsor, index) => (
                  <motion.div
                    key={sponsor.id}
                    className="group"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ 
                      delay: 1.3 + tierIndex * 0.1 + index * 0.05, 
                      duration: 0.4,
                      type: "spring",
                      stiffness: 300
                    }}
                    whileHover={{ 
                      scale: 1.05,
                      transition: { type: "spring", stiffness: 400 }
                    }}
                  >
                    {sponsor.website ? (
                      <a
                        href={sponsor.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block"
                      >
                        <SponsorCard sponsor={sponsor} tier={tier} />
                      </a>
                    ) : (
                      <SponsorCard sponsor={sponsor} tier={tier} />
                    )}
                  </motion.div>
                ))}
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Thank You Message */}
      <motion.div
        className="text-center mt-16 p-8 glass-card border border-purple-500/30 rounded-2xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.5, duration: 0.6 }}
      >
        <h3 className="text-2xl font-bold text-white mb-4">Thank You!</h3>
        <p className="text-purple-200/80 max-w-2xl mx-auto">
          We're grateful for the support of our sponsors who help make this cybersecurity event possible. 
          Their contribution enables us to provide cutting-edge challenges and prizes for our community.
        </p>
      </motion.div>
    </motion.div>
  );
}

interface SponsorCardProps {
  sponsor: Sponsor;
  tier: Sponsor['tier'];
}

function SponsorCard({ sponsor, tier }: SponsorCardProps) {
  const getTierSize = (tier: Sponsor['tier']) => {
    switch (tier) {
      case 'platinum':
        return 'h-24 md:h-32';
      case 'gold':
        return 'h-20 md:h-24';
      case 'silver':
        return 'h-16 md:h-20';
      case 'bronze':
        return 'h-12 md:h-16';
      default:
        return 'h-16 md:h-20';
    }
  };

  const getTierBorder = (tier: Sponsor['tier']) => {
    switch (tier) {
      case 'platinum':
        return 'border-gray-300/50 group-hover:border-gray-300';
      case 'gold':
        return 'border-yellow-400/50 group-hover:border-yellow-400';
      case 'silver':
        return 'border-gray-400/50 group-hover:border-gray-400';
      case 'bronze':
        return 'border-amber-500/50 group-hover:border-amber-500';
      default:
        return 'border-purple-500/50 group-hover:border-purple-500';
    }
  };

  return (
    <div className={`relative p-6 glass-card border ${getTierBorder(tier)} rounded-xl transition-all duration-300 group-hover:shadow-lg group-hover:shadow-purple-500/20`}>
      {/* Sponsor Logo */}
      <div className={`flex items-center justify-center ${getTierSize(tier)} mb-4`}>
        <img
          src={getImageUrl(sponsor.logo)}
          alt={sponsor.name}
          className="max-w-full max-h-full object-contain filter group-hover:brightness-110 transition-all duration-300"
          onError={(e) => {
            console.error('Failed to load sponsor logo:', sponsor.logo);
            e.currentTarget.style.display = 'none';
          }}
        />
      </div>

      {/* Sponsor Name */}
      <h4 className="text-center font-semibold text-white group-hover:text-purple-200 transition-colors">
        {sponsor.name}
      </h4>

      {/* Sponsor Description */}
      {sponsor.description && (
        <p className="text-center text-sm text-purple-200/60 mt-2 line-clamp-2">
          {sponsor.description}
        </p>
      )}

      {/* External Link Icon */}
      {sponsor.website && (
        <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
          <ExternalLink className="w-4 h-4 text-purple-300" />
        </div>
      )}

      {/* Tier Badge */}
      <div className="absolute top-3 left-3">
        <div className={`p-1 rounded bg-gradient-to-r ${dashboardService.getTierColor(tier)} opacity-80`}>
          {tier === 'platinum' && <Crown className="w-3 h-3 text-gray-800" />}
          {tier === 'gold' && <Award className="w-3 h-3 text-yellow-800" />}
          {tier === 'silver' && <Shield className="w-3 h-3 text-gray-800" />}
          {tier === 'bronze' && <Star className="w-3 h-3 text-amber-800" />}
        </div>
      </div>
    </div>
  );
}