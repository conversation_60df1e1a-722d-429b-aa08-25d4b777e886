#!/bin/bash

if [ -z "$1" ]; then
  echo "Usage: $0 <username>"
  exit 1
fi

USERNAME=$1
CONFIG_PATH="/etc/openvpn"
EASY_RSA="/etc/openvpn/easy-rsa"

# Create client certificate
cd "$EASY_RSA" || exit 1

echo "Generating client certificate for $USERNAME..."
./easyrsa build-client-full "$USERNAME" nopass

# Create client config file
echo "Creating client configuration..."
cat > "${CONFIG_PATH}/${USERNAME}.ovpn" << EOF
client
dev tun
proto udp
remote YOUR_VPN_SERVER_IP 1194
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
cipher AES-256-CBC
verb 3
<ca>
$(cat "${EASY_RSA}/pki/ca.crt")
</ca>
<cert>
$(openssl x509 -in "${EASY_RSA}/pki/issued/${USERNAME}.crt")
</cert>
<key>
$(cat "${EASY_RSA}/pki/private/${USERNAME}.key")
</key>
<tls-auth>
$(cat "${EASY_RSA}/ta.key")
</tls-auth>
key-direction 1
EOF

echo "Client configuration created at ${CONFIG_PATH}/${USERNAME}.ovpn"
