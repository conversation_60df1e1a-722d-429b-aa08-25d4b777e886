import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useApp } from '../../contexts/AppContext';
import { usePlatformSettings } from '../../contexts/PlatformSettingsContext';
import { Shield, LogOut, User, Settings, Menu, X, Sparkles } from 'lucide-react';
import { NotificationBell } from '../Notifications/NotificationBell';

export function Header() {
  const { state, logout } = useApp();
  const { isFeatureEnabled } = usePlatformSettings();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActive = (path: string) => location.pathname === path;

  // Build navigation items based on platform settings
  const navItems = [
    { path: '/', label: 'Dashboard' },
    { path: '/challenges', label: 'Challenges' },
    { path: '/machines', label: 'Machines' },
    // Only show Teams if enabled
    ...(isFeatureEnabled('enableTeams') ? [{ path: '/teams', label: 'Teams' }] : []),
    { path: '/leaderboard', label: 'Leaderboard' },
    { path: '/statistics', label: 'Statistics' },
    // Admin always has access
    ...(state.auth.user?.role === 'admin' ? [{ path: '/admin', label: 'Admin' }] : [])
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
      scrolled 
        ? 'glass-card-dark backdrop-blur-xl border-b border-purple-500/30' 
        : 'bg-transparent'
    }`}>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link 
            to="/" 
            className="flex items-center space-x-3 group animate-slide-in-left"
          >
            <div className="relative">
              <Shield className="w-10 h-10 text-purple-400 group-hover:text-purple-300 transition-all duration-300 animate-pulse-glow" />
              <Sparkles className="w-4 h-4 text-yellow-400 absolute -top-1 -right-1 animate-bounce" />
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent neon-text">
                Kyubisec
              </span>
              <span className="text-xs text-purple-300/70 -mt-1">Hack The Future</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item, index) => (
              <Link
                key={item.path}
                to={item.path}
                className={`relative px-4 py-2 font-medium transition-all duration-300 animate-slide-in-up stagger-${index + 1} ${
                  isActive(item.path)
                    ? 'text-purple-300 neon-text'
                    : 'text-purple-200/80 hover:text-purple-300'
                }`}
              >
                {item.label}
                {isActive(item.path) && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-purple-400 to-pink-400 animate-scale-in" />
                )}
              </Link>
            ))}
          </nav>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {/* Notification Bell */}
            <NotificationBell />
            
            {/* Score Display */}
            <div className="hidden sm:flex items-center space-x-2 px-4 py-2 glass-card rounded-full animate-slide-in-right">
              <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse" />
              <span className="text-sm font-bold text-purple-200">{state.auth.user?.score || 0}</span>
              <span className="text-xs text-purple-300/70">pts</span>
            </div>
            
            {/* User Dropdown */}
            <div className="relative group">
              <button className="flex items-center space-x-3 px-4 py-2 glass-card rounded-full hover-lift group animate-slide-in-right">
                <img
                  src={state.auth.user?.avatar || `https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1`}
                  alt={state.auth.user?.username}
                  className="w-8 h-8 rounded-full object-cover border-2 border-purple-400/50"
                />
                <span className="text-sm font-medium text-purple-200 hidden sm:block">
                  {state.auth.user?.username}
                </span>
              </button>
              
              <div className="absolute right-0 mt-2 w-56 glass-card-dark rounded-xl shadow-2xl border border-purple-500/30 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform group-hover:scale-100 scale-95">
                <div className="p-4 border-b border-purple-500/20">
                  <div className="flex items-center space-x-3">
                    <img
                      src={state.auth.user?.avatar || `https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1`}
                      alt={state.auth.user?.username}
                      className="w-12 h-12 rounded-full object-cover border-2 border-purple-400/50"
                    />
                    <div>
                      <p className="font-semibold text-purple-200">{state.auth.user?.username}</p>
                      <p className="text-xs text-purple-300/70">{state.auth.user?.email}</p>
                      <p className="text-xs text-purple-400 font-medium">Rank #{state.auth.user?.rank}</p>
                    </div>
                  </div>
                </div>
                <div className="p-2">
                  <Link
                    to="/profile"
                    className="flex items-center space-x-3 w-full px-3 py-2 text-sm text-purple-200 hover:text-white hover:bg-purple-500/20 rounded-lg transition-all duration-200"
                  >
                    <Settings className="w-4 h-4" />
                    <span>Profile Settings</span>
                  </Link>
                  <button
                    onClick={logout}
                    className="flex items-center space-x-3 w-full px-3 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-lg transition-all duration-200"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Logout</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 glass-card rounded-lg hover-lift"
            >
              {isMenuOpen ? (
                <X className="w-5 h-5 text-purple-300" />
              ) : (
                <Menu className="w-5 h-5 text-purple-300" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 glass-card-dark rounded-xl p-4 animate-slide-in-up">
            <nav className="space-y-2">
              {navItems.map((item, index) => (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => setIsMenuOpen(false)}
                  className={`block px-4 py-3 rounded-lg font-medium transition-all duration-200 animate-slide-in-up stagger-${index + 1} ${
                    isActive(item.path)
                      ? 'bg-purple-500/20 text-purple-300 neon-border'
                      : 'text-purple-200/80 hover:text-purple-300 hover:bg-purple-500/10'
                  }`}
                >
                  {item.label}
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>

      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="particle animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 8}s`,
              animationDuration: `${8 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>
    </header>
  );
}