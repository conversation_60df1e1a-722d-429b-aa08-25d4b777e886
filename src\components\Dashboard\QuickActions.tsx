import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Play, Flag, Trophy, Shield, Zap, Sparkles, ArrowRight } from 'lucide-react';

export function QuickActions() {
  const navigate = useNavigate();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const actions = [
    {
      title: 'Start Machine',
      description: 'Launch a new VM instance',
      icon: Play,
      color: 'from-blue-500/20 to-blue-600/20 border-blue-500/30 hover:from-blue-500/30 hover:to-blue-600/30',
      iconColor: 'text-blue-400',
      onClick: () => navigate('/machines')
    },
    {
      title: 'Browse Challenges',
      description: 'Find new challenges to solve',
      icon: Flag,
      color: 'from-purple-500/20 to-purple-600/20 border-purple-500/30 hover:from-purple-500/30 hover:to-purple-600/30',
      iconColor: 'text-purple-400',
      onClick: () => navigate('/challenges')
    },
    {
      title: 'View Leaderboard',
      description: 'Check your ranking',
      icon: Trophy,
      color: 'from-pink-500/20 to-pink-600/20 border-pink-500/30 hover:from-pink-500/30 hover:to-pink-600/30',
      iconColor: 'text-pink-400',
      onClick: () => navigate('/leaderboard')
    },
    {
      title: 'VPN Setup',
      description: 'Download VPN config',
      icon: Shield,
      color: 'from-indigo-500/20 to-indigo-600/20 border-indigo-500/30 hover:from-indigo-500/30 hover:to-indigo-600/30',
      iconColor: 'text-indigo-400',
      onClick: () => navigate('/vpn')
    }
  ];

  return (
    <div className={`glass-card-dark backdrop-blur-xl rounded-2xl border border-purple-500/30 p-6 neon-border ${mounted ? 'animate-slide-in-right' : 'opacity-0'}`}>
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl">
          <Zap className="w-6 h-6 text-purple-400" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-white">Quick Actions</h3>
          <p className="text-sm text-purple-200/70">Jump into action</p>
        </div>
        <div className="ml-auto">
          <Sparkles className="w-5 h-5 text-yellow-400 animate-bounce" />
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-4">
        {actions.map((action, index) => (
          <button
            key={action.title}
            onClick={action.onClick}
            className={`group relative overflow-hidden p-4 rounded-xl border bg-gradient-to-r ${action.color} transition-all duration-300 hover-lift shimmer-effect ${
              mounted ? 'animate-slide-in-up' : 'opacity-0'
            }`}
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0 p-3 bg-gradient-to-r from-purple-900/50 to-purple-800/50 rounded-xl border border-purple-500/30 group-hover:scale-110 transition-transform duration-300">
                <action.icon className={`w-6 h-6 ${action.iconColor}`} />
              </div>
              <div className="flex-1 text-left">
                <h4 className="font-semibold text-white group-hover:text-purple-200 transition-colors">
                  {action.title}
                </h4>
                <p className="text-sm text-purple-200/70 group-hover:text-purple-200/90 transition-colors">
                  {action.description}
                </p>
              </div>
              <ArrowRight className="w-5 h-5 text-purple-400 group-hover:text-purple-300 group-hover:translate-x-1 transition-all duration-300" />
            </div>

            {/* Animated border */}
            <div className="absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-r from-purple-500/0 via-purple-500/50 to-purple-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            
            {/* Floating particles */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-xl">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-1 h-1 bg-purple-400/60 rounded-full animate-float opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                    left: `${20 + i * 30}%`,
                    top: `${20 + i * 20}%`,
                    animationDelay: `${i * 0.3}s`,
                    animationDuration: `${2 + i}s`
                  }}
                />
              ))}
            </div>
          </button>
        ))}
      </div>

      {/* Call to action */}
      <div className="mt-6 p-4 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-xl border border-purple-500/30">
        <div className="text-center">
          <p className="text-sm text-purple-200 mb-2">🚀 Ready to level up?</p>
          <button 
            onClick={() => navigate('/challenges')}
            className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            Start Hacking
          </button>
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute top-4 right-4 w-16 h-16 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full morph-shape animate-float blur-lg" />
    </div>
  );
}