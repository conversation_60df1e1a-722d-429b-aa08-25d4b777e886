import { api } from './api';

export interface DashboardStats {
  totalUsers: number;
  totalChallenges: number;
  activeVMs: number;
  totalSolves: number;
  onlineUsers: number;
  recentActivity: ActivityItem[];
  platformStats: {
    totalTeams: number;
    totalSubmissions: number;
    averageScore: number;
    topPerformers: number;
  };
}

export interface ActivityItem {
  id: string;
  type: 'challenge_solved' | 'vm_started' | 'rank_up' | 'first_blood' | 'team_joined';
  title: string;
  description: string;
  timestamp: string;
  points?: number;
  userId: string;
  username: string;
}

export interface UserActivityStats {
  recentActivity: ActivityItem[];
  totalActivities: number;
}

export interface OnlineStatus {
  online: number;
  solving: number;
  elite: number;
}

class DashboardStatsService {
  async getPlatformStats(): Promise<DashboardStats> {
    const response = await api.get<DashboardStats>('/dashboard/platform-stats');
    return response.data;
  }

  async getUserActivity(limit: number = 20): Promise<UserActivityStats> {
    const response = await api.get<UserActivityStats>(`/dashboard/user-activity?limit=${limit}`);
    return response.data;
  }

  async getOnlineStatus(): Promise<OnlineStatus> {
    try {
      const stats = await this.getPlatformStats();
      
      // Calculate online status based on platform stats
      const online = stats.onlineUsers;
      const solving = Math.floor(online * 0.3); // Estimate 30% are actively solving
      const elite = Math.floor(stats.platformStats.topPerformers * 0.1); // Estimate 10% of top performers are elite
      
      return {
        online,
        solving,
        elite
      };
    } catch (error) {
      console.error('Failed to get online status:', error);
      // Return fallback data
      return {
        online: 1247,
        solving: 89,
        elite: 156
      };
    }
  }

  // Helper method to format activity items for display
  formatActivityForDisplay(activity: ActivityItem): ActivityItem {
    return {
      ...activity,
      timestamp: this.formatRelativeTime(activity.timestamp)
    };
  }

  private formatRelativeTime(timestamp: string): string {
    const now = new Date();
    const activityTime = new Date(timestamp);
    const diffMs = now.getTime() - activityTime.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return activityTime.toLocaleDateString();
  }

  // Get activity icon based on type
  getActivityIcon(type: ActivityItem['type']): string {
    switch (type) {
      case 'challenge_solved':
        return 'flag';
      case 'vm_started':
        return 'play';
      case 'rank_up':
        return 'trophy';
      case 'first_blood':
        return 'award';
      case 'team_joined':
        return 'users';
      default:
        return 'activity';
    }
  }

  // Get activity color based on type
  getActivityColor(type: ActivityItem['type']): string {
    switch (type) {
      case 'challenge_solved':
        return 'from-purple-500/20 to-purple-600/20 border-purple-500/30';
      case 'vm_started':
        return 'from-blue-500/20 to-blue-600/20 border-blue-500/30';
      case 'rank_up':
        return 'from-pink-500/20 to-pink-600/20 border-pink-500/30';
      case 'first_blood':
        return 'from-orange-500/20 to-orange-600/20 border-orange-500/30';
      case 'team_joined':
        return 'from-green-500/20 to-green-600/20 border-green-500/30';
      default:
        return 'from-gray-500/20 to-gray-600/20 border-gray-500/30';
    }
  }
}

export const dashboardStatsService = new DashboardStatsService();