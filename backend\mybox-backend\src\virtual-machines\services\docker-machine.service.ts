import { Injectable, Logger, BadRequestException, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import Docker = require('dockerode');
import * as crypto from 'crypto';
import { MachineTemplate } from '../../schemas/machine-template.schema';
import { MachineInstance } from '../../schemas/machine-instance.schema';
import { MachineOperation } from '../../schemas/machine-operation.schema';
import { MachineVPNIntegrationService } from '../../vpn/services/machine-vpn-integration.service';
import { VPNConfigService } from '../../vpn/services/vpn-config.service';

export interface ResourceStats {
  cpuUsage: number;
  memoryUsage: number;
  networkRx: number;
  networkTx: number;
  uptime: number;
}

export interface NetworkConfig {
  networkName: string;
  subnet: string;
  ipAddress: string;
  exposedPorts: { internal: number; external: number; protocol: string }[];
}

@Injectable()
export class DockerMachineService {
  private readonly logger = new Logger(DockerMachineService.name);
  private readonly docker: Docker;
  private buildLogsCache: Map<string, string[]> = new Map(); // Store build logs in memory
    constructor(
    @InjectModel(MachineTemplate.name) private machineTemplateModel: Model<MachineTemplate>,
    @InjectModel(MachineInstance.name) private machineInstanceModel: Model<MachineInstance>,
    @InjectModel(MachineOperation.name) private machineOperationModel: Model<MachineOperation>,
    private configService: ConfigService,
    private machineVPNIntegrationService: MachineVPNIntegrationService,
    private vpnConfigService: VPNConfigService,
  ) {
    // Configure Docker connection based on platform
    const isWindows = process.platform === 'win32';    if (isWindows) {
      // For Windows, prioritize TCP connection (more reliable when enabled)
      const tcpHost = this.configService.get('127.0.0.1'); // Use IPv4 explicitly
      const tcpPort = this.configService.get('2375');
      
      // Debug logging
      this.logger.log(`🐳 Docker config - Host: ${tcpHost}, Port: ${tcpPort}, Type: ${typeof tcpPort}`);
        // Use TCP connection for Windows (more reliable)
      const dockerConfig = {
        host: tcpHost,
        port: parseInt(tcpPort, 10),
        protocol: 'http' as const,
      };
      
      this.logger.log(`🐳 Docker configuration object:`, JSON.stringify(dockerConfig));
      
      this.docker = new Docker(dockerConfig);
      this.logger.log(`Docker configured for Windows using TCP at ${tcpHost}:${tcpPort}`);
    } else {
      // Unix/Linux socket path
      this.docker = new Docker({
        socketPath: this.configService.get('DOCKER_SOCKET_PATH', '/var/run/docker.sock'),
      });
      this.logger.log('Docker configured for Unix/Linux using socket path');    }
  }
  /**
   * Test Docker connection and log status
   */
  async testDockerConnection(): Promise<boolean> {
    try {
      await this.docker.ping();
      this.logger.log('✅ Docker connection successful');
      return true;
    } catch (error) {
      this.logger.error('❌ Docker connection failed:', error.message);
      this.logger.error('Make sure Docker Desktop is running and accessible');
      
      if (process.platform === 'win32') {
        this.logger.error('Windows Docker troubleshooting:');
        this.logger.error('1. Ensure Docker Desktop is running');
        this.logger.error('2. Enable "Expose daemon on tcp://localhost:2375 without TLS" in Docker Desktop > Settings > General');
        this.logger.error('3. Restart Docker Desktop after enabling TCP access');
        this.logger.error('4. Check if port 2375 is accessible: curl http://localhost:2375/version');
        this.logger.error('5. Alternative: Try Docker Desktop WSL 2 backend if available');
      }
      
      return false;
    }
  }

  /**
   * Spawn a new machine instance from a template
   */  async spawnMachine(
    templateId: string, 
    userId: string, 
    teamId?: string,
    options: { extendedRuntime?: number } = {}
  ): Promise<MachineInstance> {
    this.logger.log(`Spawning machine ${templateId} for user ${userId}`);

    // Test Docker connection first
    const dockerConnected = await this.testDockerConnection();
    if (!dockerConnected) {
      throw new BadRequestException('Docker daemon is not accessible. Please ensure Docker is running.');
    }

    // Check if user already has an active instance of this machine
    const existingInstance = await this.machineInstanceModel.findOne({
      templateId,
      ownerId: userId,
      status: { $in: ['starting', 'running'] }
    });

    if (existingInstance) {
      throw new ConflictException('User already has an active instance of this machine');
    }

    // Get machine template
    const template = await this.machineTemplateModel.findById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    if (!template.isActive) {
      throw new BadRequestException('Machine template is not active');
    }

    // Check current instance count against max
    const activeInstances = await this.machineInstanceModel.countDocuments({
      templateId,
      status: { $in: ['starting', 'running'] }
    });

    if (activeInstances >= template.maxInstances) {
      throw new ConflictException('Maximum number of instances reached for this machine');
    }

    // Generate unique container name and get VPN-integrated network
    const containerName = this.generateContainerName(template.name, userId);

    // Use VPN-integrated network instead of creating separate network
    let networkConfig: NetworkConfig;
    try {
      // Try to get user's VPN configuration
      const vpnConfig = await this.vpnConfigService.getVPNConfig(userId);

      if (vpnConfig) {
        // User has VPN, use VPN-integrated network
        const subnetBase = vpnConfig.subnet.split('/')[0].split('.').slice(0, 3).join('.');
        const vpnNetworkName = `mybox-vpn-${userId.substring(0, 8)}`;

        // Create VPN-integrated Docker network if it doesn't exist
        await this.createVPNIntegratedNetwork(vpnNetworkName, vpnConfig.subnet, vpnConfig.ipAddress);

        networkConfig = {
          networkName: vpnNetworkName,
          subnet: vpnConfig.subnet,
          ipAddress: `${subnetBase}.10`, // Start machines from .10
          exposedPorts: []
        };
        this.logger.log(`Using VPN-integrated network for user ${userId}: ${vpnConfig.subnet}`);
      } else {
        throw new Error('No VPN configuration found');
      }
    } catch (error) {
      // Fallback to regular network if VPN integration fails
      this.logger.warn(`VPN integration failed for user ${userId}: ${error.message}. Using fallback network.`);
      networkConfig = await this.createUserNetwork(userId);
    }
      // Calculate expiration time
    const maxUptime = options.extendedRuntime || 240; // 4 hours default
    const expiresAt = new Date(Date.now() + maxUptime * 60 * 1000);    // Create instance record first
    const currentTime = new Date();
    const instance = new this.machineInstanceModel({
      templateId,
      ownerId: userId,
      teamId,
      isTeamShared: !!teamId,
      containerName,
      instanceIP: networkConfig.ipAddress,
      subnet: networkConfig.subnet,
      exposedPorts: networkConfig.exposedPorts,
      status: 'starting',
      startedAt: currentTime,
      lastActivity: currentTime,
      expiresAt,
      maxUptime,
      accessToken: this.generateAccessToken(),
      isFirstTeamSpawn: teamId ? await this.isFirstTeamSpawn(templateId, teamId) : false,
    });

    await instance.save();

    try {
      // Start Docker container
      const containerId = await this.startDockerContainer(template, instance, networkConfig);      // Update instance with container ID
      instance.containerId = containerId;
      instance.status = 'running';
      await instance.save();

      // Schedule auto-shutdown
      await this.scheduleAutoShutdown((instance as any)._id.toString(), expiresAt, userId);

      // Get VPN access information for the machine
      try {
        const vpnInfo = await this.machineVPNIntegrationService.getMachineAccessInfo(
          (instance as any)._id.toString(),
          userId
        );

        if (vpnInfo) {
          this.logger.log(`VPN access configured for machine ${template.name}: VPN accessible`);
        } else {
          this.logger.log(`VPN access for machine ${template.name}: Requires VPN setup`);
        }
      } catch (error) {
        this.logger.warn(`Failed to configure VPN access for machine: ${error.message}`);
      }

      this.logger.log(`Successfully spawned machine instance ${(instance as any)._id}`);
      return instance;

    } catch (error) {
      // Cleanup on failure
      instance.status = 'error';
      await instance.save();
      
      this.logger.error(`Failed to spawn machine: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to spawn machine: ${error.message}`);
    }
  }

  /**
   * Terminate a machine instance
   */
  async terminateMachine(instanceId: string, userId: string): Promise<void> {
    this.logger.log(`Terminating machine instance ${instanceId}`);

    const instance = await this.machineInstanceModel.findOne({
      _id: instanceId,
      ownerId: userId,
    });

    if (!instance) {
      throw new NotFoundException('Machine instance not found or unauthorized');
    }

    if (instance.status === 'stopped') {
      return;
    }

    try {
      instance.status = 'stopping';
      await instance.save();

      // Stop and remove container
      if (instance.containerId) {
        await this.stopDockerContainer(instance.containerId);
      }

      // Cleanup network if needed
      await this.cleanupUserNetwork(userId, instance.subnet);

      // Update instance status
      instance.status = 'stopped';
      instance.stoppedAt = new Date();
      await instance.save();

      // Cancel any scheduled operations
      await this.cancelScheduledOperations(instanceId);

      this.logger.log(`Successfully terminated machine instance ${instanceId}`);

    } catch (error) {
      instance.status = 'error';
      await instance.save();
      
      this.logger.error(`Failed to terminate machine: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to terminate machine: ${error.message}`);
    }
  }

  /**
   * Restart a machine instance
   */
  async restartMachine(instanceId: string, userId: string): Promise<void> {
    this.logger.log(`Restarting machine instance ${instanceId}`);

    const instance = await this.machineInstanceModel.findOne({
      _id: instanceId,
      ownerId: userId,
    });

    if (!instance) {
      throw new NotFoundException('Machine instance not found or unauthorized');
    }

    try {
      instance.status = 'stopping';
      await instance.save();

      // Restart container
      if (instance.containerId) {
        const container = this.docker.getContainer(instance.containerId);
        await container.restart();
      }

      instance.status = 'running';
      instance.lastActivity = new Date();
      await instance.save();

      this.logger.log(`Successfully restarted machine instance ${instanceId}`);

    } catch (error) {
      instance.status = 'error';
      await instance.save();
      
      this.logger.error(`Failed to restart machine: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to restart machine: ${error.message}`);
    }
  }

  /**
   * Extend machine runtime
   */
  async extendMachine(instanceId: string, userId: string, additionalMinutes: number): Promise<void> {
    this.logger.log(`Extending machine instance ${instanceId} by ${additionalMinutes} minutes`);

    const instance = await this.machineInstanceModel.findOne({
      _id: instanceId,
      ownerId: userId,
      status: 'running',
    });

    if (!instance) {
      throw new NotFoundException('Active machine instance not found or unauthorized');
    }

    const maxExtension = this.configService.get('MAX_EXTENSION_MINUTES', 480); // 8 hours max
    if (additionalMinutes > maxExtension) {
      throw new BadRequestException(`Cannot extend beyond ${maxExtension} minutes`);
    }

    // Update expiration time
    const newExpiresAt = new Date(instance.expiresAt.getTime() + additionalMinutes * 60 * 1000);
    instance.expiresAt = newExpiresAt;
    instance.maxUptime += additionalMinutes;
    await instance.save();

    // Reschedule auto-shutdown
    await this.rescheduleAutoShutdown(instanceId, newExpiresAt);

    this.logger.log(`Successfully extended machine instance ${instanceId}`);
  }

  /**
   * Get machine instance statistics
   */
  async getInstanceStats(instanceId: string, userId: string): Promise<ResourceStats> {
    const instance = await this.machineInstanceModel.findOne({
      _id: instanceId,
      ownerId: userId,
    });

    if (!instance || !instance.containerId) {
      throw new NotFoundException('Machine instance not found or not running');
    }

    try {
      const container = this.docker.getContainer(instance.containerId);
      const stats = await container.stats({ stream: false });

      // Calculate CPU usage percentage
      const cpuUsage = this.calculateCpuUsage(stats);
      
      // Calculate memory usage
      const memoryUsage = (stats.memory_stats?.usage || 0) / (1024 * 1024); // MB
      
      // Calculate network usage
      const networks = stats.networks || {};
      const networkRx = Object.values(networks).reduce((sum: number, net: any) => sum + (net.rx_bytes || 0), 0);
      const networkTx = Object.values(networks).reduce((sum: number, net: any) => sum + (net.tx_bytes || 0), 0);

      // Calculate uptime
      const uptime = instance.startedAt ? Math.floor((Date.now() - instance.startedAt.getTime()) / 1000) : 0;

      // Update instance with current stats
      instance.cpuUsage = cpuUsage;
      instance.memoryUsage = memoryUsage;
      instance.networkUsage = (networkRx + networkTx) / (1024 * 1024); // MB
      instance.lastActivity = new Date();
      await instance.save();

      return {
        cpuUsage,
        memoryUsage,
        networkRx: networkRx / (1024 * 1024), // MB
        networkTx: networkTx / (1024 * 1024), // MB
        uptime,
      };

    } catch (error) {
      this.logger.error(`Failed to get instance stats: ${error.message}`);
      throw new BadRequestException('Failed to retrieve instance statistics');
    }
  }
  /**
   * Create VPN-integrated network for user
   */
  private async createVPNIntegratedNetwork(networkName: string, subnet: string, gatewayIP: string): Promise<void> {
    try {
      // Check if network already exists
      const networks = await this.docker.listNetworks();
      const existingNetwork = networks.find(net => net.Name === networkName);

      if (existingNetwork) {
        this.logger.log(`VPN-integrated network ${networkName} already exists`);
        return;
      }

      // Calculate proper gateway IP (first IP in subnet)
      const subnetBase = subnet.split('/')[0].split('.').slice(0, 3).join('.');
      const dockerGateway = `${subnetBase}.1`; // Docker gateway is always .1

      // Create Docker network with VPN subnet
      await this.docker.createNetwork({
        Name: networkName,
        Driver: 'bridge',
        IPAM: {
          Config: [{
            Subnet: subnet, // Use the full VPN subnet
            Gateway: dockerGateway, // Use .1 as Docker gateway
            // Don't specify IPRange - let Docker manage the full subnet
          }],
        },
        Options: {
          'com.docker.network.bridge.name': `br-${networkName}`,
          'com.docker.network.driver.mtu': '1420', // Match WireGuard MTU
          'com.docker.network.bridge.enable_icc': 'true', // Allow inter-container communication
          'com.docker.network.bridge.enable_ip_masquerade': 'true',
        },
        Labels: {
          'mybox.network.type': 'vpn-integrated',
          'mybox.vpn.subnet': subnet,
          'mybox.vpn.user.ip': gatewayIP, // Store user's VPN IP for reference
          'mybox.created.at': new Date().toISOString()
        }
      });

      this.logger.log(`Created VPN-integrated network ${networkName} with subnet ${subnet}`);
    } catch (error) {
      this.logger.error(`Failed to create VPN-integrated network ${networkName}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create isolated network for user
   */
  private async createUserNetwork(userId: string): Promise<NetworkConfig> {
    const networkName = `mybox-user-${userId.substring(0, 8)}`;
    
    try {
      // Check if network already exists
      const networks = await this.docker.listNetworks();
      const existingNetwork = networks.find(net => net.Name === networkName);
      
      if (existingNetwork) {
        // Reuse existing network
        const subnet = existingNetwork.IPAM?.Config?.[0]?.Subnet || '*********/24';
        const ipAddress = this.generateIPFromSubnet(subnet);
        
        this.logger.log(`Reusing existing network ${networkName} with subnet ${subnet}`);
        
        return {
          networkName,
          subnet,
          ipAddress,
          exposedPorts: [],
        };
      }

      // Find available subnet
      const subnet = await this.findAvailableSubnet(networks);
      const ipAddress = this.generateIPFromSubnet(subnet);

      // Create new network
      await this.docker.createNetwork({
        Name: networkName,
        Driver: 'bridge',
        IPAM: {
          Config: [{
            Subnet: subnet,
          }],
        },
        Options: {
          'com.docker.network.bridge.enable_icc': 'false',
          'com.docker.network.bridge.enable_ip_masquerade': 'true',
        },
      });
      
      this.logger.log(`Created network ${networkName} with subnet ${subnet}`);

      return {
        networkName,
        subnet,
        ipAddress,
        exposedPorts: [],
      };

    } catch (error) {
      this.logger.error(`Failed to create user network: ${error.message}`);
      throw new Error(`Network creation failed: ${error.message}`);
    }
  }

  /**
   * Find an available subnet in the 10.10.x.0/24 range
   */
  private async findAvailableSubnet(existingNetworks: any[]): Promise<string> {
    const usedSubnets = new Set<string>();
    
    // Collect all used subnets
    existingNetworks.forEach(network => {
      if (network.IPAM?.Config?.[0]?.Subnet) {
        usedSubnets.add(network.IPAM.Config[0].Subnet);
      }
    });

    // Find first available subnet in *********/24 to ***********/24 range
    for (let i = 1; i <= 254; i++) {
      const subnet = `10.10.${i}.0/24`;
      if (!usedSubnets.has(subnet)) {
        return subnet;
      }
    }

    // Fallback - this shouldn't happen unless there are 254+ networks
    throw new Error('No available subnets in 10.10.x.0/24 range');
  }
  /**
   * Generate a static IP address from subnet
   */
  private generateIPFromSubnet(subnet: string): string {
    const parts = subnet.split('.');
    const thirdOctet = parts[2];
    return `10.10.${thirdOctet}.10`;
  }

  /**
   * Clean up user network if no longer needed
   */
  private async cleanupUserNetwork(userId: string, subnet?: string): Promise<void> {
    const networkName = `mybox-user-${userId.substring(0, 8)}`;
    
    try {
      // Check if there are any other active instances for this user
      const activeInstances = await this.machineInstanceModel.find({
        ownerId: userId,
        status: { $in: ['starting', 'running'] }
      });

      // If no active instances, remove the network
      if (activeInstances.length === 0) {
        const networks = await this.docker.listNetworks();
        const userNetwork = networks.find(net => net.Name === networkName);
        
        if (userNetwork) {
          await this.docker.getNetwork(userNetwork.Id).remove();
          this.logger.log(`Cleaned up network ${networkName} for user ${userId}`);
        }
      } else {
        this.logger.log(`Keeping network ${networkName} - user has ${activeInstances.length} active instances`);
      }
    } catch (error) {
      this.logger.warn(`Failed to cleanup network ${networkName}: ${error.message}`);
      // Don't throw error as this is cleanup - we don't want to fail the main operation
    }
  }

  /**
   * Start Docker container with proper configuration
   */
  private async startDockerContainer(
    template: MachineTemplate, 
    instance: MachineInstance, 
    networkConfig: NetworkConfig
  ): Promise<string> {
    const portBindings: any = {};
    const exposedPorts: any = {};

    // Configure port mappings
    for (const port of template.exposedPorts) {
      const externalPort = await this.findAvailablePort();
      const portKey = `${port}/tcp`;
      
      exposedPorts[portKey] = {};
      portBindings[portKey] = [{ HostPort: externalPort.toString() }];
      
      networkConfig.exposedPorts.push({
        internal: port,
        external: externalPort,
        protocol: 'tcp',
      });
    }

    // Configure resource limits
    const hostConfig: any = {
      Memory: (template.requiredRAM || 1024) * 1024 * 1024, // Convert MB to bytes
      CpuShares: (template.requiredCPU || 1) * 1024, // CPU shares
      PortBindings: portBindings,
      RestartPolicy: { Name: 'unless-stopped' },
      SecurityOpt: ['no-new-privileges:true'], // Security hardening
      ReadonlyRootfs: false, // Allow writes for now
      Tmpfs: {
        '/tmp': 'rw,noexec,nosuid,size=100m',
      },
    };

    // Container configuration
    const containerConfig = {
      Image: template.dockerImage,
      name: instance.containerName,
      Hostname: instance.containerName,
      ExposedPorts: exposedPorts,
      HostConfig: hostConfig,
      Env: [
        `MACHINE_ID=${(instance as any)._id}`,
        `USER_ID=${instance.ownerId}`,
        `ACCESS_TOKEN=${instance.accessToken}`,
        `TEAM_ID=${instance.teamId || ''}`,
      ],
      Labels: {
        'mybox.instance.id': (instance as any)._id.toString(),
        'mybox.user.id': instance.ownerId.toString(),
        'mybox.template.id': (template as any)._id.toString(),
        'mybox.created': new Date().toISOString(),
      },
    };

    try {
      // Check if Docker image is specified
      if (!template.dockerImage) {
        throw new Error(
          `Template has no Docker image specified. Please build the Docker image first:\n` +
          `1. Go to Admin Panel → Machines\n` +
          `2. Find template "${template.name}"\n` +
          `3. Click "Build Status" and build the image\n` +
          `4. Then try spawning the machine again`
        );
      }
      
      await this.ensureImageAvailable(template.dockerImage);

      // Create container
      const container = await this.docker.createContainer(containerConfig);
      
      // Connect to user network
      const network = this.docker.getNetwork(networkConfig.networkName);
      await network.connect({
        Container: container.id,
        EndpointConfig: {
          IPAMConfig: {
            IPv4Address: networkConfig.ipAddress,
          },
        },
      });

      // Start container
      await container.start();

      this.logger.log(`Started container ${container.id} for instance ${(instance as any)._id}`);
      return container.id;

    } catch (error) {
      this.logger.error(`Failed to start container: ${error.message}`);
      throw error;
    }
  }

  /**
   * Stop and remove Docker container
   */
  private async stopDockerContainer(containerId: string): Promise<void> {
    try {
      const container = this.docker.getContainer(containerId);
      
      // Get container info to check if it's running
      const containerInfo = await container.inspect();
      
      if (containerInfo.State.Running) {
        // Stop container gracefully
        await container.stop({ t: 10 }); // 10 second timeout
      }

      // Remove container
      await container.remove({ force: true });

      this.logger.log(`Stopped and removed container ${containerId}`);

    } catch (error) {
      // Container might already be stopped/removed
      this.logger.warn(`Error stopping container ${containerId}: ${error.message}`);
    }
  }

  /**
   * Ensure Docker image is available locally
   */
  private async ensureImageAvailable(imageName: string): Promise<void> {
    try {
      await this.docker.getImage(imageName).inspect();
    } catch (error) {
      this.logger.log(`Pulling image ${imageName}...`);
      
      const stream = await this.docker.pull(imageName);
      await new Promise((resolve, reject) => {
        this.docker.modem.followProgress(stream, (err, res) => err ? reject(err) : resolve(res));
      });
      
      this.logger.log(`Successfully pulled image ${imageName}`);
    }
  }

  /**
   * Find an available port for external mapping
   */
  private async findAvailablePort(): Promise<number> {
    const net = require('net');
    const minPort = this.configService.get('MIN_EXTERNAL_PORT', 30000);
    const maxPort = this.configService.get('MAX_EXTERNAL_PORT', 65000);
    
    for (let port = minPort; port <= maxPort; port++) {
      if (await this.isPortAvailable(port)) {
        return port;
      }
    }
    
    throw new Error('No available ports found');
  }

  /**
   * Check if a port is available
   */
  private isPortAvailable(port: number): Promise<boolean> {
    const net = require('net');
    
    return new Promise((resolve) => {
      const server = net.createServer();
      server.listen(port, () => {
        server.close(() => resolve(true));
      });
      server.on('error', () => resolve(false));
    });
  }

  /**
   * Generate unique container name
   */
  private generateContainerName(machineName: string, userId: string): string {
    const timestamp = Date.now();
    const userPrefix = userId.substring(0, 8);
    const machinePrefix = machineName.toLowerCase().replace(/[^a-z0-9]/g, '');
    
    return `mybox-${machinePrefix}-${userPrefix}-${timestamp}`;
  }

  /**
   * Generate secure access token
   */
  private generateAccessToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Check if this is the first team spawn for a machine
   */
  private async isFirstTeamSpawn(templateId: string, teamId: string): Promise<boolean> {
    const existingTeamInstance = await this.machineInstanceModel.findOne({
      templateId,
      teamId,
      status: { $ne: 'error' }
    });
    
    return !existingTeamInstance;
  }

  /**
   * Calculate CPU usage percentage from Docker stats
   */
  private calculateCpuUsage(stats: any): number {
    const cpuStats = stats.cpu_stats;
    const precpuStats = stats.precpu_stats;
    
    if (!cpuStats || !precpuStats) return 0;

    const cpuDelta = cpuStats.cpu_usage.total_usage - precpuStats.cpu_usage.total_usage;
    const systemDelta = cpuStats.system_cpu_usage - precpuStats.system_cpu_usage;
    const numberCpus = cpuStats.online_cpus || cpuStats.cpu_usage.percpu_usage?.length || 1;

    if (systemDelta > 0 && cpuDelta > 0) {
      return (cpuDelta / systemDelta) * numberCpus * 100;
    }
    
    return 0;
  }
  /**
   * Schedule auto-shutdown operation
   */
  private async scheduleAutoShutdown(instanceId: string, expiresAt: Date, userId: string): Promise<void> {
    // Generate a unique job ID for tracking
    const jobId = `auto-shutdown-${instanceId}-${Date.now()}`;
    
    const operation = new this.machineOperationModel({
      instanceId,
      operationType: 'auto_shutdown',
      scheduledBy: userId,
      scheduledAt: new Date(),
      executeAt: expiresAt,
      status: 'pending',
      cancellable: true,
      jobId,
    });

    await operation.save();
    this.logger.log(`Scheduled auto-shutdown for instance ${instanceId} at ${expiresAt} (Job ID: ${jobId})`);
  }
  /**
   * Reschedule auto-shutdown operation
   */
  private async rescheduleAutoShutdown(instanceId: string, newExpiresAt: Date): Promise<void> {
    await this.machineOperationModel.updateOne(
      {
        instanceId,
        operationType: 'auto_shutdown',
        status: 'pending',
      },
      {
        executeAt: newExpiresAt,
        scheduledAt: new Date(),
      }
    );
  }

  /**
   * Cancel scheduled operations for an instance
   */
  private async cancelScheduledOperations(instanceId: string): Promise<void> {
    await this.machineOperationModel.updateMany(
      {
        instanceId,
        status: 'pending',
      },
      {
        status: 'cancelled',
      }
    );  }

  /**
   * Get all active instances for monitoring/cleanup
   */
  async getActiveInstances(): Promise<MachineInstance[]> {
    return this.machineInstanceModel.find({
      status: { $in: ['starting', 'running'] }
    }).populate('templateId');
  }

  /**
   * Build Docker image from extracted template
   */
  async buildFromTemplate(
    templatePath: string,
    imageName: string
  ): Promise<{ imageId: string; buildLogs: string[] }> {
    const buildLogs: string[] = [];

    this.logger.log(`Building Docker image: ${imageName} from ${templatePath}`);

    try {
      // Check if Dockerfile exists
      const fs = require('fs');
      const path = require('path');

      // Debug: Log the paths and directory contents
      this.logger.log(`Template path: ${templatePath}`);

      if (!fs.existsSync(templatePath)) {
        this.logger.error(`Template path does not exist: ${templatePath}`);
        throw new BadRequestException(`Template path does not exist: ${templatePath}`);
      }

      const files = fs.readdirSync(templatePath);
      this.logger.log(`Files in template directory: ${files.join(', ')}`);

      // Look for Dockerfile in multiple locations
      let dockerfilePath = path.join(templatePath, 'Dockerfile');
      let actualTemplatePath = templatePath;

      if (!fs.existsSync(dockerfilePath)) {
        // If Dockerfile not found in root, check if there's a single subdirectory
        const subdirs = files.filter(file => {
          const fullPath = path.join(templatePath, file);
          return fs.statSync(fullPath).isDirectory();
        });

        if (subdirs.length === 1) {
          // Check if Dockerfile exists in the subdirectory
          const subDirPath = path.join(templatePath, subdirs[0]);
          const subDockerfilePath = path.join(subDirPath, 'Dockerfile');

          this.logger.log(`Checking subdirectory: ${subDirPath}`);
          const subFiles = fs.readdirSync(subDirPath);
          this.logger.log(`Files in subdirectory: ${subFiles.join(', ')}`);

          if (fs.existsSync(subDockerfilePath)) {
            dockerfilePath = subDockerfilePath;
            actualTemplatePath = subDirPath;
            this.logger.log(`Found Dockerfile in subdirectory: ${dockerfilePath}`);
          }
        }
      }

      this.logger.log(`Looking for Dockerfile at: ${dockerfilePath}`);

      if (!fs.existsSync(dockerfilePath)) {
        throw new BadRequestException(`Dockerfile not found. Searched in:\n- ${path.join(templatePath, 'Dockerfile')}\n- Subdirectories: ${files.join(', ')}`);
      }

      // Update templatePath to the actual path containing Dockerfile
      templatePath = actualTemplatePath;

      // Build the image
      const stream = await this.docker.buildImage({
        context: templatePath,
        src: ['.']
      }, {
        t: imageName,
        dockerfile: 'Dockerfile',
        rm: true, // Remove intermediate containers
        forcerm: true, // Always remove intermediate containers
        pull: false // Don't pull base images unless necessary
      });

      // Collect build logs with timeout
      await new Promise((resolve, reject) => {
        let buildTimeout: NodeJS.Timeout;

        // Set a 30-minute timeout for the build (configurable)
        const timeoutMinutes = this.configService.get('DOCKER_BUILD_TIMEOUT_MINUTES', 30);
        buildTimeout = setTimeout(() => {
          this.logger.error(`Build timeout for ${imageName} after ${timeoutMinutes} minutes`);
          buildLogs.push(`ERROR: Build timed out after ${timeoutMinutes} minutes`);
          reject(new Error(`Build timed out after ${timeoutMinutes} minutes`));
        }, timeoutMinutes * 60 * 1000);

        this.docker.modem.followProgress(stream, (err, res) => {
          clearTimeout(buildTimeout);

          if (err) {
            this.logger.error(`Build failed for ${imageName}: ${err.message}`);
            buildLogs.push(`ERROR: Build failed - ${err.message}`);
            reject(err);
          } else {
            this.logger.log(`Build process completed for ${imageName}`);
            buildLogs.push('Build process completed');
            resolve(res);
          }
        }, (event) => {
          if (event.stream) {
            const logLine = event.stream.trim();
            if (logLine) {
              buildLogs.push(logLine);
              this.logger.debug(`Build: ${logLine}`);
            }
          }
          if (event.error) {
            const errorLine = `ERROR: ${event.error}`;
            buildLogs.push(errorLine);
            this.logger.error(`Build error: ${event.error}`);
          }
          if (event.errorDetail) {
            const errorDetail = `ERROR DETAIL: ${JSON.stringify(event.errorDetail)}`;
            buildLogs.push(errorDetail);
            this.logger.error(`Build error detail: ${JSON.stringify(event.errorDetail)}`);
          }
        });
      });

      // Get image info
      const image = this.docker.getImage(imageName);
      const imageInfo = await image.inspect();

      this.logger.log(`Successfully built image: ${imageName} (${imageInfo.Id})`);

      // Store build logs in cache
      const templateSlug = imageName.replace('rakcha/', '').replace(':latest', '');
      this.buildLogsCache.set(templateSlug, [...buildLogs]);

      return {
        imageId: imageInfo.Id,
        buildLogs
      };
    } catch (error) {
      this.logger.error(`Failed to build image ${imageName}: ${error.message}`);
      buildLogs.push(`FATAL ERROR: ${error.message}`);

      // Store failed build logs in cache
      const templateSlug = imageName.replace('rakcha/', '').replace(':latest', '');
      this.buildLogsCache.set(templateSlug, [...buildLogs]);

      throw new BadRequestException(`Docker build failed: ${error.message}`);
    }
  }

  /**
   * Get build logs for a template (stored in cache)
   */
  async getBuildLogs(templateSlug: string): Promise<string[]> {
    try {
      // First, check if we have cached build logs
      if (this.buildLogsCache.has(templateSlug)) {
        const cachedLogs = this.buildLogsCache.get(templateSlug);
        if (cachedLogs) {
          this.logger.log(`Retrieved ${cachedLogs.length} cached build logs for ${templateSlug}`);
          return cachedLogs;
        }
      }

      // If no cached logs, try to get from Docker image history
      const imageName = `rakcha/${templateSlug}:latest`;
      try {
        const image = this.docker.getImage(imageName);
        const history = await image.history();
        const historyLogs = history.map((h: any) => h.CreatedBy || 'Unknown command').slice(0, 20);

        this.logger.log(`Retrieved ${historyLogs.length} Docker history logs for ${templateSlug}`);
        return historyLogs;
      } catch (error) {
        this.logger.warn(`Could not retrieve build logs for ${imageName}: ${error.message}`);
        return [`No build logs available for ${templateSlug}. Try building the image first.`];
      }
    } catch (error) {
      this.logger.error(`Failed to get build logs: ${error.message}`);
      return [`Error retrieving build logs: ${error.message}`];
    }
  }

  /**
   * Remove Docker image
   */
  async removeImage(templateSlug: string): Promise<void> {
    const imageName = `rakcha/${templateSlug}:latest`;

    try {
      const image = this.docker.getImage(imageName);
      await image.remove({ force: true });
      this.logger.log(`Removed image: ${imageName}`);
    } catch (error) {
      if (error.statusCode === 404) {
        this.logger.warn(`Image ${imageName} not found, already removed`);
      } else {
        this.logger.error(`Failed to remove image ${imageName}: ${error.message}`);
        throw new BadRequestException(`Failed to remove image: ${error.message}`);
      }
    }
  }

  /**
   * Check if image exists
   */
  async imageExists(templateSlug: string): Promise<boolean> {
    const imageName = `rakcha/${templateSlug}:latest`;

    try {
      const image = this.docker.getImage(imageName);
      await image.inspect();
      return true;
    } catch (error) {
      if (error.statusCode === 404) {
        return false;
      }
      throw error;
    }
  }

  /**
   * List all built template images
   */
  async listTemplateImages(): Promise<Array<{ name: string; id: string; size: number; created: Date }>> {
    try {
      const images = await this.docker.listImages();

      return images
        .filter(img => img.RepoTags && img.RepoTags.some(tag => tag.startsWith('rakcha/')))
        .map(img => ({
          name: img.RepoTags?.find(tag => tag.startsWith('rakcha/')) || 'unknown',
          id: img.Id,
          size: img.Size,
          created: new Date(img.Created * 1000)
        }));
    } catch (error) {
      this.logger.error(`Failed to list template images: ${error.message}`);
      return [];
    }
  }

  /**
   * Cleanup orphaned containers (containers without DB records)
   */
  async cleanupOrphanedContainers(): Promise<void> {
    try {
      const containers = await this.docker.listContainers({
        all: true,
        filters: {
          label: ['mybox.instance.id']
        }
      });

      for (const containerInfo of containers) {
        const instanceId = containerInfo.Labels['mybox.instance.id'];
        
        if (instanceId) {
          const instance = await this.machineInstanceModel.findById(instanceId);
          
          if (!instance || instance.status === 'stopped') {
            // Orphaned container - remove it
            const container = this.docker.getContainer(containerInfo.Id);
            
            if (containerInfo.State === 'running') {
              await container.stop({ t: 5 });
            }
            
            await container.remove({ force: true });
            this.logger.log(`Cleaned up orphaned container ${containerInfo.Id}`);
          }
        }
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup orphaned containers: ${error.message}`);
    }
  }
}
