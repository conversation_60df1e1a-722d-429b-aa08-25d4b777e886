import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type UserRole = 'user' | 'admin' | 'moderator';

@Schema({ timestamps: true })
export class User extends Document {  @Prop({ required: true, minlength: 3, maxlength: 50 })
  username: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  passwordHash: string;

  @Prop({ 
    type: String,
    enum: ['user', 'admin', 'moderator'],
    default: 'user'
  })
  role: UserRole;

  @Prop({ default: 0 })
  score: number;

  @Prop({ default: 0 })
  rank: number;

  @Prop({ type: Types.ObjectId, ref: 'Team', default: null })
  teamId?: Types.ObjectId;

  @Prop()
  avatarUrl?: string;

  @Prop()
  apiToken?: string;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: false })
  isEmailVerified: boolean;

  @Prop()
  emailVerificationCode?: string;

  @Prop()
  emailVerificationExpires?: Date;

  @Prop({ default: Date.now })
  lastActive: Date;

  @Prop()
  country?: string;

  @Prop()
  bio?: string;

  createdAt: Date;
  updatedAt: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);

// Create indexes
UserSchema.index({ username: 1 }, { unique: true });
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ score: -1 }); // for leaderboard
UserSchema.index({ rank: 1 }); // for ranking queries
UserSchema.index({ teamId: 1 }); // for team queries
UserSchema.index({ apiToken: 1 });
