import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class TeamChallengeSolve extends Document {
  @Prop({ type: Types.ObjectId, ref: 'Team', required: true })
  teamId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Challenge', required: true })
  challengeId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  solvedBy: Types.ObjectId;

  @Prop({ required: true, default: Date.now })
  solvedAt: Date;

  @Prop({ required: true })
  pointsAwarded: number;
  @Prop({ default: false })
  isFirstBlood: boolean;
  
  @Prop({ default: -1 })
  flagIndex: number;
}

export const TeamChallengeSolveSchema = SchemaFactory.createForClass(TeamChallengeSolve);

// Create indexes
TeamChallengeSolveSchema.index({ teamId: 1, challengeId: 1 }, { unique: true });
TeamChallengeSolveSchema.index({ challengeId: 1, solvedAt: 1 });
