import React from 'react';
import { motion } from 'framer-motion';
import { DashboardConfig } from '../../services/dashboard';
import { Calendar, Clock, MapPin } from 'lucide-react';

interface EventHeaderProps {
  config: DashboardConfig;
  eventStatus: 'upcoming' | 'active' | 'ended';
  mounted: boolean;
}

export function EventHeader({ config, eventStatus, mounted }: EventHeaderProps) {
  if (!config.eventCoverImage && !config.eventDescription) {
    return null;
  }

  return (
    <motion.div
      className={`relative overflow-hidden rounded-2xl ${mounted ? 'animate-slide-in-down' : 'opacity-0'}`}
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      {/* Background Image */}
      {config.eventCoverImage && (
        <div className="absolute inset-0">
          <img
            src={config.eventCoverImage}
            alt={config.eventName}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />
        </div>
      )}

      {/* Content */}
      <div className={`relative z-10 p-8 md:p-12 ${!config.eventCoverImage ? 'glass-card border border-purple-500/30' : ''}`}>
        <div className="max-w-4xl mx-auto text-center">
          {/* Event Name */}
          <motion.h1
            className="text-4xl md:text-6xl font-black bg-gradient-to-r from-white via-purple-200 to-white bg-clip-text text-transparent mb-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            {config.eventName}
          </motion.h1>

          {/* Event Description */}
          {config.eventDescription && (
            <motion.p
              className="text-lg md:text-xl text-white/90 mb-6 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              {config.eventDescription}
            </motion.p>
          )}

          {/* Event Status Badge */}
          <motion.div
            className="flex justify-center mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6, duration: 0.4 }}
          >
            <div className={`inline-flex items-center space-x-2 px-6 py-3 rounded-full backdrop-blur-sm border ${
              eventStatus === 'active' 
                ? 'bg-green-500/20 border-green-400/50 text-green-300' 
                : eventStatus === 'upcoming'
                ? 'bg-yellow-500/20 border-yellow-400/50 text-yellow-300'
                : 'bg-red-500/20 border-red-400/50 text-red-300'
            }`}>
              <Clock className="w-5 h-5" />
              <span className="font-semibold capitalize">{eventStatus} Event</span>
            </div>
          </motion.div>

          {/* Event Dates */}
          {config.eventStartDate && config.eventEndDate && (
            <motion.div
              className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 text-white/80"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
            >
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-purple-300" />
                <span>Starts: {new Date(config.eventStartDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-pink-300" />
                <span>Ends: {new Date(config.eventEndDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</span>
              </div>
            </motion.div>
          )}

          {/* Decorative Elements */}
          <div className="absolute top-4 left-4 w-20 h-20 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-xl animate-pulse" />
          <div className="absolute bottom-4 right-4 w-16 h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl animate-pulse" style={{ animationDelay: '1s' }} />
        </div>
      </div>

      {/* Animated border for non-image headers */}
      {!config.eventCoverImage && (
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-purple-500/20 animate-pulse" />
      )}
    </motion.div>
  );
}