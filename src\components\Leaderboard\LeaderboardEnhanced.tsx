import React, { useState, useEffect } from 'react';
import { useApp } from '../../contexts/AppContext';
import { Trophy, Medal, Award, Crown, Users, TrendingUp, Calendar, ChevronLeft, ChevronRight } from 'lucide-react';

export function Leaderboard() {
  const { state, fetchUserLeaderboard, fetchTeamLeaderboard, fetchTopUsers, fetchTopTeams, fetchUserRanking } = useApp();
  const [activeTab, setActiveTab] = useState<'users' | 'teams'>('users');
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const pageSize = 20;

  useEffect(() => {
    const loadInitialData = async () => {
      setIsLoading(true);
      try {
        await Promise.all([
          fetchUserLeaderboard(1, pageSize),
          fetchTeamLeaderboard(1, pageSize),
          fetchTopUsers(10),
          fetchTopTeams(10)
        ]);
        
        // Fetch current user's ranking if logged in
        if (state.auth.user?.id) {
          await fetchUserRanking(state.auth.user.id);
        }
      } catch (error) {
        console.error('Failed to load leaderboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, []);

  useEffect(() => {
    const loadPageData = async () => {
      setIsLoading(true);
      try {
        if (activeTab === 'users') {
          await fetchUserLeaderboard(currentPage, pageSize);
        } else {
          await fetchTeamLeaderboard(currentPage, pageSize);
        }
      } catch (error) {
        console.error('Failed to load page data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPageData();
  }, [activeTab, currentPage]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-400" />;
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />;
      case 3:
        return <Award className="w-6 h-6 text-amber-600" />;
      default:
        return <Trophy className="w-6 h-6 text-slate-400" />;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border-yellow-500/30';
      case 2:
        return 'bg-gradient-to-r from-gray-500/20 to-gray-600/20 border-gray-500/30';
      case 3:
        return 'bg-gradient-to-r from-amber-500/20 to-amber-600/20 border-amber-500/30';
      default:
        return 'bg-slate-900/50 border-slate-800';
    }
  };

  const formatTimeAgo = (timestamp?: string) => {
    if (!timestamp) return 'Never';
    
    const now = new Date();
    const date = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Less than an hour ago';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const currentLeaderboard = activeTab === 'users' ? state.userLeaderboard : state.teamLeaderboard;
  const totalPages = currentLeaderboard ? Math.ceil(currentLeaderboard.total / pageSize) : 0;

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">Leaderboard</h1>
        <p className="text-slate-400 mb-6">
          See how you rank against other hackers and teams
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex justify-center">
        <div className="bg-slate-900/50 p-1 rounded-lg border border-slate-800">
          <button
            onClick={() => {
              setActiveTab('users');
              setCurrentPage(1);
            }}
            className={`px-6 py-3 rounded-md font-medium transition-all ${
              activeTab === 'users'
                ? 'bg-emerald-600 text-white shadow-lg'
                : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
            }`}
          >
            <div className="flex items-center space-x-2">
              <Trophy className="w-4 h-4" />
              <span>Users</span>
            </div>
          </button>
          <button
            onClick={() => {
              setActiveTab('teams');
              setCurrentPage(1);
            }}
            className={`px-6 py-3 rounded-md font-medium transition-all ${
              activeTab === 'teams'
                ? 'bg-emerald-600 text-white shadow-lg'
                : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
            }`}
          >
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span>Teams</span>
            </div>
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      {state.userRanking && activeTab === 'users' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Trophy className="w-6 h-6 text-emerald-400" />
              <h3 className="text-lg font-semibold text-white">Your Rank</h3>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-emerald-400">#{state.userRanking.rank}</p>
              <p className="text-sm text-slate-400">out of {state.userLeaderboard?.total || 0} users</p>
              <p className="text-xs text-slate-500 mt-1">{state.userRanking.percentile}th percentile</p>
            </div>
          </div>

          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <TrendingUp className="w-6 h-6 text-blue-400" />
              <h3 className="text-lg font-semibold text-white">Your Score</h3>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-400">{state.userRanking.score.toLocaleString()}</p>
              <p className="text-sm text-slate-400">points</p>
              <p className="text-xs text-slate-500 mt-1">{state.userRanking.solvedChallenges} challenges solved</p>
            </div>
          </div>

          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Calendar className="w-6 h-6 text-purple-400" />
              <h3 className="text-lg font-semibold text-white">Last Solve</h3>
            </div>
            <div className="text-center">
              <p className="text-sm text-white">{formatTimeAgo(state.userRanking.lastSolved)}</p>
              <p className="text-xs text-slate-500 mt-1">Keep the momentum going!</p>
            </div>
          </div>
        </div>
      )}

      {/* Main Leaderboard */}
      <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 overflow-hidden">
        <div className="p-6 border-b border-slate-800">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white">
              {activeTab === 'users' ? 'Global User Rankings' : 'Team Rankings'}
            </h2>
            <div className="flex items-center space-x-2">
              <Trophy className="w-5 h-5 text-emerald-400" />
              <span className="text-sm text-slate-400">Updated in real-time</span>
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className="p-12 text-center">
            <div className="inline-flex items-center space-x-2 text-slate-400">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-400"></div>
              <span>Loading leaderboard...</span>
            </div>
          </div>
        ) : (
          <>
            <div className="divide-y divide-slate-800">
              {activeTab === 'users' && state.userLeaderboard?.users.map((entry) => (
                <div
                  key={entry.userId}
                  className={`p-6 hover:bg-slate-800/50 transition-colors ${
                    entry.userId === state.auth.user?.id ? 'bg-emerald-500/10 border-l-4 border-emerald-500' : ''
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      {getRankIcon(entry.rank)}
                    </div>
                    
                    <div className="flex-shrink-0">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${getRankColor(entry.rank)}`}>
                        #{entry.rank}
                      </div>
                    </div>

                    <div className="flex-shrink-0">
                      <img
                        src={entry.avatar || `https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1`}
                        alt={entry.username}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-white truncate">{entry.username}</h3>
                        {entry.userId === state.auth.user?.id && (
                          <span className="px-2 py-1 bg-emerald-500/20 text-emerald-400 rounded text-xs font-medium">
                            You
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-slate-400">
                        {entry.solvedChallenges} challenges solved • {entry.totalSubmissions} total submissions
                      </p>
                    </div>

                    <div className="flex-shrink-0 text-right">
                      <div className="text-2xl font-bold text-emerald-400">
                        {entry.score.toLocaleString()}
                      </div>
                      <div className="text-xs text-slate-400">
                        points
                      </div>
                    </div>

                    <div className="flex-shrink-0 text-right">
                      <div className="text-sm text-slate-400">
                        Last solve
                      </div>
                      <div className="text-xs text-slate-500">
                        {formatTimeAgo(entry.lastSolved)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {activeTab === 'teams' && state.teamLeaderboard?.teams.map((entry) => (
                <div
                  key={entry.teamId}
                  className="p-6 hover:bg-slate-800/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      {getRankIcon(entry.rank)}
                    </div>
                    
                    <div className="flex-shrink-0">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${getRankColor(entry.rank)}`}>
                        #{entry.rank}
                      </div>
                    </div>

                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                        <Users className="w-6 h-6 text-white" />
                      </div>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-white truncate">{entry.teamName}</h3>
                      </div>
                      <p className="text-sm text-slate-400">
                        {entry.memberCount} members • {entry.solvedChallenges} challenges solved
                      </p>
                      <p className="text-xs text-slate-500">
                        Captain: {entry.captain.username}
                      </p>
                    </div>

                    <div className="flex-shrink-0 text-right">
                      <div className="text-2xl font-bold text-purple-400">
                        {entry.teamScore.toLocaleString()}
                      </div>
                      <div className="text-xs text-slate-400">
                        team points
                      </div>
                    </div>

                    <div className="flex-shrink-0 text-right">
                      <div className="text-sm text-slate-400">
                        Last solve
                      </div>
                      <div className="text-xs text-slate-500">
                        {formatTimeAgo(entry.lastSolved)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {currentLeaderboard && totalPages > 1 && (
              <div className="p-6 border-t border-slate-800">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-slate-400">
                    Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, currentLeaderboard.total)} of {currentLeaderboard.total} entries
                  </p>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="p-2 text-slate-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="w-5 h-5" />
                    </button>
                    
                    <div className="flex space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`px-3 py-1 rounded text-sm ${
                              currentPage === page
                                ? 'bg-emerald-600 text-white'
                                : 'text-slate-400 hover:text-white hover:bg-slate-800'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      })}
                    </div>
                    
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="p-2 text-slate-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRight className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Top Performers Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Top Users */}
        <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Crown className="w-6 h-6 text-yellow-400" />
            <h3 className="text-lg font-semibold text-white">Top 10 Users</h3>
          </div>
          <div className="space-y-3">
            {state.topUsers.slice(0, 10).map((user, index) => (
              <div key={user.userId} className="flex items-center space-x-3">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center text-white font-bold text-sm">
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-white truncate">{user.username}</p>
                  <p className="text-xs text-slate-400">{user.score.toLocaleString()} points</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Teams */}
        <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Users className="w-6 h-6 text-purple-400" />
            <h3 className="text-lg font-semibold text-white">Top 10 Teams</h3>
          </div>
          <div className="space-y-3">
            {state.topTeams.slice(0, 10).map((team, index) => (
              <div key={team.teamId} className="flex items-center space-x-3">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center text-white font-bold text-sm">
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-white truncate">{team.teamName}</p>
                  <p className="text-xs text-slate-400">{team.teamScore.toLocaleString()} points • {team.memberCount} members</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
