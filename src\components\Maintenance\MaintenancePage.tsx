import React from 'react';
import { <PERSON><PERSON>, <PERSON>, AlertTriangle, RefreshCw } from 'lucide-react';

interface MaintenancePageProps {
  onRetry?: () => void;
}

export function MaintenancePage({ onRetry }: MaintenancePageProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Animated maintenance icon */}
        <div className="relative mb-8">
          <div className="w-32 h-32 mx-auto bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center border border-purple-500/30">
            <Wrench className="w-16 h-16 text-purple-400 animate-pulse" />
          </div>
          <div className="absolute inset-0 w-32 h-32 mx-auto border-4 border-purple-500/30 rounded-full animate-spin border-t-purple-400"></div>
        </div>

        {/* Main message */}
        <div className="mb-8">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent mb-4">
            Under Maintenance
          </h1>
          <p className="text-xl text-purple-200/80 mb-6">
            We're currently performing scheduled maintenance to improve your experience.
          </p>
          <p className="text-lg text-purple-300/70">
            The platform will be back online shortly. Thank you for your patience!
          </p>
        </div>

        {/* Status indicators */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
            <div className="flex items-center justify-center mb-3">
              <Clock className="w-8 h-8 text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Estimated Time</h3>
            <p className="text-blue-200">30-60 minutes</p>
          </div>

          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
            <div className="flex items-center justify-center mb-3">
              <AlertTriangle className="w-8 h-8 text-yellow-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Status</h3>
            <p className="text-yellow-200">In Progress</p>
          </div>

          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
            <div className="flex items-center justify-center mb-3">
              <Wrench className="w-8 h-8 text-purple-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Type</h3>
            <p className="text-purple-200">System Updates</p>
          </div>
        </div>

        {/* Retry button */}
        {onRetry && (
          <div className="mb-8">
            <button
              onClick={onRetry}
              className="inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              <RefreshCw className="w-5 h-5" />
              <span>Check Again</span>
            </button>
          </div>
        )}

        {/* Additional info */}
        <div className="text-sm text-purple-300/60">
          <p className="mb-2">
            If you need immediate assistance, please contact our support team.
          </p>
          <p>
            Follow us on social media for real-time updates on the maintenance progress.
          </p>
        </div>

        {/* Floating decorative elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-xl animate-float" />
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-xl animate-float" style={{ animationDelay: '3s' }} />
      </div>
    </div>
  );
}