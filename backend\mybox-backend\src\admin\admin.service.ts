import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { User } from '../schemas/user.schema';
import { Team } from '../schemas/team.schema';
import { Challenge } from '../schemas/challenge.schema';
import { ChallengeSubmission } from '../schemas/challenge-submission.schema';
import { TeamChallengeSolve } from '../schemas/team-challenge-solve.schema';
import { AdminPaginationQueryDto, SortDirection } from './dto/admin-pagination.dto';
import { CreateUserDto, UserRole } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateChallengeDto } from './dto/create-challenge.dto';
import { UpdateChallengeDto } from './dto/update-challenge.dto';
import { NotificationsService } from '../notifications/notifications.service';
import { NotificationsGateway } from '../notifications/notifications.gateway';
import * as bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

@Injectable()
export class AdminService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Team.name) private teamModel: Model<Team>,
    @InjectModel(Challenge.name) private challengeModel: Model<Challenge>,
    @InjectModel(ChallengeSubmission.name) private challengeSubmissionModel: Model<ChallengeSubmission>,
    @InjectModel(TeamChallengeSolve.name) private teamChallengeSolveModel: Model<TeamChallengeSolve>,
    private notificationsService: NotificationsService,
    private notificationsGateway: NotificationsGateway
  ) {}

  // User management methods
  async createUser(createUserDto: CreateUserDto) {
    // Check if user already exists
    const existingUser = await this.userModel.findOne({
      $or: [
        { username: createUserDto.username },
        { email: createUserDto.email }
      ]
    });

    if (existingUser) {
      throw new ConflictException('Username or email already exists');
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(createUserDto.password, saltRounds);

    // Generate API token
    const apiToken = `htb_api_token_${uuidv4().replace(/-/g, '')}`;

    const user = new this.userModel({
      username: createUserDto.username,
      email: createUserDto.email,
      passwordHash,
      apiToken,
      role: createUserDto.role || UserRole.USER,
      avatarUrl: createUserDto.avatarUrl,
      country: createUserDto.country,
      bio: createUserDto.bio,
      isActive: true,
      isEmailVerified: createUserDto.role === 'admin', // Admin users have verified email by default
      lastActive: new Date(),
      createdAt: new Date(),
      score: 0,
      rank: 0
    });

    const savedUser = await user.save();
    const { passwordHash: _, ...result } = savedUser.toObject();
    return result;
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID');
    }

    const user = await this.userModel.findById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if username/email is being changed and if it's already taken
    if (updateUserDto.username && updateUserDto.username !== user.username) {
      const existingUser = await this.userModel.findOne({ username: updateUserDto.username });
      if (existingUser) {
        throw new ConflictException('Username already exists');
      }
    }

    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.userModel.findOne({ email: updateUserDto.email });
      if (existingUser) {
        throw new ConflictException('Email already exists');
      }
    }

    const updatedUser = await this.userModel.findByIdAndUpdate(
      id,
      {
        ...updateUserDto,
        updatedAt: new Date()
      },
      { new: true }
    ).select('-passwordHash');

    if (!updatedUser) {
      throw new NotFoundException('User not found');
    }

    return updatedUser;
  }

  async updateUserRole(id: string, role: UserRole) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID');
    }

    const user = await this.userModel.findByIdAndUpdate(
      id,
      { role, updatedAt: new Date() },
      { new: true }
    ).select('-passwordHash');

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async getUsers(query: AdminPaginationQueryDto) {
    const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = query;
    
    const filter: any = {};
    
    if (search) {
      filter.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    const sort: any = { [sortBy]: sortDirection === SortDirection.ASC ? 1 : -1 };
    const skip = (page - 1) * limit;
    
    const [users, total] = await Promise.all([
      this.userModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .select('-passwordHash')
        .lean(),
      this.userModel.countDocuments(filter)
    ]);
    
    return {
      users,
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    };
  }

  async getUserById(id: string) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID');
    }
    
    const user = await this.userModel.findById(id).select('-passwordHash').lean();
    
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    return user;
  }

  async updateUserStatus(id: string, isActive: boolean) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID');
    }
    
    const user = await this.userModel.findByIdAndUpdate(
      id,
      { isActive },
      { new: true }
    ).select('-passwordHash');
    
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    return user;
  }

  async deleteUser(id: string) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID');
    }

    const user = await this.userModel.findByIdAndDelete(id);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Clean up related data
    await Promise.all([
      this.challengeSubmissionModel.deleteMany({ userId: id }),
      this.teamModel.updateMany({ captainId: id }, { $unset: { captainId: "" } })
    ]);

    return { message: 'User deleted successfully' };
  }

  async verifyUserEmail(id: string) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID');
    }

    const user = await this.userModel.findByIdAndUpdate(
      id,
      {
        isEmailVerified: true,
        emailVerificationCode: undefined,
        emailVerificationExpires: undefined
      },
      { new: true }
    );

    if (!user) {
      throw new NotFoundException('User not found');
    }

    console.log(`✅ Admin manually verified email for user: ${user.username} (${user.email})`);

    return user;
  }

  // Team management methods
  async getTeams(query: AdminPaginationQueryDto) {
    const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = query;
    
    const filter: any = {};
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    
    const sort: any = { [sortBy]: sortDirection === SortDirection.ASC ? 1 : -1 };
    const skip = (page - 1) * limit;
    
    const [teams, total] = await Promise.all([
      this.teamModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      this.teamModel.countDocuments(filter)
    ]);
    
    // Get member counts for each team
    const teamsWithMemberCount = await Promise.all(
      teams.map(async (team) => {
        const memberCount = await this.userModel.countDocuments({ teamId: team._id });
        return { ...team, memberCount };
      })
    );
    
    return {
      teams: teamsWithMemberCount,
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    };
  }

  async getTeamById(id: string) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid team ID');
    }
    
    const team = await this.teamModel.findById(id).lean();
    
    if (!team) {
      throw new NotFoundException('Team not found');
    }
    
    // Get team members
    const members = await this.userModel
      .find({ teamId: id })
      .select('username email avatar score lastActive')
      .lean();
    
    return { ...team, members };
  }

  async updateTeamStatus(id: string, isActive: boolean) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid team ID');
    }
    
    const team = await this.teamModel.findByIdAndUpdate(
      id,
      { isActive },
      { new: true }
    ).lean();
    
    if (!team) {
      throw new NotFoundException('Team not found');
    }
    
    return team;
  }

  async deleteTeam(id: string) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid team ID');
    }
    
    const team = await this.teamModel.findByIdAndDelete(id);
    
    if (!team) {
      throw new NotFoundException('Team not found');
    }
    
    // Update users to remove team reference
    await this.userModel.updateMany(
      { teamId: id },
      { $unset: { teamId: "" } }
    );
    
    // Clean up related data
    await this.teamChallengeSolveModel.deleteMany({ teamId: id });
    
    return { message: 'Team deleted successfully' };
  }

  // Challenge management methods
  async getChallenges(query: AdminPaginationQueryDto) {
    const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = query;
    
    const filter: any = {};
    
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } }
      ];
    }
    
    const sort: any = { [sortBy]: sortDirection === SortDirection.ASC ? 1 : -1 };
    const skip = (page - 1) * limit;
    
    const [challenges, total] = await Promise.all([
      this.challengeModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .select('-flag')
        .lean(),
      this.challengeModel.countDocuments(filter)
    ]);
    
    return {
      challenges,
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    };
  }

  async createChallenge(createChallengeDto: CreateChallengeDto) {
    // Check if challenge with same title already exists
    const existingChallenge = await this.challengeModel.findOne({
      title: createChallengeDto.title
    });

    if (existingChallenge) {
      throw new ConflictException('Challenge with this title already exists');
    }
    
    // Convert authorId to ObjectId if provided
    let authorObjectId: Types.ObjectId | null = null;
    if (createChallengeDto.authorId) {
      if (!Types.ObjectId.isValid(createChallengeDto.authorId)) {
        throw new BadRequestException('Invalid author ID');
      }
      authorObjectId = new Types.ObjectId(createChallengeDto.authorId);
      
      // Fetch author name if not provided
      if (!createChallengeDto.authorName) {
        const author = await this.userModel.findById(authorObjectId).select('username').lean();
        if (author) {
          createChallengeDto.authorName = author.username;
        }
      }
    }
    
    // Handle flags
    let flags = createChallengeDto.flags || [];
    
    // If legacy flag is provided but no flags array, convert to flags array
    if (createChallengeDto.flag && (!flags || flags.length === 0)) {
      flags = [{
        value: createChallengeDto.flag,
        isCaseSensitive: false,
        points: createChallengeDto.points || 0,
        description: 'Primary flag'
      }];
    }
    
    // Calculate total points if not specified
    if (!createChallengeDto.points && flags.length > 0) {
      createChallengeDto.points = flags.reduce((sum, flag) => sum + (flag.points || 0), 0);
    }

    const challenge = new this.challengeModel({
      ...createChallengeDto,
      authorId: authorObjectId,
      flags,
      isActive: createChallengeDto.isActive ?? true,
      solveCount: 0,
      createdAt: new Date()
    });

    const savedChallenge = await challenge.save();

    // If challenge is created as active, send notification
    if (savedChallenge.isActive) {
      try {
        console.log(`🎯 Creating new challenge notification for "${savedChallenge.title}" (Admin Service)`);
        const notification = await this.notificationsService.createNewChallengeNotification(
          (savedChallenge._id as any).toString(),
          savedChallenge.title,
          savedChallenge.category,
          savedChallenge.difficulty,
          savedChallenge.points
        );
        
        // Broadcast the new challenge notification to all users
        await this.notificationsGateway.broadcastGlobalNotification(notification);
        console.log(`✅ New challenge notification broadcasted successfully (Admin Service)`);
      } catch (error) {
        console.error('Failed to create new challenge notification (Admin Service):', error);
      }
    }

    return savedChallenge;
  }

  async getChallengeById(id: string) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid challenge ID');
    }
    
    const challenge = await this.challengeModel.findById(id).lean();
    
    if (!challenge) {
      throw new NotFoundException('Challenge not found');
    }
    
    // Get solve count
    const solveCount = await this.challengeSubmissionModel.countDocuments({
      challengeId: id,
      isCorrect: true
    });
    
    return { ...challenge, solveCount };
  }

  async updateChallengeStatus(id: string, isActive: boolean) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid challenge ID');
    }
    
    // Get the original challenge to check if status changed
    const originalChallenge = await this.challengeModel.findById(id);
    if (!originalChallenge) {
      throw new NotFoundException('Challenge not found');
    }
    
    const challenge = await this.challengeModel.findByIdAndUpdate(
      id,
      { isActive },
      { new: true }
    ).select('-flag').lean();
    
    if (!challenge) {
      throw new NotFoundException('Challenge not found');
    }
    
    // If challenge status changed from inactive to active, send notification
    if (!originalChallenge.isActive && challenge.isActive) {
      try {
        console.log(`🎯 Creating new challenge notification for activated challenge "${challenge.title}" (Admin Service)`);
        const notification = await this.notificationsService.createNewChallengeNotification(
          (challenge._id as any).toString(),
          challenge.title,
          challenge.category,
          challenge.difficulty,
          challenge.points
        );
        
        // Broadcast the new challenge notification to all users
        await this.notificationsGateway.broadcastGlobalNotification(notification);
        console.log(`✅ New challenge notification broadcasted successfully (Admin Service)`);
      } catch (error) {
        console.error('Failed to create new challenge notification (Admin Service):', error);
      }
    }
    
    return challenge;
  }

  async deleteChallenge(id: string) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid challenge ID');
    }
    
    const challenge = await this.challengeModel.findByIdAndDelete(id);
    
    if (!challenge) {
      throw new NotFoundException('Challenge not found');
    }
    
    // Clean up related data
    await Promise.all([
      this.challengeSubmissionModel.deleteMany({ challengeId: id }),
      this.teamChallengeSolveModel.deleteMany({ challengeId: id })
    ]);
    
    return { message: 'Challenge deleted successfully' };
  }

  async updateChallenge(id: string, updateChallengeDto: UpdateChallengeDto) {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid challenge ID');
    }

    // Check if challenge exists
    const challenge = await this.challengeModel.findById(id);
    if (!challenge) {
      throw new NotFoundException('Challenge not found');
    }

    // Check if title is being changed and if it's already taken
    if (updateChallengeDto.title && updateChallengeDto.title !== challenge.title) {
      const existingChallenge = await this.challengeModel.findOne({ 
        title: updateChallengeDto.title,
        _id: { $ne: id }
      });
      
      if (existingChallenge) {
        throw new ConflictException('Challenge with this title already exists');
      }
    }    
    
    // Convert authorId to ObjectId if provided
    let authorObjectId: Types.ObjectId | null = null;
    if (updateChallengeDto.authorId) {
      if (!Types.ObjectId.isValid(updateChallengeDto.authorId)) {
        throw new BadRequestException('Invalid author ID');
      }
      authorObjectId = new Types.ObjectId(updateChallengeDto.authorId);
      
      // Fetch author name if not provided
      if (!updateChallengeDto.authorName) {
        const author = await this.userModel.findById(authorObjectId).select('username').lean();
        if (author) {
          updateChallengeDto.authorName = author.username;
        }
      }
    }
    
    // Handle flags
    let flagsUpdate = {};
    if (updateChallengeDto.flags) {
      flagsUpdate = { flags: updateChallengeDto.flags };
    } else if (updateChallengeDto.flag && (!challenge.flags || challenge.flags.length === 0)) {
      // If updating legacy flag but no flags array exists, create a new flags array
      flagsUpdate = {
        flags: [{
          value: updateChallengeDto.flag,
          isCaseSensitive: false,
          points: updateChallengeDto.points || challenge.points || 0,
          description: 'Primary flag'
        }]
      };
    }
    
    // Calculate total points if flags are updated but points aren't
    if (!updateChallengeDto.points && updateChallengeDto.flags && updateChallengeDto.flags.length > 0) {
      updateChallengeDto.points = updateChallengeDto.flags.reduce(
        (sum, flag) => sum + (flag.points || 0), 
        0
      );
    }
    
    // Remove authorId from DTO to avoid direct assignment
    const { authorId, ...challengeUpdates } = updateChallengeDto;
    
    const updatedChallenge = await this.challengeModel.findByIdAndUpdate(
      id,
      {
        ...challengeUpdates,
        ...flagsUpdate,
        ...(authorObjectId && { authorId: authorObjectId }),
        updatedAt: new Date()
      },
      { new: true }
    );

    return updatedChallenge;
  }

  // System statistics
  async getSystemStats() {
    const [
      totalUsers,
      activeUsers,
      totalTeams,
      activeTeams,
      totalChallenges,
      activeChallenges,
      totalSubmissions,
      correctSubmissions
    ] = await Promise.all([
      this.userModel.countDocuments(),
      this.userModel.countDocuments({ isActive: true }),
      this.teamModel.countDocuments(),
      this.teamModel.countDocuments({ isActive: true }),
      this.challengeModel.countDocuments(),
      this.challengeModel.countDocuments({ isActive: true }),
      this.challengeSubmissionModel.countDocuments(),
      this.challengeSubmissionModel.countDocuments({ isCorrect: true })
    ]);
    
    const submissionSuccessRate = totalSubmissions ? 
      Math.round((correctSubmissions / totalSubmissions) * 100) : 0;
    
    // Get top users by score
    const topUsers = await this.userModel
      .find({ isActive: true })
      .sort({ score: -1 })
      .limit(5)
      .select('username score')
      .lean();
    
    // Get top teams by score
    const topTeams = await this.teamModel
      .find({ isActive: true })
      .sort({ teamScore: -1 })
      .limit(5)
      .select('name teamScore')
      .lean();
    
    // Get most solved challenges
    const mostSolvedChallenges = await this.challengeSubmissionModel.aggregate([
      { $match: { isCorrect: true } },
      { $group: { _id: '$challengeId', solveCount: { $sum: 1 } } },
      { $sort: { solveCount: -1 } },
      { $limit: 5 }
    ]);
    
    const challengeIds = mostSolvedChallenges.map(item => item._id);
    const challengeDetails = await this.challengeModel
      .find({ _id: { $in: challengeIds } })
      .select('title category difficulty')
      .lean();
    
    const challengesWithSolves = mostSolvedChallenges.map(item => {
      const details = challengeDetails.find(c => c._id.toString() === item._id.toString());
      return {
        ...details,
        solveCount: item.solveCount
      };
    });
    
    return {
      users: {
        total: totalUsers,
        active: activeUsers,
        topUsers
      },
      teams: {
        total: totalTeams,
        active: activeTeams,
        topTeams
      },
      challenges: {
        total: totalChallenges,
        active: activeChallenges,
        mostSolved: challengesWithSolves
      },
      submissions: {
        total: totalSubmissions,
        correct: correctSubmissions,
        successRate: submissionSuccessRate
      }
    };
  }

  // Activity logs (could be expanded with a dedicated activity log schema)
  async getActivityLogs(query: AdminPaginationQueryDto) {
    // For now, we'll use challenge submissions as a basic activity log
    const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = query;
    
    const sort: any = { [sortBy]: sortDirection === SortDirection.ASC ? 1 : -1 };
    const skip = (page - 1) * limit;
    
    const submissions = await this.challengeSubmissionModel
      .find()
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('userId', 'username email')
      .populate('challengeId', 'title category')
      .lean();
    
    const total = await this.challengeSubmissionModel.countDocuments();
      return {
      activities: submissions,
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    };
  }

  // File upload handling
  async uploadFile(file: Express.Multer.File, challengeName?: string) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    try {
      // Create challenge-specific folder path
      let folderPath = 'uploads/challenges';
      
      if (challengeName) {
        // Sanitize challenge name for folder path
        const sanitizedChallengeName = challengeName
          .toLowerCase()
          .replace(/[^a-z0-9-_]/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        
        folderPath = `uploads/challenges/${sanitizedChallengeName}`;
        
        // Create directory if it doesn't exist
        const fullDirPath = path.join(process.cwd(), folderPath);
        if (!fs.existsSync(fullDirPath)) {
          await fs.promises.mkdir(fullDirPath, { recursive: true });
        }
        
        // Move file from temporary location to challenge-specific folder
        const oldPath = file.path;
        const newFilename = `${Date.now()}-${file.originalname}`;
        const newPath = path.join(fullDirPath, newFilename);
        
        // Move the file
        await fs.promises.rename(oldPath, newPath);
        
        // Update file info
        file.filename = newFilename;
        file.path = newPath;
        
        folderPath = `${folderPath}/${newFilename}`;
      } else {
        // Default behavior for backward compatibility
        folderPath = `uploads/challenges/${file.filename}`;
      }
      
      return {
        originalName: file.originalname,
        filename: file.filename,
        filePath: folderPath,
        size: file.size,
        mimeType: file.mimetype,
        url: `/${folderPath}`
      };
    } catch (error) {      throw new BadRequestException(`File upload failed: ${error.message}`);
    }
  }
  // File download handling
  async downloadFile(filePath: string, res: any) {
    try {
      console.log('=== DOWNLOAD FILE DEBUG ===');
      console.log('Original filePath:', filePath);
      
      // Sanitize file path to prevent directory traversal attacks
      const safePath = path.normalize(filePath).replace(/^(\.\.[\/\\])+/, '');
      console.log('Safe path after normalization:', safePath);
      
      const fullPath = path.join(process.cwd(), 'uploads', safePath);
      console.log('Full path constructed:', fullPath);
      console.log('Current working directory:', process.cwd());

      // Check if file exists
      const fileExists = fs.existsSync(fullPath);
      console.log('File exists check:', fileExists);
      
      if (!fileExists) {
        console.log('File not found, throwing NotFoundException');
        throw new NotFoundException('File not found');
      }      // Verify the file is within the uploads directory (security check)
      const uploadsDir = path.join(process.cwd(), 'uploads');
      const resolvedPath = path.resolve(fullPath);
      const resolvedUploadsDir = path.resolve(uploadsDir);
      
      console.log('Security check - uploads dir:', resolvedUploadsDir);
      console.log('Security check - resolved path:', resolvedPath);
      console.log('Path starts with uploads dir:', resolvedPath.startsWith(resolvedUploadsDir));
      
      if (!resolvedPath.startsWith(resolvedUploadsDir)) {
        console.log('Security check failed - Invalid file path');
        throw new BadRequestException('Invalid file path');
      }

      // Get file stats
      const stats = fs.statSync(fullPath);
      const fileName = path.basename(fullPath);
      
      console.log('File stats:', {
        size: stats.size,
        fileName: fileName,
        isFile: stats.isFile()
      });

      // Set appropriate headers
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Length', stats.size);
      
      console.log('Headers set, starting file stream...');      // Stream the file
      const fileStream = fs.createReadStream(fullPath);
      fileStream.pipe(res);
      
      return new Promise<void>((resolve, reject) => {
        fileStream.on('end', () => {
          console.log('File stream ended successfully');
          resolve();
        });
        fileStream.on('error', (error) => {
          console.log('File stream error:', error);
          reject(error);
        });
      });
    } catch (error) {
      console.log('=== DOWNLOAD ERROR ===');
      console.log('Error type:', error.constructor.name);
      console.log('Error message:', error.message);
      console.log('Error stack:', error.stack);
      console.log('======================');
      throw new BadRequestException(`File download failed: ${error.message}`);
    }
  }
}