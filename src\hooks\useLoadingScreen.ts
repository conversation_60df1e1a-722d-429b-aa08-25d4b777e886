import { useState, useEffect } from 'react';

interface UseLoadingScreenOptions {
  minLoadingTime?: number; // Minimum time to show loading screen (in ms)
  initialDelay?: number;   // Initial delay before starting loading (in ms)
}

export function useLoadingScreen(
  isActuallyLoading: boolean,
  options: UseLoadingScreenOptions = {}
) {
  const { minLoadingTime = 2000, initialDelay = 0 } = options;
  
  const [showLoading, setShowLoading] = useState(true);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    let startTimer: NodeJS.Timeout;
    let minTimer: NodeJS.Timeout;

    // Start the loading screen after initial delay
    startTimer = setTimeout(() => {
      setHasStarted(true);
    }, initialDelay);

    // If actual loading is done, wait for minimum time before hiding
    if (!isActuallyLoading && hasStarted) {
      minTimer = setTimeout(() => {
        setShowLoading(false);
      }, minLoadingTime);
    }

    return () => {
      clearTimeout(startTimer);
      clearTimeout(minTimer);
    };
  }, [isActuallyLoading, hasStarted, minLoadingTime, initialDelay]);

  // Reset loading state when actual loading starts again
  useEffect(() => {
    if (isActuallyLoading) {
      setShowLoading(true);
    }
  }, [isActuallyLoading]);

  return {
    showLoading: showLoading && (isActuallyLoading || hasStarted),
    onLoadingComplete: () => setShowLoading(false)
  };
}
