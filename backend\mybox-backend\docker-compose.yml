version: '3.8'

services:
  openvpn:
    image: kylemanna/openvpn
    container_name: openvpn
    ports:
      - "1194:1194/udp"
    restart: always
    cap_add:
      - NET_ADMIN
      - SYS_ADMIN
      - NET_RAW
    sysctls:
      net.ipv4.ip_forward: 1
    environment:
      - EASYRSA_KEY_SIZE=2048
      - EASYRSA_CA_EXPIRE=3650
      - EASYRSA_CERT_EXPIRE=3650
      - EASYRSA_REQ_CN=OpenVPN-CA
    volumes:
      - ./openvpn-data:/etc/openvpn
    networks:
      vpn-network:
        ipv4_address: ********
    privileged: true
    devices:
      - "/dev/net/tun"
    command: ovpn_run --mute-replay-warnings

  vpn-api:
    build:
      context: ./vpn2go
      dockerfile: Dockerfile
    container_name: vpn-api
    ports:
      - "8080:5000"
    restart: always
    environment:
      - SERVICE_USER=admin
      - SERVICE_PASSWORD=admin123
    volumes:
      - ./openvpn-data:/etc/openvpn
    depends_on:
      - openvpn
    networks:
      - vpn-network


networks:
  vpn-network:
    driver: bridge
    ipam:
      config:
        - subnet: ********/24