import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as os from 'os';
import * as path from 'path';

import { VPNConfig, VPNConfigDocument } from '../../schemas/vpn-config.schema';
import { MachineNetwork, MachineNetworkDocument } from '../../schemas/machine-network.schema';
import { VPNConnectionLog, VPNConnectionLogDocument } from '../../schemas/vpn-connection-log.schema';

const execAsync = promisify(exec);

export interface WireGuardKeyPair {
  privateKey: string;
  publicKey: string;
}

export interface VPNConfigData {
  userId: string;
  clientConfig: string;
  serverConfig: string;
  ipAddress: string;
  subnet: string;
}

@Injectable()
export class VPNConfigService {
  private readonly logger = new Logger(VPNConfigService.name);
  private readonly vpnSubnetBase = '10.'; // Base for VPN subnets (10.x.0.0/24)
  private readonly serverPrivateKey: string;
  private readonly serverPublicKey: string;
  private readonly serverEndpoint: string;
  private readonly serverPort: number;
  private readonly isWindows: boolean;
  private readonly isLinux: boolean;
  private readonly platform: string;

  constructor(
    @InjectModel(VPNConfig.name) private vpnConfigModel: Model<VPNConfigDocument>,
    @InjectModel(MachineNetwork.name) private machineNetworkModel: Model<MachineNetworkDocument>,
    @InjectModel(VPNConnectionLog.name) private vpnConnectionLogModel: Model<VPNConnectionLogDocument>,
    private configService: ConfigService,
  ) {
    // Initialize OS detection
    this.platform = os.platform();
    this.isWindows = this.platform === 'win32';
    this.isLinux = this.platform === 'linux';

    this.logger.log(`Detected OS: ${this.platform} (Windows: ${this.isWindows}, Linux: ${this.isLinux})`);

    // Initialize server keys from environment or generate new ones
    this.serverPrivateKey = this.configService.get('WIREGUARD_SERVER_PRIVATE_KEY') || '';
    this.serverPublicKey = this.configService.get('WIREGUARD_SERVER_PUBLIC_KEY') || '';
    this.serverEndpoint = this.configService.get('WIREGUARD_SERVER_ENDPOINT', 'localhost');
    this.serverPort = this.configService.get('WIREGUARD_SERVER_PORT', 51820);

    if (!this.serverPrivateKey || !this.serverPublicKey) {
      this.logger.warn('WireGuard server keys not configured. Please set WIREGUARD_SERVER_PRIVATE_KEY and WIREGUARD_SERVER_PUBLIC_KEY');
    }
  }

  /**
   * Get the appropriate WireGuard command for the current OS
   */
  private getWireGuardCommand(): string {
    if (this.isWindows) {
      // On Windows, WireGuard might be installed in different locations
      const possiblePaths = [
        'wg',                                    // If in PATH
        '"C:\\Program Files\\WireGuard\\wg.exe"', // Default installation
        '"C:\\Program Files (x86)\\WireGuard\\wg.exe"', // 32-bit installation
      ];

      // For now, return the first one. In production, you might want to check which exists
      return possiblePaths[0];
    } else {
      // Linux/Unix systems
      return 'wg';
    }
  }

  /**
   * Get WireGuard configuration directory for the current OS
   */
  private getWireGuardConfigDir(): string {
    if (this.isWindows) {
      return path.join(os.homedir(), 'AppData', 'Local', 'WireGuard', 'Configurations');
    } else {
      return '/etc/wireguard';
    }
  }

  /**
   * Get the appropriate file extension for WireGuard config files
   */
  private getConfigFileExtension(): string {
    return '.conf'; // Same for both Windows and Linux
  }

  /**
   * Generate WireGuard key pair
   */
  async generateKeyPair(): Promise<WireGuardKeyPair> {
    try {
      // Generate private key
      const { stdout: privateKey } = await execAsync('wg genkey');
      
      // Generate public key from private key
      const { stdout: publicKey } = await execAsync(`echo "${privateKey.trim()}" | wg pubkey`);

      return {
        privateKey: privateKey.trim(),
        publicKey: publicKey.trim()
      };
    } catch (error) {
      this.logger.error(`Failed to generate WireGuard keys: ${error.message}`);
      
      // Fallback to crypto-based key generation if wg command is not available
      return this.generateKeyPairFallback();
    }
  }

  /**
   * Fallback key generation using crypto (for development/testing)
   */
  private generateKeyPairFallback(): WireGuardKeyPair {
    const privateKey = crypto.randomBytes(32).toString('base64');
    const publicKey = crypto.randomBytes(32).toString('base64');
    
    this.logger.warn('Using fallback key generation. Install WireGuard tools for production use.');
    
    return { privateKey, publicKey };
  }

  /**
   * Get next available subnet for a user
   */
  private async getNextAvailableSubnet(): Promise<{ subnet: string; ipAddress: string }> {
    // Get all existing subnets
    const existingConfigs = await this.vpnConfigModel.find({}, 'subnet').exec();
    const usedSubnets = new Set(existingConfigs.map(config => config.subnet));

    // Find next available subnet (********/24, ********/24, etc.)
    for (let i = 1; i <= 254; i++) {
      const subnet = `${this.vpnSubnetBase}${i}.0.0/24`;
      const ipAddress = `${this.vpnSubnetBase}${i}.0.1`; // User gets .1 in their subnet
      
      if (!usedSubnets.has(subnet)) {
        return { subnet, ipAddress };
      }
    }

    throw new BadRequestException('No available subnets. Maximum users reached.');
  }

  /**
   * Create VPN configuration for a user
   */
  async createVPNConfig(userId: string): Promise<VPNConfigData> {
    try {
      // Check if user already has a VPN config
      const existingConfig = await this.vpnConfigModel.findOne({ userId: new Types.ObjectId(userId) });
      if (existingConfig) {
        throw new BadRequestException('User already has a VPN configuration');
      }

      // Generate key pair
      const keyPair = await this.generateKeyPair();
      
      // Get next available subnet
      const { subnet, ipAddress } = await this.getNextAvailableSubnet();

      // Encrypt private key before storage
      const encryptedPrivateKey = this.encryptPrivateKey(keyPair.privateKey);

      // Create VPN config in database
      const vpnConfig = new this.vpnConfigModel({
        userId: new Types.ObjectId(userId),
        publicKey: keyPair.publicKey,
        privateKey: encryptedPrivateKey,
        ipAddress,
        subnet,
        allowedIPs: [subnet], // User can access their entire subnet
        serverEndpoint: this.serverEndpoint,
        serverPort: this.serverPort,
        dns: '*******,*******', // Default DNS servers
      });

      await vpnConfig.save();

      // Generate client and server configurations
      const clientConfig = this.generateClientConfig(keyPair.privateKey, vpnConfig);
      const serverConfig = this.generateServerConfig(vpnConfig);

      this.logger.log(`Created VPN configuration for user ${userId} with subnet ${subnet}`);

      return {
        userId,
        clientConfig,
        serverConfig,
        ipAddress,
        subnet
      };
    } catch (error) {
      this.logger.error(`Failed to create VPN config for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get VPN configuration for a user
   */
  async getVPNConfig(userId: string): Promise<VPNConfigData | null> {
    try {
      const vpnConfig = await this.vpnConfigModel.findOne({ 
        userId: new Types.ObjectId(userId),
        isActive: true 
      });

      if (!vpnConfig) {
        return null;
      }

      // Decrypt private key
      const privateKey = this.decryptPrivateKey(vpnConfig.privateKey);

      // Generate configurations
      const clientConfig = this.generateClientConfig(privateKey, vpnConfig);
      const serverConfig = this.generateServerConfig(vpnConfig);

      return {
        userId,
        clientConfig,
        serverConfig,
        ipAddress: vpnConfig.ipAddress,
        subnet: vpnConfig.subnet
      };
    } catch (error) {
      this.logger.error(`Failed to get VPN config for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate WireGuard client configuration
   */
  private generateClientConfig(privateKey: string, vpnConfig: VPNConfigDocument): string {
    return `[Interface]
PrivateKey = ${privateKey}
Address = ${vpnConfig.ipAddress}/24
DNS = ${vpnConfig.dns}
MTU = ${vpnConfig.mtu}

[Peer]
PublicKey = ${this.serverPublicKey}
Endpoint = ${vpnConfig.serverEndpoint}:${vpnConfig.serverPort}
AllowedIPs = ${vpnConfig.subnet}
PersistentKeepalive = 25`;
  }

  /**
   * Generate WireGuard server configuration entry
   */
  private generateServerConfig(vpnConfig: VPNConfigDocument): string {
    return `# User: ${vpnConfig.userId}
[Peer]
PublicKey = ${vpnConfig.publicKey}
AllowedIPs = ${vpnConfig.ipAddress}/32`;
  }

  /**
   * Encrypt private key for storage
   */
  private encryptPrivateKey(privateKey: string): string {
    const algorithm = 'aes-256-gcm';
    const secretKey = this.configService.get('VPN_ENCRYPTION_KEY', 'default-secret-key-change-in-production');
    const iv = crypto.randomBytes(16);

    // Create a 32-byte key from the secret
    const key = crypto.scryptSync(secretKey, 'salt', 32);

    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(privateKey, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }

  /**
   * Decrypt private key from storage
   */
  private decryptPrivateKey(encryptedPrivateKey: string): string {
    const algorithm = 'aes-256-gcm';
    const secretKey = this.configService.get('VPN_ENCRYPTION_KEY', 'default-secret-key-change-in-production');

    const [ivHex, authTagHex, encrypted] = encryptedPrivateKey.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');

    // Create a 32-byte key from the secret
    const key = crypto.scryptSync(secretKey, 'salt', 32);

    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
   * Regenerate VPN configuration for a user
   */
  async regenerateVPNConfig(userId: string): Promise<VPNConfigData> {
    try {
      // Remove existing config
      await this.revokeVPNConfig(userId);

      // Create new config
      return await this.createVPNConfig(userId);
    } catch (error) {
      this.logger.error(`Failed to regenerate VPN config for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Revoke VPN configuration for a user
   */
  async revokeVPNConfig(userId: string): Promise<void> {
    try {
      const result = await this.vpnConfigModel.deleteOne({ userId: new Types.ObjectId(userId) });

      if (result.deletedCount === 0) {
        throw new NotFoundException('VPN configuration not found');
      }

      this.logger.log(`Revoked VPN configuration for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to revoke VPN config for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get all VPN configurations (admin)
   */
  async getAllVPNConfigs(): Promise<VPNConfigDocument[]> {
    return this.vpnConfigModel.find({})
      .populate('userId', 'username email')
      .sort({ createdAt: -1 })
      .exec();
  }

  /**
   * Get VPN server status
   */
  async getVPNServerStatus(): Promise<{
    isRunning: boolean;
    activeConnections: number;
    totalUsers: number;
    serverEndpoint: string;
    serverPort: number;
  }> {
    try {
      const totalUsers = await this.vpnConfigModel.countDocuments({ isActive: true });

      // Try to get WireGuard interface status (cross-platform)
      let isRunning = false;
      let activeConnections = 0;

      try {
        const wgCommand = this.getWireGuardCommand();
        const { stdout } = await execAsync(`${wgCommand} show`);
        isRunning = stdout.includes('interface:') || stdout.includes('public key:');
        // Count active peers (simplified)
        activeConnections = (stdout.match(/peer:/g) || []).length;

        this.logger.log(`WireGuard status detected: Running=${isRunning}, Connections=${activeConnections}`);
      } catch (error) {
        this.logger.warn(`WireGuard not installed or not running on ${this.platform}. Using development mode.`);
        // In development mode, simulate some activity if users exist
        if (totalUsers > 0) {
          isRunning = true;
          activeConnections = Math.floor(totalUsers * 0.6); // Simulate 60% of users being connected
          this.logger.log(`Development mode: Simulating ${activeConnections} active connections`);
        }
      }

      return {
        isRunning,
        activeConnections,
        totalUsers,
        serverEndpoint: this.serverEndpoint,
        serverPort: this.serverPort
      };
    } catch (error) {
      this.logger.error(`Failed to get VPN server status: ${error.message}`);
      throw error;
    }
  }

  /**
   * Log VPN connection event
   */
  async logConnectionEvent(
    userId: string,
    action: 'connect' | 'disconnect' | 'handshake' | 'error',
    clientIP: string,
    vpnIP: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const vpnConfig = await this.vpnConfigModel.findOne({ userId: new Types.ObjectId(userId) });
      if (!vpnConfig) {
        throw new NotFoundException('VPN configuration not found');
      }

      const logEntry = new this.vpnConnectionLogModel({
        userId: new Types.ObjectId(userId),
        vpnConfigId: vpnConfig._id,
        clientIP,
        vpnIP,
        action,
        metadata: metadata || {}
      });

      await logEntry.save();

      // Update last connected timestamp
      if (action === 'connect') {
        vpnConfig.lastConnected = new Date();
        vpnConfig.totalConnections += 1;
        await vpnConfig.save();
      }
    } catch (error) {
      this.logger.error(`Failed to log VPN connection event: ${error.message}`);
      // Don't throw error for logging failures
    }
  }

  /**
   * Get connection logs for a user
   */
  async getUserConnectionLogs(userId: string, limit: number = 50): Promise<VPNConnectionLogDocument[]> {
    return this.vpnConnectionLogModel
      .find({ userId: new Types.ObjectId(userId) })
      .sort({ timestamp: -1 })
      .limit(limit)
      .exec();
  }

  /**
   * Get all connection logs (admin)
   */
  async getAllConnectionLogs(limit: number = 100): Promise<VPNConnectionLogDocument[]> {
    return this.vpnConnectionLogModel
      .find({})
      .populate('userId', 'username email')
      .sort({ timestamp: -1 })
      .limit(limit)
      .exec();
  }

  /**
   * Get setup instructions for different operating systems
   */
  async getSetupInstructions(): Promise<{
    [key: string]: {
      title: string;
      steps: string[];
      recommended?: boolean;
    };
  }> {
    const instructions = {
      windows: {
        title: 'Windows Setup',
        recommended: this.isWindows,
        steps: [
          '1. Download and install WireGuard from https://www.wireguard.com/install/',
          '2. Download your VPN configuration file from the platform',
          '3. Open WireGuard application',
          '4. Click "Add Tunnel" and select your configuration file',
          '5. Click "Activate" to connect to the VPN',
          '6. You should now have access to your isolated machines',
          '7. Alternative: Use "wg-quick up <config-file>" in Command Prompt (if CLI tools installed)'
        ]
      },
      linux: {
        title: 'Linux Setup',
        recommended: this.isLinux,
        steps: [
          '1. Install WireGuard:',
          '   • Ubuntu/Debian: sudo apt update && sudo apt install wireguard',
          '   • CentOS/RHEL: sudo yum install epel-release && sudo yum install wireguard-tools',
          '   • Arch: sudo pacman -S wireguard-tools',
          '2. Download your VPN configuration file from the platform',
          '3. Copy config to WireGuard directory: sudo cp mybox-vpn.conf /etc/wireguard/',
          '4. Start the VPN: sudo wg-quick up mybox-vpn',
          '5. Enable auto-start on boot: sudo systemctl enable wg-quick@mybox-vpn',
          '6. Check connection status: sudo wg show',
          '7. Stop VPN when needed: sudo wg-quick down mybox-vpn'
        ]
      },
      macos: {
        title: 'macOS Setup',
        steps: [
          '1. Install WireGuard from the App Store or https://www.wireguard.com/install/',
          '2. Download your VPN configuration file from the platform',
          '3. Open WireGuard application',
          '4. Click "Add Tunnel from File" and select your configuration',
          '5. Toggle the connection to activate',
          '6. Your machines will be accessible through the VPN tunnel',
          '7. Alternative: Use Homebrew: brew install wireguard-tools'
        ]
      },
      android: {
        title: 'Android Setup',
        steps: [
          '1. Install WireGuard app from Google Play Store',
          '2. Download your VPN configuration file or scan QR code',
          '3. Open WireGuard app and tap "+" to add tunnel',
          '4. Select "Import from file" or "Scan from QR code"',
          '5. Toggle the connection to activate',
          '6. Access your machines through the VPN connection'
        ]
      },
      ios: {
        title: 'iOS Setup',
        steps: [
          '1. Install WireGuard app from the App Store',
          '2. Download your VPN configuration file or scan QR code',
          '3. Open WireGuard app and tap "+" to add tunnel',
          '4. Select "Create from file" or "Create from QR code"',
          '5. Toggle the VPN connection to activate',
          '6. Your isolated machines will be accessible'
        ]
      }
    };

    // Add current OS detection info
    const currentOS = this.isWindows ? 'windows' : this.isLinux ? 'linux' : 'unknown';
    this.logger.log(`Providing setup instructions. Current OS: ${currentOS}`);

    return instructions;
  }
}
