import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UserSchema } from '../schemas/user.schema';
import { TeamSchema } from '../schemas/team.schema';
import { ChallengeSchema } from '../schemas/challenge.schema';
import { ChallengeSubmissionSchema } from '../schemas/challenge-submission.schema';
import { TeamChallengeSolveSchema } from '../schemas/team-challenge-solve.schema';
import { Category, CategorySchema } from '../schemas/category.schema';
import { AdminSettings, AdminSettingsSchema } from '../schemas/admin-settings.schema';
import { AdminInitService } from './admin-init.service';
import { AdminService } from './admin.service';
import { AdminSettingsService } from './admin-settings.service';
import { PublicSettingsService } from './public-settings.service';
import { CategoriesService } from '../categories/categories.service';
import { AdminController } from './admin.controller';
import { PublicSettingsController } from './public-settings.controller';
import { NotificationsModule } from '../notifications/notifications.module';
import { ConfigModule } from '@nestjs/config';
import { MulterModule } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { v4 as uuidv4 } from 'uuid';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'User', schema: UserSchema },
      { name: 'Team', schema: TeamSchema },
      { name: 'Challenge', schema: ChallengeSchema },
      { name: 'ChallengeSubmission', schema: ChallengeSubmissionSchema },
      { name: 'TeamChallengeSolve', schema: TeamChallengeSolveSchema },
      { name: Category.name, schema: CategorySchema },
      { name: AdminSettings.name, schema: AdminSettingsSchema },
    ]),
    NotificationsModule,
    ConfigModule,
    MulterModule.register({
      storage: diskStorage({
        destination: (req, file, cb) => {
          // Use temporary upload path first, files will be moved in the service
          const uploadPath = join(process.cwd(), 'uploads/temp');
          // Create directory if it doesn't exist
          if (!existsSync(uploadPath)) {
            mkdirSync(uploadPath, { recursive: true });
          }
          cb(null, uploadPath);
        },
        filename: (req, file, cb) => {
          // Generate unique filename to prevent collisions
          const uniqueFilename = `${Date.now()}-${uuidv4()}${extname(file.originalname)}`;
          cb(null, uniqueFilename);
        },
      }),
      fileFilter: (req, file, cb) => {
        // Add file type validation for security
        const allowedMimeTypes = [
          'text/plain',
          'application/zip',
          'application/x-zip-compressed',
          'application/pdf',
          'image/jpeg',
          'image/png',
          'image/gif',
          'application/octet-stream',
          'text/markdown',
          'text/html',
          'application/json'
        ];
        
        if (allowedMimeTypes.includes(file.mimetype) || file.originalname.match(/\.(txt|zip|pdf|jpg|jpeg|png|gif|md|html|json|py|js|ts|c|cpp|java)$/i)) {
          cb(null, true);
        } else {
          cb(new Error('Invalid file type'), false);
        }
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB max file size
      },
    }),
  ],
  providers: [AdminInitService, AdminService, AdminSettingsService, PublicSettingsService, CategoriesService],
  controllers: [AdminController, PublicSettingsController],
  exports: [PublicSettingsService, AdminSettingsService],
})
export class AdminModule {}