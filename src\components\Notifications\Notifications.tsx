import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, 
  <PERSON>Off, 
  Trophy, 
  MessageSquare, 
  AlertCircle, 
  Info, 
  Users, 
  Calendar,
  Filter,
  Check,
  CheckCheck,
  Trash2,
  Search,
  RefreshCw,
  Settings,
  Volume2
} from 'lucide-react';
import { useNotifications } from '../../contexts/NotificationsContext';
import { Notification } from '../../services/notifications';
import { NotificationSettings } from './NotificationSettings';

const priorityColors = {
  low: 'text-gray-400 bg-gray-500/20 border-gray-500/30',
  medium: 'text-blue-400 bg-blue-500/20 border-blue-500/30',
  high: 'text-orange-400 bg-orange-500/20 border-orange-500/30',
  urgent: 'text-red-400 bg-red-500/20 border-red-500/30'
};

const typeIcons = {
  admin_message: MessageSquare,
  first_blood: Trophy,
  challenge_solved: CheckCheck,
  system_announcement: AlertCircle,
  team_invitation: Users,
  competition_update: Info
};

const typeColors = {
  admin_message: 'text-purple-400 bg-purple-500/20',
  first_blood: 'text-yellow-400 bg-yellow-500/20',
  challenge_solved: 'text-green-400 bg-green-500/20',
  system_announcement: 'text-blue-400 bg-blue-500/20',
  team_invitation: 'text-pink-400 bg-pink-500/20',
  competition_update: 'text-cyan-400 bg-cyan-500/20'
};

export function Notifications() {
  const { 
    notifications, 
    unreadCount, 
    loading, 
    markAsRead, 
    markAllAsRead, 
    loadNotifications 
  } = useNotifications();
  
  const [filter, setFilter] = useState<{
    type: string;
    priority: string;
    unreadOnly: boolean;
  }>({
    type: '',
    priority: '',
    unreadOnly: false
  });
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [showSettings, setShowSettings] = useState(false);

  // Filter notifications based on current filters
  const filteredNotifications = notifications.filter(notification => {
    if (filter.type && notification.type !== filter.type) return false;
    if (filter.priority && notification.priority !== filter.priority) return false;
    if (filter.unreadOnly && notification.isRead) return false;
    if (searchTerm && !notification.title.toLowerCase().includes(searchTerm.toLowerCase()) && 
        !notification.message.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    return true;
  });

  const handleNotificationClick = async (notification: Notification) => {
    if (!notification.isRead) {
      await markAsRead([notification._id]);
    }
    
    // Navigate to action URL if provided
    if (notification.metadata.actionUrl) {
      window.location.href = notification.metadata.actionUrl;
    }
  };

  const handleSelectNotification = (notificationId: string) => {
    setSelectedNotifications(prev => 
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    );
  };

  const handleMarkSelectedAsRead = async () => {
    if (selectedNotifications.length > 0) {
      await markAsRead(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Show settings view if settings is active
  if (showSettings) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900 p-6">
        {/* Animated Background */}
        <div className="fixed inset-0 pointer-events-none">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-pink-900/20"></div>
          
          {/* Floating geometric shapes */}
          <div className="absolute inset-0 overflow-hidden">
            <motion.div 
              className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full"
              animate={{ 
                y: [0, -20, 0],
                rotate: [0, 180, 360],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div 
              className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg"
              animate={{ 
                y: [0, 15, 0],
                rotate: [0, -180, -360],
                scale: [1, 0.9, 1]
              }}
              transition={{ 
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1
              }}
            />
            <motion.div 
              className="absolute bottom-20 left-1/3 w-20 h-20 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-full"
              animate={{ 
                y: [0, -25, 0],
                x: [0, 10, 0],
                scale: [1, 1.2, 1]
              }}
              transition={{ 
                duration: 7,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2
              }}
            />
          </div>
          
          {/* Gradient overlay for depth */}
          <div className="absolute inset-0 bg-gradient-to-t from-purple-900/20 via-transparent to-purple-900/20" />
        </div>

        <div className="relative z-10 max-w-6xl mx-auto">
          {/* Settings Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
                  Notification Settings
                </h1>
                <p className="text-slate-400 mt-2">
                  Customize your notification preferences and sounds
                </p>
              </div>
              
              <button
                onClick={() => setShowSettings(false)}
                className="flex items-center space-x-2 px-4 py-2 bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 transition-colors"
              >
                <Bell className="w-4 h-4" />
                <span>Back to Notifications</span>
              </button>
            </div>
          </motion.div>

          {/* Settings Content */}
          <NotificationSettings />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900 p-6">
      {/* Animated Background */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-pink-900/20"></div>
        
        {/* Floating geometric shapes */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div 
            className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full"
            animate={{ 
              y: [0, -20, 0],
              rotate: [0, 180, 360],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div 
            className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg"
            animate={{ 
              y: [0, 15, 0],
              rotate: [0, -180, -360],
              scale: [1, 0.9, 1]
            }}
            transition={{ 
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />
          <motion.div 
            className="absolute bottom-20 left-1/3 w-20 h-20 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-full"
            animate={{ 
              y: [0, -25, 0],
              x: [0, 10, 0],
              scale: [1, 1.2, 1]
            }}
            transition={{ 
              duration: 7,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />
        </div>
        
        {/* Gradient overlay for depth */}
        <div className="absolute inset-0 bg-gradient-to-t from-purple-900/20 via-transparent to-purple-900/20" />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
                Notifications
              </h1>
              <p className="text-slate-400 mt-2">
                Stay updated with the latest announcements and achievements
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 px-4 py-2 bg-purple-500/20 border border-purple-500/30 rounded-lg">
                <Bell className="w-5 h-5 text-purple-400" />
                <span className="text-purple-400 font-medium">
                  {unreadCount} unread
                </span>
              </div>
              
              <button
                onClick={() => setShowSettings(!showSettings)}
                className={`p-2 border rounded-lg transition-colors ${
                  showSettings 
                    ? 'bg-purple-600/20 border-purple-500/50 text-purple-400' 
                    : 'bg-slate-800/50 border-slate-700/50 text-slate-400 hover:bg-slate-700/50'
                }`}
                title="Notification Settings"
              >
                <Settings className="w-5 h-5" />
              </button>
              
              <button
                onClick={() => loadNotifications()}
                disabled={loading}
                className="p-2 bg-slate-800/50 border border-slate-700/50 rounded-lg hover:bg-slate-700/50 transition-colors"
              >
                <RefreshCw className={`w-5 h-5 text-slate-400 ${loading ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>

          {/* Filters and Actions */}
          <div className="flex flex-wrap items-center gap-4 p-4 bg-slate-800/30 backdrop-blur-sm border border-slate-700/50 rounded-xl">
            {/* Search */}
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <input
                  type="text"
                  placeholder="Search notifications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-slate-800/50 border border-slate-700/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-purple-400 focus:ring-1 focus:ring-purple-400"
                />
              </div>
            </div>

            {/* Type Filter */}
            <select
              value={filter.type}
              onChange={(e) => setFilter(prev => ({ ...prev, type: e.target.value }))}
              className="px-3 py-2 bg-slate-800/50 border border-slate-700/50 rounded-lg text-white focus:outline-none focus:border-purple-400"
            >
              <option value="">All Types</option>
              <option value="admin_message">Admin Messages</option>
              <option value="first_blood">First Blood</option>
              <option value="system_announcement">Announcements</option>
              <option value="team_invitation">Team Invitations</option>
              <option value="competition_update">Competition Updates</option>
            </select>

            {/* Priority Filter */}
            <select
              value={filter.priority}
              onChange={(e) => setFilter(prev => ({ ...prev, priority: e.target.value }))}
              className="px-3 py-2 bg-slate-800/50 border border-slate-700/50 rounded-lg text-white focus:outline-none focus:border-purple-400"
            >
              <option value="">All Priorities</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>

            {/* Unread Only Toggle */}
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filter.unreadOnly}
                onChange={(e) => setFilter(prev => ({ ...prev, unreadOnly: e.target.checked }))}
                className="w-4 h-4 text-purple-600 bg-slate-800 border-slate-600 rounded focus:ring-purple-500"
              />
              <span className="text-slate-300 text-sm">Unread only</span>
            </label>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              {selectedNotifications.length > 0 && (
                <button
                  onClick={handleMarkSelectedAsRead}
                  className="flex items-center space-x-2 px-3 py-2 bg-green-600/20 text-green-400 border border-green-600/30 rounded-lg hover:bg-green-600/30 transition-colors"
                >
                  <Check className="w-4 h-4" />
                  <span>Mark Read</span>
                </button>
              )}
              
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="flex items-center space-x-2 px-3 py-2 bg-blue-600/20 text-blue-400 border border-blue-600/30 rounded-lg hover:bg-blue-600/30 transition-colors"
                >
                  <CheckCheck className="w-4 h-4" />
                  <span>Mark All Read</span>
                </button>
              )}
            </div>
          </div>
        </motion.div>

        {/* Notifications List */}
        <div className="space-y-4">
          <AnimatePresence>
            {filteredNotifications.length === 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-12"
              >
                <BellOff className="w-16 h-16 text-slate-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-slate-400 mb-2">No notifications found</h3>
                <p className="text-slate-500">
                  {searchTerm || filter.type || filter.priority || filter.unreadOnly
                    ? 'Try adjusting your filters'
                    : 'You\'re all caught up!'}
                </p>
              </motion.div>
            ) : (
              filteredNotifications.map((notification, index) => {
                const TypeIcon = typeIcons[notification.type] || Bell;
                
                return (
                  <motion.div
                    key={notification._id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                    className={`group relative p-6 bg-slate-800/30 backdrop-blur-sm border rounded-xl transition-all duration-300 cursor-pointer ${
                      notification.isRead 
                        ? 'border-slate-700/50 hover:border-slate-600/50' 
                        : 'border-purple-500/30 hover:border-purple-400/50 bg-purple-500/5'
                    } ${selectedNotifications.includes(notification._id) ? 'ring-2 ring-purple-400/50' : ''}`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    {/* Selection Checkbox */}
                    <div className="absolute top-4 left-4">
                      <input
                        type="checkbox"
                        checked={selectedNotifications.includes(notification._id)}
                        onChange={(e) => {
                          e.stopPropagation();
                          handleSelectNotification(notification._id);
                        }}
                        className="w-4 h-4 text-purple-600 bg-slate-800 border-slate-600 rounded focus:ring-purple-500"
                      />
                    </div>

                    <div className="ml-8">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg ${typeColors[notification.type]}`}>
                            <TypeIcon className="w-5 h-5" />
                          </div>
                          
                          <div>
                            <h3 className="font-semibold text-white group-hover:text-purple-400 transition-colors">
                              {notification.title}
                            </h3>
                            <div className="flex items-center space-x-2 mt-1">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full border ${priorityColors[notification.priority]}`}>
                                {notification.priority.toUpperCase()}
                              </span>
                              <span className="text-xs text-slate-500">
                                {formatDate(notification.createdAt)}
                              </span>
                              {!notification.isRead && (
                                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                              )}
                            </div>
                          </div>
                        </div>

                        {notification.metadata.icon && (
                          <div className="text-2xl">
                            {notification.metadata.icon}
                          </div>
                        )}
                      </div>

                      <p className="text-slate-300 mb-4 leading-relaxed">
                        {notification.message}
                      </p>

                      {/* Metadata */}
                      {(notification.metadata.challengeName || notification.metadata.points || notification.metadata.username) && (
                        <div className="flex flex-wrap items-center gap-3 text-sm">
                          {notification.metadata.challengeName && (
                            <div className="flex items-center space-x-1 text-blue-400">
                              <Trophy className="w-4 h-4" />
                              <span>{notification.metadata.challengeName}</span>
                            </div>
                          )}
                          {notification.metadata.points && (
                            <div className="flex items-center space-x-1 text-green-400">
                              <span className="font-medium">{notification.metadata.points} points</span>
                            </div>
                          )}
                          {notification.metadata.username && (
                            <div className="flex items-center space-x-1 text-purple-400">
                              <Users className="w-4 h-4" />
                              <span>{notification.metadata.username}</span>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Sender Info */}
                      {notification.sender && (
                        <div className="mt-3 pt-3 border-t border-slate-700/50">
                          <div className="flex items-center space-x-2 text-sm text-slate-400">
                            <MessageSquare className="w-4 h-4" />
                            <span>From: {notification.sender.username}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                );
              })
            )}
          </AnimatePresence>
        </div>

        {/* Load More Button */}
        {filteredNotifications.length > 0 && filteredNotifications.length % 20 === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center mt-8"
          >
            <button
              onClick={() => loadNotifications({ page: Math.floor(filteredNotifications.length / 20) + 1 })}
              disabled={loading}
              className="px-6 py-3 bg-purple-600/20 text-purple-400 border border-purple-600/30 rounded-lg hover:bg-purple-600/30 transition-colors disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Load More'}
            </button>
          </motion.div>
        )}
      </div>
    </div>
  );
}