import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';

// Schemas
import { VPNConfig, VPNConfigSchema } from '../schemas/vpn-config.schema';
import { MachineNetwork, MachineNetworkSchema } from '../schemas/machine-network.schema';
import { VPNConnectionLog, VPNConnectionLogSchema } from '../schemas/vpn-connection-log.schema';
import { MachineTemplate, MachineTemplateSchema } from '../schemas/machine-template.schema';
import { MachineInstance, MachineInstanceSchema } from '../schemas/machine-instance.schema';

// Services
import { VPNConfigService } from './services/vpn-config.service';
import { NetworkIsolationService } from './services/network-isolation.service';
import { MachineVPNIntegrationService } from './services/machine-vpn-integration.service';

// Controllers
import { WireGuardController } from './controllers/wireguard.controller';
import { WireGuardAdminController } from './controllers/wireguard-admin.controller';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      { name: VPNConfig.name, schema: VPNConfigSchema },
      { name: MachineNetwork.name, schema: MachineNetworkSchema },
      { name: VPNConnectionLog.name, schema: VPNConnectionLogSchema },
      { name: MachineTemplate.name, schema: MachineTemplateSchema },
      { name: MachineInstance.name, schema: MachineInstanceSchema },
    ]),
  ],
  controllers: [
    WireGuardController,
    WireGuardAdminController,
  ],
  providers: [
    VPNConfigService,
    NetworkIsolationService,
    MachineVPNIntegrationService,
  ],
  exports: [
    VPNConfigService,
    NetworkIsolationService,
    MachineVPNIntegrationService,
  ],
})
export class VpnModule {}
