import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';

// Schemas
import { VPNConfig, VPNConfigSchema } from '../schemas/vpn-config.schema';
import { MachineNetwork, MachineNetworkSchema } from '../schemas/machine-network.schema';
import { VPNConnectionLog, VPNConnectionLogSchema } from '../schemas/vpn-connection-log.schema';
import { MachineTemplate, MachineTemplateSchema } from '../schemas/machine-template.schema';
import { MachineInstance, MachineInstanceSchema } from '../schemas/machine-instance.schema';

// Services
import { VpnService } from './vpn.service';
import { VPNConfigService } from './services/vpn-config.service';
import { NetworkIsolationService } from './services/network-isolation.service';
import { MachineVPNIntegrationService } from './services/machine-vpn-integration.service';

// Controllers
import { VpnController } from './vpn.controller';
import { WireGuardController } from './controllers/wireguard.controller';
import { WireGuardAdminController } from './controllers/wireguard-admin.controller';

@Module({
  imports: [
    HttpModule,
    ConfigModule,
    MongooseModule.forFeature([
      { name: VPNConfig.name, schema: VPNConfigSchema },
      { name: MachineNetwork.name, schema: MachineNetworkSchema },
      { name: VPNConnectionLog.name, schema: VPNConnectionLogSchema },
      { name: MachineTemplate.name, schema: MachineTemplateSchema },
      { name: MachineInstance.name, schema: MachineInstanceSchema },
    ]),
  ],
  controllers: [
    VpnController, // Keep existing controller
    WireGuardController,
    WireGuardAdminController,
  ],
  providers: [
    VpnService,
    VPNConfigService,
    NetworkIsolationService,
    MachineVPNIntegrationService,
  ],
  exports: [
    VpnService,
    VPNConfigService,
    NetworkIsolationService,
    MachineVPNIntegrationService,
  ],
})
export class VpnModule {}
