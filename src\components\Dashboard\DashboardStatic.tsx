import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useApp } from '../../contexts/AppContext';
import { StatsCard } from './StatsCard';
import { RecentActivity } from './RecentActivity';
import { QuickActions } from './QuickActions';
import { EventHeader } from './EventHeader';
import { SocialLinks } from './SocialLinks';
import { SponsorsSection } from './SponsorsSection';
import { DashboardConfig, dashboardService } from '../../services/dashboard';
import { Users, Trophy, Monitor, Target, Sparkles, Zap, Star, Calendar, Clock } from 'lucide-react';

export function Dashboard() {
  const { state } = useApp();
  const [mounted, setMounted] = useState(false);
  const [dashboardConfig, setDashboardConfig] = useState<DashboardConfig | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setMounted(true);
    loadDashboardConfig();
  }, []);

  const loadDashboardConfig = async () => {
    try {
      const config = await dashboardService.getDashboardConfig();
      setDashboardConfig(config);
    } catch (error) {
      console.error('Failed to load dashboard configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-3 text-purple-200/80">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500/30 border-t-purple-400"></div>
          <span className="text-lg">Loading dashboard...</span>
        </div>
      </div>
    );
  }

  const eventStatus = dashboardConfig ? dashboardService.getEventStatus(
    dashboardConfig.eventStartDate,
    dashboardConfig.eventEndDate
  ) : 'active';

  return (
    <div className="space-y-12 relative">
      {/* Event Header */}
      {dashboardConfig && (
        <EventHeader 
          config={dashboardConfig}
          eventStatus={eventStatus}
          mounted={mounted}
        />
      )}

      {/* Welcome Section */}
      <div className={`text-center relative ${mounted ? 'animate-slide-in-up' : 'opacity-0'}`}>
        <div className="relative inline-block">
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent neon-text mb-4">
            Welcome back, {state.auth.user?.username}!
          </h1>
          <div className="absolute -top-4 -right-4">
            <Sparkles className="w-8 h-8 text-yellow-400 animate-bounce" />
          </div>
          <div className="absolute -bottom-2 -left-4">
            <Zap className="w-6 h-6 text-blue-400 animate-pulse" />
          </div>
        </div>
        
        {/* Event Message */}
        {dashboardConfig?.eventMessage && (
          <motion.p 
            className="text-xl text-purple-200/80 mb-8 animate-slide-in-up stagger-1 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            {dashboardConfig.eventMessage}
          </motion.p>
        )}
        
        {/* Event Status */}
        {dashboardConfig?.eventStartDate && dashboardConfig?.eventEndDate && (
          <motion.div 
            className="flex justify-center items-center space-x-6 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5 text-purple-400" />
              <span className="text-purple-200">
                {dashboardService.formatEventDate(dashboardConfig.eventStartDate)} - {dashboardService.formatEventDate(dashboardConfig.eventEndDate)}
              </span>
            </div>
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${
              eventStatus === 'active' ? 'bg-green-500/20 text-green-400' :
              eventStatus === 'upcoming' ? 'bg-yellow-500/20 text-yellow-400' :
              'bg-red-500/20 text-red-400'
            }`}>
              <Clock className="w-4 h-4" />
              <span className="text-sm font-medium capitalize">{eventStatus}</span>
            </div>
          </motion.div>
        )}
        
        {/* Animated status indicators */}
        <div className="flex justify-center space-x-6 animate-slide-in-up stagger-2">
          {[
            { label: 'Online', color: 'bg-green-400', count: '1,247' },
            { label: 'Solving', color: 'bg-yellow-400', count: '89' },
            { label: 'Elite', color: 'bg-purple-400', count: '156' }
          ].map((status, index) => (
            <div key={status.label} className="flex items-center space-x-2">
              <div className={`w-3 h-3 ${status.color} rounded-full animate-pulse`} />
              <span className="text-sm text-purple-200">{status.count} {status.label}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Stats Grid */}
      {(!dashboardConfig?.features || dashboardConfig.features.showStats) && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Your Score"
            value={state.auth.user?.score || 0}
            icon={Trophy}
            color="purple"
            trend={{ value: 12, isPositive: true }}
            delay={100}
          />
          <StatsCard
            title="Your Rank"
            value={`#${state.auth.user?.rank || 0}`}
            icon={Target}
            color="pink"
            trend={{ value: 3, isPositive: true }}
            delay={200}
          />
          <StatsCard
            title="Challenges Solved"
            value={state.challenges.filter(c => c.isSolved).length}
            icon={Star}
            color="blue"
            delay={300}
          />
          <StatsCard
            title="Active Machines"
            value={state.vms.filter(vm => vm.status === 'running').length}
            icon={Monitor}
            color="indigo"
            delay={400}
          />
        </div>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {(!dashboardConfig?.features || dashboardConfig.features.showRecentActivity) && (
          <div className="lg:col-span-2 animate-slide-in-left">
            <RecentActivity />
          </div>
        )}
        {(!dashboardConfig?.features || dashboardConfig.features.showQuickActions) && (
          <div className="animate-slide-in-right">
            <QuickActions />
          </div>
        )}
      </div>

      {/* Global Stats */}
      {(!dashboardConfig?.features || dashboardConfig.features.showStats) && (
        <div className="animate-slide-in-up stagger-3">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2">
              Platform Statistics
            </h2>
            <p className="text-purple-200/70">Real-time insights from the {dashboardConfig?.eventName || 'MyBox'} community</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatsCard
              title="Total Users"
              value={state.stats.totalUsers}
              icon={Users}
              color="purple"
              delay={500}
            />
            <StatsCard
              title="Total Challenges"
              value={state.stats.totalChallenges}
              icon={Target}
              color="pink"
              delay={600}
            />
            <StatsCard
              title="Active VMs"
              value={state.stats.activeVMs}
              icon={Monitor}
              color="blue"
              delay={700}
            />
            <StatsCard
              title="Total Solves"
              value={state.stats.totalSolves}
              icon={Trophy}
              color="indigo"
              delay={800}
            />
          </div>
        </div>
      )}

      {/* Social Links */}
      {dashboardConfig?.socialLinks && (
        <SocialLinks socialLinks={dashboardConfig.socialLinks} />
      )}

      {/* Sponsors Section */}
      {dashboardConfig?.sponsors && dashboardConfig.sponsors.length > 0 && 
       (!dashboardConfig?.features || dashboardConfig.features.showSponsors) && (
        <SponsorsSection sponsors={dashboardConfig.sponsors} />
      )}

      {/* Floating decorative elements */}
      <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full morph-shape animate-float blur-xl" />
      <div className="absolute bottom-20 left-10 w-24 h-24 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full morph-shape animate-float blur-xl" style={{ animationDelay: '3s' }} />
    </div>
  );
}