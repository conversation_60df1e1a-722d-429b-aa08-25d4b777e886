@echo off
if "%~1"=="" (
    echo Usage: %~n0 ^<username^>
    exit /b 1
)

set USERNAME=%~1
set EASY_RSA=C:\Program Files\OpenVPN\easy-rsa
set CONFIG_PATH=C:\Program Files\OpenVPN\config

:: Generate client certificate
echo Generating client certificate for %USERNAME%...
cd /d "%EASY_RSA%"
call vars.bat
call easyrsa build-client-full %USERNAME% nopass

:: Create client config file
echo Creating client configuration...
(
echo client
echo dev tun
echo proto udp
echo remote YOUR_VPN_SERVER_IP 1194
echo resolv-retry infinite
echo nobind
echo persist-key
echo persist-tun
echo remote-cert-tls server
echo cipher AES-256-CBC
echo verb 3
echo ^<ca^
) > "%CONFIG_PATH%\%USERNAME%.ovpn"

type "%EASY_RSA%\pki\ca.crt" >> "%CONFIG_PATH%\%USERNAME%.ovpn"
(
echo ^</ca^
echo ^<cert^
) >> "%CONFIG_PATH%\%USERNAME%.ovpn"

openssl x509 -in "%EASY_RSA%\pki\issued\%USERNAME%.crt" >> "%CONFIG_PATH%\%USERNAME%.ovpn"
(
echo ^</cert^
echo ^<key^
) >> "%CONFIG_PATH%\%USERNAME%.ovpn"

type "%EASY_RSA%\pki\private\%USERNAME%.key" >> "%CONFIG_PATH%\%USERNAME%.ovpn"
(
echo ^</key^
echo ^<tls-auth^
) >> "%CONFIG_PATH%\%USERNAME%.ovpn"

type "%EASY_RSA%\ta.key" >> "%CONFIG_PATH%\%USERNAME%.ovpn"
echo ^</tls-auth^
echo key-direction 1
) >> "%CONFIG_PATH%\%USERNAME%.ovpn"

echo Client configuration created at %CONFIG_PATH%\%USERNAME%.ovpn
