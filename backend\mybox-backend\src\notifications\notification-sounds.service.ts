import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { NotificationSound, NotificationSoundDocument } from '../schemas/notification-sound.schema';
import { UserNotificationSettings, UserNotificationSettingsDocument } from '../schemas/user-notification-settings.schema';
import { User } from '../schemas/user.schema';
import { CreateNotificationSoundDto, UpdateNotificationSoundDto, UpdateUserNotificationSettingsDto } from './dto/notification-sound.dto';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class NotificationSoundsService {
  constructor(
    @InjectModel(NotificationSound.name) private notificationSoundModel: Model<NotificationSoundDocument>,
    @InjectModel(UserNotificationSettings.name) private userSettingsModel: Model<UserNotificationSettingsDocument>,
    @InjectModel(User.name) private userModel: Model<User>,
  ) {}

  async createNotificationSound(
    createDto: CreateNotificationSoundDto,
    file: Express.Multer.File,
    uploadedBy: string
  ): Promise<NotificationSound> {
    // Validate file type
    const allowedMimeTypes = ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp3'];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException('Invalid file type. Only MP3, WAV, and OGG files are allowed.');
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new BadRequestException('File size too large. Maximum size is 5MB.');
    }

    // Check if name already exists
    const existingSound = await this.notificationSoundModel.findOne({ name: createDto.name });
    if (existingSound) {
      throw new BadRequestException('A notification sound with this name already exists.');
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads', 'notification-sounds');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // Generate unique filename
    const fileExtension = path.extname(file.originalname);
    const fileName = `${createDto.name}-${Date.now()}${fileExtension}`;
    const filePath = path.join(uploadsDir, fileName);

    // Save file to disk
    if (!file.buffer) {
      throw new BadRequestException('File buffer is empty. Please try uploading the file again.');
    }
    fs.writeFileSync(filePath, file.buffer);

    // Get audio duration (simplified - in production, use a proper audio library)
    const duration = await this.getAudioDuration(filePath);

    // If this is set as default, remove default from other sounds
    if (createDto.isDefault) {
      await this.notificationSoundModel.updateMany(
        { isDefault: true },
        { isDefault: false }
      );
    }

    const notificationSound = new this.notificationSoundModel({
      ...createDto,
      fileName,
      filePath: `/uploads/notification-sounds/${fileName}`,
      fileSize: file.size,
      duration,
      uploadedBy: new Types.ObjectId(uploadedBy),
    });

    const savedSound = await notificationSound.save();

    // If this is the first sound or set as default, update all users without a selected sound
    if (createDto.isDefault || await this.notificationSoundModel.countDocuments() === 1) {
      await this.userSettingsModel.updateMany(
        { selectedSoundId: { $exists: false } },
        { selectedSoundId: savedSound._id }
      );
    }

    return savedSound;
  }

  async getAllNotificationSounds(): Promise<NotificationSound[]> {
    return await this.notificationSoundModel
      .find({ isActive: true })
      .populate('uploadedBy', 'username')
      .sort({ isDefault: -1, createdAt: -1 });
  }

  async getNotificationSoundById(id: string): Promise<NotificationSound> {
    const sound = await this.notificationSoundModel
      .findById(id)
      .populate('uploadedBy', 'username');

    if (!sound) {
      throw new NotFoundException('Notification sound not found');
    }

    return sound;
  }

  async updateNotificationSound(
    id: string,
    updateDto: UpdateNotificationSoundDto,
    userId: string
  ): Promise<NotificationSound> {
    const sound = await this.notificationSoundModel.findById(id);
    if (!sound) {
      throw new NotFoundException('Notification sound not found');
    }

    // Check if user is admin or the uploader
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.role !== 'admin' && sound.uploadedBy.toString() !== userId) {
      throw new ForbiddenException('You can only update your own notification sounds');
    }

    // If setting as default, remove default from other sounds
    if (updateDto.isDefault) {
      await this.notificationSoundModel.updateMany(
        { _id: { $ne: id }, isDefault: true },
        { isDefault: false }
      );
    }

    Object.assign(sound, updateDto);
    return await sound.save();
  }

  async deleteNotificationSound(id: string, userId: string): Promise<void> {
    const sound = await this.notificationSoundModel.findById(id);
    if (!sound) {
      throw new NotFoundException('Notification sound not found');
    }

    // Check if user is admin or the uploader
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.role !== 'admin' && sound.uploadedBy.toString() !== userId) {
      throw new ForbiddenException('You can only delete your own notification sounds');
    }

    // Don't allow deletion if it's the only sound or if users are using it
    const totalSounds = await this.notificationSoundModel.countDocuments({ isActive: true });
    if (totalSounds <= 1) {
      throw new BadRequestException('Cannot delete the last notification sound');
    }

    const usersUsingSound = await this.userSettingsModel.countDocuments({ selectedSoundId: id });
    if (usersUsingSound > 0) {
      // Move users to default sound
      const defaultSound = await this.notificationSoundModel.findOne({ isDefault: true });
      if (defaultSound) {
        await this.userSettingsModel.updateMany(
          { selectedSoundId: id },
          { selectedSoundId: defaultSound._id }
        );
      }
    }

    // Delete file from disk
    if (fs.existsSync(sound.filePath)) {
      fs.unlinkSync(sound.filePath);
    }

    await this.notificationSoundModel.findByIdAndDelete(id);
  }

  async getUserNotificationSettings(userId: string): Promise<UserNotificationSettings> {
    let settings = await this.userSettingsModel
      .findOne({ userId: new Types.ObjectId(userId) })
      .populate('selectedSoundId');

    if (!settings) {
      // Create default settings for user
      const defaultSound = await this.notificationSoundModel.findOne({ isDefault: true });
      
      settings = new this.userSettingsModel({
        userId: new Types.ObjectId(userId),
        selectedSoundId: defaultSound?._id,
      });
      
      await settings.save();
      await settings.populate('selectedSoundId');
    }

    return settings;
  }

  async updateUserNotificationSettings(
    userId: string,
    updateDto: UpdateUserNotificationSettingsDto
  ): Promise<UserNotificationSettings> {
    let settings = await this.userSettingsModel.findOne({ userId: new Types.ObjectId(userId) });

    if (!settings) {
      settings = new this.userSettingsModel({
        userId: new Types.ObjectId(userId),
        ...updateDto,
        selectedSoundId: updateDto.selectedSoundId ? new Types.ObjectId(updateDto.selectedSoundId) : undefined,
      });
    } else {
      Object.assign(settings, updateDto);
      if (updateDto.selectedSoundId) {
        settings.selectedSoundId = new Types.ObjectId(updateDto.selectedSoundId);
      }
    }

    await settings.save();
    await settings.populate('selectedSoundId');
    return settings;
  }

  async incrementSoundUsage(soundId: string): Promise<void> {
    await this.notificationSoundModel.findByIdAndUpdate(
      soundId,
      { $inc: { usageCount: 1 } }
    );
  }

  async getNotificationSoundStats(): Promise<{
    totalSounds: number;
    totalUsers: number;
    soundUsageStats: Array<{ soundName: string; usageCount: number; userCount: number }>;
  }> {
    const [totalSounds, totalUsers, sounds, userSettings] = await Promise.all([
      this.notificationSoundModel.countDocuments({ isActive: true }),
      this.userSettingsModel.countDocuments(),
      this.notificationSoundModel.find({ isActive: true }).lean(),
      this.userSettingsModel.find().populate('selectedSoundId').lean(),
    ]);

    const soundUsageStats = sounds.map((sound: any) => {
      const userCount = userSettings.filter(
        (setting: any) => setting.selectedSoundId?._id?.toString() === sound._id.toString()
      ).length;

      return {
        soundName: sound.displayName,
        usageCount: sound.usageCount,
        userCount,
      };
    });

    return {
      totalSounds,
      totalUsers,
      soundUsageStats,
    };
  }

  private async getAudioDuration(filePath: string): Promise<number> {
    // Simplified duration calculation
    // In production, use a proper audio library like node-ffmpeg or music-metadata
    try {
      const stats = fs.statSync(filePath);
      // Rough estimation: 1MB ≈ 60 seconds for average quality audio
      return Math.round((stats.size / (1024 * 1024)) * 60);
    } catch (error) {
      return 5; // Default 5 seconds if calculation fails
    }
  }
}