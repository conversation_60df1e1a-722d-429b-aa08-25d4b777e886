import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { DashboardConfig, DashboardConfigDocument } from '../schemas/dashboard-config.schema';
import { CreateDashboardConfigDto, UpdateDashboardConfigDto, CreateSponsorDto, UpdateSponsorDto } from './dto/dashboard-config.dto';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class DashboardService {
  constructor(
    @InjectModel(DashboardConfig.name) private dashboardConfigModel: Model<DashboardConfigDocument>,
  ) {}

  async getDashboardConfig(): Promise<DashboardConfigDocument> {
    let config = await this.dashboardConfigModel.findOne({ configId: 'default' });
    
    if (!config) {
      // Create default configuration if it doesn't exist
      try {
        config = await this.dashboardConfigModel.create({
          configId: 'default',
          eventName: 'Rakcha Pentest v2 CTF Platform',
          eventDescription: 'Welcome to the ultimate cybersecurity challenge platform',
          eventMessage: 'Test your skills, learn new techniques, and compete with the best!',
          eventCoverImage: '',
          socialLinks: {
            github: '',
            facebook: '',
            instagram: '',
            linkedin: '',
            twitter: '',
            discord: '',
            website: '',
          },
          sponsors: [],
          theme: {
            primaryColor: '#8b5cf6',
            secondaryColor: '#ec4899',
            accentColor: '#06b6d4',
            backgroundColor: '#0f0f23',
          },
          features: {
            showStats: true,
            showRecentActivity: true,
            showQuickActions: true,
            showLeaderboard: true,
            showAnnouncements: true,
            showSponsors: true,
          },
          updatedBy: new Types.ObjectId('000000000000000000000000'), // Default admin ID
          isActive: true,
        });
      } catch (error) {
        console.error('Error creating default dashboard config:', error);
        throw error;
      }
    }
    
    return config;
  }

  async updateDashboardConfig(
    updateDto: UpdateDashboardConfigDto,
    userId: string
  ): Promise<DashboardConfigDocument> {
    const config = await this.dashboardConfigModel.findOne({ configId: 'default' });
    
    if (!config) {
      // Create default config first if it doesn't exist
      const newConfig = await this.getDashboardConfig();
      // Update the newly created config
      Object.assign(newConfig, updateDto);
      newConfig.updatedBy = new Types.ObjectId(userId);
      return await newConfig.save();
    }

    // Update the existing configuration
    Object.assign(config, updateDto);
    config.updatedBy = new Types.ObjectId(userId);
    
    return await config.save();
  }

  async uploadCoverImage(file: Express.Multer.File, userId: string): Promise<{ imageUrl: string }> {
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads', 'dashboard');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = file.originalname.split('.').pop();
    const filename = `cover_${timestamp}.${fileExtension}`;
    const filePath = path.join(uploadsDir, filename);
    
    // Save file to disk
    fs.writeFileSync(filePath, file.buffer);
    
    // Return URL path
    const imageUrl = `/uploads/dashboard/${filename}`;
    
    // Update the dashboard config with the new cover image
    await this.updateDashboardConfig({ eventCoverImage: imageUrl }, userId);
    
    return { imageUrl };
  }

  // Sponsor Management
  async addSponsor(createSponsorDto: CreateSponsorDto, userId: string): Promise<DashboardConfigDocument> {
    try {
      console.log('Adding sponsor with data:', createSponsorDto);
      
      const config = await this.getDashboardConfig();
      
      // Validate required fields
      if (!createSponsorDto.name || !createSponsorDto.logo) {
        throw new BadRequestException('Name and logo are required for sponsor');
      }
      
      const sponsorId = uuidv4();
      const newSponsor = {
        id: sponsorId,
        name: createSponsorDto.name,
        logo: createSponsorDto.logo,
        website: createSponsorDto.website || '',
        description: createSponsorDto.description || '',
        tier: createSponsorDto.tier || 'bronze',
        isActive: createSponsorDto.isActive !== undefined ? createSponsorDto.isActive : true,
        order: createSponsorDto.order !== undefined ? createSponsorDto.order : config.sponsors.length,
      };
      
      console.log('Created sponsor object:', newSponsor);
      
      // Ensure sponsors array exists
      if (!config.sponsors) {
        config.sponsors = [];
      }
      
      config.sponsors.push(newSponsor);
      config.updatedBy = new Types.ObjectId(userId);
      
      console.log('Config sponsors before save:', config.sponsors);
      
      const savedConfig = await config.save();
      console.log('Config saved successfully');
      
      return savedConfig;
    } catch (error) {
      console.error('Error in addSponsor:', error);
      throw error;
    }
  }

  async updateSponsor(updateSponsorDto: UpdateSponsorDto, userId: string): Promise<DashboardConfigDocument> {
    try {
      console.log('Updating sponsor with data:', updateSponsorDto);
      
      const config = await this.getDashboardConfig();
      
      const sponsorIndex = config.sponsors.findIndex(s => s.id === updateSponsorDto.id);
      if (sponsorIndex === -1) {
        throw new NotFoundException('Sponsor not found');
      }
      
      console.log('Found sponsor at index:', sponsorIndex);
      console.log('Current sponsor:', config.sponsors[sponsorIndex]);
      
      // Update sponsor with proper type handling, ensuring ID is preserved
      const updatedSponsor = {
        id: updateSponsorDto.id, // Explicitly preserve the ID
        name: updateSponsorDto.name || config.sponsors[sponsorIndex].name,
        logo: updateSponsorDto.logo || config.sponsors[sponsorIndex].logo,
        website: updateSponsorDto.website !== undefined ? updateSponsorDto.website : config.sponsors[sponsorIndex].website,
        description: updateSponsorDto.description !== undefined ? updateSponsorDto.description : config.sponsors[sponsorIndex].description,
        tier: updateSponsorDto.tier || config.sponsors[sponsorIndex].tier,
        isActive: updateSponsorDto.isActive !== undefined ? updateSponsorDto.isActive : config.sponsors[sponsorIndex].isActive,
        order: updateSponsorDto.order !== undefined ? updateSponsorDto.order : config.sponsors[sponsorIndex].order,
      };
      
      console.log('Updated sponsor object:', updatedSponsor);
      
      config.sponsors[sponsorIndex] = updatedSponsor;
      config.updatedBy = new Types.ObjectId(userId);
      
      console.log('Config sponsors before save:', config.sponsors);
      
      const savedConfig = await config.save();
      console.log('Sponsor updated successfully');
      
      return savedConfig;
    } catch (error) {
      console.error('Error in updateSponsor:', error);
      throw error;
    }
  }

  async deleteSponsor(sponsorId: string, userId: string): Promise<DashboardConfigDocument> {
    const config = await this.getDashboardConfig();
    
    const sponsorIndex = config.sponsors.findIndex(s => s.id === sponsorId);
    if (sponsorIndex === -1) {
      throw new NotFoundException('Sponsor not found');
    }
    
    config.sponsors.splice(sponsorIndex, 1);
    config.updatedBy = new Types.ObjectId(userId);
    
    return await config.save();
  }

  async reorderSponsors(sponsorIds: string[], userId: string): Promise<DashboardConfigDocument> {
    const config = await this.getDashboardConfig();
    
    // Validate that all sponsor IDs exist
    const existingIds = config.sponsors.map(s => s.id);
    const invalidIds = sponsorIds.filter(id => !existingIds.includes(id));
    
    if (invalidIds.length > 0) {
      throw new BadRequestException(`Invalid sponsor IDs: ${invalidIds.join(', ')}`);
    }
    
    // Reorder sponsors with proper type handling
    const reorderedSponsors = sponsorIds.map((id, index) => {
      const sponsor = config.sponsors.find(s => s.id === id);
      if (!sponsor) {
        throw new NotFoundException(`Sponsor with ID ${id} not found`);
      }
      return {
        id: sponsor.id,
        name: sponsor.name,
        logo: sponsor.logo,
        website: sponsor.website,
        description: sponsor.description,
        tier: sponsor.tier,
        isActive: sponsor.isActive,
        order: index,
      };
    });
    
    config.sponsors = reorderedSponsors;
    config.updatedBy = new Types.ObjectId(userId);
    
    return await config.save();
  }

  async uploadTempSponsorLogo(file: Express.Multer.File, userId: string): Promise<{ logoUrl: string }> {
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads', 'temp', 'sponsors');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = file.originalname.split('.').pop();
    const filename = `${timestamp}_${file.originalname}`;
    const filePath = path.join(uploadsDir, filename);
    
    // Save file to disk
    fs.writeFileSync(filePath, file.buffer);
    
    // Return URL path
    const logoUrl = `/uploads/temp/sponsors/${filename}`;
    
    return { logoUrl };
  }

  async uploadSponsorLogo(sponsorId: string, file: Express.Multer.File, userId: string): Promise<{ logoUrl: string }> {
    const config = await this.getDashboardConfig();
    
    const sponsor = config.sponsors.find(s => s.id === sponsorId);
    if (!sponsor) {
      throw new NotFoundException('Sponsor not found');
    }
    
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads', 'sponsors');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = file.originalname.split('.').pop();
    const filename = `${sponsorId}_${timestamp}.${fileExtension}`;
    const filePath = path.join(uploadsDir, filename);
    
    // Save file to disk
    fs.writeFileSync(filePath, file.buffer);
    
    // Return URL path
    const logoUrl = `/uploads/sponsors/${filename}`;
    
    sponsor.logo = logoUrl;
    config.updatedBy = new Types.ObjectId(userId);
    
    await config.save();
    
    return { logoUrl };
  }

  // Analytics and Statistics
  async getDashboardStats(): Promise<any> {
    const config = await this.getDashboardConfig();
    
    return {
      totalSponsors: config.sponsors.length,
      activeSponsors: config.sponsors.filter(s => s.isActive).length,
      sponsorsByTier: {
        platinum: config.sponsors.filter(s => s.tier === 'platinum').length,
        gold: config.sponsors.filter(s => s.tier === 'gold').length,
        silver: config.sponsors.filter(s => s.tier === 'silver').length,
        bronze: config.sponsors.filter(s => s.tier === 'bronze').length,
      },
      lastUpdated: (config as any).updatedAt,
      isActive: config.isActive,
    };
  }
}