import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { AdminSettings } from '../schemas/admin-settings.schema';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as archiver from 'archiver';
import * as extract from 'extract-zip';

const execAsync = promisify(exec);

export interface UpdateAdminSettingsDto {
  allowRegistration?: boolean;
  emailVerification?: boolean;
  maintenanceMode?: boolean;
  enableTeams?: boolean;
  maxTeamSize?: number;
  autoBackup?: boolean;
  backupInterval?: number;
  defaultSessionTime?: number;
  maxConcurrentVMs?: number;
  maxLoginAttempts?: number;
  lockoutDuration?: number;
  minPasswordLength?: number;
  requirePasswordComplexity?: boolean;
  smtpHost?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpPassword?: string;
  smtpSecure?: boolean;
  fromEmail?: string;
  maxFileUploadSize?: number;
  sessionTimeout?: number;
  enableLogging?: boolean;
  logLevel?: string;
  apiRateLimit?: number;
  loginRateLimit?: number;
}

export interface SystemMaintenanceResult {
  success: boolean;
  message: string;
  data?: any;
}

export interface BackupInfo {
  filename: string;
  path: string;
  timestamp: string;
  size: number;
  sizeFormatted: string;
  createdAt: Date;
}

export interface BackupListResult {
  success: boolean;
  message: string;
  backups: BackupInfo[];
}

interface LogFile {
  name: string;
  size: number;
  modified: Date;
}

@Injectable()
export class AdminSettingsService {
  constructor(
    @InjectModel(AdminSettings.name) private adminSettingsModel: Model<AdminSettings>,
  ) {}

  async getAdminSettings(): Promise<AdminSettings> {
    let settings = await this.adminSettingsModel.findOne({ configId: 'default' });
    
    if (!settings) {
      // Create default settings if they don't exist
      try {
        settings = await this.adminSettingsModel.create({
          configId: 'default',
          allowRegistration: true,
          emailVerification: false,
          maintenanceMode: false,
          enableTeams: true,
          maxTeamSize: 5,
          autoBackup: true,
          backupInterval: 24,
          defaultSessionTime: 2,
          maxConcurrentVMs: 1,
          maxLoginAttempts: 3,
          lockoutDuration: 15,
          minPasswordLength: 8,
          requirePasswordComplexity: true,
          smtpHost: '',
          smtpPort: 587,
          smtpUsername: '',
          smtpPassword: '',
          smtpSecure: false,
          fromEmail: '',
          maxFileUploadSize: 1000,
          sessionTimeout: 30,
          enableLogging: true,
          logLevel: 'info',
          apiRateLimit: 100,
          loginRateLimit: 10,
          lastBackup: new Date(),
          totalBackups: 0,
        });
      } catch (error) {
        // If duplicate key error, try to find the existing document
        if (error.code === 11000) {
          console.log('Default admin settings already exist, fetching existing document');
          settings = await this.adminSettingsModel.findOne({ configId: 'default' });
          if (!settings) {
            throw new Error('Failed to create or find default admin settings');
          }
        } else {
          console.error('Error creating default admin settings:', error);
          throw error;
        }
      }
    }
    
    return settings;
  }

  async updateAdminSettings(
    updateDto: UpdateAdminSettingsDto,
    userId: string
  ): Promise<AdminSettings> {
    // Use findOneAndUpdate with upsert to handle both create and update cases
    const settings = await this.adminSettingsModel.findOneAndUpdate(
      { configId: 'default' },
      {
        ...updateDto,
        updatedBy: new Types.ObjectId(userId),
      },
      {
        new: true,
        upsert: true,
        setDefaultsOnInsert: true,
      }
    );
    
    return settings;
  }

  async createBackup(): Promise<SystemMaintenanceResult> {
    try {
      const backupDir = path.join(process.cwd(), 'backups');
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(backupDir, `mybox-backup-${timestamp}.zip`);

      // Create temporary directory for backup preparation
      const tempDir = path.join(backupDir, `temp-${timestamp}`);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      try {
        // 1. Export database collections using Mongoose (cross-platform)
        const dbBackupPath = path.join(tempDir, 'database');
        fs.mkdirSync(dbBackupPath, { recursive: true });

        await this.exportDatabaseCollections(dbBackupPath);

        // 2. Copy uploads directory if it exists (cross-platform)
        const uploadsPath = path.join(process.cwd(), 'uploads');
        if (fs.existsSync(uploadsPath)) {
          const backupUploadsPath = path.join(tempDir, 'uploads');
          await this.copyDirectory(uploadsPath, backupUploadsPath);
        }

        // 3. Create backup metadata
        const metadata = {
          version: '1.0',
          timestamp: new Date().toISOString(),
          platform: 'Rakcha Pentest V2',
          os: process.platform,
          collections: ['users', 'challenges', 'teams', 'machinetemplates', 'machineinstances', 'adminsettings', 'dashboardconfigs'],
          includesUploads: fs.existsSync(uploadsPath)
        };
        fs.writeFileSync(path.join(tempDir, 'backup-metadata.json'), JSON.stringify(metadata, null, 2));

        // 4. Create compressed archive (cross-platform using archiver)
        await this.createZipArchive(tempDir, backupPath);

        // 5. Clean up temporary directory
        await this.removeDirectory(tempDir);

        // Update backup statistics
        const settings = await this.getAdminSettings();
        settings.lastBackup = new Date();
        settings.totalBackups += 1;
        await settings.save();

        const backupSize = fs.existsSync(backupPath) ? fs.statSync(backupPath).size : 0;

        return {
          success: true,
          message: 'Backup created successfully',
          data: {
            filename: path.basename(backupPath),
            backupPath,
            timestamp,
            size: backupSize,
            sizeFormatted: this.formatFileSize(backupSize)
          }
        };
      } catch (error) {
        // Clean up temporary directory on error
        if (fs.existsSync(tempDir)) {
          await this.removeDirectory(tempDir).catch(() => {});
        }
        throw error;
      }
    } catch (error) {
      console.error('Backup creation failed:', error);
      return {
        success: false,
        message: `Backup failed: ${error.message}`
      };
    }
  }

  async listBackups(): Promise<BackupListResult> {
    try {
      const backupDir = path.join(process.cwd(), 'backups');
      if (!fs.existsSync(backupDir)) {
        return {
          success: true,
          message: 'No backups found',
          backups: []
        };
      }

      const files = fs.readdirSync(backupDir);
      const backupFiles = files.filter(file =>
        file.startsWith('mybox-backup-') && (file.endsWith('.tar.gz') || file.endsWith('.zip'))
      );

      const backups: BackupInfo[] = [];

      for (const filename of backupFiles) {
        const filePath = path.join(backupDir, filename);
        const stats = fs.statSync(filePath);

        // Extract timestamp from filename
        const timestampMatch = filename.match(/mybox-backup-(.+)\.tar\.gz/);
        const timestamp = timestampMatch ? timestampMatch[1] : '';

        backups.push({
          filename,
          path: filePath,
          timestamp,
          size: stats.size,
          sizeFormatted: this.formatFileSize(stats.size),
          createdAt: stats.birthtime
        });
      }

      // Sort by creation date (newest first)
      backups.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      return {
        success: true,
        message: `Found ${backups.length} backup(s)`,
        backups
      };
    } catch (error) {
      console.error('Failed to list backups:', error);
      return {
        success: false,
        message: `Failed to list backups: ${error.message}`,
        backups: []
      };
    }
  }

  async deleteBackup(filename: string): Promise<SystemMaintenanceResult> {
    try {
      const backupDir = path.join(process.cwd(), 'backups');
      const backupPath = path.join(backupDir, filename);

      // Security check: ensure filename is valid and within backup directory
      if (!filename.startsWith('mybox-backup-') || (!filename.endsWith('.tar.gz') && !filename.endsWith('.zip'))) {
        return {
          success: false,
          message: 'Invalid backup filename'
        };
      }

      if (!fs.existsSync(backupPath)) {
        return {
          success: false,
          message: 'Backup file not found'
        };
      }

      fs.unlinkSync(backupPath);

      return {
        success: true,
        message: 'Backup deleted successfully'
      };
    } catch (error) {
      console.error('Failed to delete backup:', error);
      return {
        success: false,
        message: `Failed to delete backup: ${error.message}`
      };
    }
  }

  async restoreBackup(filename: string): Promise<SystemMaintenanceResult> {
    try {
      const backupDir = path.join(process.cwd(), 'backups');
      const backupPath = path.join(backupDir, filename);

      // Security check - support both .zip and .tar.gz files
      if (!filename.startsWith('mybox-backup-') || (!filename.endsWith('.zip') && !filename.endsWith('.tar.gz'))) {
        return {
          success: false,
          message: 'Invalid backup filename'
        };
      }

      if (!fs.existsSync(backupPath)) {
        return {
          success: false,
          message: 'Backup file not found'
        };
      }

      // Create temporary directory for extraction
      const tempDir = path.join(backupDir, `restore-${Date.now()}`);
      fs.mkdirSync(tempDir, { recursive: true });

      try {
        // Extract backup (cross-platform)
        if (filename.endsWith('.zip')) {
          await extract(backupPath, { dir: tempDir });
        } else {
          // For .tar.gz files, try to use cross-platform extraction
          await this.extractTarGz(backupPath, tempDir);
        }

        // Check backup metadata
        const metadataPath = path.join(tempDir, 'backup-metadata.json');
        if (!fs.existsSync(metadataPath)) {
          throw new Error('Invalid backup: missing metadata');
        }

        const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
        console.log('Restoring backup with metadata:', metadata);

        // Restore database collections
        const dbBackupPath = path.join(tempDir, 'database');
        if (fs.existsSync(dbBackupPath)) {
          await this.restoreDatabaseCollections(dbBackupPath);
        }

        // Restore uploads directory
        const backupUploadsPath = path.join(tempDir, 'uploads');
        if (fs.existsSync(backupUploadsPath)) {
          const uploadsPath = path.join(process.cwd(), 'uploads');

          // Backup current uploads if they exist
          if (fs.existsSync(uploadsPath)) {
            const backupCurrentUploads = path.join(process.cwd(), `uploads-backup-${Date.now()}`);
            if (fs.existsSync(uploadsPath)) {
              await this.copyDirectory(uploadsPath, backupCurrentUploads);
              await this.removeDirectory(uploadsPath);
            }
          }

          // Restore uploads from backup
          await this.copyDirectory(backupUploadsPath, uploadsPath);
        }

        // Clean up temporary directory
        await this.removeDirectory(tempDir);

        return {
          success: true,
          message: 'Backup restored successfully. Please restart the application for changes to take full effect.',
          data: metadata
        };

      } catch (error) {
        // Clean up on error
        if (fs.existsSync(tempDir)) {
          await this.removeDirectory(tempDir).catch(() => {});
        }
        throw error;
      }

    } catch (error) {
      console.error('Failed to restore backup:', error);
      return {
        success: false,
        message: `Failed to restore backup: ${error.message}`
      };
    }
  }

  private async exportDatabaseCollections(dbBackupPath: string): Promise<void> {
    const collections = ['users', 'challenges', 'teams', 'machinetemplates', 'machineinstances', 'adminsettings', 'dashboardconfigs'];

    for (const collectionName of collections) {
      try {
        // Get the model for the collection
        let model;
        switch (collectionName) {
          case 'users':
            model = this.adminSettingsModel.db.model('User');
            break;
          case 'challenges':
            model = this.adminSettingsModel.db.model('Challenge');
            break;
          case 'teams':
            model = this.adminSettingsModel.db.model('Team');
            break;
          case 'machinetemplates':
            model = this.adminSettingsModel.db.model('MachineTemplate');
            break;
          case 'machineinstances':
            model = this.adminSettingsModel.db.model('MachineInstance');
            break;
          case 'adminsettings':
            model = this.adminSettingsModel;
            break;
          case 'dashboardconfigs':
            model = this.adminSettingsModel.db.model('DashboardConfig');
            break;
          default:
            continue;
        }

        // Export collection data
        const data = await model.find({}).lean();
        const outputPath = path.join(dbBackupPath, `${collectionName}.json`);
        fs.writeFileSync(outputPath, JSON.stringify(data, null, 2));

        console.log(`Exported ${data.length} documents from ${collectionName}`);
      } catch (error) {
        console.warn(`Failed to export collection ${collectionName}:`, error.message);
        // Create empty file to maintain structure
        const outputPath = path.join(dbBackupPath, `${collectionName}.json`);
        fs.writeFileSync(outputPath, '[]');
      }
    }
  }

  private async copyDirectory(src: string, dest: string): Promise<void> {
    if (!fs.existsSync(src)) return;

    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    const entries = fs.readdirSync(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    }
  }

  private async removeDirectory(dir: string): Promise<void> {
    if (!fs.existsSync(dir)) return;

    const entries = fs.readdirSync(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);

      if (entry.isDirectory()) {
        await this.removeDirectory(fullPath);
      } else {
        fs.unlinkSync(fullPath);
      }
    }

    fs.rmdirSync(dir);
  }

  private async createZipArchive(sourceDir: string, outputPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(outputPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => {
        console.log(`Archive created: ${archive.pointer()} total bytes`);
        resolve();
      });

      archive.on('error', (err) => {
        reject(err);
      });

      archive.pipe(output);
      archive.directory(sourceDir, false);
      archive.finalize();
    });
  }

  private async extractTarGz(archivePath: string, extractPath: string): Promise<void> {
    // For cross-platform tar.gz extraction, we'll use a fallback approach
    try {
      if (process.platform === 'win32') {
        // On Windows, try to use 7zip or fall back to error
        throw new Error('tar.gz extraction not supported on Windows. Please use .zip backups.');
      } else {
        // On Unix-like systems, use tar command
        await execAsync(`tar -xzf "${archivePath}" -C "${extractPath}"`);
      }
    } catch (error) {
      throw new Error(`Failed to extract backup: ${error.message}`);
    }
  }

  private async restoreDatabaseCollections(dbBackupPath: string): Promise<void> {
    const dbFiles = fs.readdirSync(dbBackupPath);

    for (const dbFile of dbFiles) {
      if (dbFile.endsWith('.json')) {
        const collectionName = dbFile.replace('.json', '');
        const filePath = path.join(dbBackupPath, dbFile);

        try {
          const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));

          // Get the model for the collection
          let model;
          switch (collectionName) {
            case 'users':
              model = this.adminSettingsModel.db.model('User');
              break;
            case 'challenges':
              model = this.adminSettingsModel.db.model('Challenge');
              break;
            case 'teams':
              model = this.adminSettingsModel.db.model('Team');
              break;
            case 'machinetemplates':
              model = this.adminSettingsModel.db.model('MachineTemplate');
              break;
            case 'machineinstances':
              model = this.adminSettingsModel.db.model('MachineInstance');
              break;
            case 'adminsettings':
              model = this.adminSettingsModel;
              break;
            case 'dashboardconfigs':
              model = this.adminSettingsModel.db.model('DashboardConfig');
              break;
            default:
              continue;
          }

          // Clear existing data and insert backup data
          await model.deleteMany({});
          if (data.length > 0) {
            await model.insertMany(data);
          }

          console.log(`Restored ${data.length} documents to ${collectionName}`);
        } catch (error) {
          console.warn(`Failed to restore collection ${collectionName}:`, error.message);
        }
      }
    }
  }

  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async downloadSystemLogs(): Promise<SystemMaintenanceResult> {
    try {
      const logsDir = path.join(process.cwd(), 'logs');
      const logFiles: LogFile[] = [];

      if (fs.existsSync(logsDir)) {
        const files = fs.readdirSync(logsDir);
        files.forEach(file => {
          const filePath = path.join(logsDir, file);
          const stats = fs.statSync(filePath);
          logFiles.push({
            name: file,
            size: stats.size,
            modified: stats.mtime
          });
        });
      }

      return {
        success: true,
        message: 'Log files retrieved successfully',
        data: {
          logFiles,
          totalFiles: logFiles.length,
          totalSize: logFiles.reduce((sum, file) => sum + file.size, 0)
        }
      };
    } catch (error) {
      console.error('Log retrieval failed:', error);
      return {
        success: false,
        message: `Log retrieval failed: ${error.message}`
      };
    }
  }

  async clearCache(): Promise<SystemMaintenanceResult> {
    try {
      const cacheDir = path.join(process.cwd(), 'cache');
      const tempDir = path.join(process.cwd(), 'temp');
      
      let clearedFiles = 0;
      let clearedSize = 0;

      // Clear cache directory
      if (fs.existsSync(cacheDir)) {
        const files = fs.readdirSync(cacheDir);
        files.forEach(file => {
          const filePath = path.join(cacheDir, file);
          const stats = fs.statSync(filePath);
          clearedSize += stats.size;
          fs.unlinkSync(filePath);
          clearedFiles++;
        });
      }

      // Clear temp directory
      if (fs.existsSync(tempDir)) {
        const files = fs.readdirSync(tempDir);
        files.forEach(file => {
          const filePath = path.join(tempDir, file);
          const stats = fs.statSync(filePath);
          clearedSize += stats.size;
          fs.unlinkSync(filePath);
          clearedFiles++;
        });
      }

      return {
        success: true,
        message: 'Cache cleared successfully',
        data: {
          clearedFiles,
          clearedSize,
          clearedSizeMB: Math.round(clearedSize / (1024 * 1024) * 100) / 100
        }
      };
    } catch (error) {
      console.error('Cache clearing failed:', error);
      return {
        success: false,
        message: `Cache clearing failed: ${error.message}`
      };
    }
  }

  async getSystemStats(): Promise<any> {
    try {
      const settings = await this.getAdminSettings();
      const uploadsDir = path.join(process.cwd(), 'uploads');
      const backupsDir = path.join(process.cwd(), 'backups');
      
      let uploadsSize = 0;
      let backupsSize = 0;
      let uploadsCount = 0;
      let backupsCount = 0;

      // Calculate uploads directory size
      if (fs.existsSync(uploadsDir)) {
        const calculateDirSize = (dir: string): { size: number; count: number } => {
          let totalSize = 0;
          let totalCount = 0;
          
          try {
            const files = fs.readdirSync(dir);
            
            files.forEach(file => {
              try {
                const filePath = path.join(dir, file);
                const stats = fs.statSync(filePath);
                
                if (stats.isDirectory()) {
                  const subDir = calculateDirSize(filePath);
                  totalSize += subDir.size;
                  totalCount += subDir.count;
                } else {
                  totalSize += stats.size;
                  totalCount++;
                }
              } catch (fileError) {
                console.warn(`Error processing file ${file}:`, fileError.message);
              }
            });
          } catch (dirError) {
            console.warn(`Error reading directory ${dir}:`, dirError.message);
          }
          
          return { size: totalSize, count: totalCount };
        };

        const uploadsStats = calculateDirSize(uploadsDir);
        uploadsSize = uploadsStats.size;
        uploadsCount = uploadsStats.count;
      }

      // Calculate backups directory size
      if (fs.existsSync(backupsDir)) {
        try {
          const files = fs.readdirSync(backupsDir);
          files.forEach(file => {
            try {
              const filePath = path.join(backupsDir, file);
              const stats = fs.statSync(filePath);
              if (stats.isFile()) {
                backupsSize += stats.size;
                backupsCount++;
              }
            } catch (fileError) {
              console.warn(`Error processing backup file ${file}:`, fileError.message);
            }
          });
        } catch (dirError) {
          console.warn(`Error reading backups directory:`, dirError.message);
        }
      }

      return {
        settings: {
          lastBackup: settings.lastBackup,
          totalBackups: settings.totalBackups,
          maintenanceMode: settings.maintenanceMode,
          autoBackup: settings.autoBackup,
          backupInterval: settings.backupInterval
        },
        storage: {
          uploadsSize: Math.round(uploadsSize / (1024 * 1024) * 100) / 100, // MB
          uploadsCount,
          backupsSize: Math.round(backupsSize / (1024 * 1024) * 100) / 100, // MB
          backupsCount,
          totalSize: Math.round((uploadsSize + backupsSize) / (1024 * 1024) * 100) / 100 // MB
        },
        system: {
          nodeVersion: process.version,
          platform: process.platform,
          uptime: Math.round(process.uptime()),
          memoryUsage: process.memoryUsage()
        }
      };
    } catch (error) {
      console.error('System stats retrieval failed:', error);
      throw error;
    }
  }

  async testEmailSettings(settings: Partial<UpdateAdminSettingsDto>): Promise<SystemMaintenanceResult> {
    try {
      // This would typically use nodemailer to test email settings
      // For now, we'll just validate the settings format
      
      if (!settings.smtpHost || !settings.fromEmail) {
        return {
          success: false,
          message: 'SMTP host and from email are required'
        };
      }

      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(settings.fromEmail)) {
        return {
          success: false,
          message: 'Invalid from email format'
        };
      }

      return {
        success: true,
        message: 'Email settings validation passed',
        data: {
          host: settings.smtpHost,
          port: settings.smtpPort,
          secure: settings.smtpSecure,
          fromEmail: settings.fromEmail
        }
      };
    } catch (error) {
      console.error('Email settings test failed:', error);
      return {
        success: false,
        message: `Email settings test failed: ${error.message}`
      };
    }
  }
}