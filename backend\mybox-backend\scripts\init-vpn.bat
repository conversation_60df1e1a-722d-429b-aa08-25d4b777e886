@echo off
echo Creating directories...
mkdir %USERPROFILE%\wg-easy 2>nul

echo Stopping existing WireGuard Easy container...
docker stop wg-easy 2>nul
docker rm wg-easy 2>nul

echo Starting WireGuard Easy container...
docker run -d --name=wg-easy ^
  -e WG_HOST=127.0.0.1 ^
  -e PASSWORD=yourpassword ^
  -e WG_DEFAULT_ADDRESS=******** ^
  -e WG_ALLOWED_IPS=0.0.0.0/0 ^
  -v %USERPROFILE%/wg-easy:/etc/wireguard ^
  -p 51820:51820/udp ^
  -p 51821:51821/tcp ^
  --cap-add=NET_ADMIN ^
  --cap-add=SYS_MODULE ^
  --sysctl="net.ipv4.conf.all.src_valid_mark=1" ^
  --restart unless-stopped ^
  weejewel/wg-easy

echo WireGuard Easy setup complete!
echo Web interface available at: http://localhost:51821
echo Default password: yourpassword
echo WireGuard port: 51820/udp
