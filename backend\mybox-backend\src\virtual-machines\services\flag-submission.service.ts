import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { MachineTemplate } from '../../schemas/machine-template.schema';
import { MachineInstance } from '../../schemas/machine-instance.schema';
import { MachineSubmission } from '../../schemas/machine-submission.schema';
import { User } from '../../schemas/user.schema';
import { Team } from '../../schemas/team.schema';
import { NotificationsService } from '../../notifications/notifications.service';
import { NotificationsGateway } from '../../notifications/notifications.gateway';
import * as crypto from 'crypto';

export interface FlagSubmissionDto {
  instanceId: string;
  flagName: string;
  flag: string;
}

export interface FlagSubmissionResult {
  isCorrect: boolean;
  pointsAwarded: number;
  teamPointsAwarded: number;
  isFirstBlood: boolean;
  isFirstTeamSolve: boolean;
  message: string;
  flagInfo?: {
    name: string;
    description?: string;
  };
}

export interface UserSubmissionHistory {
  id: string;
  template: {
    id: string;
    name: string;
    difficulty: string;
  };
  flagName: string;
  isCorrect: boolean;
  pointsAwarded: number;
  isFirstBlood: boolean;
  submittedAt: Date;
}

export interface TeamSubmissionStats {
  teamId: string;
  teamName: string;
  totalSolves: number;
  totalPoints: number;
  firstBloods: number;
  uniqueMachinesSolved: number;
  recentSolves: Array<{
    username: string;
    machineName: string;
    flagName: string;
    points: number;
    solvedAt: Date;
  }>;
}

@Injectable()
export class FlagSubmissionService {
  private readonly logger = new Logger(FlagSubmissionService.name);

  constructor(
    @InjectModel(MachineTemplate.name) private machineTemplateModel: Model<MachineTemplate>,
    @InjectModel(MachineInstance.name) private machineInstanceModel: Model<MachineInstance>,
    @InjectModel(MachineSubmission.name) private machineSubmissionModel: Model<MachineSubmission>,
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Team.name) private teamModel: Model<Team>,
    private notificationsService: NotificationsService,
    private notificationsGateway: NotificationsGateway,
  ) {}

  /**
   * Submit a flag for a machine instance
   */
  async submitFlag(userId: string, submissionDto: FlagSubmissionDto): Promise<FlagSubmissionResult> {
    this.logger.log(`Flag submission for instance ${submissionDto.instanceId} by user ${userId}`);

    // Get and validate instance
    const instance = await this.machineInstanceModel
      .findOne({
        _id: submissionDto.instanceId,
        $or: [
          { ownerId: userId },
          { teamId: { $exists: true }, isTeamShared: true }
        ],
        status: 'running'
      })
      .populate('templateId')
      .exec();

    if (!instance) {
      throw new NotFoundException('Machine instance not found, not running, or unauthorized');
    }

    // Verify team access if it's a team instance
    let teamId: string | undefined;
    if (instance.teamId && instance.ownerId.toString() !== userId) {
      const team = await this.teamModel.findById(instance.teamId);
      const isMember = team?.members.some(member => 
        member.userId.toString() === userId || team.captainId.toString() === userId
      );

      if (!isMember) {
        throw new BadRequestException('User is not authorized to submit flags for this instance');
      }
      teamId = instance.teamId.toString();
    } else if (instance.teamId) {
      teamId = instance.teamId.toString();
    }

    const template = instance.templateId as unknown as MachineTemplate;

    // Find the flag definition
    const flagDefinition = template.flags.find(f => f.name === submissionDto.flagName);
    if (!flagDefinition) {
      throw new BadRequestException('Invalid flag name for this machine');
    }

    // Check if user already submitted this flag correctly
    const existingCorrectSubmission = await this.machineSubmissionModel.findOne({
      templateId: (template as any)._id.toString(),
      userId,
      flagName: submissionDto.flagName,
      isCorrect: true,
    });

    if (existingCorrectSubmission) {
      return {
        isCorrect: true,
        pointsAwarded: 0,
        teamPointsAwarded: 0,
        isFirstBlood: false,
        isFirstTeamSolve: false,
        message: 'You have already solved this flag',
        flagInfo: {
          name: flagDefinition.name,
          description: flagDefinition.description,
        },
      };
    }

    // Validate flag (simple string comparison for now)
    // In production, this could be more sophisticated with regex patterns, etc.
    const isCorrect = this.validateFlag(submissionDto.flag, flagDefinition);

    // Check for first blood (globally)
    const isFirstBlood = isCorrect && !(await this.machineSubmissionModel.findOne({
      templateId: (template as any)._id.toString(),
      flagName: submissionDto.flagName,
      isCorrect: true,
    }));

    // Check for first team solve
    let isFirstTeamSolve = false;
    if (isCorrect && teamId) {
      const existingTeamSolve = await this.machineSubmissionModel.findOne({
        templateId: (template as any)._id.toString(),
        flagName: submissionDto.flagName,
        teamId,
        isCorrect: true,
      });
      isFirstTeamSolve = !existingTeamSolve;
    }

    // Calculate points
    let pointsAwarded = 0;
    let teamPointsAwarded = 0;

    if (isCorrect) {
      pointsAwarded = flagDefinition.points;
      
      // Bonus points for first blood
      if (isFirstBlood) {
        pointsAwarded = Math.floor(pointsAwarded * 1.5); // 50% bonus
      }

      // Team points only awarded for first team solve
      if (teamId && isFirstTeamSolve) {
        teamPointsAwarded = pointsAwarded;
      }
    }

    // Create submission record
    const submission = new this.machineSubmissionModel({
      instanceId: instance._id,
      templateId: (template as any)._id.toString(),
      userId: new Types.ObjectId(userId),
      teamId: teamId ? new Types.ObjectId(teamId) : undefined,
      flagName: submissionDto.flagName,
      submittedFlag: this.hashFlag(submissionDto.flag), // Store hash for security
      isCorrect,
      pointsAwarded,
      teamPointsAwarded,
      isFirstBlood,
      isFirstTeamSolve,
      submissionIP: 'unknown', // TODO: Extract from request
      submissionMethod: 'api',
    });

    await submission.save();

    // Update user points if correct
    if (isCorrect && pointsAwarded > 0) {
      await this.userModel.findByIdAndUpdate(userId, {
        $inc: { points: pointsAwarded }
      });
    }

    // Update team points if correct and first team solve
    if (isCorrect && teamPointsAwarded > 0 && teamId) {
      await this.teamModel.findByIdAndUpdate(teamId, {
        $inc: { points: teamPointsAwarded }
      });
    }

    // Update template solve count if correct
    if (isCorrect) {
      await this.machineTemplateModel.findByIdAndUpdate((template as any)._id.toString(), {
        $inc: { solveCount: 1 }
      });
    }

    // Send machine first blood notification if applicable
    if (isCorrect && isFirstBlood) {
      try {
        const user = await this.userModel.findById(userId).select('username');
        if (user) {
          console.log(`🏆 Creating machine first blood notification for ${user.username} on machine "${template.name}"`);
          const notification = await this.notificationsService.createMachineFirstBloodNotification(
            (template as any)._id.toString(),
            template.name,
            user.username,
            pointsAwarded
          );
          
          // Broadcast the machine first blood notification to all users
          await this.notificationsGateway.broadcastGlobalNotification(notification);
          console.log(`✅ Machine first blood notification broadcasted successfully`);
        }
      } catch (error) {
        console.error('Failed to create machine first blood notification:', error);
      }
    }

    // Generate response message
    let message = '';
    if (isCorrect) {
      message = `Correct! You earned ${pointsAwarded} points.`;
      if (isFirstBlood) {
        message += ' 🩸 First Blood! (+50% bonus)';
      }
      if (isFirstTeamSolve && teamId) {
        message += ` Your team earned ${teamPointsAwarded} points!`;
      }
    } else {
      message = 'Incorrect flag. Try again!';
    }

    this.logger.log(`Flag submission result: ${isCorrect ? 'CORRECT' : 'INCORRECT'} - User: ${userId}, Flag: ${submissionDto.flagName}, Points: ${pointsAwarded}`);

    return {
      isCorrect,
      pointsAwarded,
      teamPointsAwarded,
      isFirstBlood,
      isFirstTeamSolve,
      message,
      flagInfo: {
        name: flagDefinition.name,
        description: flagDefinition.description,
      },
    };
  }

  /**
   * Get available flags for an instance
   */
  async getInstanceFlags(instanceId: string, userId: string): Promise<Array<{
    name: string;
    description?: string;
    points: number;
    isSolved: boolean;
    solvedAt?: Date;
  }>> {
    // Verify access to instance
    const instance = await this.machineInstanceModel
      .findOne({
        _id: instanceId,
        $or: [
          { ownerId: userId },
          { teamId: { $exists: true }, isTeamShared: true }
        ]
      })
      .populate('templateId')
      .exec();

    if (!instance) {
      throw new NotFoundException('Machine instance not found or unauthorized');
    }

    const template = instance.templateId as unknown as MachineTemplate;

    // Get user's submissions for this template
    const userSubmissions = await this.machineSubmissionModel.find({
      templateId: (template as any)._id.toString(),
      userId,
      isCorrect: true,
    });

    const solvedFlags = new Map(
      userSubmissions.map(sub => [sub.flagName, sub.submittedAt])
    );

    return template.flags.map(flag => ({
      name: flag.name,
      description: flag.description,
      points: flag.points,
      isSolved: solvedFlags.has(flag.name),
      solvedAt: solvedFlags.get(flag.name),
    }));
  }

  /**
   * Get user's submission history
   */
  async getUserSubmissionHistory(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      correctOnly?: boolean;
    } = {}
  ): Promise<{ submissions: UserSubmissionHistory[]; total: number; hasMore: boolean }> {
    const { page = 1, limit = 20, correctOnly = false } = options;
    
    const query: any = { userId };
    if (correctOnly) {
      query.isCorrect = true;
    }

    const skip = (page - 1) * limit;

    const [submissions, total] = await Promise.all([
      this.machineSubmissionModel
        .find(query)
        .populate('templateId', 'name difficulty')
        .sort({ submittedAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.machineSubmissionModel.countDocuments(query),
    ]);

    const formattedSubmissions: UserSubmissionHistory[] = submissions.map(sub => {
      const template = sub.templateId as unknown as MachineTemplate;
      return {
        id: sub._id.toString(),
        template: {
          id: (template as any)._id.toString().toString(),
          name: template.name,
          difficulty: template.difficulty,
        },
        flagName: sub.flagName,
        isCorrect: sub.isCorrect,
        pointsAwarded: sub.pointsAwarded,
        isFirstBlood: sub.isFirstBlood,
        submittedAt: sub.submittedAt,
      };
    });

    return {
      submissions: formattedSubmissions,
      total,
      hasMore: skip + submissions.length < total,
    };
  }

  /**
   * Get team submission statistics
   */
  async getTeamSubmissionStats(teamId: string): Promise<TeamSubmissionStats> {
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    // Get team member IDs
    const memberIds = [
      team.captainId,
      ...team.members.map(m => m.userId)
    ];

    // Get team submissions
    const [submissions, recentSolves] = await Promise.all([
      this.machineSubmissionModel
        .find({
          userId: { $in: memberIds },
          isCorrect: true,
        })
        .populate('templateId', 'name')
        .populate('userId', 'username')
        .exec(),
      this.machineSubmissionModel
        .find({
          userId: { $in: memberIds },
          isCorrect: true,
        })
        .populate('templateId', 'name')
        .populate('userId', 'username')
        .sort({ submittedAt: -1 })
        .limit(10)
        .exec(),
    ]);

    // Calculate statistics
    const totalSolves = submissions.length;
    const totalPoints = submissions.reduce((sum, sub) => sum + sub.pointsAwarded, 0);
    const firstBloods = submissions.filter(sub => sub.isFirstBlood).length;
    
    // Count unique machines solved
    const uniqueMachines = new Set(submissions.map(sub => sub.templateId.toString()));
    const uniqueMachinesSolved = uniqueMachines.size;

    // Format recent solves
    const formattedRecentSolves = recentSolves.map(sub => {
      const template = sub.templateId as unknown as MachineTemplate;
      const user = sub.userId as unknown as User;
      return {
        username: user.username,
        machineName: template.name,
        flagName: sub.flagName,
        points: sub.pointsAwarded,
        solvedAt: sub.submittedAt,
      };
    });

    return {
      teamId: (team as any)._id.toString(),
      teamName: team.name,
      totalSolves,
      totalPoints,
      firstBloods,
      uniqueMachinesSolved,
      recentSolves: formattedRecentSolves,
    };
  }

  /**
   * Get machine solve statistics
   */
  async getMachineSolveStats(templateId: string): Promise<{
    totalSolves: number;
    uniqueSolvers: number;
    firstBloodUser?: string;
    firstBloodDate?: Date;
    flagStats: Array<{
      flagName: string;
      solves: number;
      points: number;
    }>;
    recentSolves: Array<{
      username: string;
      flagName: string;
      solvedAt: Date;
      isFirstBlood: boolean;
    }>;
  }> {
    const template = await this.machineTemplateModel.findById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    // Get all correct submissions for this machine
    const submissions = await this.machineSubmissionModel
      .find({
        templateId,
        isCorrect: true,
      })
      .populate('userId', 'username')
      .sort({ submittedAt: 1 })
      .exec();

    // Find first blood
    const firstBloodSubmission = submissions.find(sub => sub.isFirstBlood);
    const firstBloodUser = firstBloodSubmission ? 
      (firstBloodSubmission.userId as unknown as User).username : undefined;
    const firstBloodDate = firstBloodSubmission?.submittedAt;

    // Calculate statistics
    const totalSolves = submissions.length;
    const uniqueSolvers = new Set(submissions.map(sub => sub.userId.toString())).size;

    // Flag-specific statistics
    const flagStatsMap = new Map<string, { solves: number; points: number }>();
    
    template.flags.forEach(flag => {
      flagStatsMap.set(flag.name, { solves: 0, points: flag.points });
    });

    submissions.forEach(sub => {
      const stats = flagStatsMap.get(sub.flagName);
      if (stats) {
        stats.solves++;
      }
    });

    const flagStats = Array.from(flagStatsMap.entries()).map(([flagName, stats]) => ({
      flagName,
      solves: stats.solves,
      points: stats.points,
    }));

    // Recent solves (last 20)
    const recentSubmissions = submissions.slice(-20);
    const recentSolves = recentSubmissions.map(sub => {
      const user = sub.userId as unknown as User;
      return {
        username: user.username,
        flagName: sub.flagName,
        solvedAt: sub.submittedAt,
        isFirstBlood: sub.isFirstBlood,
      };
    });

    return {
      totalSolves,
      uniqueSolvers,
      firstBloodUser,
      firstBloodDate,
      flagStats,
      recentSolves,
    };
  }

  /**
   * Get global flag submission leaderboard
   */
  async getGlobalLeaderboard(timeframe: 'daily' | 'weekly' | 'monthly' | 'all' = 'all'): Promise<Array<{
    userId: string;
    username: string;
    totalPoints: number;
    totalSolves: number;
    firstBloods: number;
    rank: number;
  }>> {
    // Calculate date filter
    let dateFilter: any = {};
    const now = new Date();
    
    switch (timeframe) {
      case 'daily':
        dateFilter.submittedAt = {
          $gte: new Date(now.getFullYear(), now.getMonth(), now.getDate())
        };
        break;
      case 'weekly':
        const weekStart = new Date(now);
        weekStart.setDate(now.getDate() - now.getDay());
        weekStart.setHours(0, 0, 0, 0);
        dateFilter.submittedAt = { $gte: weekStart };
        break;
      case 'monthly':
        dateFilter.submittedAt = {
          $gte: new Date(now.getFullYear(), now.getMonth(), 1)
        };
        break;
    }

    const leaderboardData = await this.machineSubmissionModel.aggregate([
      {
        $match: {
          isCorrect: true,
          ...dateFilter,
        }
      },
      {
        $group: {
          _id: '$userId',
          totalPoints: { $sum: '$pointsAwarded' },
          totalSolves: { $sum: 1 },
          firstBloods: {
            $sum: { $cond: ['$isFirstBlood', 1, 0] }
          },
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          userId: '$_id',
          username: '$user.username',
          totalPoints: 1,
          totalSolves: 1,
          firstBloods: 1,
        }
      },
      { $sort: { totalPoints: -1, firstBloods: -1, totalSolves: -1 } },
      { $limit: 100 },
    ]);

    // Add ranks
    return leaderboardData.map((entry, index) => ({
      ...entry,
      userId: entry.userId.toString(),
      rank: index + 1,
    }));
  }

  /**
   * Validate submitted flag against flag definition
   */
  private validateFlag(submittedFlag: string, flagDefinition: any): boolean {
    // Basic string comparison (case-sensitive)
    // In production, this could support:
    // - Regex patterns
    // - Case-insensitive matching
    // - Multiple valid answers
    // - Custom validation functions

    // For now, we'll use a simple hash comparison if the flag is stored as hash
    // or direct string comparison

    // TODO: Implement proper flag format validation
    // HTB style flags: HTB{...} or custom format per machine

    // Use the 'value' field for validation, fallback to 'name' for backward compatibility
    const flagValue = flagDefinition.value || flagDefinition.name;
    return submittedFlag.trim() === flagValue.trim();
  }

  /**
   * Hash flag for secure storage
   */
  private hashFlag(flag: string): string {
    return crypto.createHash('sha256').update(flag.trim()).digest('hex');
  }
}