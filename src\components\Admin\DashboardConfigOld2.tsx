import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  DashboardConfig as DashboardConfigType, 
  dashboardService, 
  CreateSponsorDto,
  UpdateSponsorDto 
} from '../../services/dashboard';
import { 
  Settings, 
  Upload, 
  Save, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  Move,
  ExternalLink,
  Calendar,
  Palette,
  Users,
  Award,
  Globe
} from 'lucide-react';
import { SponsorModal } from './SponsorModal';

export function DashboardConfig() {
  const [config, setConfig] = useState<DashboardConfigType | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'general' | 'social' | 'sponsors' | 'theme' | 'features'>('general');
  const [showSponsorModal, setShowSponsorModal] = useState(false);
  const [editingSponsor, setEditingSponsor] = useState<any>(null);

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const data = await dashboardService.getDashboardConfig();
      setConfig(data);
    } catch (error) {
      console.error('Failed to load dashboard config:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (updates: Partial<DashboardConfigType>) => {
    if (!config) return;
    
    setSaving(true);
    try {
      // Merge updates with existing config to preserve all fields
      const mergedConfig = {
        ...config,
        ...updates,
        // Ensure nested objects are properly merged
        socialLinks: updates.socialLinks ? { ...config.socialLinks, ...updates.socialLinks } : config.socialLinks,
        theme: updates.theme ? { ...config.theme, ...updates.theme } : config.theme,
        features: updates.features ? { ...config.features, ...updates.features } : config.features,
      };
      
      const response = await dashboardService.updateDashboardConfig(mergedConfig);
      setConfig(response.config);
      // Show success message
    } catch (error) {
      console.error('Failed to update config:', error);
      // Show error message
    } finally {
      setSaving(false);
    }
  };

  const handleCoverImageUpload = async (file: File) => {
    try {
      const response = await dashboardService.uploadCoverImage(file);
      if (config) {
        setConfig({ ...config, eventCoverImage: response.imageUrl });
      }
    } catch (error) {
      console.error('Failed to upload cover image:', error);
    }
  };

  const handleAddSponsor = async (sponsorData: CreateSponsorDto) => {
    try {
      const response = await dashboardService.addSponsor(sponsorData);
      setConfig(response.config);
      setShowSponsorModal(false);
      setEditingSponsor(null);
    } catch (error) {
      console.error('Failed to add sponsor:', error);
    }
  };

  const handleUpdateSponsor = async (sponsorData: UpdateSponsorDto) => {
    try {
      const response = await dashboardService.updateSponsor(sponsorData.id, sponsorData);
      setConfig(response.config);
      setShowSponsorModal(false);
      setEditingSponsor(null);
    } catch (error) {
      console.error('Failed to update sponsor:', error);
    }
  };

  const handleDeleteSponsor = async (sponsorId: string) => {
    if (!confirm('Are you sure you want to delete this sponsor?')) return;
    
    try {
      const response = await dashboardService.deleteSponsor(sponsorId);
      setConfig(response.config);
    } catch (error) {
      console.error('Failed to delete sponsor:', error);
    }
  };

  const openSponsorModal = (sponsor?: any) => {
    setEditingSponsor(sponsor || null);
    setShowSponsorModal(true);
  };

  const closeSponsorModal = () => {
    setShowSponsorModal(false);
    setEditingSponsor(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500/30 border-t-purple-400"></div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="text-center p-12">
        <p className="text-red-400">Failed to load dashboard configuration</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
            Dashboard Configuration
          </h1>
          <p className="text-purple-200/70 mt-2">
            Customize your platform's appearance and content
          </p>
        </div>
        <motion.button
          onClick={() => handleSave(config)}
          disabled={saving}
          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-lg font-medium transition-all duration-200 disabled:opacity-50"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Save className="w-5 h-5" />
          <span>{saving ? 'Saving...' : 'Save Changes'}</span>
        </motion.button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-slate-800/50 p-1 rounded-lg">
        {[
          { id: 'general', label: 'General', icon: Settings },
          { id: 'social', label: 'Social Links', icon: Globe },
          { id: 'sponsors', label: 'Sponsors', icon: Award },
          { id: 'theme', label: 'Theme', icon: Palette },
          { id: 'features', label: 'Features', icon: Users },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md font-medium transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-purple-600 text-white'
                : 'text-purple-200 hover:text-white hover:bg-purple-600/50'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="glass-card p-8 border border-purple-500/30">
        {activeTab === 'general' && (
          <GeneralSettings 
            config={config} 
            onUpdate={handleSave}
            onCoverImageUpload={handleCoverImageUpload}
          />
        )}
        
        {activeTab === 'social' && (
          <SocialSettings 
            config={config} 
            onUpdate={handleSave}
          />
        )}
        
        {activeTab === 'sponsors' && (
          <SponsorsSettings 
            config={config}
            onAddSponsor={openSponsorModal}
            onEditSponsor={openSponsorModal}
            onDeleteSponsor={handleDeleteSponsor}
          />
        )}
        
        {activeTab === 'theme' && (
          <ThemeSettings 
            config={config} 
            onUpdate={handleSave}
          />
        )}
        
        {activeTab === 'features' && (
          <FeaturesSettings 
            config={config} 
            onUpdate={handleSave}
          />
        )}
      </div>

      {/* Top-level Sponsor Modal */}
      {showSponsorModal && (
        <SponsorModal
          sponsor={editingSponsor}
          onSave={(sponsorData) => {
            if (editingSponsor) {
              handleUpdateSponsor({ ...sponsorData, id: editingSponsor.id });
            } else {
              handleAddSponsor(sponsorData);
            }
          }}
          onClose={closeSponsorModal}
        />
      )}
    </div>
  );
}

function GeneralSettings({ config, onUpdate, onCoverImageUpload }: any) {
  const [formData, setFormData] = useState({
    eventName: config.eventName || '',
    eventDescription: config.eventDescription || '',
    eventMessage: config.eventMessage || '',
    eventStartDate: config.eventStartDate ? config.eventStartDate.split('T')[0] : '',
    eventEndDate: config.eventEndDate ? config.eventEndDate.split('T')[0] : '',
  });

  // Update local state when config changes
  useEffect(() => {
    setFormData({
      eventName: config.eventName || '',
      eventDescription: config.eventDescription || '',
      eventMessage: config.eventMessage || '',
      eventStartDate: config.eventStartDate ? config.eventStartDate.split('T')[0] : '',
      eventEndDate: config.eventEndDate ? config.eventEndDate.split('T')[0] : '',
    });
  }, [config]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h3 className="text-xl font-semibold text-white mb-4">General Settings</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-purple-200 mb-2">
            Event Name
          </label>
          <input
            type="text"
            value={formData.eventName}
            onChange={(e) => setFormData({ ...formData, eventName: e.target.value })}
            className="w-full px-4 py-3 bg-slate-800 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:outline-none"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-purple-200 mb-2">
            Event Description
          </label>
          <textarea
            value={formData.eventDescription}
            onChange={(e) => setFormData({ ...formData, eventDescription: e.target.value })}
            rows={3}
            className="w-full px-4 py-3 bg-slate-800 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:outline-none"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-purple-200 mb-2">
            Welcome Message
          </label>
          <textarea
            value={formData.eventMessage}
            onChange={(e) => setFormData({ ...formData, eventMessage: e.target.value })}
            rows={3}
            className="w-full px-4 py-3 bg-slate-800 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:outline-none"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-purple-200 mb-2">
            Start Date
          </label>
          <input
            type="date"
            value={formData.eventStartDate}
            onChange={(e) => setFormData({ ...formData, eventStartDate: e.target.value })}
            className="w-full px-4 py-3 bg-slate-800 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:outline-none"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-purple-200 mb-2">
            End Date
          </label>
          <input
            type="date"
            value={formData.eventEndDate}
            onChange={(e) => setFormData({ ...formData, eventEndDate: e.target.value })}
            className="w-full px-4 py-3 bg-slate-800 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:outline-none"
          />
        </div>
      </div>

      {/* Cover Image Upload */}
      <div>
        <label className="block text-sm font-medium text-purple-200 mb-2">
          Cover Image
        </label>
        <div className="flex items-center space-x-4">
          {config.eventCoverImage && (
            <img
              src={config.eventCoverImage}
              alt="Cover"
              className="w-32 h-20 object-cover rounded-lg"
            />
          )}
          <input
            type="file"
            accept="image/*"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) onCoverImageUpload(file);
            }}
            className="hidden"
            id="cover-upload"
          />
          <label
            htmlFor="cover-upload"
            className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg cursor-pointer transition-colors"
          >
            <Upload className="w-4 h-4" />
            <span>Upload Cover</span>
          </label>
        </div>
      </div>

      <button
        type="submit"
        className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-lg font-medium transition-all duration-200"
      >
        Save General Settings
      </button>
    </form>
  );
}

function SocialSettings({ config, onUpdate }: any) {
  const [socialLinks, setSocialLinks] = useState(config.socialLinks || {});

  // Update local state when config changes
  useEffect(() => {
    setSocialLinks(config.socialLinks || {});
  }, [config.socialLinks]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate({ socialLinks });
  };

  const socialPlatforms = [
    { key: 'github', label: 'GitHub', placeholder: 'https://github.com/yourorg' },
    { key: 'facebook', label: 'Facebook', placeholder: 'https://facebook.com/yourpage' },
    { key: 'instagram', label: 'Instagram', placeholder: 'https://instagram.com/youraccount' },
    { key: 'linkedin', label: 'LinkedIn', placeholder: 'https://linkedin.com/company/yourcompany' },
    { key: 'twitter', label: 'Twitter', placeholder: 'https://twitter.com/youraccount' },
    { key: 'discord', label: 'Discord', placeholder: 'https://discord.gg/yourserver' },
    { key: 'website', label: 'Website', placeholder: 'https://yourwebsite.com' },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h3 className="text-xl font-semibold text-white mb-4">Social Media Links</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {socialPlatforms.map((platform) => (
          <div key={platform.key}>
            <label className="block text-sm font-medium text-purple-200 mb-2">
              {platform.label}
            </label>
            <input
              type="url"
              value={socialLinks[platform.key] || ''}
              onChange={(e) => setSocialLinks({ 
                ...socialLinks, 
                [platform.key]: e.target.value 
              })}
              placeholder={platform.placeholder}
              className="w-full px-4 py-3 bg-slate-800 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:outline-none"
            />
          </div>
        ))}
      </div>

      <button
        type="submit"
        className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-lg font-medium transition-all duration-200"
      >
        Save Social Links
      </button>
    </form>
  );
}

function SponsorsSettings({ config, onAddSponsor, onEditSponsor, onDeleteSponsor }: any) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold text-white">Sponsors Management</h3>
        <button
          onClick={() => onAddSponsor()}
          className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add Sponsor</span>
        </button>
      </div>

      {/* Sponsors List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {(config.sponsors || []).map((sponsor: any) => (
          <div key={sponsor.id} className="bg-slate-800 p-4 rounded-lg border border-purple-500/30">
            <div className="flex items-center justify-between mb-3">
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                sponsor.tier === 'platinum' ? 'bg-gray-200 text-gray-800' :
                sponsor.tier === 'gold' ? 'bg-yellow-200 text-yellow-800' :
                sponsor.tier === 'silver' ? 'bg-gray-300 text-gray-800' :
                'bg-amber-200 text-amber-800'
              }`}>
                {sponsor.tier.toUpperCase()}
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={() => onEditSponsor(sponsor)}
                  className="p-1 text-purple-400 hover:text-purple-300"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={() => onDeleteSponsor(sponsor.id)}
                  className="p-1 text-red-400 hover:text-red-300"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 mb-3">
              <img
                src={sponsor.logo}
                alt={sponsor.name}
                className="w-12 h-12 object-contain"
              />
              <div>
                <h4 className="font-medium text-white">{sponsor.name}</h4>
                {sponsor.website && (
                  <a
                    href={sponsor.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-purple-400 hover:text-purple-300 flex items-center space-x-1"
                  >
                    <span>Visit</span>
                    <ExternalLink className="w-3 h-3" />
                  </a>
                )}
              </div>
            </div>
            
            {sponsor.description && (
              <p className="text-sm text-purple-200/70 line-clamp-2">
                {sponsor.description}
              </p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

function ThemeSettings({ config, onUpdate }: any) {
  const [theme, setTheme] = useState(config.theme || {
    primaryColor: '#8b5cf6',
    secondaryColor: '#ec4899',
    accentColor: '#10b981',
    backgroundColor: '#0f172a'
  });

  // Update local state when config changes
  useEffect(() => {
    setTheme(config.theme || {
      primaryColor: '#8b5cf6',
      secondaryColor: '#ec4899',
      accentColor: '#10b981',
      backgroundColor: '#0f172a'
    });
  }, [config.theme]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate({ theme });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h3 className="text-xl font-semibold text-white mb-4">Theme Customization</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-purple-200 mb-2">
            Primary Color
          </label>
          <input
            type="color"
            value={theme.primaryColor}
            onChange={(e) => setTheme({ ...theme, primaryColor: e.target.value })}
            className="w-full h-12 bg-slate-800 border border-purple-500/30 rounded-lg"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-purple-200 mb-2">
            Secondary Color
          </label>
          <input
            type="color"
            value={theme.secondaryColor}
            onChange={(e) => setTheme({ ...theme, secondaryColor: e.target.value })}
            className="w-full h-12 bg-slate-800 border border-purple-500/30 rounded-lg"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-purple-200 mb-2">
            Accent Color
          </label>
          <input
            type="color"
            value={theme.accentColor}
            onChange={(e) => setTheme({ ...theme, accentColor: e.target.value })}
            className="w-full h-12 bg-slate-800 border border-purple-500/30 rounded-lg"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-purple-200 mb-2">
            Background Color
          </label>
          <input
            type="color"
            value={theme.backgroundColor}
            onChange={(e) => setTheme({ ...theme, backgroundColor: e.target.value })}
            className="w-full h-12 bg-slate-800 border border-purple-500/30 rounded-lg"
          />
        </div>
      </div>

      <button
        type="submit"
        className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-lg font-medium transition-all duration-200"
      >
        Save Theme Settings
      </button>
    </form>
  );
}

function FeaturesSettings({ config, onUpdate }: any) {
  const [features, setFeatures] = useState(config.features || {
    showStats: true,
    showRecentActivity: true,
    showQuickActions: true,
    showLeaderboard: true,
    showAnnouncements: true,
    showSponsors: true
  });

  // Update local state when config changes
  useEffect(() => {
    setFeatures(config.features || {
      showStats: true,
      showRecentActivity: true,
      showQuickActions: true,
      showLeaderboard: true,
      showAnnouncements: true,
      showSponsors: true
    });
  }, [config.features]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate({ features });
  };

  const featureOptions = [
    { key: 'showStats', label: 'Show Statistics Cards', description: 'Display user and platform statistics' },
    { key: 'showRecentActivity', label: 'Show Recent Activity', description: 'Display recent user activities' },
    { key: 'showQuickActions', label: 'Show Quick Actions', description: 'Display quick action buttons' },
    { key: 'showLeaderboard', label: 'Show Leaderboard', description: 'Display leaderboard section' },
    { key: 'showAnnouncements', label: 'Show Announcements', description: 'Display announcements section' },
    { key: 'showSponsors', label: 'Show Sponsors', description: 'Display sponsors section' },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h3 className="text-xl font-semibold text-white mb-4">Dashboard Features</h3>
      
      <div className="space-y-4">
        {featureOptions.map((feature) => (
          <div key={feature.key} className="flex items-center justify-between p-4 bg-slate-800/50 rounded-lg">
            <div>
              <h4 className="font-medium text-white">{feature.label}</h4>
              <p className="text-sm text-purple-200/70">{feature.description}</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={features[feature.key]}
                onChange={(e) => setFeatures({ 
                  ...features, 
                  [feature.key]: e.target.checked 
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
        ))}
      </div>

      <button
        type="submit"
        className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-lg font-medium transition-all duration-200"
      >
        Save Feature Settings
      </button>
    </form>
  );
}