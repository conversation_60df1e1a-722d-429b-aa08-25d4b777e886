import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { User } from '../schemas/user.schema';
import * as bcrypt from 'bcryptjs';
import * as bcryptLegacy from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
  ) {}

  async create(createUserData: {
    username: string;
    email: string;
    password: string;
  }): Promise<User> {
    // Check if user already exists
    const existingUser = await this.userModel.findOne({
      $or: [
        { username: createUserData.username },
        { email: createUserData.email }
      ]
    });

    if (existingUser) {
      throw new ConflictException('Username or email already exists');
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(createUserData.password, saltRounds);

    // Generate API token
    const apiToken = `htb_api_token_${uuidv4().replace(/-/g, '')}`;

    const user = new this.userModel({
      username: createUserData.username,
      email: createUserData.email,
      passwordHash,
      apiToken,
      lastActive: new Date(),
    });

    return user.save();
  }

  async findById(id: string): Promise<User | null> {
    if (!Types.ObjectId.isValid(id)) {
      return null;
    }
    return this.userModel.findById(id).exec();
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.userModel.findOne({ username }).exec();
  }

  async findByEmail(email: string): Promise<User | null> {
    console.log('🔍 Looking up user by email:', email);
    try {
      const user = await this.userModel.findOne({ email }).exec();
      if (user) {
        console.log('✅ User found in database:', email, 'ID:', user._id, 'Active:', user.isActive);
      } else {
        console.log('❌ No user found in database for email:', email);
      }
      return user;
    } catch (error) {
      console.error('❌ Database error during user lookup:', error);
      return null;
    }
  }

  async validatePassword(user: User, password: string): Promise<boolean> {
    try {
      // First try with bcryptjs (new method)
      const isValidBcryptjs = await bcrypt.compare(password, user.passwordHash);
      if (isValidBcryptjs) {
        return true;
      }

      // If bcryptjs fails, try with legacy bcrypt (for existing users)
      console.log('Trying legacy bcrypt validation for user:', user.email);
      const isValidLegacy = await bcryptLegacy.compare(password, user.passwordHash);

      if (isValidLegacy) {
        // Password is valid with legacy bcrypt, let's rehash it with bcryptjs
        console.log('Password valid with legacy bcrypt, rehashing with bcryptjs for user:', user.email);
        const saltRounds = 12;
        const newPasswordHash = await bcrypt.hash(password, saltRounds);

        // Update the user's password hash in the database
        await this.userModel.findByIdAndUpdate(user._id, {
          passwordHash: newPasswordHash
        });

        console.log('Password successfully rehashed for user:', user.email);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Password validation error for user:', user.email, error);
      return false;
    }
  }
  async updateProfile(
    userId: string, 
    updates: Partial<Pick<User, 'username' | 'email' | 'avatarUrl' | 'country' | 'bio'>>
  ): Promise<User | null> {
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if username/email is being changed and if it's already taken
    if (updates.username && updates.username !== user.username) {
      const existingUser = await this.findByUsername(updates.username);
      if (existingUser) {
        throw new ConflictException('Username already exists');
      }
    }

    if (updates.email && updates.email !== user.email) {
      const existingUser = await this.findByEmail(updates.email);
      if (existingUser) {
        throw new ConflictException('Email already exists');
      }
    }

    return this.userModel.findByIdAndUpdate(
      userId,
      { ...updates, lastActive: new Date() },
      { new: true }
    ).exec();
  }
  async regenerateApiToken(userId: string): Promise<User | null> {
    const apiToken = `htb_api_token_${uuidv4().replace(/-/g, '')}`;
    
    return this.userModel.findByIdAndUpdate(
      userId,
      { apiToken, lastActive: new Date() },
      { new: true }
    ).exec();
  }

  async updateLastActive(userId: string): Promise<void> {
    await this.userModel.findByIdAndUpdate(
      userId,
      { lastActive: new Date() }
    ).exec();
  }
  async updateScore(userId: string, points: number): Promise<User | null> {
    return this.userModel.findByIdAndUpdate(
      userId,
      { $inc: { score: points } },
      { new: true }
    ).exec();
  }

  async joinTeam(userId: string, teamId: string): Promise<User | null> {
    return this.userModel.findByIdAndUpdate(
      userId,
      { teamId: new Types.ObjectId(teamId) },
      { new: true }
    ).exec();
  }
  async leaveTeam(userId: string): Promise<User | null> {
    return this.userModel.findByIdAndUpdate(
      userId,
      { teamId: null },
      { new: true }
    ).exec();
  }

  async updateRanking(): Promise<void> {
    const users = await this.userModel
      .find({ isActive: true })
      .sort({ score: -1, lastActive: 1 })
      .exec();

    const bulkOps = users.map((user, index) => ({
      updateOne: {
        filter: { _id: user._id },
        update: { rank: index + 1 }
      }
    }));

    if (bulkOps.length > 0) {
      await this.userModel.bulkWrite(bulkOps);
    }
  }
}
