import React, { useState, useEffect } from 'react';
import { 
  Settings, 
  Server, 
  Shield, 
  Mail, 
  Database,
  Globe,
  Clock,
  Users,
  Save,
  Loader,
  CheckCircle,
  AlertCircle,
  Download,
  Trash2,
  HardDrive,
  Upload
} from 'lucide-react';
import {
  adminSettingsService,
  AdminSettings as AdminSettingsType,
  UpdateAdminSettingsDto,
  SystemStats,
  BackupInfo
} from '../../../services/adminSettings';

export function AdminSettings() {
  const [settings, setSettings] = useState<AdminSettingsType | null>(null);
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [backups, setBackups] = useState<BackupInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [activeOperations, setActiveOperations] = useState<{ [key: string]: boolean }>({});
  const [showBackupUpload, setShowBackupUpload] = useState(false);

  useEffect(() => {
    loadSettings();
    loadSystemStats();
    loadBackups();
  }, []);

  const loadSettings = async () => {
    try {
      const data = await adminSettingsService.getAdminSettings();
      setSettings(data);
    } catch (error) {
      console.error('Failed to load admin settings:', error);
      setMessage({ type: 'error', text: 'Failed to load settings' });
    } finally {
      setLoading(false);
    }
  };

  const loadSystemStats = async () => {
    try {
      const stats = await adminSettingsService.getSystemStats();
      setSystemStats(stats);
    } catch (error) {
      console.error('Failed to load system stats:', error);
    }
  };

  const loadBackups = async () => {
    try {
      const result = await adminSettingsService.listBackups();
      if (result.success) {
        setBackups(result.backups);
      }
    } catch (error) {
      console.error('Failed to load backups:', error);
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    setSaving(true);
    setMessage(null);

    try {
      // Validate settings
      const generalErrors = adminSettingsService.validateGeneralSettings(settings);
      const emailErrors = adminSettingsService.validateEmailSettings(settings);
      const allErrors = [...generalErrors, ...emailErrors];

      if (allErrors.length > 0) {
        setMessage({ type: 'error', text: allErrors.join(', ') });
        setSaving(false);
        return;
      }

      const updateDto: UpdateAdminSettingsDto = {
        allowRegistration: settings.allowRegistration,
        emailVerification: settings.emailVerification,
        maintenanceMode: settings.maintenanceMode,
        enableTeams: settings.enableTeams,
        maxTeamSize: settings.maxTeamSize,
        autoBackup: settings.autoBackup,
        backupInterval: settings.backupInterval,
        defaultSessionTime: settings.defaultSessionTime,
        maxConcurrentVMs: settings.maxConcurrentVMs,
        maxLoginAttempts: settings.maxLoginAttempts,
        lockoutDuration: settings.lockoutDuration,
        minPasswordLength: settings.minPasswordLength,
        requirePasswordComplexity: settings.requirePasswordComplexity,
        smtpHost: settings.smtpHost,
        smtpPort: settings.smtpPort,
        smtpUsername: settings.smtpUsername,
        smtpPassword: settings.smtpPassword,
        smtpSecure: settings.smtpSecure,
        fromEmail: settings.fromEmail,
        maxFileUploadSize: settings.maxFileUploadSize,
        sessionTimeout: settings.sessionTimeout,
        enableLogging: settings.enableLogging,
        logLevel: settings.logLevel,
        apiRateLimit: settings.apiRateLimit,
        loginRateLimit: settings.loginRateLimit,
      };

      const updatedSettings = await adminSettingsService.updateAdminSettings(updateDto);
      setSettings(updatedSettings);
      setMessage({ type: 'success', text: 'Settings saved successfully!' });
      
      // Reload system stats to reflect changes
      loadSystemStats();
    } catch (error) {
      console.error('Failed to save settings:', error);
      setMessage({ type: 'error', text: 'Failed to save settings' });
    } finally {
      setSaving(false);
    }
  };

  const handleMaintenanceOperation = async (operation: string, operationFn: () => Promise<any>) => {
    setActiveOperations(prev => ({ ...prev, [operation]: true }));
    setMessage(null);

    try {
      const result = await operationFn();

      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        if (operation === 'backup') {
          loadSystemStats(); // Refresh stats after backup
          loadBackups(); // Refresh backup list
        }
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      console.error(`${operation} failed:`, error);
      setMessage({ type: 'error', text: `${operation} failed` });
    } finally {
      setActiveOperations(prev => ({ ...prev, [operation]: false }));
    }
  };

  const handleDownloadBackup = async (filename: string) => {
    try {
      await adminSettingsService.downloadBackup(filename);
      setMessage({ type: 'success', text: 'Backup download started' });
    } catch (error) {
      console.error('Download failed:', error);
      setMessage({ type: 'error', text: 'Failed to download backup' });
    }
  };

  const handleDeleteBackup = async (filename: string) => {
    if (!confirm('Are you sure you want to delete this backup? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await adminSettingsService.deleteBackup(filename);
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        loadBackups(); // Refresh backup list
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      console.error('Delete failed:', error);
      setMessage({ type: 'error', text: 'Failed to delete backup' });
    }
  };

  const handleRestoreBackup = async (filename: string) => {
    if (!confirm('Are you sure you want to restore this backup? This will overwrite current data and cannot be undone.')) {
      return;
    }

    setActiveOperations(prev => ({ ...prev, [`restore-${filename}`]: true }));
    try {
      const result = await adminSettingsService.restoreBackup(filename);
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      console.error('Restore failed:', error);
      setMessage({ type: 'error', text: 'Failed to restore backup' });
    } finally {
      setActiveOperations(prev => ({ ...prev, [`restore-${filename}`]: false }));
    }
  };

  const handleUploadBackup = async (file: File) => {
    setActiveOperations(prev => ({ ...prev, upload: true }));
    try {
      const result = await adminSettingsService.uploadBackup(file);
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        loadBackups(); // Refresh backup list
        setShowBackupUpload(false);
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      console.error('Upload failed:', error);
      setMessage({ type: 'error', text: 'Failed to upload backup' });
    } finally {
      setActiveOperations(prev => ({ ...prev, upload: false }));
    }
  };

  const handleTestEmail = async () => {
    if (!settings) return;

    setActiveOperations(prev => ({ ...prev, testEmail: true }));
    setMessage(null);

    try {
      const result = await adminSettingsService.testEmailSettings({
        smtpHost: settings.smtpHost,
        smtpPort: settings.smtpPort,
        smtpUsername: settings.smtpUsername,
        smtpPassword: settings.smtpPassword,
        smtpSecure: settings.smtpSecure,
        fromEmail: settings.fromEmail,
      });

      if (result.success) {
        setMessage({ type: 'success', text: result.message });
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      console.error('Email test failed:', error);
      setMessage({ type: 'error', text: 'Email test failed' });
    } finally {
      setActiveOperations(prev => ({ ...prev, testEmail: false }));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="flex items-center space-x-3 text-purple-200/80">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500/30 border-t-purple-400"></div>
          <span className="text-lg">Loading settings...</span>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="text-center p-12">
        <p className="text-red-400">Failed to load admin settings</p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg border ${
          message.type === 'success' 
            ? 'bg-emerald-500/10 border-emerald-500/30 text-emerald-400' 
            : 'bg-red-500/10 border-red-500/30 text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            <span>{message.text}</span>
          </div>
        </div>
      )}

      {/* System Overview */}
      {systemStats && (
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 bg-indigo-500/20 rounded-lg">
              <HardDrive className="w-5 h-5 text-indigo-400" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white">System Overview</h3>
              <p className="text-sm text-slate-400">Current system status and statistics</p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div className="text-sm text-slate-400 mb-1">Storage Usage</div>
              <div className="text-2xl font-bold text-white">{systemStats.storage.totalSize} MB</div>
              <div className="text-xs text-slate-500">
                {systemStats.storage.uploadsCount + systemStats.storage.backupsCount} files
              </div>
            </div>
            
            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div className="text-sm text-slate-400 mb-1">System Uptime</div>
              <div className="text-2xl font-bold text-white">
                {adminSettingsService.formatUptime(systemStats.system.uptime)}
              </div>
              <div className="text-xs text-slate-500">{systemStats.system.platform}</div>
            </div>
            
            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div className="text-sm text-slate-400 mb-1">Last Backup</div>
              <div className="text-lg font-bold text-white">
                {adminSettingsService.formatDate(systemStats.settings.lastBackup)}
              </div>
              <div className="text-xs text-slate-500">
                {systemStats.settings.totalBackups} total backups
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Platform Settings */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-blue-500/20 rounded-lg">
            <Globe className="w-5 h-5 text-blue-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Platform Settings</h3>
            <p className="text-sm text-slate-400">Configure general platform behavior</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div>
                <div className="font-medium text-white">Allow Registration</div>
                <div className="text-sm text-slate-400">Enable new user registration</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.allowRegistration}
                  onChange={(e) => setSettings({ ...settings, allowRegistration: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div>
                <div className="font-medium text-white">Email Verification</div>
                <div className="text-sm text-slate-400">Require email verification for new accounts</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.emailVerification}
                  onChange={(e) => setSettings({ ...settings, emailVerification: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div>
                <div className="font-medium text-white">Maintenance Mode</div>
                <div className="text-sm text-slate-400">Put platform in maintenance mode</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.maintenanceMode}
                  onChange={(e) => setSettings({ ...settings, maintenanceMode: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
              </label>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div>
                <div className="font-medium text-white">Enable Teams</div>
                <div className="text-sm text-slate-400">Allow users to create and join teams</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.enableTeams}
                  onChange={(e) => setSettings({ ...settings, enableTeams: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
              </label>
            </div>

            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <label className="block text-sm font-medium text-white mb-2">
                Max Team Size
              </label>
              <input
                type="number"
                value={settings.maxTeamSize}
                onChange={(e) => setSettings({ ...settings, maxTeamSize: parseInt(e.target.value) || 1 })}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:border-emerald-400 focus:outline-none"
                min="1"
                max="20"
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div>
                <div className="font-medium text-white">Auto Backup</div>
                <div className="text-sm text-slate-400">Automatically backup system data</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.autoBackup}
                  onChange={(e) => setSettings({ ...settings, autoBackup: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* VM Settings */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-purple-500/20 rounded-lg">
            <Server className="w-5 h-5 text-purple-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Virtual Machine Settings</h3>
            <p className="text-sm text-slate-400">Configure VM behavior and limits</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
            <label className="block text-sm font-medium text-white mb-2">
              <Clock className="w-4 h-4 inline mr-2" />
              Default Session Time (hours)
            </label>
            <input
              type="number"
              value={settings.defaultSessionTime}
              onChange={(e) => setSettings({ ...settings, defaultSessionTime: parseInt(e.target.value) || 1 })}
              className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:border-emerald-400 focus:outline-none"
              min="1"
              max="24"
            />
            <p className="text-xs text-slate-400 mt-1">How long VMs stay active by default</p>
          </div>

          <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
            <label className="block text-sm font-medium text-white mb-2">
              <Users className="w-4 h-4 inline mr-2" />
              Max Concurrent VMs per User
            </label>
            <input
              type="number"
              value={settings.maxConcurrentVMs}
              onChange={(e) => setSettings({ ...settings, maxConcurrentVMs: parseInt(e.target.value) || 1 })}
              className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:border-emerald-400 focus:outline-none"
              min="1"
              max="10"
            />
            <p className="text-xs text-slate-400 mt-1">Maximum VMs a user can run simultaneously</p>
          </div>
        </div>
      </div>

      {/* Email Settings */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-pink-500/20 rounded-lg">
            <Mail className="w-5 h-5 text-pink-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Email Settings</h3>
            <p className="text-sm text-slate-400">Configure SMTP settings for email notifications</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <label className="block text-sm font-medium text-white mb-2">SMTP Host</label>
              <input
                type="text"
                value={settings.smtpHost}
                onChange={(e) => setSettings({ ...settings, smtpHost: e.target.value })}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:border-emerald-400 focus:outline-none"
                placeholder="smtp.gmail.com"
              />
            </div>

            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <label className="block text-sm font-medium text-white mb-2">SMTP Port</label>
              <input
                type="number"
                value={settings.smtpPort}
                onChange={(e) => setSettings({ ...settings, smtpPort: parseInt(e.target.value) || 587 })}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:border-emerald-400 focus:outline-none"
                placeholder="587"
              />
            </div>

            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <label className="block text-sm font-medium text-white mb-2">From Email</label>
              <input
                type="email"
                value={settings.fromEmail}
                onChange={(e) => setSettings({ ...settings, fromEmail: e.target.value })}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:border-emerald-400 focus:outline-none"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <label className="block text-sm font-medium text-white mb-2">SMTP Username</label>
              <input
                type="text"
                value={settings.smtpUsername}
                onChange={(e) => setSettings({ ...settings, smtpUsername: e.target.value })}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:border-emerald-400 focus:outline-none"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <label className="block text-sm font-medium text-white mb-2">SMTP Password</label>
              <input
                type="password"
                value={settings.smtpPassword}
                onChange={(e) => setSettings({ ...settings, smtpPassword: e.target.value })}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:border-emerald-400 focus:outline-none"
                placeholder="••••••••"
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <div>
                <div className="font-medium text-white">Use SSL/TLS</div>
                <div className="text-sm text-slate-400">Enable secure connection</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.smtpSecure}
                  onChange={(e) => setSettings({ ...settings, smtpSecure: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
              </label>
            </div>

            <button
              onClick={handleTestEmail}
              disabled={activeOperations.testEmail}
              className="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              {activeOperations.testEmail ? (
                <Loader className="w-4 h-4 animate-spin" />
              ) : (
                <Mail className="w-4 h-4" />
              )}
              <span>{activeOperations.testEmail ? 'Testing...' : 'Test Email Settings'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* System Maintenance */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-orange-500/20 rounded-lg">
            <Database className="w-5 h-5 text-orange-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">System Maintenance</h3>
            <p className="text-sm text-slate-400">Backup and maintenance settings</p>
          </div>
        </div>
        
        <div className="space-y-6">
          {/* Backup Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <label className="block text-sm font-medium text-white mb-2">
                Backup Interval (hours)
              </label>
              <input
                type="number"
                value={settings.backupInterval}
                onChange={(e) => setSettings({ ...settings, backupInterval: parseInt(e.target.value) || 1 })}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:border-emerald-400 focus:outline-none"
                min="1"
                max="168"
              />
              <p className="text-xs text-slate-400 mt-1">How often to create system backups</p>
            </div>

            <div className="space-y-3">
              <button
                onClick={() => handleMaintenanceOperation('backup', adminSettingsService.createBackup)}
                disabled={activeOperations.backup}
                className="w-full px-4 py-3 bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-600/50 text-white rounded-lg transition-colors flex items-center justify-center space-x-2"
              >
                {activeOperations.backup ? (
                  <Loader className="w-4 h-4 animate-spin" />
                ) : (
                  <Database className="w-4 h-4" />
                )}
                <span>{activeOperations.backup ? 'Creating...' : 'Create Backup Now'}</span>
              </button>

              <button
                onClick={() => setShowBackupUpload(!showBackupUpload)}
                className="w-full px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center justify-center space-x-2"
              >
                <Upload className="w-4 h-4" />
                <span>Upload Backup</span>
              </button>
            </div>
          </div>

          {/* Backup Upload Section */}
          {showBackupUpload && (
            <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
              <h4 className="text-lg font-medium text-white mb-4">Upload Backup File</h4>
              <input
                type="file"
                accept=".tar.gz,.zip"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleUploadBackup(file);
                  }
                }}
                disabled={activeOperations.upload}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white focus:border-purple-400 focus:outline-none file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-purple-600 file:text-white hover:file:bg-purple-700"
              />
              <p className="text-xs text-slate-400 mt-2">Supports .zip and .tar.gz backup files</p>
              {activeOperations.upload && (
                <div className="flex items-center space-x-2 mt-2 text-purple-400">
                  <Loader className="w-4 h-4 animate-spin" />
                  <span>Uploading backup...</span>
                </div>
              )}
            </div>
          )}

          {/* Existing Backups */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-medium text-white">Existing Backups</h4>
              <button
                onClick={loadBackups}
                className="px-3 py-1 bg-slate-700 hover:bg-slate-600 text-white rounded text-sm transition-colors"
              >
                Refresh
              </button>
            </div>

            {backups.length === 0 ? (
              <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700 text-center">
                <p className="text-slate-400">No backups found</p>
              </div>
            ) : (
              <div className="space-y-2">
                {backups.map((backup) => (
                  <div key={backup.filename} className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-white font-medium">{backup.filename}</h5>
                        <p className="text-sm text-slate-400">
                          Size: {backup.sizeFormatted} • Created: {new Date(backup.createdAt).toLocaleString()}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleDownloadBackup(backup.filename)}
                          className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors flex items-center space-x-1"
                        >
                          <Download className="w-3 h-3" />
                          <span>Download</span>
                        </button>
                        <button
                          onClick={() => handleRestoreBackup(backup.filename)}
                          disabled={activeOperations[`restore-${backup.filename}`]}
                          className="px-3 py-1 bg-green-600 hover:bg-green-700 disabled:bg-green-600/50 text-white rounded text-sm transition-colors flex items-center space-x-1"
                        >
                          {activeOperations[`restore-${backup.filename}`] ? (
                            <Loader className="w-3 h-3 animate-spin" />
                          ) : (
                            <Database className="w-3 h-3" />
                          )}
                          <span>Restore</span>
                        </button>
                        <button
                          onClick={() => handleDeleteBackup(backup.filename)}
                          className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors flex items-center space-x-1"
                        >
                          <Trash2 className="w-3 h-3" />
                          <span>Delete</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Other Maintenance Operations */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => handleMaintenanceOperation('logs', adminSettingsService.downloadSystemLogs)}
              disabled={activeOperations.logs}
              className="px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              {activeOperations.logs ? (
                <Loader className="w-4 h-4 animate-spin" />
              ) : (
                <Download className="w-4 h-4" />
              )}
              <span>{activeOperations.logs ? 'Preparing...' : 'Download System Logs'}</span>
            </button>

            <button
              onClick={() => handleMaintenanceOperation('cache', adminSettingsService.clearCache)}
              disabled={activeOperations.cache}
              className="px-4 py-3 bg-orange-600 hover:bg-orange-700 disabled:bg-orange-600/50 text-white rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              {activeOperations.cache ? (
                <Loader className="w-4 h-4 animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4" />
              )}
              <span>{activeOperations.cache ? 'Clearing...' : 'Clear Cache'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={saving}
          className="flex items-center space-x-2 px-8 py-4 bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-600/50 text-white rounded-lg transition-colors text-lg font-medium"
        >
          {saving ? (
            <Loader className="w-5 h-5 animate-spin" />
          ) : (
            <Save className="w-5 h-5" />
          )}
          <span>{saving ? 'Saving...' : 'Save All Settings'}</span>
        </button>
      </div>
    </div>
  );
}