# HardWork Challenge Architecture

This project implements a multi-service vulnerable environment for security testing and CTF challenges.

## 🏗️ Architecture Overview

```
hardwork/
├── Dockerfile                 # Main container build configuration
├── docker-compose.yml        # Service orchestration
├── entrypoint.sh             # Container startup script
├── setup_imagemagick.sh      # ImageMagick setup script
├── README.md                 # This documentation
└── machine/                  # All application components
    ├── apache/               # Apache web server configurations
    │   ├── 000-default.conf
    │   ├── gym-local.conf
    │   ├── hardwork.kybs.conf
    │   ├── index.html
    │   ├── networkManagementlocalAdministrationP4nel.hardwork.kybs.conf
    │   ├── ports.conf
    │   └── uploadsSubdomain.hardwork.kybs
    ├── backdoor/             # Backdoor components
    │   ├── ahaha
    │   └── bdob.php
    ├── ftpserver/            # FTP server configuration
    │   ├── ftp_files/
    │   └── vsftpd.conf
    ├── network-app/          # Network management Next.js application
    │   ├── components/
    │   ├── pages/
    │   ├── public/
    │   ├── styles/
    │   ├── package.json
    │   └── ...
    ├── nextjs-app/           # Main Next.js application
    │   ├── app/
    │   ├── components/
    │   ├── hooks/
    │   ├── lib/
    │   ├── package.json
    │   └── ...
    ├── repo/                 # Repository files and secrets
    │   ├── cloud.passwd.gpg
    │   ├── decrypted.txt
    │   ├── dev-project.pdf
    │   ├── id_ed25519
    │   └── id_ed25519.pub
    ├── smbserver/            # Samba server configuration
    │   ├── shared/
    │   │   ├── admin/
    │   │   ├── dev/
    │   │   └── guest/
    │   └── smb.conf
    └── web-machine/          # Flask web application
        ├── __pycache__/
        ├── instance/
        ├── public/
        ├── templates/
        ├── app.py
        ├── init_db.py
        └── requirements.txt
```

## 🚀 Services

### Web Services
- **Apache HTTP Server** (Port 8080): Main web server with multiple virtual hosts
- **Next.js Applications**: 
  - Main application at `/opt/nextjs-app`
  - Network management app at `/opt/network-app`
- **Flask Application**: Python web app at `/home/<USER>/app/`

### File Services
- **FTP Server** (Port 21): vsftpd with passive ports 10000-10100
- **Samba Server** (Ports 139, 445): SMB file sharing with multiple shares

### Security Features
- **ImageMagick**: Installed with potential vulnerabilities
- **Backdoors**: Hidden PHP backdoors in obscure paths
- **User Accounts**: Developer user with configured credentials

## 🔧 Quick Start

1. **Build and run the container:**
   ```bash
   docker-compose up --build
   ```

2. **Access services:**
   - Web interface: http://localhost:8080
   - FTP: ftp://localhost:21
   - SMB shares: //localhost/[guest|dev|admin]

## 🛠️ Development

### Adding New Components
All application components should be placed in the `machine/` directory to maintain the clean architecture.

### Modifying Services
- Apache configurations: `machine/apache/`
- Application code: `machine/nextjs-app/`, `machine/network-app/`, `machine/web-machine/`
- Server configurations: `machine/ftpserver/`, `machine/smbserver/`

## 🔒 Security Notes

This environment contains intentional vulnerabilities for educational purposes:
- Weak credentials
- Exposed services
- Potential privilege escalation paths
- Hidden backdoors

**⚠️ WARNING: Only use in isolated environments for security testing!**

## 📝 Configuration Files

- `Dockerfile`: Multi-service container build
- `docker-compose.yml`: Service orchestration and port mapping
- `entrypoint.sh`: Container initialization script
- `setup_imagemagick.sh`: ImageMagick vulnerability setup

## 🎯 Challenge Objectives

This environment is designed for:
- Web application security testing
- Network service enumeration
- File system exploration
- Privilege escalation practice
- CTF-style challenges

## 🤝 Contributing

When contributing:
1. Keep all application code in the `machine/` directory
2. Update this README for architectural changes
3. Test with `docker-compose up --build`
4. Document any new vulnerabilities or features
