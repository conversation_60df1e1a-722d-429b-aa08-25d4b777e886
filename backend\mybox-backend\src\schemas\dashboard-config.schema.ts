import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type DashboardConfigDocument = DashboardConfig & Document;

@Schema({ timestamps: true })
export class DashboardConfig {
  @Prop({ required: true, unique: true, default: 'default' })
  configId: string;

  // Event Information
  @Prop({ default: 'MyBox CTF Platform' })
  eventName: string;

  @Prop({ default: '' })
  eventDescription: string;

  @Prop({ default: '' })
  eventCoverImage: string;

  @Prop({ default: '' })
  eventMessage: string;

  @Prop({ type: Date })
  eventStartDate?: Date;

  @Prop({ type: Date })
  eventEndDate?: Date;

  // Social Links
  @Prop({ 
    type: {
      github: { type: String, default: '' },
      facebook: { type: String, default: '' },
      instagram: { type: String, default: '' },
      linkedin: { type: String, default: '' },
      twitter: { type: String, default: '' },
      discord: { type: String, default: '' },
      website: { type: String, default: '' },
    },
    default: {
      github: '',
      facebook: '',
      instagram: '',
      linkedin: '',
      twitter: '',
      discord: '',
      website: '',
    },
    _id: false
  })
  socialLinks: {
    github: string;
    facebook: string;
    instagram: string;
    linkedin: string;
    twitter: string;
    discord: string;
    website: string;
  };

  // Sponsor Banners
  @Prop({
    type: [{
      id: { type: String, required: true },
      name: { type: String, required: true },
      logo: { type: String, required: true },
      website: { type: String, default: '' },
      description: { type: String, default: '' },
      tier: { type: String, enum: ['platinum', 'gold', 'silver', 'bronze'], default: 'bronze' },
      isActive: { type: Boolean, default: true },
      order: { type: Number, default: 0 },
    }],
    default: [],
    _id: false
  })
  sponsors: Array<{
    id: string;
    name: string;
    logo: string;
    website: string;
    description: string;
    tier: 'platinum' | 'gold' | 'silver' | 'bronze';
    isActive: boolean;
    order: number;
  }>;

  // Theme Configuration
  @Prop({
    type: {
      primaryColor: { type: String, default: '#8b5cf6' },
      secondaryColor: { type: String, default: '#ec4899' },
      accentColor: { type: String, default: '#06b6d4' },
      backgroundColor: { type: String, default: '#0f0f23' },
    },
    default: {
      primaryColor: '#8b5cf6',
      secondaryColor: '#ec4899',
      accentColor: '#06b6d4',
      backgroundColor: '#0f0f23',
    },
    _id: false
  })
  theme: {
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    backgroundColor: string;
  };

  // Dashboard Features
  @Prop({
    type: {
      showStats: { type: Boolean, default: true },
      showRecentActivity: { type: Boolean, default: true },
      showQuickActions: { type: Boolean, default: true },
      showLeaderboard: { type: Boolean, default: true },
      showAnnouncements: { type: Boolean, default: true },
      showSponsors: { type: Boolean, default: true },
    },
    default: {
      showStats: true,
      showRecentActivity: true,
      showQuickActions: true,
      showLeaderboard: true,
      showAnnouncements: true,
      showSponsors: true,
    },
    _id: false
  })
  features: {
    showStats: boolean;
    showRecentActivity: boolean;
    showQuickActions: boolean;
    showLeaderboard: boolean;
    showAnnouncements: boolean;
    showSponsors: boolean;
  };

  @Prop({ type: Types.ObjectId, ref: 'User', default: () => new Types.ObjectId('000000000000000000000000') })
  updatedBy: Types.ObjectId;

  @Prop({ default: true })
  isActive: boolean;
}

export const DashboardConfigSchema = SchemaFactory.createForClass(DashboardConfig);