import { IsString, IsEnum, IsOptional, IsBoolean, IsArray, IsObject, IsDateString } from 'class-validator';
import { NotificationType, NotificationPriority } from '../../schemas/notification.schema';

export class CreateNotificationDto {
  @IsString()
  title: string;

  @IsString()
  message: string;

  @IsEnum(NotificationType)
  type: NotificationType;

  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @IsOptional()
  @IsArray()
  recipients?: string[];

  @IsOptional()
  @IsBoolean()
  isGlobal?: boolean;

  @IsOptional()
  @IsObject()
  metadata?: {
    challengeId?: string;
    challengeName?: string;
    username?: string;
    teamName?: string;
    points?: number;
    icon?: string;
    color?: string;
    actionUrl?: string;
    [key: string]: any;
  };

  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}

export class UpdateNotificationDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  message?: string;

  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}

export class MarkAsReadDto {
  @IsArray()
  notificationIds: string[];
}

export class NotificationQueryDto {
  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @IsOptional()
  @IsBoolean()
  unreadOnly?: boolean;

  @IsOptional()
  @IsString()
  page?: string;

  @IsOptional()
  @IsString()
  limit?: string;
}