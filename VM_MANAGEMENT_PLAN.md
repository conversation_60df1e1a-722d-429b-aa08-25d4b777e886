# VM Management System - HackTheBox Style Implementation

## 🎯 Overview

This document outlines the implementation plan for a HackTheBox-style virtual machine management system that allows users and teams to spawn isolated pentesting lab instances from Docker containers or OVA files.

## 🏗️ Architecture Overview

### Core Concepts

1. **Pentesting Machines**: Virtual vulnerable systems for cybersecurity training
2. **User Isolation**: Each user gets their own isolated instance 
3. **Team Sharing**: Team members can share access to the same instance
4. **Multi-format Support**: Docker containers and OVA/VMDK files
5. **Network Isolation**: Isolated networks with unique IP assignment
6. **Resource Management**: Time-based access control and automatic cleanup

### Technology Stack

- **Container Orchestration**: Docker + Docker Compose
- **VM Management**: QEMU/KVM for OVA files (when needed)
- **Network Isolation**: Docker networks with custom subnets
- **Reverse Proxy**: Nginx for port forwarding and access control
- **Job Scheduling**: Bull Queue (Redis) for background tasks
- **File Storage**: Local filesystem + optional cloud storage

## 📊 Database Schema Design

### Machine Types & Instances

```typescript
// Machine Templates (The "boxes" like HTB machines)
interface MachineTemplate {
  _id: ObjectId;
  name: string; // e.g., "Lame", "Legacy", "Blue"
  slug: string; // URL-friendly name
  description: string;
  
  // Classification
  category: 'web' | 'crypto' | 'pwn' | 'reverse' | 'forensics' | 'misc' | 'osint';
  difficulty: 'easy' | 'medium' | 'hard' | 'insane';
  os: 'linux' | 'windows' | 'other';
  
  // Machine Configuration
  machineType: 'docker' | 'ova' | 'qcow2';
  dockerImage?: string; // For Docker-based machines
  dockerFile?: string; // Path to Dockerfile if building
  ovaFile?: string; // Path to OVA file
  
  // Network & Resources
  exposedPorts: number[]; // Ports that will be accessible
  requiredRAM: number; // MB
  requiredCPU: number; // vCPU cores
  maxInstances: number; // Max concurrent instances
  
  // Challenge Information
  flags: {
    name: string; // e.g., "user.txt", "root.txt"
    points: number;
    description?: string;
  }[];
  hints: string[];
  walkthrough?: string; // Path to writeup/solution
  
  // Access Control
  isActive: boolean;
  requiresVPN: boolean; // Must be on VPN to access
  releaseDate: Date;
  retireDate?: Date;
  
  // Metadata
  authorId: ObjectId; // ref: User
  authorName: string;
  machineIP?: string; // Static IP in lab network
  downloadCount: number;
  solveCount: number;
  rating: number; // Average user rating
  
  createdAt: Date;
  updatedAt: Date;
}

// Active Machine Instances
interface MachineInstance {
  _id: ObjectId;
  templateId: ObjectId; // ref: MachineTemplate
  
  // Ownership
  ownerId: ObjectId; // ref: User (who spawned it)
  teamId?: ObjectId; // ref: Team (if spawned by team member)
  isTeamShared: boolean; // If team members can access
  
  // Container/VM Information
  containerId?: string; // Docker container ID
  containerName: string; // Unique container name
  vmId?: string; // QEMU VM ID for OVA files
  
  // Network Configuration
  instanceIP: string; // Assigned IP (e.g., ***********)
  subnet: string; // Network subnet (e.g., **********/24)
  exposedPorts: {
    internal: number;
    external: number; // Mapped external port
    protocol: 'tcp' | 'udp';
  }[];
  
  // Access Control
  accessToken: string; // Unique token for access
  vpnRequired: boolean;
  allowedUsers: ObjectId[]; // Users who can access (team members)
  
  // State Management
  status: 'starting' | 'running' | 'stopping' | 'stopped' | 'error' | 'resetting';
  startedAt: Date;
  lastActivity: Date;
  expiresAt: Date; // Auto-shutdown time
  maxUptime: number; // Max minutes allowed
  
  // Resource Usage
  cpuUsage?: number;
  memoryUsage?: number;
  networkUsage?: number;
  
  // Point Calculation
  isFirstTeamSpawn: boolean; // For team point calculation
  spawnPointsAwarded: number; // Points given for first team spawn
  
  createdAt: Date;
  updatedAt: Date;
}

// Machine Access Logs
interface MachineAccess {
  _id: ObjectId;
  instanceId: ObjectId; // ref: MachineInstance
  userId: ObjectId; // ref: User
  
  // Access Information
  accessType: 'spawn' | 'connect' | 'terminate';
  accessMethod: 'web' | 'ssh' | 'rdp' | 'vpn';
  sourceIP: string;
  userAgent?: string;
  
  // Session Information
  sessionDuration?: number; // minutes
  commandsExecuted?: number; // for SSH sessions
  filesDownloaded?: string[]; // downloaded files
  
  createdAt: Date;
}

// Scheduled Operations (like Boxer's delayed operations)
interface MachineOperation {
  _id: ObjectId;
  instanceId: ObjectId; // ref: MachineInstance
  operationType: 'stop' | 'restart' | 'extend' | 'cleanup';
  
  // Scheduling
  scheduledBy: ObjectId; // ref: User
  scheduledAt: Date;
  executeAt: Date; // Delayed execution (e.g., auto-shutdown)
  
  // Status
  status: 'pending' | 'executing' | 'completed' | 'cancelled' | 'failed';
  cancellable: boolean; // Can user cancel this operation?
  
  // Background Job
  jobId: string; // Bull queue job ID
  attempts: number;
  lastError?: string;
  
  createdAt: Date;
  updatedAt: Date;
}

// Flag Submissions for Machines
interface MachineSubmission {
  _id: ObjectId;
  instanceId: ObjectId; // ref: MachineInstance
  templateId: ObjectId; // ref: MachineTemplate
  userId: ObjectId; // ref: User
  teamId?: ObjectId; // ref: Team
  
  // Submission Details
  flagName: string; // e.g., "user.txt", "root.txt"
  submittedFlag: string;
  isCorrect: boolean;
  
  // Point Calculation
  pointsAwarded: number; // Individual points
  teamPointsAwarded: number; // Team points (0 if already solved)
  isFirstBlood: boolean; // First solve globally
  isFirstTeamSolve: boolean; // First solve for this team
  
  // Verification
  submissionIP: string;
  submissionMethod: 'web' | 'api';
  verificationData?: any; // Additional verification info
  
  submittedAt: Date;
}

// Machine Files & Resources
interface MachineFile {
  _id: ObjectId;
  templateId: ObjectId; // ref: MachineTemplate
  
  // File Information
  fileName: string;
  filePath: string; // Relative path in machine
  fileType: 'dockerfile' | 'ova' | 'vmdk' | 'qcow2' | 'resource' | 'writeup';
  fileSize: number; // bytes
  fileHash: string; // SHA256 hash
  
  // Access Control
  isDownloadable: boolean; // Users can download?
  requiresAuth: boolean; // Requires authentication?
  
  // Metadata
  description?: string;
  uploadedBy: ObjectId; // ref: User
  
  createdAt: Date;
  updatedAt: Date;
}
```

## 🎮 Implementation Plan

### Phase 1: Core Infrastructure (3-4 hours)

#### 1.1 Database Schema Implementation
- [ ] Create MachineTemplate schema
- [ ] Create MachineInstance schema  
- [ ] Create MachineOperation schema
- [ ] Create MachineSubmission schema
- [ ] Create MachineFile schema
- [ ] Add indexes for performance

#### 1.2 Docker Management Service
- [ ] Implement Docker container lifecycle management
- [ ] Create network isolation (custom Docker networks)
- [ ] Implement port mapping and forwarding
- [ ] Add container resource monitoring
- [ ] Create container cleanup and garbage collection

#### 1.3 File Management System
- [ ] Create file upload system for OVA/Docker files
- [ ] Implement file validation and scanning
- [ ] Add file storage management (local + cloud)
- [ ] Create Dockerfile parsing and building
- [ ] Add OVA to Docker conversion (optional)

### Phase 2: VM Management APIs (2-3 hours)

#### 2.1 Machine Template Management
```typescript
// Admin APIs for managing machine templates
POST   /api/machines/templates           // Upload new machine (OVA/Docker)
GET    /api/machines/templates           // List all machine templates
GET    /api/machines/templates/:id       // Get machine template details
PUT    /api/machines/templates/:id       // Update machine template
DELETE /api/machines/templates/:id       // Delete machine template
POST   /api/machines/templates/:id/build // Build Docker image from Dockerfile
```

#### 2.2 Machine Instance Management
```typescript
// User APIs for managing machine instances
GET    /api/machines                     // List available machines
GET    /api/machines/:id                 // Get machine details
POST   /api/machines/:id/spawn           // Spawn new instance
GET    /api/machines/instances           // Get user's active instances
POST   /api/machines/instances/:id/terminate // Terminate instance
POST   /api/machines/instances/:id/restart   // Restart instance
POST   /api/machines/instances/:id/extend    // Extend runtime
GET    /api/machines/instances/:id/status    // Get instance status
GET    /api/machines/instances/:id/logs      // Get instance logs
```

#### 2.3 Flag Submission System
```typescript
// Flag submission for machines
POST   /api/machines/instances/:id/submit    // Submit flag
GET    /api/machines/instances/:id/flags     // Get available flags
GET    /api/machines/submissions             // Get user's submissions
```

#### 2.4 Team Integration
```typescript
// Team-specific machine APIs
POST   /api/teams/:id/machines/:machineId/spawn    // Spawn as team
GET    /api/teams/:id/machines/instances           // Get team instances
POST   /api/teams/:id/machines/instances/:id/share // Share with team
```

### Phase 3: Network & Security (2-3 hours)

#### 3.1 Network Isolation
- [ ] Create isolated Docker networks per user/team
- [ ] Implement dynamic IP assignment (10.10.x.x ranges)
- [ ] Add network traffic monitoring
- [ ] Create VPN integration for secure access
- [ ] Implement firewall rules and access control

#### 3.2 Security Implementation
- [ ] Container security hardening
- [ ] Resource limits and quotas per user/team
- [ ] Access token generation and validation
- [ ] Audit logging for all machine operations
- [ ] Rate limiting for spawning operations

#### 3.3 Reverse Proxy Setup
- [ ] Nginx configuration for port forwarding
- [ ] SSL/TLS termination for secure access
- [ ] Load balancing for multiple backend nodes
- [ ] WebSocket support for real-time connections

### Phase 4: Background Services (2-3 hours)

#### 4.1 Job Queue System
- [ ] Redis + Bull queue implementation
- [ ] Scheduled instance cleanup
- [ ] Automatic instance shutdown
- [ ] Resource monitoring jobs
- [ ] Health check services

#### 4.2 Resource Management
- [ ] Instance lifecycle management
- [ ] Automatic garbage collection
- [ ] Resource usage monitoring
- [ ] Capacity planning and limits
- [ ] Performance optimization

#### 4.3 Notification System
- [ ] Instance status notifications
- [ ] Resource usage alerts
- [ ] Scheduled shutdown warnings
- [ ] Team activity notifications

### Phase 5: Frontend Integration (3-4 hours)

#### 5.1 Machine Browsing Interface
- [ ] Machine catalog with filtering/search
- [ ] Machine difficulty and category displays
- [ ] Machine ratings and statistics
- [ ] Author information and writeups

#### 5.2 Instance Management Dashboard
- [ ] Active instances overview
- [ ] Real-time status monitoring
- [ ] Resource usage graphs
- [ ] Connection instructions (SSH, RDP, Web)
- [ ] Team sharing controls

#### 5.3 Interactive Features
- [ ] One-click instance spawning
- [ ] Terminal/console access (web-based)
- [ ] File download interface
- [ ] Flag submission form
- [ ] Hint system integration

### Phase 6: Advanced Features (Optional - 2-3 hours)

#### 6.1 Machine Building Tools
- [ ] Web-based Dockerfile editor
- [ ] OVA upload and conversion
- [ ] Machine testing and validation
- [ ] Automated flag verification
- [ ] Machine publishing workflow

#### 6.2 Analytics & Monitoring
- [ ] Machine popularity analytics
- [ ] Resource usage statistics
- [ ] Performance monitoring dashboard
- [ ] Error tracking and alerting
- [ ] User behavior analytics

## 🔧 Technical Implementation Details

### Docker Container Management

```typescript
class DockerMachineService {
  // Core container operations
  async spawnMachine(templateId: string, userId: string, teamId?: string): Promise<MachineInstance>;
  async terminateMachine(instanceId: string): Promise<void>;
  async restartMachine(instanceId: string): Promise<void>;
  
  // Network management
  async createUserNetwork(userId: string): Promise<string>;
  async assignStaticIP(containerId: string, network: string): Promise<string>;
  async configurePortMapping(instanceId: string): Promise<void>;
  
  // Resource monitoring
  async getInstanceStats(instanceId: string): Promise<ResourceStats>;
  async enforceResourceLimits(instanceId: string): Promise<void>;
  
  // Security
  async applySecurityPolicies(containerId: string): Promise<void>;
  async setupAccessControl(instanceId: string, allowedUsers: string[]): Promise<void>;
}
```

### Network Architecture

```yaml
# Docker Compose Example for Isolated Networks
version: '3.8'
services:
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    networks:
      - proxy-network

  user-instance-1:
    image: vulnerable-machine:latest
    networks:
      user-network-1:
        ipv4_address: **********
    environment:
      - USER_ID=${USER_ID}
      - INSTANCE_TOKEN=${TOKEN}

networks:
  proxy-network:
    driver: bridge
  user-network-1:
    driver: bridge
    ipam:
      config:
        - subnet: *********/24
```

### Security Considerations

1. **Container Isolation**: Each instance runs in isolated containers with restricted privileges
2. **Network Segmentation**: Users can only access their own instances 
3. **Resource Limits**: CPU, memory, and disk quotas per user/team
4. **Access Control**: Token-based authentication for instance access
5. **Audit Logging**: All machine operations are logged and monitored
6. **VPN Integration**: Optional VPN requirement for accessing machines
7. **Auto-cleanup**: Automatic cleanup of inactive instances

## 🚀 Development Roadmap

### Week 1: Foundation
- [ ] Complete Phase 1: Core Infrastructure
- [ ] Complete Phase 2: VM Management APIs
- [ ] Basic Docker container management

### Week 2: Features & Security  
- [ ] Complete Phase 3: Network & Security
- [ ] Complete Phase 4: Background Services
- [ ] Integration testing

### Week 3: User Interface
- [ ] Complete Phase 5: Frontend Integration
- [ ] User experience testing
- [ ] Performance optimization

### Week 4: Advanced Features & Polish
- [ ] Complete Phase 6: Advanced Features
- [ ] Security auditing
- [ ] Documentation and deployment

## 📈 Success Metrics

1. **Performance**: Instance spawn time < 30 seconds
2. **Capacity**: Support 100+ concurrent instances
3. **Security**: Zero security incidents in testing
4. **Usability**: 95% user satisfaction rating
5. **Reliability**: 99.9% uptime for machine management
6. **Scalability**: Easy horizontal scaling capability

## 🔗 Integration Points

### Existing Systems Integration
- **Teams System**: Team-based machine sharing and points
- **Challenge System**: Flag submission integration
- **Leaderboard**: Machine solve tracking
- **VPN System**: Secure access to machine instances
- **User Management**: Authentication and authorization

### External Dependencies
- **Docker Engine**: Container runtime
- **Redis**: Job queue and caching
- **Nginx**: Reverse proxy and load balancing
- **File Storage**: Local or cloud storage for machine files

## 📚 Documentation Plan

1. **API Documentation**: OpenAPI/Swagger specs
2. **Admin Guide**: Machine management and deployment
3. **User Guide**: How to use the platform
4. **Developer Guide**: Architecture and development setup
5. **Security Guide**: Security best practices and policies

---

## 🎯 Next Steps

1. **Review and Approve**: Review this plan and provide feedback
2. **Environment Setup**: Prepare development environment with Docker
3. **Schema Implementation**: Start with database schemas
4. **Docker Service**: Implement basic container management
5. **API Development**: Build core VM management APIs

**Ready to begin implementation? Please confirm the approach and we'll start with Phase 1!**
