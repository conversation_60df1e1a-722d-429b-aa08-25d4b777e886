import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  UseInterceptors,
  UploadedFile,
  Res,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { memoryStorage } from 'multer';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { NotificationsService } from './notifications.service';
import { NotificationSoundsService } from './notification-sounds.service';
import { NotificationsGateway } from './notifications.gateway';
import {
  CreateNotificationDto,
  UpdateNotificationDto,
  MarkAsReadDto,
  NotificationQueryDto,
} from './dto/notification.dto';
import {
  CreateNotificationSoundDto,
  UpdateNotificationSoundDto,
  UpdateUserNotificationSettingsDto,
} from './dto/notification-sound.dto';
import * as path from 'path';

@Controller('notifications')
@UseGuards(JwtAuthGuard)
export class NotificationsController {
  constructor(
    private readonly notificationsService: NotificationsService,
    private readonly notificationSoundsService: NotificationSoundsService,
    private readonly notificationsGateway: NotificationsGateway,
  ) {}

  @Get()
  async getUserNotifications(@Request() req, @Query() query: NotificationQueryDto) {
    return await this.notificationsService.getUserNotifications(req.user.userId, query);
  }

  @Get('unread-count')
  async getUnreadCount(@Request() req) {
    const { unreadCount } = await this.notificationsService.getUserNotifications(req.user.userId, {});
    return { count: unreadCount };
  }

  // Notification Sounds endpoints - MUST be before :id route
  @Get('sounds')
  async getAllNotificationSounds() {
    return await this.notificationSoundsService.getAllNotificationSounds();
  }

  @Get('sounds/:id')
  async getNotificationSound(@Param('id') id: string) {
    return await this.notificationSoundsService.getNotificationSoundById(id);
  }

  @Get('sounds/:id/file')
  async getNotificationSoundFile(@Param('id') id: string, @Res() res: Response) {
    const sound = await this.notificationSoundsService.getNotificationSoundById(id);
    const filePath = path.join(process.cwd(), 'uploads', 'notification-sounds', sound.fileName);
    
    // Increment usage count
    await this.notificationSoundsService.incrementSoundUsage(id);
    
    res.sendFile(filePath);
  }

  // User notification settings endpoints - MUST be before :id route
  @Get('settings')
  async getUserNotificationSettings(@Request() req) {
    return await this.notificationSoundsService.getUserNotificationSettings(req.user.userId);
  }

  @Get(':id')
  async getNotification(@Param('id') id: string, @Request() req) {
    return await this.notificationsService.getNotificationById(id, req.user.userId);
  }

  @Post('mark-as-read')
  @HttpCode(HttpStatus.OK)
  async markAsRead(@Request() req, @Body() markAsReadDto: MarkAsReadDto) {
    await this.notificationsService.markAsRead(req.user.userId, markAsReadDto.notificationIds);
    
    // Get updated unread count
    const { unreadCount } = await this.notificationsService.getUserNotifications(req.user.userId, {});
    
    return { 
      message: 'Notifications marked as read',
      unreadCount,
    };
  }

  @Post('mark-all-as-read')
  @HttpCode(HttpStatus.OK)
  async markAllAsRead(@Request() req) {
    await this.notificationsService.markAllAsRead(req.user.userId);
    
    return { 
      message: 'All notifications marked as read',
      unreadCount: 0,
    };
  }

  // Admin endpoints
  @Post('admin/send')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async sendAdminNotification(@Request() req, @Body() createNotificationDto: CreateNotificationDto) {
    const notification = await this.notificationsService.createAdminNotification(
      createNotificationDto.title,
      createNotificationDto.message,
      req.user.userId,
      createNotificationDto.recipients,
    );

    // Broadcast notification
    if (notification.isGlobal) {
      await this.notificationsGateway.broadcastGlobalNotification(notification);
    } else if (notification.recipients && notification.recipients.length > 0) {
      await this.notificationsGateway.sendNotificationToUsers(
        notification,
        notification.recipients.map(id => id.toString()),
      );
    }

    return {
      message: 'Notification sent successfully',
      notification,
    };
  }

  @Post('admin/announcement')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async createSystemAnnouncement(@Body() createNotificationDto: CreateNotificationDto) {
    const notification = await this.notificationsService.createSystemAnnouncement(
      createNotificationDto.title,
      createNotificationDto.message,
      createNotificationDto.priority,
    );

    // Broadcast announcement
    await this.notificationsGateway.broadcastGlobalNotification(notification);

    return {
      message: 'System announcement created successfully',
      notification,
    };
  }

  @Get('admin/all')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getAdminNotifications(@Query() query: NotificationQueryDto) {
    return await this.notificationsService.getAdminNotifications(query);
  }

  @Put('admin/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async updateNotification(
    @Param('id') id: string,
    @Request() req,
    @Body() updateNotificationDto: UpdateNotificationDto,
  ) {
    const notification = await this.notificationsService.updateNotification(
      id,
      updateNotificationDto,
      req.user.userId,
    );

    return {
      message: 'Notification updated successfully',
      notification,
    };
  }

  @Delete('admin/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async deleteNotification(@Param('id') id: string, @Request() req) {
    await this.notificationsService.deleteNotification(id, req.user.userId);

    return {
      message: 'Notification deleted successfully',
    };
  }

  @Post('admin/cleanup')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async cleanupExpiredNotifications() {
    await this.notificationsService.cleanupExpiredNotifications();

    return {
      message: 'Expired notifications cleaned up successfully',
    };
  }

  @Get('admin/stats')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getNotificationStats() {
    const connectedUsers = this.notificationsGateway.getConnectedUsersCount();
    
    return {
      connectedUsers,
      connectedUsersList: this.notificationsGateway.getConnectedUsers(),
    };
  }

  @Post('sounds')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @UseInterceptors(FileInterceptor('file', {
    storage: memoryStorage(),
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, callback) => {
      const allowedMimeTypes = ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp3'];
      if (allowedMimeTypes.includes(file.mimetype)) {
        callback(null, true);
      } else {
        callback(new Error('Invalid file type. Only MP3, WAV, and OGG files are allowed.'), false);
      }
    },
  }))
  async createNotificationSound(
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
  ) {
    // Debug logging
    console.log('File received:', file);
    console.log('File buffer length:', file?.buffer?.length);
    console.log('File size:', file?.size);
    console.log('File mimetype:', file?.mimetype);
    console.log('Request body:', req.body);

    // Validate required fields
    if (!req.body.name || !req.body.displayName) {
      throw new BadRequestException('Name and displayName are required');
    }

    if (!file) {
      throw new BadRequestException('Audio file is required');
    }

    // Extract form data from request body
    const createDto: CreateNotificationSoundDto = {
      name: req.body.name,
      displayName: req.body.displayName,
      description: req.body.description || '',
      isDefault: req.body.isDefault === 'true' || req.body.isDefault === true,
    };

    const sound = await this.notificationSoundsService.createNotificationSound(
      createDto,
      file,
      req.user.userId,
    );

    return {
      message: 'Notification sound created successfully',
      sound,
    };
  }

  @Put('sounds/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async updateNotificationSound(
    @Param('id') id: string,
    @Request() req,
    @Body() updateDto: UpdateNotificationSoundDto,
  ) {
    const sound = await this.notificationSoundsService.updateNotificationSound(
      id,
      updateDto,
      req.user.userId,
    );

    return {
      message: 'Notification sound updated successfully',
      sound,
    };
  }

  @Delete('sounds/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async deleteNotificationSound(@Param('id') id: string, @Request() req) {
    await this.notificationSoundsService.deleteNotificationSound(id, req.user.userId);

    return {
      message: 'Notification sound deleted successfully',
    };
  }

  @Get('admin/sounds/stats')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getNotificationSoundStats() {
    return await this.notificationSoundsService.getNotificationSoundStats();
  }

  @Put('settings')
  async updateUserNotificationSettings(
    @Request() req,
    @Body() updateDto: UpdateUserNotificationSettingsDto,
  ) {
    const settings = await this.notificationSoundsService.updateUserNotificationSettings(
      req.user.userId,
      updateDto,
    );

    return {
      message: 'Notification settings updated successfully',
      settings,
    };
  }
}