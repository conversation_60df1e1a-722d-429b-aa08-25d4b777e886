import { Strategy } from 'passport-local';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super({
      usernameField: 'email', // Use email instead of username for login
    });
  }

  async validate(email: string, password: string): Promise<any> {
    console.log('🔐 LocalStrategy.validate called for email:', email);

    const user = await this.authService.validateUser(email, password);
    if (!user) {
      console.log('❌ LocalStrategy.validate failed - user validation returned null');
      throw new UnauthorizedException('Invalid credentials');
    }

    console.log('✅ LocalStrategy.validate successful for email:', email);
    return user;
  }
}
