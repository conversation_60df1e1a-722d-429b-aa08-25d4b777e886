import React from 'react';
import { motion } from 'framer-motion';
import { SocialLinks as SocialLinksType } from '../../services/dashboard';
import { Github, Facebook, Instagram, Linkedin, Twitter, MessageCircle, Globe, ExternalLink } from 'lucide-react';

interface SocialLinksProps {
  socialLinks: SocialLinksType;
}

export function SocialLinks({ socialLinks }: SocialLinksProps) {
  const getSocialIcon = (platform: keyof SocialLinksType) => {
    const iconProps = { className: "w-6 h-6" };
    
    switch (platform) {
      case 'github':
        return <Github {...iconProps} />;
      case 'facebook':
        return <Facebook {...iconProps} />;
      case 'instagram':
        return <Instagram {...iconProps} />;
      case 'linkedin':
        return <Linkedin {...iconProps} />;
      case 'twitter':
        return <Twitter {...iconProps} />;
      case 'discord':
        return <MessageCircle {...iconProps} />;
      case 'website':
        return <Globe {...iconProps} />;
      default:
        return <ExternalLink {...iconProps} />;
    }
  };

  const getSocialColor = (platform: keyof SocialLinksType) => {
    switch (platform) {
      case 'github':
        return 'hover:bg-gray-600/20 hover:text-gray-300 hover:border-gray-400/50';
      case 'facebook':
        return 'hover:bg-blue-600/20 hover:text-blue-300 hover:border-blue-400/50';
      case 'instagram':
        return 'hover:bg-pink-600/20 hover:text-pink-300 hover:border-pink-400/50';
      case 'linkedin':
        return 'hover:bg-blue-700/20 hover:text-blue-400 hover:border-blue-500/50';
      case 'twitter':
        return 'hover:bg-sky-600/20 hover:text-sky-300 hover:border-sky-400/50';
      case 'discord':
        return 'hover:bg-indigo-600/20 hover:text-indigo-300 hover:border-indigo-400/50';
      case 'website':
        return 'hover:bg-green-600/20 hover:text-green-300 hover:border-green-400/50';
      default:
        return 'hover:bg-purple-600/20 hover:text-purple-300 hover:border-purple-400/50';
    }
  };

  const getPlatformName = (platform: keyof SocialLinksType) => {
    switch (platform) {
      case 'github':
        return 'GitHub';
      case 'facebook':
        return 'Facebook';
      case 'instagram':
        return 'Instagram';
      case 'linkedin':
        return 'LinkedIn';
      case 'twitter':
        return 'Twitter';
      case 'discord':
        return 'Discord';
      case 'website':
        return 'Website';
      default:
        return platform.charAt(0).toUpperCase() + platform.slice(1);
    }
  };

  // Filter out empty social links
  const activeSocialLinks = Object.entries(socialLinks).filter(([_, url]) => url && url.trim() !== '');

  if (activeSocialLinks.length === 0) {
    return null;
  }

  return (
    <motion.div
      className="animate-slide-in-up stagger-4"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.9, duration: 0.6 }}
    >
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2">
          Connect With Us
        </h2>
        <p className="text-purple-200/70">Follow us on social media for updates and community discussions</p>
      </div>

      <div className="flex flex-wrap justify-center gap-4">
        {activeSocialLinks.map(([platform, url], index) => (
          <motion.a
            key={platform}
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className={`group flex items-center space-x-3 px-6 py-4 glass-card border border-purple-500/30 rounded-xl transition-all duration-300 ${getSocialColor(platform as keyof SocialLinksType)}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 + index * 0.1, duration: 0.4 }}
            whileHover={{ 
              scale: 1.05, 
              y: -5,
              transition: { type: "spring", stiffness: 300 }
            }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="transition-transform duration-300 group-hover:scale-110">
              {getSocialIcon(platform as keyof SocialLinksType)}
            </div>
            <span className="font-medium text-white group-hover:text-current transition-colors">
              {getPlatformName(platform as keyof SocialLinksType)}
            </span>
            <ExternalLink className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity" />
          </motion.a>
        ))}
      </div>

      {/* Decorative elements */}
      <div className="relative mt-8">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full max-w-md h-px bg-gradient-to-r from-transparent via-purple-500/30 to-transparent" />
        </div>
        <div className="relative flex justify-center">
          <div className="px-4 bg-slate-900">
            <motion.div
              className="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.7, 1, 0.7],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </div>
      </div>
    </motion.div>
  );
}