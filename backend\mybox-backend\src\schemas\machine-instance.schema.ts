import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type MachineInstanceDocument = MachineInstance & Document;

@Schema({ timestamps: true })
export class MachineInstance {
  @Prop({ type: Types.ObjectId, ref: 'MachineTemplate', required: true })
  templateId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  ownerId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Team', required: false })
  teamId?: Types.ObjectId;

  @Prop({ default: false })
  isTeamShared: boolean;

  @Prop({ required: false })
  containerId?: string;

  @Prop({ required: true, unique: true })
  containerName: string;

  @Prop({ required: false })
  vmId?: string;

  @Prop({ required: true })
  instanceIP: string;

  @Prop({ required: true, default: '*********/16' })
  subnet: string;

  @Prop({ type: [{ 
    internal: Number, 
    external: Number, 
    protocol: { type: String, enum: ['tcp', 'udp'], default: 'tcp' }
  }], default: [] })
  exposedPorts: {
    internal: number;
    external: number;
    protocol: string;
  }[];

  @Prop({ required: true })
  accessToken: string;

  @Prop({ default: false })
  vpnRequired: boolean;

  @Prop({ type: [Types.ObjectId], ref: 'User', default: [] })
  allowedUsers: Types.ObjectId[];

  @Prop({ 
    required: true, 
    enum: ['starting', 'running', 'stopping', 'stopped', 'error', 'resetting'],
    default: 'starting'
  })
  status: string;

  @Prop({ required: true })
  startedAt: Date;
  @Prop({ required: true })
  lastActivity: Date;

  @Prop({ type: Date })
  lastWarningAt?: Date;

  @Prop({ required: true })
  expiresAt: Date;

  @Prop({ required: true, default: 60 })
  maxUptime: number; // minutes

  @Prop({ required: false })
  cpuUsage?: number;

  @Prop({ required: false })
  memoryUsage?: number;

  @Prop({ required: false })
  networkUsage?: number;

  @Prop({ default: false })
  isFirstTeamSpawn: boolean;

  @Prop({ default: 0 })
  spawnPointsAwarded: number;

  @Prop({ required: false })
  dockerNetworkId?: string;

  @Prop({ required: false })
  dockerNetworkName?: string;

  @Prop({ type: Object, default: {} })
  environmentVariables: Record<string, string>;

  @Prop({ type: [String], default: [] })
  volumeMounts: string[];

  @Prop({ required: false })
  lastError?: string;

  @Prop({ default: 0 })
  restartCount: number;
  @Prop({ required: false })
  terminatedAt?: Date;

  @Prop({ required: false })
  stoppedAt?: Date;

  @Prop({ required: false })
  terminatedBy?: Types.ObjectId;
}

export const MachineInstanceSchema = SchemaFactory.createForClass(MachineInstance);
