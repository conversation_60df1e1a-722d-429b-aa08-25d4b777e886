import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import * as yauzl from 'yauzl';
import * as Seven from 'node-7z';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface ExtractionResult {
  files: string[];
  structure: ArchiveStructure;
  extractPath: string;
}

export interface ArchiveStructure {
  hasDockerfile: boolean;
  hasDockerCompose: boolean;
  hasMachineDir: boolean;
  hasEntrypoint: boolean;
  files: string[];
  directories: string[];
  totalSize: number;
}

@Injectable()
export class ArchiveExtractorService {
  private readonly logger = new Logger(ArchiveExtractorService.name);
  private readonly maxFileSize = 500 * 1024 * 1024; // 500MB
  private readonly maxFiles = 1000;
  private readonly dangerousExtensions = [
    '.exe', '.bat', '.cmd', '.com', '.scr', '.pif',
    '.vbs', '.js', '.jar', '.sh', '.ps1', '.msi'
  ];
  private sevenZipAvailable: boolean | null = null;

  /**
   * Get 7-Zip executable path based on platform
   */
  private get7ZipPath(): string {
    if (process.platform === 'win32') {
      // Try common Windows installation paths
      const commonPaths = [
        '"C:\\Program Files\\7-Zip\\7z.exe"',
        '"C:\\Program Files (x86)\\7-Zip\\7z.exe"',
        '7z.exe' // Fallback to PATH
      ];
      return commonPaths[0]; // Start with the most common path
    } else {
      return '7z'; // Linux/macOS
    }
  }

  /**
   * Check if 7-Zip is available on the system
   */
  private async check7ZipAvailability(): Promise<boolean> {
    if (this.sevenZipAvailable !== null) {
      return this.sevenZipAvailable;
    }

    if (process.platform === 'win32') {
      // Try multiple paths on Windows
      const paths = [
        '"C:\\Program Files\\7-Zip\\7z.exe"',
        '"C:\\Program Files (x86)\\7-Zip\\7z.exe"',
        '7z.exe'
      ];

      for (const path of paths) {
        try {
          await execAsync(`${path} --help`);
          this.sevenZipAvailable = true;
          this.logger.log(`7-Zip found at: ${path}`);
          return true;
        } catch (error) {
          // Continue to next path
        }
      }

      this.sevenZipAvailable = false;
      this.logger.warn('7-Zip not found in any common Windows locations');
      return false;
    } else {
      // Linux/macOS
      try {
        await execAsync('7z --help');
        this.sevenZipAvailable = true;
        this.logger.log('7-Zip is available');
        return true;
      } catch (error) {
        this.sevenZipAvailable = false;
        this.logger.warn('7-Zip is not available');
        return false;
      }
    }
  }

  /**
   * Install 7-Zip automatically based on the operating system
   */
  private async install7Zip(): Promise<boolean> {
    this.logger.log('Attempting to install 7-Zip...');

    try {
      if (process.platform === 'win32') {
        // Windows: Don't auto-install, just provide instructions
        this.logger.warn('7-Zip not found. Please install manually from https://www.7-zip.org/');
        return false;
      } else if (process.platform === 'linux') {
        // Ubuntu/Debian - try auto-install on Linux only
        try {
          await execAsync('sudo apt-get update && sudo apt-get install -y p7zip-full');
          this.logger.log('7-Zip installed successfully via apt');
        } catch (aptError) {
          try {
            // Try yum for CentOS/RHEL
            await execAsync('sudo yum install -y p7zip p7zip-plugins');
            this.logger.log('7-Zip installed successfully via yum');
          } catch (yumError) {
            this.logger.warn('Could not install 7-Zip automatically on Linux. Please install manually.');
            return false;
          }
        }
      } else {
        this.logger.warn(`Unsupported platform: ${process.platform}`);
        return false;
      }

      // Reset availability check and verify installation
      this.sevenZipAvailable = null;
      return await this.check7ZipAvailability();
    } catch (error) {
      this.logger.error(`Failed to install 7-Zip: ${error.message}`);
      return false;
    }
  }

  /**
   * Extract archive to specified path
   */
  async extractArchive(
    filePath: string,
    extractPath: string,
    archiveType: 'zip' | '7z'
  ): Promise<ExtractionResult> {
    this.logger.log(`Extracting ${archiveType} archive: ${path.basename(filePath)}`);
    
    // Validate file size
    const stats = await fs.stat(filePath);
    if (stats.size > this.maxFileSize) {
      throw new BadRequestException(`Archive too large: ${stats.size} bytes (max: ${this.maxFileSize})`);
    }
    
    // Ensure extraction directory exists and is empty
    await fs.ensureDir(extractPath);
    await fs.emptyDir(extractPath);
    
    let result: ExtractionResult;
    
    if (archiveType === 'zip') {
      result = await this.extractZip(filePath, extractPath);
    } else if (archiveType === '7z') {
      // Check if 7-Zip is available, try to install if not
      const is7ZipAvailable = await this.check7ZipAvailability();
      if (!is7ZipAvailable) {
        this.logger.log('7-Zip not found, attempting automatic installation...');
        const installSuccess = await this.install7Zip();
        if (!installSuccess) {
          throw new BadRequestException(
            '7-Zip is required for .7z files but could not be installed automatically. ' +
            'Please install 7-Zip manually or use ZIP files instead. ' +
            `Installation commands:\n` +
            `Windows: winget install 7zip.7zip or choco install 7zip\n` +
            `Ubuntu: sudo apt-get install p7zip-full`
          );
        }
      }
      result = await this.extract7z(filePath, extractPath);
    } else {
      throw new BadRequestException('Unsupported archive format');
    }
    
    // Validate extraction results
    await this.validateExtraction(result);
    
    this.logger.log(`Successfully extracted ${result.files.length} files to ${extractPath}`);
    return result;
  }

  /**
   * Extract ZIP archive
   */
  private async extractZip(filePath: string, extractPath: string): Promise<ExtractionResult> {
    return new Promise((resolve, reject) => {
      const files: string[] = [];
      const directories: string[] = [];
      let totalSize = 0;
      
      yauzl.open(filePath, { lazyEntries: true }, (err, zipfile) => {
        if (err) {
          this.logger.error(`Failed to open ZIP file: ${err.message}`);
          return reject(new BadRequestException('Invalid ZIP file'));
        }
        
        zipfile.readEntry();
        
        zipfile.on('entry', (entry) => {
          // Security check: prevent path traversal
          if (entry.fileName.includes('..') || entry.fileName.startsWith('/')) {
            return reject(new BadRequestException(`Dangerous path detected: ${entry.fileName}`));
          }
          
          // Check file count limit
          if (files.length + directories.length >= this.maxFiles) {
            return reject(new BadRequestException(`Too many files in archive (max: ${this.maxFiles})`));
          }
          
          if (/\/$/.test(entry.fileName)) {
            // Directory entry
            directories.push(entry.fileName);
            zipfile.readEntry();
          } else {
            // File entry
            files.push(entry.fileName);
            totalSize += entry.uncompressedSize;
            
            // Check for dangerous file extensions
            const ext = path.extname(entry.fileName).toLowerCase();
            if (this.dangerousExtensions.includes(ext)) {
              this.logger.warn(`Potentially dangerous file: ${entry.fileName}`);
            }
            
            const fullPath = path.join(extractPath, entry.fileName);
            
            // Ensure directory exists
            fs.ensureDirSync(path.dirname(fullPath));
            
            zipfile.openReadStream(entry, (err, readStream) => {
              if (err) {
                return reject(new BadRequestException(`Failed to read file: ${entry.fileName}`));
              }
              
              const writeStream = fs.createWriteStream(fullPath);
              readStream.pipe(writeStream);
              
              writeStream.on('close', () => {
                zipfile.readEntry();
              });
              
              writeStream.on('error', (error) => {
                reject(new BadRequestException(`Failed to write file: ${error.message}`));
              });
            });
          }
        });
        
        zipfile.on('end', () => {
          const structure = this.analyzeStructure(extractPath, files, directories, totalSize);
          resolve({
            files,
            structure,
            extractPath
          });
        });
        
        zipfile.on('error', (error) => {
          reject(new BadRequestException(`ZIP extraction failed: ${error.message}`));
        });
      });
    });
  }

  /**
   * Extract 7z archive
   */
  private async extract7z(filePath: string, extractPath: string): Promise<ExtractionResult> {
    return new Promise((resolve, reject) => {
      const files: string[] = [];
      const directories: string[] = [];
      let totalSize = 0;

      try {
        const sevenZipPath = this.get7ZipPath();
        const stream = Seven.extractFull(filePath, extractPath, {
          $progress: true,
          $bin: sevenZipPath.replace(/"/g, '') // Remove quotes for node-7z
        });

        stream.on('data', (data) => {
          if (data.file) {
            // Security check: prevent path traversal
            if (data.file.includes('..') || data.file.startsWith('/')) {
              return reject(new BadRequestException(`Dangerous path detected: ${data.file}`));
            }

            if (data.file.endsWith('/')) {
              directories.push(data.file);
            } else {
              files.push(data.file);

              // Check for dangerous file extensions
              const ext = path.extname(data.file).toLowerCase();
              if (this.dangerousExtensions.includes(ext)) {
                this.logger.warn(`Potentially dangerous file: ${data.file}`);
              }
            }

            // Check file count limit
            if (files.length + directories.length >= this.maxFiles) {
              return reject(new BadRequestException(`Too many files in archive (max: ${this.maxFiles})`));
            }
          }
        });

        stream.on('end', () => {
          const structure = this.analyzeStructure(extractPath, files, directories, totalSize);
          resolve({
            files,
            structure,
            extractPath
          });
        });

        stream.on('error', (error) => {
          this.logger.error(`7z extraction failed: ${error.message}`);

          // Check if it's a missing 7z executable error
          if (error.message.includes('ENOENT') || error.message.includes('spawn 7z')) {
            reject(new BadRequestException(
              '7-Zip executable not found. This should not happen after installation check. ' +
              'Please restart the application or install 7-Zip manually.'
            ));
          } else {
            reject(new BadRequestException(`7z extraction failed: ${error.message}`));
          }
        });
      } catch (error) {
        this.logger.error(`Failed to start 7z extraction: ${error.message}`);

        // Handle case where 7z executable is not found
        if (error.message.includes('ENOENT') || error.message.includes('spawn 7z')) {
          reject(new BadRequestException(
            '7-Zip executable not found. This should not happen after installation check. ' +
            'Please restart the application or install 7-Zip manually.'
          ));
        } else {
          reject(new BadRequestException(`Failed to extract 7z archive: ${error.message}`));
        }
      }
    });
  }

  /**
   * Analyze extracted archive structure
   */
  private analyzeStructure(
    extractPath: string, 
    files: string[], 
    directories: string[], 
    totalSize: number
  ): ArchiveStructure {
    const structure: ArchiveStructure = {
      hasDockerfile: false,
      hasDockerCompose: false,
      hasMachineDir: false,
      hasEntrypoint: false,
      files,
      directories,
      totalSize
    };
    
    // Check for required files
    structure.hasDockerfile = files.some(f => 
      f.toLowerCase() === 'dockerfile' || f.toLowerCase().endsWith('/dockerfile')
    );
    
    structure.hasDockerCompose = files.some(f => 
      f.toLowerCase().includes('docker-compose.yml') || 
      f.toLowerCase().includes('docker-compose.yaml')
    );
    
    structure.hasMachineDir = directories.some(d => 
      d.toLowerCase().includes('machine/') || d.toLowerCase() === 'machine/'
    );
    
    structure.hasEntrypoint = files.some(f => 
      f.toLowerCase().includes('entrypoint.sh') || 
      f.toLowerCase().includes('startup.sh')
    );
    
    return structure;
  }

  /**
   * Validate machine template structure
   */
  validateMachineTemplate(structure: ArchiveStructure): boolean {
    const isValid = structure.hasDockerfile && structure.hasDockerCompose;
    
    if (!isValid) {
      const missing: string[] = [];
      if (!structure.hasDockerfile) missing.push('Dockerfile');
      if (!structure.hasDockerCompose) missing.push('docker-compose.yml');

      this.logger.warn(`Invalid machine template structure. Missing: ${missing.join(', ')}`);
    }
    
    return isValid;
  }

  /**
   * Validate extraction results
   */
  private async validateExtraction(result: ExtractionResult): Promise<void> {
    // Check if extraction path exists and has files
    const exists = await fs.pathExists(result.extractPath);
    if (!exists) {
      throw new BadRequestException('Extraction failed: path does not exist');
    }
    
    const extractedFiles = await fs.readdir(result.extractPath);
    if (extractedFiles.length === 0) {
      throw new BadRequestException('Extraction failed: no files extracted');
    }
    
    // Validate required files exist on disk
    const dockerfilePath = path.join(result.extractPath, 'Dockerfile');
    const dockerComposeExists = await fs.pathExists(dockerfilePath);
    
    if (!dockerComposeExists && result.structure.hasDockerfile) {
      this.logger.warn('Dockerfile reported in structure but not found on disk');
    }
  }

  /**
   * Clean up extracted files
   */
  async cleanupExtraction(extractPath: string): Promise<void> {
    try {
      await fs.remove(extractPath);
      this.logger.log(`Cleaned up extraction path: ${extractPath}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup extraction path: ${error.message}`);
    }
  }

  /**
   * Get archive type from file extension
   */
  getArchiveType(filename: string): 'zip' | '7z' | null {
    const ext = path.extname(filename).toLowerCase();

    if (ext === '.zip') return 'zip';
    if (ext === '.7z') return '7z';

    return null;
  }

  /**
   * Get installation instructions for 7-Zip based on the current platform
   */
  get7ZipInstallationInstructions(): string {
    const platform = process.platform;

    if (platform === 'win32') {
      return `To install 7-Zip on Windows:
• Download from: https://www.7-zip.org/download.html
• Install to default location: C:\\Program Files\\7-Zip\\
• Or use package managers:
  - winget install 7zip.7zip
  - choco install 7zip (requires Chocolatey)

Expected installation path: "C:\\Program Files\\7-Zip\\7z.exe"`;
    } else if (platform === 'linux') {
      return `To install 7-Zip on Linux, run:
• Ubuntu/Debian: sudo apt-get install p7zip-full
• CentOS/RHEL: sudo yum install p7zip p7zip-plugins
• Or: sudo dnf install p7zip p7zip-plugins`;
    } else if (platform === 'darwin') {
      return `To install 7-Zip on macOS, run:
• brew install p7zip
• Or download from: https://www.7-zip.org/download.html`;
    } else {
      return `Please install 7-Zip for your operating system (${platform}) or use ZIP files instead.`;
    }
  }

  /**
   * Check system status and provide recommendations
   */
  async getSystemStatus(): Promise<{
    platform: string;
    zipSupported: boolean;
    sevenZipSupported: boolean;
    installationInstructions?: string;
  }> {
    const platform = process.platform;
    const sevenZipSupported = await this.check7ZipAvailability();

    return {
      platform,
      zipSupported: true, // ZIP is always supported
      sevenZipSupported,
      installationInstructions: sevenZipSupported ? undefined : this.get7ZipInstallationInstructions()
    };
  }
}
