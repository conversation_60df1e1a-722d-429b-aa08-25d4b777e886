import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { DashboardStatsService } from './dashboard-stats.service';
import { DashboardConfig, DashboardConfigSchema } from '../schemas/dashboard-config.schema';
import { User, UserSchema } from '../schemas/user.schema';
import { Challenge, ChallengeSchema } from '../schemas/challenge.schema';
import { MachineInstance, MachineInstanceSchema } from '../schemas/machine-instance.schema';
import { ChallengeSubmission, ChallengeSubmissionSchema } from '../schemas/challenge-submission.schema';
import { MachineSubmission, MachineSubmissionSchema } from '../schemas/machine-submission.schema';
import { Team, TeamSchema } from '../schemas/team.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: DashboardConfig.name, schema: DashboardConfigSchema },
      { name: User.name, schema: UserSchema },
      { name: Challenge.name, schema: ChallengeSchema },
      { name: MachineInstance.name, schema: MachineInstanceSchema },
      { name: ChallengeSubmission.name, schema: ChallengeSubmissionSchema },
      { name: MachineSubmission.name, schema: MachineSubmissionSchema },
      { name: Team.name, schema: TeamSchema },
    ]),
  ],
  controllers: [DashboardController],
  providers: [DashboardService, DashboardStatsService],
  exports: [DashboardService, DashboardStatsService],
})
export class DashboardModule {}