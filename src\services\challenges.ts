// Challenges API Service
import { API_BASE_URL } from './api';

export interface Flag {
  value: string;
  isCaseSensitive: boolean;
  points: number;
  description?: string;
}

export interface ChallengeFile {
  name: string;
  path: string;
  size?: number;
  type?: string;
  description?: string;
}

export interface FirstBlood {
  userId: string;
  username: string;
  timestamp: string;
  teamId?: string;
  teamName?: string;
  flagIndex?: number;
}

export interface Challenge {
  _id: string;
  title: string;
  description: string;
  category: 'web' | 'crypto' | 'pwn' | 'reverse' | 'forensics' | 'misc';
  difficulty: 'easy' | 'medium' | 'hard' | 'insane';
  points: number;
  hints: string[];
  tags: string[];
  authorId: string;
  authorName?: string;
  isActive: boolean;
  releaseDate: string;
  solveCount: number;
  solved?: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Legacy flag support
  flag?: string;
  
  // Multi-flag support
  flags?: Flag[];
  
  // File attachments
  files?: ChallengeFile[];
  
  // First blood tracking
  firstBlood?: FirstBlood;
  flagsFirstBlood?: Record<string, FirstBlood>;
  
  // Server configuration
  dockerImage?: string;
  requiresServer?: boolean;
  serverConfig?: any;
}

export interface ChallengeSubmission {
  challengeId: string;
  title: string;
  category: string;
  difficulty: string;
  points: number;
  solvedAt: string;
  isFirstBlood: boolean;
}

export interface ChallengeStats {
  totalChallenges: number;
  solvedChallenges: number;
  completionPercentage: number;
  categoryBreakdown: {
    category: string;
    total: number;
    solved: number;
    percentage: number;
  }[];
  difficultyBreakdown: {
    difficulty: string;
    total: number;
    solved: number;
    percentage: number;
  }[];
}

export interface ChallengesResponse {
  challenges: Challenge[];
  total: number;
  page: number;
  pages: number;
  limit: number;
}

export interface FlagSubmissionResponse {
  success: boolean;
  isFirstTeamSolve?: boolean;
  isFirstBlood?: boolean;
  pointsAwarded: number;
  message: string;
}

export interface CategoriesResponse {
  categories: string[];
  difficulties: string[];
}

export class ChallengesService {
  private static getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('mybox_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  private static async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Request failed' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  static async getAllChallenges(
    page: number = 1,
    limit: number = 20,
    category?: string,
    difficulty?: string,
    search?: string,
    onlySolved?: boolean,
    onlyUnsolved?: boolean
  ): Promise<ChallengesResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (category) params.append('category', category);
    if (difficulty) params.append('difficulty', difficulty);
    if (search) params.append('search', search);
    if (onlySolved) params.append('onlySolved', 'true');
    if (onlyUnsolved) params.append('onlyUnsolved', 'true');

    const response = await fetch(`${API_BASE_URL}/challenges?${params}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<ChallengesResponse>(response);
  }

  static async getChallengeById(id: string): Promise<Challenge> {
    const response = await fetch(`${API_BASE_URL}/challenges/${id}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<Challenge>(response);
  }

  static async submitFlag(challengeId: string, flag: string): Promise<FlagSubmissionResponse> {
    const response = await fetch(`${API_BASE_URL}/challenges/submit-flag`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ challengeId, flag }),
    });

    return this.handleResponse<FlagSubmissionResponse>(response);
  }

  static async getCategories(): Promise<CategoriesResponse> {
    const response = await fetch(`${API_BASE_URL}/challenges/categories`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<CategoriesResponse>(response);
  }

  static async getChallengeStats(): Promise<ChallengeStats> {
    const response = await fetch(`${API_BASE_URL}/challenges/stats`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<ChallengeStats>(response);
  }

  static async getSolvedChallenges(): Promise<ChallengeSubmission[]> {
    const response = await fetch(`${API_BASE_URL}/challenges/solved`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<ChallengeSubmission[]>(response);
  }
}
