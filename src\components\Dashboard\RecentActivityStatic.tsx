import React, { useState, useEffect } from 'react';
import { Trophy, Flag, Play, Clock, Zap, Star, Award } from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'challenge_solved' | 'vm_started' | 'rank_up' | 'first_blood';
  title: string;
  description: string;
  timestamp: string;
  points?: number;
}

const mockActivity: ActivityItem[] = [
  {
    id: '1',
    type: 'challenge_solved',
    title: 'SQL Injection Basics',
    description: 'Solved in 45 minutes',
    timestamp: '2 hours ago',
    points: 20
  },
  {
    id: '2',
    type: 'vm_started',
    title: 'WebHawk',
    description: 'Started machine instance',
    timestamp: '3 hours ago'
  },
  {
    id: '3',
    type: 'rank_up',
    title: 'Rank Updated',
    description: 'Moved up to rank #15',
    timestamp: '1 day ago'
  },
  {
    id: '4',
    type: 'first_blood',
    title: 'First Blood!',
    description: 'First to solve "XSS Playground"',
    timestamp: '2 days ago',
    points: 50
  }
];

const getActivityIcon = (type: ActivityItem['type']) => {
  switch (type) {
    case 'challenge_solved':
      return <Flag className="w-5 h-5 text-purple-400" />;
    case 'vm_started':
      return <Play className="w-5 h-5 text-blue-400" />;
    case 'rank_up':
      return <Trophy className="w-5 h-5 text-pink-400" />;
    case 'first_blood':
      return <Award className="w-5 h-5 text-orange-400" />;
  }
};

const getActivityColor = (type: ActivityItem['type']) => {
  switch (type) {
    case 'challenge_solved':
      return 'from-purple-500/20 to-purple-600/20 border-purple-500/30';
    case 'vm_started':
      return 'from-blue-500/20 to-blue-600/20 border-blue-500/30';
    case 'rank_up':
      return 'from-pink-500/20 to-pink-600/20 border-pink-500/30';
    case 'first_blood':
      return 'from-orange-500/20 to-orange-600/20 border-orange-500/30';
  }
};

export function RecentActivity() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className={`glass-card-dark backdrop-blur-xl rounded-2xl border border-purple-500/30 p-6 neon-border ${mounted ? 'animate-slide-in-left' : 'opacity-0'}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl">
            <Clock className="w-6 h-6 text-purple-400" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-white">Recent Activity</h3>
            <p className="text-sm text-purple-200/70">Your latest achievements</p>
          </div>
        </div>
        <div className="flex space-x-1">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse"
              style={{ animationDelay: `${i * 0.2}s` }}
            />
          ))}
        </div>
      </div>

      <div className="space-y-4">
        {mockActivity.map((activity, index) => (
          <div 
            key={activity.id} 
            className={`group relative overflow-hidden rounded-xl bg-gradient-to-r ${getActivityColor(activity.type)} border p-4 hover-lift shimmer-effect ${
              mounted ? 'animate-slide-in-up' : 'opacity-0'
            }`}
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 p-3 bg-gradient-to-r from-purple-900/50 to-purple-800/50 rounded-xl border border-purple-500/30">
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-white truncate group-hover:text-purple-200 transition-colors">
                    {activity.title}
                  </h4>
                  {activity.points && (
                    <div className="flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-full">
                      <Star className="w-3 h-3 text-yellow-400" />
                      <span className="text-xs font-bold text-yellow-400">+{activity.points}</span>
                    </div>
                  )}
                </div>
                <p className="text-sm text-purple-200/80 mb-2">{activity.description}</p>
                <div className="flex items-center space-x-2">
                  <div className="w-1 h-1 bg-purple-400 rounded-full animate-pulse" />
                  <span className="text-xs text-purple-300/70">{activity.timestamp}</span>
                </div>
              </div>
            </div>

            {/* Animated border */}
            <div className="absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-r from-purple-500/0 via-purple-500/50 to-purple-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl">
          <div className="flex items-center space-x-2">
            <span>View All Activity</span>
            <Zap className="w-4 h-4" />
          </div>
        </button>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-purple-400/40 rounded-full animate-float"
            style={{
              left: `${10 + i * 20}%`,
              top: `${10 + i * 15}%`,
              animationDelay: `${i * 0.8}s`,
              animationDuration: `${4 + i}s`
            }}
          />
        ))}
      </div>
    </div>
  );
}