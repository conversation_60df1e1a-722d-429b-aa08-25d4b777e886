import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

// Remove hardcoded category type - now dynamic
export type ChallengeDifficulty = 'easy' | 'medium' | 'hard' | 'insane';

// Define a Flag schema for multiple flags with different values and behaviors
export class Flag {
  @Prop({ required: true })
  value: string;

  @Prop({ default: false })
  isCaseSensitive: boolean;

  @Prop({ default: 100 })
  points: number;

  @Prop({ default: '' })
  description: string;
}

// Define a File schema for challenge attachments
export class ChallengeFile {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  path: string;

  @Prop()
  size: number;

  @Prop()
  type: string;

  @Prop()
  description: string;
}

// Define a FirstBlood schema to track first solvers
export class FirstBlood {
  @Prop({ type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;

  @Prop()
  username: string;

  @Prop()
  timestamp: Date;

  @Prop({ type: Types.ObjectId, ref: 'Team' })
  teamId: Types.ObjectId;

  @Prop()
  teamName: string;
  
  @Prop({ type: Number, default: -1 })
  flagIndex: number;
}

@Schema({ timestamps: true })
export class Challenge extends Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop({
    type: String,
    required: true
  })
  category: string;

  @Prop({ 
    type: String,
    enum: ['easy', 'medium', 'hard', 'insane'],
    required: true
  })
  difficulty: ChallengeDifficulty;

  @Prop({ required: true, default: 0 })
  points: number;

  // Legacy single flag support
  @Prop({ required: false })
  flag: string;

  // Support for multiple flags
  @Prop({ type: [Object], default: [] })
  flags: Flag[];

  @Prop({ type: [String], default: [] })
  hints: string[];

  @Prop({ type: [String], default: [] })
  tags: string[];
  // Author information
  @Prop({ type: Types.ObjectId, ref: 'User' })
  authorId: Types.ObjectId;
  
  @Prop()
  authorName: string;

  // File attachments
  @Prop({ type: [Object], default: [] })
  files: ChallengeFile[];

  // First blood information for legacy single flag
  @Prop({ type: Object, default: null })
  firstBlood: FirstBlood;
  
  // First blood information for multiple flags (indexed by flagIndex)
  @Prop({ type: Map, of: Object, default: {} })
  flagsFirstBlood: Map<string, FirstBlood>;

  @Prop({ default: true })
  isActive: boolean;

  @Prop()
  releaseDate: Date;

  @Prop()
  expiryDate: Date;

  @Prop({ default: 0 })
  solveCount: number;

  // Additional challenge properties  @Prop()
  dockerImage: string;

  @Prop({ default: false })
  requiresServer: boolean;

  @Prop({ type: Object, default: {} })
  serverConfig: Record<string, any>;

  createdAt: Date;
  updatedAt: Date;
}

export const ChallengeSchema = SchemaFactory.createForClass(Challenge);

// Create indexes
ChallengeSchema.index({ category: 1, difficulty: 1 });
ChallengeSchema.index({ isActive: 1 });
ChallengeSchema.index({ releaseDate: 1 });
ChallengeSchema.index({ solveCount: -1 });
