import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { AppState, User, Challenge, VM, LeaderboardEntry, UserLeaderboardResponse, TeamLeaderboardResponse, UserLeaderboardEntry, TeamLeaderboardEntry, UserRanking, TeamRanking } from '../types';
import { AuthService } from '../services/api';
import { ChallengesService } from '../services/challenges';
import { CategoriesService, Category } from '../services/categories';
import { leaderboardService } from '../services/leaderboard';

interface AppContextType {
  state: AppState;
  login: (email: string, password: string) => Promise<any>;
  logout: () => void;
  register: (username: string, email: string, password: string) => Promise<any>;
  startVM: (vmId: string) => Promise<void>;
  stopVM: (vmId: string) => Promise<void>;
  resetVM: (vmId: string) => Promise<void>;
  submitFlag: (challengeId: string, flag: string) => Promise<boolean>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  fetchChallenges: () => Promise<void>;
  fetchCategories: () => Promise<void>;
  fetchUserLeaderboard: (page?: number, limit?: number) => Promise<void>;
  fetchTeamLeaderboard: (page?: number, limit?: number) => Promise<void>;
  fetchTopUsers: (limit?: number) => Promise<void>;
  fetchTopTeams: (limit?: number) => Promise<void>;
  fetchUserRanking: (userId: string) => Promise<void>;
  fetchTeamRanking: (teamId: string) => Promise<void>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Mock data
const mockUser: User = {
  id: '1',
  username: 'hackmaster',
  email: '<EMAIL>',
  role: 'user',
  score: 1337,
  rank: 15,
  avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
  joinedDate: '2024-01-15',
  lastActive: '2024-12-30T10:30:00Z',
  apiToken: 'htb_api_token_1234567890abcdef'
};

const mockChallenges: Challenge[] = [
  {
    id: '1',
    title: 'SQL Injection Basics',
    description: 'Learn the fundamentals of SQL injection attacks by exploiting a vulnerable login form.',
    category: 'web',
    difficulty: 'easy',
    points: 20,
    solves: 1247,
    hints: ['Try different SQL payloads', 'Focus on bypassing authentication'],
    isSolved: true,
    releaseDate: '2024-01-10',
    author: 'webmaster',
    tags: ['sql', 'injection', 'authentication']
  },
  {
    id: '2',
    title: 'RSA Encryption',
    description: 'Decrypt a message encrypted with RSA by exploiting weak key generation.',
    category: 'crypto',
    difficulty: 'medium',
    points: 30,
    solves: 856,
    hints: ['Check the key size', 'Factor the modulus'],
    isSolved: false,
    releaseDate: '2024-01-12',
    author: 'cryptoking',
    tags: ['rsa', 'factorization', 'encryption']
  },
  {
    id: '3',
    title: 'Buffer Overflow',
    description: 'Exploit a buffer overflow vulnerability to gain shell access.',
    category: 'pwn',
    difficulty: 'hard',
    points: 40,
    solves: 423,
    hints: ['Find the offset', 'Use ROP chains'],
    isSolved: false,
    releaseDate: '2024-01-15',
    author: 'pwnmaster',
    tags: ['buffer-overflow', 'rop', 'exploitation']
  },
  {
    id: '4',
    title: 'Reverse Engineering',
    description: 'Analyze a binary to understand its functionality and extract the flag.',
    category: 'reverse',
    difficulty: 'insane',
    points: 50,
    solves: 127,
    hints: ['Use dynamic analysis', 'Check for anti-debugging'],
    isSolved: false,
    releaseDate: '2024-01-20',
    author: 'reverser',
    tags: ['reverse', 'binary', 'analysis']
  }
];

const mockVMs: VM[] = [
  {
    id: '1',
    name: 'WebHawk',
    ipAddress: '************',
    status: 'running',
    timeRemaining: 3600,
    maxTime: 7200,
    ports: [80, 443, 22],
    os: 'Linux',
    difficulty: 'easy',
    category: 'web'
  },
  {
    id: '2',
    name: 'CryptoDome',
    ipAddress: '************',
    status: 'stopped',
    timeRemaining: 0,
    maxTime: 7200,
    ports: [8080, 22],
    os: 'Linux',
    difficulty: 'medium',
    category: 'crypto'
  }
];

const mockLeaderboard: LeaderboardEntry[] = [
  { rank: 1, user: { ...mockUser, id: '2', username: 'elite_hacker', score: 5420 }, score: 5420, solvedChallenges: 87, lastSolved: '2024-12-30T09:15:00Z' },
  { rank: 2, user: { ...mockUser, id: '3', username: 'cyber_ninja', score: 4890 }, score: 4890, solvedChallenges: 76, lastSolved: '2024-12-30T08:30:00Z' },
  { rank: 3, user: { ...mockUser, id: '4', username: 'code_breaker', score: 4250 }, score: 4250, solvedChallenges: 68, lastSolved: '2024-12-29T22:45:00Z' },
  { rank: 4, user: mockUser, score: 1337, solvedChallenges: 23, lastSolved: '2024-12-29T16:20:00Z' }
];

const initialState: AppState = {
  auth: {
    user: null,
    isAuthenticated: false,
    isLoading: true
  },
  challenges: mockChallenges,
  categories: [],
  vms: mockVMs,
  leaderboard: mockLeaderboard,
  userLeaderboard: null,
  teamLeaderboard: null,
  topUsers: [],
  topTeams: [],
  userRanking: null,
  teamRanking: null,
  stats: {
    totalUsers: 1247,
    totalChallenges: 156,
    activeVMs: 89,
    totalSolves: 12847
  }
};

type Action =
  | { type: 'SET_AUTH'; payload: { user: User | null; isAuthenticated: boolean; isLoading: boolean } }
  | { type: 'UPDATE_VM'; payload: { vmId: string; updates: Partial<VM> } }
  | { type: 'UPDATE_CHALLENGE'; payload: { challengeId: string; updates: Partial<Challenge> } }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'SET_CHALLENGES'; payload: Challenge[] }
  | { type: 'SET_CATEGORIES'; payload: Category[] }
  | { type: 'SET_USER_LEADERBOARD'; payload: UserLeaderboardResponse }
  | { type: 'SET_TEAM_LEADERBOARD'; payload: TeamLeaderboardResponse }
  | { type: 'SET_TOP_USERS'; payload: UserLeaderboardEntry[] }
  | { type: 'SET_TOP_TEAMS'; payload: TeamLeaderboardEntry[] }
  | { type: 'SET_USER_RANKING'; payload: UserRanking }
  | { type: 'SET_TEAM_RANKING'; payload: TeamRanking };

function appReducer(state: AppState, action: Action): AppState {
  switch (action.type) {
    case 'SET_AUTH':
      return {
        ...state,
        auth: action.payload
      };
    case 'UPDATE_VM':
      return {
        ...state,
        vms: state.vms.map(vm => 
          vm.id === action.payload.vmId 
            ? { ...vm, ...action.payload.updates }
            : vm
        )
      };
    case 'UPDATE_CHALLENGE':
      return {
        ...state,
        challenges: state.challenges.map(challenge =>
          challenge.id === action.payload.challengeId
            ? { ...challenge, ...action.payload.updates }
            : challenge
        )
      };
    case 'UPDATE_USER':
      return {
        ...state,
        auth: {
          ...state.auth,
          user: state.auth.user ? { ...state.auth.user, ...action.payload } : null
        }
      };
    case 'SET_CHALLENGES':
      return {
        ...state,
        challenges: action.payload
      };
    case 'SET_CATEGORIES':
      return {
        ...state,
        categories: action.payload
      };
    case 'SET_USER_LEADERBOARD':
      return {
        ...state,
        userLeaderboard: action.payload
      };
    case 'SET_TEAM_LEADERBOARD':
      return {
        ...state,
        teamLeaderboard: action.payload
      };
    case 'SET_TOP_USERS':
      return {
        ...state,
        topUsers: action.payload
      };
    case 'SET_TOP_TEAMS':
      return {
        ...state,
        topTeams: action.payload
      };
    case 'SET_USER_RANKING':
      return {
        ...state,
        userRanking: action.payload
      };
    case 'SET_TEAM_RANKING':
      return {
        ...state,
        teamRanking: action.payload
      };
    default:
      return state;
  }
}

export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  const fetchChallenges = async () => {
    try {
      // Fetch challenges from the API
      const challengesResponse = await ChallengesService.getAllChallenges();

      // Map backend challenge format to frontend format
      const challengesList = challengesResponse.challenges.map(c => ({
        id: c._id,
        title: c.title,
        description: c.description,
        category: c.category,
        difficulty: c.difficulty,
        points: c.points,
        solves: c.solveCount || 0,
        hints: c.hints || [],
        isSolved: c.solved || false,
        releaseDate: c.releaseDate || new Date().toISOString(),
        author: c.authorName || c.authorId || 'Anonymous',
        authorId: c.authorId,
        authorName: c.authorName,
        tags: c.tags || [],
        // Multi-flag support
        flags: c.flags || [],
        // File attachments
        files: c.files || [],
        // First blood tracking
        firstBlood: c.firstBlood,
        flagsFirstBlood: c.flagsFirstBlood || {},
        // Server configuration
        dockerImage: c.dockerImage,
        requiresServer: c.requiresServer || false,
        serverConfig: c.serverConfig,
        // Team solve information
        solvedByTeammate: (c as any).solvedByTeammate
      }));
      
      dispatch({
        type: 'SET_CHALLENGES',
        payload: challengesList
      });
      
      // Also fetch solved challenges to ensure consistency
      const solvedChallenges = await ChallengesService.getSolvedChallenges();
      console.log('Solved challenges:', solvedChallenges);
    } catch (error) {
      console.error('Failed to fetch challenges:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const categories = await CategoriesService.getCategories();
      dispatch({
        type: 'SET_CATEGORIES',
        payload: categories
      });
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  // Check for existing session and validate token
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if we have a token
        if (!AuthService.isAuthenticated()) {
          dispatch({
            type: 'SET_AUTH',
            payload: {
              user: null,
              isAuthenticated: false,
              isLoading: false
            }
          });
          return;
        }

        // Try to get user profile to validate token
        const userProfile = await AuthService.getProfile();
        
        // Convert UserProfile to User format
        const user: User = {
          id: userProfile.id,
          username: userProfile.username,
          email: userProfile.email,
          role: userProfile.role,
          score: userProfile.score,
          rank: userProfile.rank,
          teamId: userProfile.teamId,
          avatarUrl: userProfile.avatarUrl,
          country: userProfile.country,
          bio: userProfile.bio,
          isActive: userProfile.isActive,
          lastActive: userProfile.lastActive,
          createdAt: userProfile.createdAt,
          updatedAt: userProfile.updatedAt,        // Legacy fields for backward compatibility
        avatar: userProfile.avatarUrl || undefined,
        joinedDate: userProfile.createdAt
        };

        dispatch({
          type: 'SET_AUTH',
          payload: {
            user,
            isAuthenticated: true,
            isLoading: false
          }
        });
        
        // Fetch challenges and categories from the API once authenticated
        fetchChallenges();
        fetchCategories();
      } catch (error) {
        console.error('Auth check failed:', error);
        // Token is invalid, clear it
        localStorage.removeItem('mybox_token');
        localStorage.removeItem('mybox_user');
        dispatch({
          type: 'SET_AUTH',
          payload: {
            user: null,
            isAuthenticated: false,
            isLoading: false
          }
        });
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      // Call the real API with email
      const authResponse = await AuthService.login({
        email,
        password
      });

      // Check if email verification is required
      if (authResponse.requiresEmailVerification) {
        return authResponse; // Return the response so the component can handle it
      }

      // Convert AuthResponse user to User format
      const user: User = {
        id: authResponse.user!.id,
        username: authResponse.user!.username,
        email: authResponse.user!.email,
        role: authResponse.user!.role,
        score: authResponse.user!.score,
        rank: authResponse.user!.rank,
        teamId: authResponse.user!.teamId,
        // Set default values for optional fields
        avatarUrl: null,
        country: null,
        bio: null,
        isActive: true,
        lastActive: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        // Legacy fields for backward compatibility
        avatar: undefined,
        joinedDate: new Date().toISOString()
      };

      dispatch({
        type: 'SET_AUTH',
        payload: {
          user,
          isAuthenticated: true,
          isLoading: false
        }
      });

      // Fetch challenges and categories after successful login
      fetchChallenges();
      fetchCategories();
    } catch (error) {
      console.error('Login failed:', error);
      throw error; // Re-throw to let the component handle the error
    }
  };

  const logout = async () => {
    try {
      await AuthService.logout();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      dispatch({
        type: 'SET_AUTH',
        payload: {
          user: null,
          isAuthenticated: false,
          isLoading: false
        }
      });
    }
  };

  const register = async (username: string, email: string, password: string) => {
    try {
      const authResponse = await AuthService.register({
        username,
        email,
        password
      });

      // Check if email verification is required
      if (authResponse.requiresEmailVerification) {
        return authResponse; // Return the response so the component can handle it
      }

      // Convert AuthResponse user to User format
      const user: User = {
        id: authResponse.user!.id,
        username: authResponse.user!.username,
        email: authResponse.user!.email,
        role: authResponse.user!.role,
        score: authResponse.user!.score,
        rank: authResponse.user!.rank,
        teamId: authResponse.user!.teamId,
        // Set default values for optional fields
        avatarUrl: null,
        country: null,
        bio: null,
        isActive: true,
        lastActive: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        // Legacy fields for backward compatibility
        avatar: undefined,
        joinedDate: new Date().toISOString()
      };

      dispatch({
        type: 'SET_AUTH',
        payload: {
          user,
          isAuthenticated: true,
          isLoading: false
        }
      });

      // Fetch challenges and categories after successful registration
      fetchChallenges();
      fetchCategories();
    } catch (error) {
      console.error('Registration failed:', error);
      throw error; // Re-throw to let the component handle the error
    }
  };

  const startVM = async (vmId: string) => {
    dispatch({
      type: 'UPDATE_VM',
      payload: {
        vmId,
        updates: { status: 'starting' }
      }
    });

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    dispatch({
      type: 'UPDATE_VM',
      payload: {
        vmId,
        updates: { 
          status: 'running',
          timeRemaining: 7200,
          ipAddress: `10.10.10.${Math.floor(Math.random() * 100) + 100}`
        }
      }
    });
  };

  const stopVM = async (vmId: string) => {
    dispatch({
      type: 'UPDATE_VM',
      payload: {
        vmId,
        updates: { status: 'stopping' }
      }
    });

    await new Promise(resolve => setTimeout(resolve, 1000));
    
    dispatch({
      type: 'UPDATE_VM',
      payload: {
        vmId,
        updates: { 
          status: 'stopped',
          timeRemaining: 0
        }
      }
    });
  };

  const resetVM = async (vmId: string) => {
    await stopVM(vmId);
    await new Promise(resolve => setTimeout(resolve, 1000));
    await startVM(vmId);
  };

  const submitFlag = async (challengeId: string, flag: string) => {
    try {
      // Call the real API
      const response = await ChallengesService.submitFlag(challengeId, flag);
      
      if (response.success) {
        dispatch({
          type: 'UPDATE_CHALLENGE',
          payload: {
            challengeId,
            updates: { isSolved: true }
          }
        });
        
        // Update user score if points were awarded
        if (response.pointsAwarded > 0 && state.auth.user) {
          dispatch({
            type: 'UPDATE_USER',
            payload: {
              score: state.auth.user.score + response.pointsAwarded
            }
          });
        }
      }
      
      return response.success;
    } catch (error) {
      console.error('Flag submission failed:', error);
      return false;
    }
  };

  const updateProfile = async (updates: Partial<User>) => {
    try {
      // Map User updates to UpdateProfileRequest format, filtering out undefined values
      const profileUpdates: any = {};
      if (updates.username !== undefined) profileUpdates.username = updates.username;
      if (updates.email !== undefined) profileUpdates.email = updates.email;
      if (updates.avatarUrl !== undefined) profileUpdates.avatarUrl = updates.avatarUrl;
      if (updates.country !== undefined) profileUpdates.country = updates.country;
      if (updates.bio !== undefined) profileUpdates.bio = updates.bio;

      const updatedProfile = await AuthService.updateProfile(profileUpdates);
      
      // Convert updated profile back to User format
      const user: User = {
        id: updatedProfile.id,
        username: updatedProfile.username,
        email: updatedProfile.email,
        role: updatedProfile.role,
        score: updatedProfile.score,
        rank: updatedProfile.rank,
        teamId: updatedProfile.teamId,
        avatarUrl: updatedProfile.avatarUrl,
        country: updatedProfile.country,
        bio: updatedProfile.bio,
        isActive: updatedProfile.isActive,
        lastActive: updatedProfile.lastActive,
        createdAt: updatedProfile.createdAt,
        updatedAt: updatedProfile.updatedAt,
        // Legacy fields for backward compatibility
        avatar: updatedProfile.avatarUrl || undefined,
        joinedDate: updatedProfile.createdAt
      };

      dispatch({
        type: 'UPDATE_USER',
        payload: user
      });
    } catch (error) {
      console.error('Profile update failed:', error);
      throw error;
    }
  };

  const fetchUserLeaderboard = async (page: number = 1, limit: number = 20) => {
    try {
      const leaderboard = await leaderboardService.getUserLeaderboard({ page, limit });
      dispatch({
        type: 'SET_USER_LEADERBOARD',
        payload: leaderboard
      });
    } catch (error) {
      console.error('Failed to fetch user leaderboard:', error);
      throw error;
    }
  };

  const fetchTeamLeaderboard = async (page: number = 1, limit: number = 20) => {
    try {
      const leaderboard = await leaderboardService.getTeamLeaderboard({ page, limit });
      dispatch({
        type: 'SET_TEAM_LEADERBOARD',
        payload: leaderboard
      });
    } catch (error) {
      console.error('Failed to fetch team leaderboard:', error);
      throw error;
    }
  };

  const fetchTopUsers = async (limit: number = 10) => {
    try {
      const topUsers = await leaderboardService.getTopUsers(limit);
      dispatch({
        type: 'SET_TOP_USERS',
        payload: topUsers
      });
    } catch (error) {
      console.error('Failed to fetch top users:', error);
      throw error;
    }
  };

  const fetchTopTeams = async (limit: number = 10) => {
    try {
      const topTeams = await leaderboardService.getTopTeams(limit);
      dispatch({
        type: 'SET_TOP_TEAMS',
        payload: topTeams
      });
    } catch (error) {
      console.error('Failed to fetch top teams:', error);
      throw error;
    }
  };

  const fetchUserRanking = async (userId: string) => {
    try {
      const ranking = await leaderboardService.getUserRanking(userId);
      dispatch({
        type: 'SET_USER_RANKING',
        payload: ranking
      });
    } catch (error) {
      console.error('Failed to fetch user ranking:', error);
      throw error;
    }
  };

  const fetchTeamRanking = async (teamId: string) => {
    try {
      const ranking = await leaderboardService.getTeamRanking(teamId);
      dispatch({
        type: 'SET_TEAM_RANKING',
        payload: ranking
      });
    } catch (error) {
      console.error('Failed to fetch team ranking:', error);
      throw error;
    }
  };

  return (
    <AppContext.Provider value={{
      state,
      login,
      logout,
      register,
      startVM,
      stopVM,
      resetVM,
      submitFlag,
      updateProfile,
      fetchChallenges,
      fetchCategories,
      fetchUserLeaderboard,
      fetchTeamLeaderboard,
      fetchTopUsers,
      fetchTopTeams,
      fetchUserRanking,
      fetchTeamRanking
    }}>
      {children}
    </AppContext.Provider>
  );
}

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};