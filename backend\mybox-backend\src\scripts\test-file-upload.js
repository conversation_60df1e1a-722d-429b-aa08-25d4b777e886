#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');

async function testFileUpload() {
  try {
    // First we need to get a JWT token
    const authResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin', // Replace with your admin username
      password: 'Admin@123' // Replace with your admin password
    });

    const token = authResponse.data.access_token;
    console.log('Successfully authenticated');

    // Create a test file to upload
    const testFilePath = path.join(__dirname, 'test-upload.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for upload functionality');

    // Create form data
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath));

    // Upload the file
    const uploadResponse = await axios.post(
      'http://localhost:3001/api/admin/upload', 
      formData, 
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        }
      }
    );

    console.log('File upload successful!');
    console.log('Response:', JSON.stringify(uploadResponse.data, null, 2));

    // Test accessing the uploaded file
    const fileUrl = `http://localhost:3001${uploadResponse.data.url}`;
    console.log(`Testing file access at: ${fileUrl}`);
    
    const fileResponse = await axios.get(fileUrl);
    console.log('File access successful, content:', fileResponse.data);

    // Clean up
    fs.unlinkSync(testFilePath);
    console.log('Test completed successfully');
  } catch (error) {
    console.error('Error during test:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

testFileUpload();
