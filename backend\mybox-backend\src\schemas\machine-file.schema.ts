import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type MachineFileDocument = MachineFile & Document;

@Schema({ timestamps: true })
export class MachineFile {
  @Prop({ type: Types.ObjectId, ref: 'MachineTemplate', required: true })
  templateId: Types.ObjectId;

  @Prop({ required: true })
  fileName: string;

  @Prop({ required: true })
  filePath: string;

  @Prop({ 
    required: true, 
    enum: ['dockerfile', 'ova', 'vmdk', 'qcow2', 'resource', 'writeup', 'config'] 
  })
  fileType: string;

  @Prop({ required: true })
  fileSize: number;

  @Prop({ required: true })
  fileHash: string;

  @Prop({ default: false })
  isDownloadable: boolean;

  @Prop({ default: true })
  requiresAuth: boolean;

  @Prop({ required: false })
  description?: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  uploadedBy: Types.ObjectId;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop({ required: false })
  mimeType?: string;

  @Prop({ default: false })
  isCompressed: boolean;

  @Prop({ required: false })
  originalFileName?: string;
}

export const MachineFileSchema = SchemaFactory.createForClass(MachineFile);
