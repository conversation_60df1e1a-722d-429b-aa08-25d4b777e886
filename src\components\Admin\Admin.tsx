import { useState } from 'react';
import { useApp } from '../../contexts/AppContext';
import {
  Users,
  Target,
  Monitor,
  Settings,
  Plus,
  Edit,
  Shield,
  Activity,
  Bell,
  Volume2,
  Layout,
  ChevronLeft,
  ChevronRight,
  Tag
} from 'lucide-react';
import { AdminOverview } from './pages/AdminOverview';
import { AdminUsers } from './pages/AdminUsers';
import { AdminTeams } from './pages/AdminTeams';
import { AdminChallenges } from './pages/AdminChallenges';
import { AdminMachines } from './pages/AdminMachines';
import { AdminNotifications } from './pages/AdminNotifications';
import { AdminDashboard } from './pages/AdminDashboard';
import { AdminSettings } from './pages/AdminSettings';
import { AdminCategories } from './Categories/AdminCategories';

export function Admin() {
  const { state } = useApp();
  const [activeTab, setActiveTab] = useState('overview');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const navigationItems = [
    { id: 'overview', label: 'Overview', icon: Activity, description: 'System overview and statistics' },
    { id: 'users', label: 'Users', icon: Users, description: 'Manage platform users' },
    { id: 'teams', label: 'Teams', icon: Users, description: 'Manage teams and groups' },
    { id: 'challenges', label: 'Challenges', icon: Target, description: 'Manage CTF challenges' },
    { id: 'categories', label: 'Categories', icon: Tag, description: 'Manage challenge categories' },
    { id: 'machines', label: 'Machines', icon: Monitor, description: 'Manage virtual machines' },
    { id: 'notifications', label: 'Notifications', icon: Bell, description: 'Send notifications and alerts' },
    { id: 'dashboard', label: 'Dashboard Config', icon: Layout, description: 'Configure dashboard appearance' },
    { id: 'settings', label: 'Settings', icon: Settings, description: 'System settings and configuration' }
  ];

  if (state.auth.user?.role !== 'admin') {
    return (
      <div className="text-center py-12">
        <Shield className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-white mb-2">Access Denied</h1>
        <p className="text-slate-400">You don't have permission to access this area.</p>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <AdminOverview />;
      case 'users':
        return <AdminUsers />;
      case 'teams':
        return <AdminTeams />;
      case 'challenges':
        return <AdminChallenges />;
      case 'categories':
        return <AdminCategories />;
      case 'machines':
        return <AdminMachines />;
      case 'notifications':
        return <AdminNotifications />;
      case 'dashboard':
        return <AdminDashboard />;
      case 'settings':
        return <AdminSettings />;
      default:
        return <AdminOverview />;
    }
  };

  return (
    <div className="flex h-screen bg-slate-900">
      {/* Sidebar */}
      <div className={`${sidebarCollapsed ? 'w-16' : 'w-64'} bg-slate-800/50 backdrop-blur-sm border-r border-slate-700 transition-all duration-300 flex flex-col`}>
        {/* Sidebar Header */}
        <div className="p-4 border-b border-slate-700">
          <div className="flex items-center justify-between">
            {!sidebarCollapsed && (
              <div>
                <h2 className="text-lg font-bold text-white">Admin Panel</h2>
                <p className="text-xs text-slate-400">Platform Management</p>
              </div>
            )}
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="p-2 rounded-lg bg-slate-700/50 hover:bg-slate-700 transition-colors text-slate-300 hover:text-white"
            >
              {sidebarCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
            </button>
          </div>
        </div>

        {/* Navigation Items */}
        <nav className="flex-1 p-4 space-y-2">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
              className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 group ${
                activeTab === item.id
                  ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/30'
                  : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
              }`}
              title={sidebarCollapsed ? item.label : ''}
            >
              <item.icon className={`w-5 h-5 ${activeTab === item.id ? 'text-emerald-400' : 'text-slate-400 group-hover:text-white'}`} />
              {!sidebarCollapsed && (
                <div className="flex-1 text-left">
                  <div className="font-medium">{item.label}</div>
                  <div className="text-xs text-slate-500 group-hover:text-slate-400">{item.description}</div>
                </div>
              )}
            </button>
          ))}
        </nav>

        {/* Sidebar Footer */}
        {!sidebarCollapsed && (
          <div className="p-4 border-t border-slate-700">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                <Shield className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-white">{state.auth.user?.username}</div>
                <div className="text-xs text-slate-400">Administrator</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-slate-800/30 backdrop-blur-sm border-b border-slate-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">
                {navigationItems.find(item => item.id === activeTab)?.label || 'Admin Panel'}
              </h1>
              <p className="text-slate-400 text-sm">
                {navigationItems.find(item => item.id === activeTab)?.description || 'Platform management'}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm text-slate-400">Last login</div>
                <div className="text-xs text-slate-500">2 hours ago</div>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}