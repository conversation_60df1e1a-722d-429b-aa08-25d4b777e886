import { useState } from 'react';
import Layout from '../components/Layout';
import { ChevronDown, ChevronUp } from 'lucide-react';

const FaqItem = ({ question, answer }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <div className="border-b border-gray-800 py-6">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="w-full flex justify-between items-center text-left"
            >
                <h3 className="text-lg font-semibold text-white">{question}</h3>
                {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
            </button>
            {isOpen && (
                <div className="mt-4 text-gray-400 pr-8">
                    <p>{answer}</p>
                </div>
            )}
        </div>
    );
};

const faqData = [
    {
        question: "How do I change my password?",
        answer: "To change your password, navigate to the 'Settings' page. Under the 'Security' section, you will find an option to change your password. You will be required to enter your current password and a new password."
    },
    {
        question: "What is Two-Factor Authentication (2FA)?",
        answer: "Two-Factor Authentication adds an extra layer of security to your account by requiring a second verification step, typically a code from your mobile device, in addition to your password. You can enable or disable 2FA in the 'Settings' page."
    },
     {
        question: "What qualifies as a 'Denied' access event?",
        answer: "A 'Denied' access event indicates that an authentication or authorization attempt failed. This could be due to an incorrect password, insufficient permissions, or a connection attempt from an untrusted IP address. All denied events should be reviewed for potential security risks."
    },
    {
        question: "How often is the network traffic data updated?",
        answer: "The live network traffic chart on the dashboard is updated automatically every 5 seconds to provide a near real-time view of system throughput."
    }
];

export default function FaqPage() {
    return (
        <Layout>
            <h1 className="text-4xl font-bold text-white mb-2">Frequently Asked Questions</h1>
            <p className="text-gray-400 mb-8">Find answers to common questions about the platform.</p>
            
            <div className="bg-black border border-gray-800 rounded-lg p-6">
                {faqData.map((item, index) => (
                    <FaqItem key={index} question={item.question} answer={item.answer} />
                ))}
            </div>
        </Layout>
    );
}
