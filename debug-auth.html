<!DOCTYPE html>
<html>
<head>
    <title>Debug Authentication</title>
</head>
<body>
    <h1>Authentication Debug</h1>
    <div id="results"></div>
      <script>
        // Function to get cookie value
        const getCookieValue = (name) => {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [cookieName, cookieValue] = cookie.trim().split('=');
                if (cookieName === name) {
                    return cookieValue;
                }
            }
            return null;
        };
        
        const token = getCookieValue('token');
        const resultsDiv = document.getElementById('results');
        
        resultsDiv.innerHTML = `<h2>All Cookies:</h2><p>${document.cookie || 'No cookies found'}</p>`;
        
        if (token) {
            resultsDiv.innerHTML += `
                <h2>Token found in cookies:</h2>
                <p><strong>Token:</strong> ${token.substring(0, 50)}...</p>
                <h3>Token Details:</h3>
            `;
            
            try {
                // Decode JWT payload (without verification)
                const payload = JSON.parse(atob(token.split('.')[1]));
                resultsDiv.innerHTML += `
                    <p><strong>User ID:</strong> ${payload.sub}</p>
                    <p><strong>Username:</strong> ${payload.username}</p>
                    <p><strong>Role:</strong> ${payload.role}</p>
                    <p><strong>Issued At:</strong> ${new Date(payload.iat * 1000).toLocaleString()}</p>
                    <p><strong>Expires At:</strong> ${new Date(payload.exp * 1000).toLocaleString()}</p>
                    <p><strong>Current Time:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>Token Expired:</strong> ${payload.exp * 1000 < Date.now() ? 'YES' : 'NO'}</p>
                `;
            } catch (error) {
                resultsDiv.innerHTML += `<p><strong>Error parsing token:</strong> ${error.message}</p>`;
            }
        } else {
            resultsDiv.innerHTML += '<p><strong>No token found in cookies</strong></p>';
        }
        
        // Test API call
        async function testAPI() {
            try {
                const response = await fetch('http://localhost:3001/api/admin/users', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                resultsDiv.innerHTML += `
                    <h3>API Test Result:</h3>
                    <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                `;
                
                if (response.ok) {
                    const data = await response.json();
                    resultsDiv.innerHTML += `<p><strong>Response:</strong> Success - Got ${data.users ? data.users.length : 'unknown'} users</p>`;
                } else {
                    const errorData = await response.json();
                    resultsDiv.innerHTML += `<p><strong>Error:</strong> ${JSON.stringify(errorData)}</p>`;
                }
            } catch (error) {
                resultsDiv.innerHTML += `<p><strong>API Error:</strong> ${error.message}</p>`;
            }
        }
        
        if (token) {
            testAPI();
        }
    </script>
</body>
</html>
