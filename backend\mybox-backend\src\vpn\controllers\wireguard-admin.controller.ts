import { 
  Controller, 
  Get, 
  Post, 
  Delete, 
  Put,
  Param,
  Query,
  UseGuards, 
  HttpException, 
  HttpStatus,
  Logger
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { AdminGuard } from '../../auth/admin.guard';
import { VPNConfigService } from '../services/vpn-config.service';
import { NetworkIsolationService } from '../services/network-isolation.service';

@ApiTags('Admin - VPN Management')
@Controller('admin/vpn')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth()
export class WireGuardAdminController {
  private readonly logger = new Logger(WireGuardAdminController.name);

  constructor(
    private readonly vpnConfigService: VPNConfigService,
    private readonly networkIsolationService: NetworkIsolationService,
  ) {}

  /**
   * Get VPN server status and statistics
   */
  @Get('status')
  @ApiOperation({ summary: 'Get VPN server status and statistics' })
  @ApiResponse({ status: 200, description: 'VPN server status retrieved' })
  async getVPNServerStatus() {
    try {
      const serverStatus = await this.vpnConfigService.getVPNServerStatus();
      const networkStats = await this.networkIsolationService.getNetworkStats();

      return {
        success: true,
        data: {
          server: serverStatus,
          networks: networkStats,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`Failed to get VPN server status: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get all VPN configurations
   */
  @Get('configs')
  @ApiOperation({ summary: 'Get all user VPN configurations' })
  @ApiResponse({ status: 200, description: 'VPN configurations retrieved' })
  async getAllVPNConfigs() {
    try {
      const configs = await this.vpnConfigService.getAllVPNConfigs();

      return {
        success: true,
        data: configs.map(config => ({
          id: config._id,
          userId: config.userId,
          user: config.userId, // Will be populated if user data is needed
          ipAddress: config.ipAddress,
          subnet: config.subnet,
          isActive: config.isActive,
          createdAt: config.createdAt,
          lastConnected: config.lastConnected,
          totalConnections: config.totalConnections,
          totalBytesTransferred: config.totalBytesTransferred
        }))
      };
    } catch (error) {
      this.logger.error(`Failed to get VPN configurations: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get VPN configuration for a specific user
   */
  @Get('user/:userId')
  @ApiOperation({ summary: 'Get VPN configuration for specific user' })
  @ApiResponse({ status: 200, description: 'User VPN configuration retrieved' })
  @ApiResponse({ status: 404, description: 'VPN configuration not found' })
  async getUserVPNConfig(@Param('userId') userId: string) {
    try {
      const vpnConfig = await this.vpnConfigService.getVPNConfig(userId);
      
      if (!vpnConfig) {
        throw new HttpException('VPN configuration not found', HttpStatus.NOT_FOUND);
      }

      const userNetworks = await this.networkIsolationService.getUserNetworks(userId);

      return {
        success: true,
        data: {
          vpnConfig: {
            ipAddress: vpnConfig.ipAddress,
            subnet: vpnConfig.subnet,
            serverConfig: vpnConfig.serverConfig
          },
          networks: userNetworks.map(network => ({
            machineId: network.machineId,
            templateId: network.templateId,
            networkName: network.networkName,
            internalIP: network.internalIP,
            exposedPorts: network.exposedPorts,
            status: network.status,
            createdAt: network.createdAt
          }))
        }
      };
    } catch (error) {
      this.logger.error(`Failed to get user VPN config: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Reset VPN configuration for a user
   */
  @Post('user/:userId/reset')
  @ApiOperation({ summary: 'Reset VPN configuration for user (regenerate keys)' })
  @ApiResponse({ status: 200, description: 'VPN configuration reset successfully' })
  async resetUserVPNConfig(@Param('userId') userId: string) {
    try {
      const vpnConfig = await this.vpnConfigService.regenerateVPNConfig(userId);
      
      this.logger.log(`Admin reset VPN configuration for user ${userId}`);
      
      return {
        success: true,
        message: 'VPN configuration reset successfully',
        data: {
          ipAddress: vpnConfig.ipAddress,
          subnet: vpnConfig.subnet
        }
      };
    } catch (error) {
      this.logger.error(`Failed to reset user VPN config: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Revoke VPN access for a user
   */
  @Delete('user/:userId')
  @ApiOperation({ summary: 'Revoke VPN access for user' })
  @ApiResponse({ status: 200, description: 'VPN access revoked successfully' })
  async revokeUserVPNAccess(@Param('userId') userId: string) {
    try {
      await this.vpnConfigService.revokeVPNConfig(userId);
      await this.networkIsolationService.removeUserNetwork(userId);
      
      this.logger.log(`Admin revoked VPN access for user ${userId}`);
      
      return {
        success: true,
        message: 'VPN access revoked successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to revoke user VPN access: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Get all VPN connection logs
   */
  @Get('logs')
  @ApiOperation({ summary: 'Get all VPN connection logs' })
  @ApiResponse({ status: 200, description: 'Connection logs retrieved' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of logs to retrieve' })
  @ApiQuery({ name: 'userId', required: false, description: 'Filter by user ID' })
  async getAllConnectionLogs(
    @Query('limit') limit?: string,
    @Query('userId') userId?: string
  ) {
    try {
      const logLimit = limit ? parseInt(limit) : 100;
      
      let logs;
      if (userId) {
        logs = await this.vpnConfigService.getUserConnectionLogs(userId, logLimit);
      } else {
        logs = await this.vpnConfigService.getAllConnectionLogs(logLimit);
      }

      return {
        success: true,
        data: logs.map(log => ({
          id: log._id,
          userId: log.userId,
          user: log.userId, // Will be populated if user data is needed
          action: log.action,
          timestamp: log.timestamp,
          clientIP: log.clientIP,
          vpnIP: log.vpnIP,
          disconnectedAt: log.disconnectedAt,
          connectionStats: log.connectionStats,
          errorMessage: log.errorMessage,
          accessedMachines: log.accessedMachines
        }))
      };
    } catch (error) {
      this.logger.error(`Failed to get connection logs: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get network statistics and overview
   */
  @Get('networks')
  @ApiOperation({ summary: 'Get network statistics and overview' })
  @ApiResponse({ status: 200, description: 'Network statistics retrieved' })
  async getNetworkOverview() {
    try {
      const networkStats = await this.networkIsolationService.getNetworkStats();
      
      // Get all machine networks for detailed view
      const allNetworks = await this.networkIsolationService.getUserNetworks(''); // This needs to be updated to get all networks
      
      return {
        success: true,
        data: {
          statistics: networkStats,
          networks: allNetworks.map(network => ({
            id: network._id,
            machineId: network.machineId,
            userId: network.userId,
            templateId: network.templateId,
            networkName: network.networkName,
            internalIP: network.internalIP,
            subnetCIDR: network.subnetCIDR,
            exposedPorts: network.exposedPorts,
            status: network.status,
            vpnAccessible: network.vpnAccessible,
            createdAt: network.createdAt,
            lastAccessedAt: network.lastAccessedAt
          }))
        }
      };
    } catch (error) {
      this.logger.error(`Failed to get network overview: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Force cleanup of orphaned networks
   */
  @Post('cleanup')
  @ApiOperation({ summary: 'Cleanup orphaned networks and configurations' })
  @ApiResponse({ status: 200, description: 'Cleanup completed' })
  async cleanupOrphanedNetworks() {
    try {
      // This would implement cleanup logic for orphaned Docker networks
      // and VPN configurations that are no longer needed
      
      this.logger.log('Admin initiated VPN/Network cleanup');
      
      return {
        success: true,
        message: 'Cleanup completed successfully',
        data: {
          cleanedNetworks: 0, // Would be actual count
          cleanedConfigs: 0,  // Would be actual count
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`Failed to cleanup networks: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Generate WireGuard server configuration
   */
  @Get('server-config')
  @ApiOperation({ summary: 'Generate complete WireGuard server configuration' })
  @ApiResponse({ status: 200, description: 'Server configuration generated' })
  async generateServerConfig() {
    try {
      const configs = await this.vpnConfigService.getAllVPNConfigs();
      const serverStatus = await this.vpnConfigService.getVPNServerStatus();
      
      // Generate complete server configuration
      let serverConfig = `# WireGuard Server Configuration
# Generated: ${new Date().toISOString()}
[Interface]
Address = ********/8
ListenPort = ${serverStatus.serverPort}
PrivateKey = [SERVER_PRIVATE_KEY]

# PostUp and PostDown rules for traffic routing
PostUp = iptables -A FORWARD -i wg0 -j ACCEPT; iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
PostDown = iptables -D FORWARD -i wg0 -j ACCEPT; iptables -t nat -D POSTROUTING -o eth0 -j MASQUERADE

`;

      // Add all user peers
      for (const config of configs) {
        if (config.isActive) {
          serverConfig += `# User: ${config.userId}
[Peer]
PublicKey = ${config.publicKey}
AllowedIPs = ${config.ipAddress}/32

`;
        }
      }

      return {
        success: true,
        data: {
          serverConfig,
          totalPeers: configs.filter(c => c.isActive).length,
          generatedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`Failed to generate server config: ${error.message}`);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
