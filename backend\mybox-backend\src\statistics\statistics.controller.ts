import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { StatisticsService } from './statistics.service';
import { GetUser } from '../users/get-user.decorator';

@Controller('statistics')
export class StatisticsController {
  constructor(private readonly statisticsService: StatisticsService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  async getGlobalStatistics(@GetUser() user: any) {
    return this.statisticsService.getGlobalStatistics();
  }

  @Get('challenges')
  @UseGuards(JwtAuthGuard)
  async getChallengeStatistics(@GetUser() user: any) {
    return this.statisticsService.getChallengeStatistics();
  }

  @Get('first-bloods')
  @UseGuards(JwtAuthGuard)
  async getFirstBloodStatistics(@GetUser() user: any) {
    return this.statisticsService.getFirstBloodStatistics();
  }

  @Get('user-activity')
  @UseGuards(JwtAuthGuard)
  async getUserActivityStatistics(@GetUser() user: any) {
    return this.statisticsService.getUserActivityStatistics();
  }

  @Get('team-activity')
  @UseGuards(JwtAuthGuard)
  async getTeamActivityStatistics(@GetUser() user: any) {
    return this.statisticsService.getTeamActivityStatistics();
  }
}