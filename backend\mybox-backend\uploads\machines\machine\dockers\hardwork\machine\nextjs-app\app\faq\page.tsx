'use client';

import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

export default function FAQ() {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(item => item !== index)
        : [...prev, index]
    );
  };

  const faqData = [
    {
      category: "Membership",
      questions: [
        {
          question: "What are your membership options?",
          answer: "We offer three membership tiers: Basic ($29/month), Premium ($59/month), and Elite ($99/month). Each tier includes different amenities and services to fit your fitness needs and budget."
        },
        {
          question: "Is there a contract or can I cancel anytime?",
          answer: "We offer both month-to-month memberships and annual contracts. Month-to-month memberships can be cancelled with 30 days notice, while annual contracts offer significant savings."
        },
        {
          question: "Do you offer student or senior discounts?",
          answer: "Yes! We offer 20% off for students with valid ID and 15% off for seniors (65+). We also have family plans with discounts for multiple members."
        },
        {
          question: "Can I freeze my membership?",
          answer: "Yes, you can freeze your membership for up to 3 months per year for medical reasons, travel, or other circumstances. A small administrative fee may apply."
        }
      ]
    },
    {
      category: "Facilities & Equipment",
      questions: [
        {
          question: "What equipment do you have available?",
          answer: "We have a full range of cardio equipment (treadmills, ellipticals, bikes), strength training machines, free weights, functional training areas, and specialized equipment for group classes."
        },
        {
          question: "Are your facilities clean and well-maintained?",
          answer: "Absolutely! We clean equipment throughout the day and perform deep cleaning every night. Hand sanitizer and cleaning supplies are available throughout the gym."
        },
        {
          question: "Do you have locker rooms and showers?",
          answer: "Yes, we have spacious, clean locker rooms with private showers, changing areas, and complimentary towel service for all members."
        },
        {
          question: "Is parking available?",
          answer: "Yes, we provide free parking for all members and guests. Our parking lot is well-lit and monitored for security."
        }
      ]
    },
    {
      category: "Classes & Training",
      questions: [
        {
          question: "Do I need to book classes in advance?",
          answer: "For popular classes, we recommend booking in advance through our app or website. Walk-ins are welcome based on availability."
        },
        {
          question: "What types of group classes do you offer?",
          answer: "We offer HIIT, strength training, yoga, pilates, cycling, dance fitness, and more. Check our schedule for current class offerings and times."
        },
        {
          question: "Do you offer personal training?",
          answer: "Yes! We have certified personal trainers available for one-on-one sessions, partner training, and small group training. Packages are available for all membership levels."
        },
        {
          question: "Are classes included in membership?",
          answer: "Group classes are included with Premium and Elite memberships. Basic members can purchase class packages or upgrade their membership."
        }
      ]
    },
    {
      category: "General",
      questions: [
        {
          question: "What are your operating hours?",
          answer: "We're open Monday-Friday 5:00 AM - 11:00 PM, Saturday-Sunday 6:00 AM - 10:00 PM. Premium and Elite members have 24/7 access."
        },
        {
          question: "Do you offer guest passes?",
          answer: "Yes! Premium members get 2 guest passes per month, Elite members get unlimited guest passes. Basic members can purchase day passes for guests."
        },
        {
          question: "Is there an age requirement?",
          answer: "Members must be 16+ to use the facility independently. Ages 14-15 can use the gym with parent/guardian supervision. We offer youth programs for younger children."
        },
        {
          question: "Do you have nutrition services?",
          answer: "Yes! We have a registered dietitian available for consultations. Premium and Elite members receive nutrition consultations as part of their membership."
        }
      ]
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl font-bold mb-6">Frequently Asked Questions</h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Find answers to common questions about membership, facilities, classes, and more.
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {faqData.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                {category.category}
              </h2>
              
              <div className="space-y-4">
                {category.questions.map((faq, questionIndex) => {
                  const globalIndex = categoryIndex * 100 + questionIndex;
                  const isOpen = openItems.includes(globalIndex);
                  
                  return (
                    <Card key={questionIndex} className="shadow-lg">
                      <CardContent className="p-0">
                        <button
                          onClick={() => toggleItem(globalIndex)}
                          className="w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors"
                        >
                          <h3 className="text-lg font-semibold text-gray-900 pr-4">
                            {faq.question}
                          </h3>
                          {isOpen ? (
                            <ChevronUp className="h-5 w-5 text-gray-500 flex-shrink-0" />
                          ) : (
                            <ChevronDown className="h-5 w-5 text-gray-500 flex-shrink-0" />
                          )}
                        </button>
                        
                        {isOpen && (
                          <div className="px-6 pb-6">
                            <p className="text-gray-600 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            Still Have Questions?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Our friendly staff is here to help! Contact us or visit our gym for more information.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="/contact"
              className="bg-black text-white px-8 py-4 rounded-md font-semibold hover:bg-gray-800 transition-colors"
            >
              Contact Us
            </a>
            <a 
              href="tel:+1555123456"
              className="bg-white text-black border-2 border-black px-8 py-4 rounded-md font-semibold hover:bg-gray-100 transition-colors"
            >
              Call Us Now
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}