import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type TeamMemberRole = 'captain' | 'member';
export type TeamMemberStatus = 'active' | 'pending' | 'left';

@Schema()
export class TeamMember {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ default: Date.now })
  joinedAt: Date;

  @Prop({ 
    type: String,
    enum: ['captain', 'member'],
    default: 'member'
  })
  role: TeamMemberRole;

  @Prop({ 
    type: String,
    enum: ['active', 'pending', 'left'],
    default: 'active'
  })
  status: TeamMemberStatus;
}

@Schema()
export class SolvedChallenge {
  @Prop({ type: Types.ObjectId, ref: 'Challenge', required: true })
  challengeId: Types.ObjectId;

  @Prop({ default: Date.now })
  solvedAt: Date;

  @Prop({ required: true })
  points: number;
}

@Schema({ timestamps: true })
export class Team extends Document {
  @Prop({ required: true, unique: true, minlength: 3, maxlength: 50 })
  name: string;

  @Prop()
  description?: string;

  @Prop({ default: true })
  isPublic: boolean;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  captainId: Types.ObjectId;

  @Prop({ type: [TeamMember], default: [] })
  members: TeamMember[];

  @Prop({ default: 5 })
  maxMembers: number;
  @Prop({ default: 0 })
  teamScore: number;

  @Prop({ default: 0 })
  rank: number;

  @Prop({ type: [SolvedChallenge], default: [] })
  solvedChallenges: SolvedChallenge[];

  @Prop()
  inviteCode?: string;

  @Prop()
  avatar?: string;

  @Prop()
  country?: string;

  @Prop()
  website?: string;

  @Prop({ default: true })
  isActive: boolean;

  createdAt: Date;
  updatedAt: Date;
}

export const TeamSchema = SchemaFactory.createForClass(Team);

// Create indexes (name already has unique constraint from @Prop decorator)
TeamSchema.index({ isPublic: 1 });
TeamSchema.index({ teamScore: -1 }); // for team leaderboard
TeamSchema.index({ rank: 1 });
TeamSchema.index({ inviteCode: 1 });
TeamSchema.index({ captainId: 1 });
