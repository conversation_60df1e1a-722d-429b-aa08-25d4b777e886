import { useState, useEffect } from 'react';
import machineService, { MachineTemplate, MachineInstance } from '../../services/machines';
import { CreateTemplateModal } from './CreateTemplateModal';
import { MachineTopicsManager } from './MachineTopicsManager';
import { MachineStartModal } from './MachineStartModal';
import { BuildStatusModal } from './BuildStatusModal';
import {
  Plus,
  Edit,
  Trash2,
  Monitor,
  Play,
  Square,
  Settings,
  AlertCircle,
  CheckCircle,
  Users,
  Activity,
  RefreshCw,
  BookOpen,
  Flag,
  Rocket,
  PlayCircle
} from 'lucide-react';

export function MachineManagement() {
  const [activeTab, setActiveTab] = useState('templates');
  const [templates, setTemplates] = useState<MachineTemplate[]>([]);
  const [instances, setInstances] = useState<MachineInstance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<MachineTemplate | null>(null);
  const [showTopicsManager, setShowTopicsManager] = useState(false);
  const [managingTemplate, setManagingTemplate] = useState<MachineTemplate | null>(null);
  const [showStartModal, setShowStartModal] = useState(false);
  const [startingTemplate, setStartingTemplate] = useState<MachineTemplate | null>(null);
  const [showBuildStatusModal, setShowBuildStatusModal] = useState(false);
  const [buildStatusTemplate, setBuildStatusTemplate] = useState<MachineTemplate | null>(null);

  // Build status tracking
  const [imageStatuses, setImageStatuses] = useState<Record<string, {
    imageExists: boolean;
    hasExtractedFiles: boolean;
    imageName: string;
    canBuild: boolean;
  }>>({});

  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
    // Auto-refresh state
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<number | null>(null);

  useEffect(() => {
    loadTemplates();
    if (activeTab === 'instances') {
      loadInstances();
    }
  }, [activeTab]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        if (activeTab === 'templates') {
          loadTemplates();
        } else if (activeTab === 'instances') {
          loadInstances();
        }
      }, 10000); // Refresh every 10 seconds
      
      setRefreshInterval(interval);
      return () => clearInterval(interval);
    } else if (refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }
  }, [autoRefresh, activeTab]);const loadTemplates = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await machineService.getAdminTemplates();
      
      // Handle the case where backend returns { templates: [], total: 0, hasMore: false }
      if (data && typeof data === 'object' && 'templates' in data) {
        const templateList = Array.isArray(data.templates) ? data.templates : [];
        setTemplates(templateList);

        // Load image statuses for Docker templates
        if (templateList.length > 0) {
          setTimeout(() => loadImageStatuses(), 100);
        }
      } else if (Array.isArray(data)) {
        setTemplates(data);

        // Load image statuses for Docker templates
        if (data.length > 0) {
          setTimeout(() => loadImageStatuses(), 100);
        }
      } else {
        console.warn('Unexpected templates data format:', data);
        setTemplates([]);
        setError('Invalid data format received from server');
      }
    } catch (err) {
      setError('Failed to load machine templates');
      console.error('Error loading templates:', err);
      setTemplates([]); // Ensure templates is always an array
    } finally {
      setLoading(false);
    }
  };
  const loadInstances = async () => {
    try {
      const data = await machineService.getAdminInstances();
      
      // Handle the case where backend might return { instances: [], total: 0, hasMore: false }
      if (data && typeof data === 'object' && 'instances' in data) {
        setInstances(Array.isArray(data.instances) ? data.instances : []);
      } else if (Array.isArray(data)) {
        setInstances(data);
      } else {
        console.warn('Unexpected instances data format:', data);
        setInstances([]);
      }
    } catch (err) {
      console.error('Error loading instances:', err);
      setInstances([]); // Ensure instances is always an array
    }
  };

  const loadImageStatuses = async () => {
    try {
      const statuses: Record<string, any> = {};

      // Load image status for all templates (all are Docker-based now)
      await Promise.all(
        templates.map(async (template) => {
          try {
            const status = await machineService.getImageStatus(template.id);
            statuses[template.id] = status;
          } catch (error) {
            // Ignore errors for individual templates
            console.warn(`Failed to get image status for ${template.name}:`, error);
          }
        })
      );

      setImageStatuses(statuses);
    } catch (error) {
      console.error('Error loading image statuses:', error);
    }
  };

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setShowCreateModal(true);
  };

  const handleEditTemplate = (template: MachineTemplate) => {
    setEditingTemplate(template);
    setShowCreateModal(true);
  };

  const handleManageTopics = (template: MachineTemplate) => {
    setManagingTemplate(template);
    setShowTopicsManager(true);
  };

  const handleUpdateTopics = async (updatedData: Partial<MachineTemplate>) => {
    if (!managingTemplate) return;

    try {
      // Use id or _id for template identification
      const templateId = managingTemplate.id || managingTemplate._id;
      if (!templateId) {
        throw new Error('Template ID is missing');
      }
      await machineService.updateTemplate(templateId, updatedData);
      await loadTemplates();
      setShowTopicsManager(false);
      setManagingTemplate(null);
    } catch (err) {
      console.error('Error updating machine topics:', err);
      throw err;
    }
  };

  const handleStartMachine = (template: MachineTemplate) => {
    console.log('Starting machine modal for:', template.name);
    setStartingTemplate(template);
    setShowStartModal(true);
  };

  const handleShowBuildStatus = (template: MachineTemplate) => {
    setBuildStatusTemplate(template);
    setShowBuildStatusModal(true);
  };

  const handleBuildStatusUpdate = () => {
    // Refresh image statuses when build status changes
    loadImageStatuses();
  };

  const handleStartInstance = async (machineId: string) => {
    try {
      console.log('Starting machine instance for ID:', machineId);
      await machineService.spawnMachine(machineId);
      await loadTemplates();
      setShowStartModal(false);
      setStartingTemplate(null);
    } catch (err) {
      console.error('Error starting machine instance:', err);
      throw err;
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return;
    
    try {
      await machineService.deleteTemplate(templateId);
      await loadTemplates();
    } catch (err) {
      alert('Failed to delete template');
    }
  };

  const handleToggleTemplate = async (templateId: string, isActive: boolean) => {
    try {
      await machineService.toggleTemplateStatus(templateId, !isActive);
      await loadTemplates();
    } catch (err) {
      alert('Failed to update template status');
    }
  };
  const handleForceTerminate = async (instanceId: string) => {
    if (!confirm('Are you sure you want to force terminate this instance?')) return;
    
    try {
      await machineService.forceTerminateInstance(instanceId);
      await loadInstances();
    } catch (err) {
      alert('Failed to terminate instance');
    }
  };

  const handleSaveTemplate = async (templateData: Partial<MachineTemplate>) => {
    try {
      if (editingTemplate) {
        // Update existing template - use id or _id
        const templateId = editingTemplate.id || editingTemplate._id;
        if (!templateId) {
          throw new Error('Template ID is missing');
        }
        await machineService.updateTemplate(templateId, templateData);
      } else {
        // Create new template
        await machineService.createTemplate(templateData);
      }
      await loadTemplates();
      setShowCreateModal(false);
      setEditingTemplate(null);
    } catch (err) {
      console.error('Error saving template:', err);
      throw err; // Let the modal handle the error display
    }
  };  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      easy: 'text-green-400',
      medium: 'text-yellow-400',
      hard: 'text-orange-400',
      insane: 'text-red-400'
    };
    return colors[difficulty as keyof typeof colors] || 'text-gray-400';
  };

  const getStatusColor = (status: string) => {
    const colors = {
      starting: 'text-yellow-400',
      running: 'text-green-400',
      stopping: 'text-orange-400',
      stopped: 'text-gray-400',
      error: 'text-red-400'
    };
    return colors[status as keyof typeof colors] || 'text-gray-400';
  };

  // Filtering functions
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.category.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = categoryFilter === 'all' || template.category === categoryFilter;
    const matchesDifficulty = difficultyFilter === 'all' || template.difficulty === difficultyFilter;
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && template.isActive) ||
                         (statusFilter === 'inactive' && !template.isActive);
    
    return matchesSearch && matchesCategory && matchesDifficulty && matchesStatus;
  });
  const filteredInstances = instances.filter(instance => {
    return instance.template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           instance.ownerId.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
           instance.instanceIP.includes(searchQuery);
  });

  const handleRefresh = () => {
    if (activeTab === 'templates') {
      loadTemplates();
    } else if (activeTab === 'instances') {
      loadInstances();
    }
  };

  const resetFilters = () => {    setSearchQuery('');
    setCategoryFilter('all');
    setDifficultyFilter('all');
    setStatusFilter('all');
  };

  const formatTimeRemaining = (expiresAt: string) => {
    const now = new Date();
    const expires = new Date(expiresAt);
    const diff = expires.getTime() - now.getTime();
    
    if (diff <= 0) return 'Expired';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-400" />
        <span className="ml-2 text-slate-400">Loading...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Machine Management</h2>
          <p className="text-slate-400">Manage machine templates and active instances</p>
        </div>
        <button
          onClick={handleCreateTemplate}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>New Template</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-slate-700">
        {[
          { id: 'templates', label: 'Templates', icon: Monitor },
          { id: 'instances', label: 'Active Instances', icon: Activity },
          { id: 'statistics', label: 'Statistics', icon: Settings }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center space-x-2 px-6 py-3 font-medium transition-colors ${
              activeTab === tab.id
                ? 'text-blue-400 border-b-2 border-blue-400'
                : 'text-slate-400 hover:text-white'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>      {/* Templates Tab */}
      {activeTab === 'templates' && (
        <div className="space-y-6">
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-400" />
                <span className="text-red-400">{error}</span>
              </div>
            </div>
          )}

          {/* Search and Filter Controls */}
          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
              <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search */}
                <div className="md:col-span-2">
                  <input
                    type="text"
                    placeholder="Search templates..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                  />
                </div>

                {/* Category Filter */}
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
                >
                  <option value="all">All Categories</option>
                  <option value="web">Web</option>
                  <option value="crypto">Crypto</option>
                  <option value="pwn">PWN</option>
                  <option value="reverse">Reverse</option>
                  <option value="forensics">Forensics</option>
                  <option value="misc">Misc</option>
                  <option value="osint">OSINT</option>
                </select>

                {/* Difficulty Filter */}
                <select
                  value={difficultyFilter}
                  onChange={(e) => setDifficultyFilter(e.target.value)}
                  className="px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
                >
                  <option value="all">All Difficulties</option>
                  <option value="easy">Easy</option>
                  <option value="medium">Medium</option>
                  <option value="hard">Hard</option>
                  <option value="insane">Insane</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                {/* Status Filter */}
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>

                {/* Auto-refresh Toggle */}
                <button
                  onClick={() => setAutoRefresh(!autoRefresh)}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    autoRefresh 
                      ? 'bg-green-600 text-white' 
                      : 'bg-slate-700 text-slate-400 hover:text-white'
                  }`}
                  title="Auto-refresh every 10 seconds"
                >
                  <RefreshCw className={`w-4 h-4 ${autoRefresh ? 'animate-spin' : ''}`} />
                </button>

                {/* Manual Refresh */}
                <button
                  onClick={handleRefresh}
                  className="px-3 py-2 bg-slate-700 text-slate-400 hover:text-white rounded-lg transition-colors"
                  title="Refresh now"
                >
                  <RefreshCw className="w-4 h-4" />
                </button>

                {/* Reset Filters */}
                <button
                  onClick={resetFilters}
                  className="px-4 py-2 bg-slate-700 text-slate-400 hover:text-white rounded-lg transition-colors text-sm"
                >
                  Reset
                </button>
              </div>
            </div>

            {/* Results Count */}
            <div className="mt-4 text-sm text-slate-400">
              Showing {filteredTemplates.length} of {templates.length} templates
            </div>
          </div>          <div className="grid gap-6">
            {Array.isArray(filteredTemplates) && filteredTemplates.map((template) => (
              <div
                key={template.id}
                className="bg-slate-800/50 rounded-xl border border-slate-700 p-6"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-slate-700/50 rounded-lg">
                      <Monitor className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white">{template.name}</h3>
                      <p className="text-slate-400 text-sm">{template.description}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="text-xs text-slate-500">Category: {template.category}</span>
                        <span className={`text-xs font-medium ${getDifficultyColor(template.difficulty)}`}>
                          {template.difficulty.toUpperCase()}
                        </span>
                        <span className="text-xs text-slate-500">OS: {template.os}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      template.isActive
                        ? 'bg-green-500/20 text-green-400'
                        : 'bg-gray-500/20 text-gray-400'
                    }`}>
                      {template.isActive ? 'Active' : 'Inactive'}
                    </div>

                    {/* Build Status Indicator - All templates are Docker-based now */}
                    {(
                      <div className="flex items-center space-x-1">
                        {imageStatuses[template.id] ? (
                          <>
                            <div className={`w-2 h-2 rounded-full ${
                              imageStatuses[template.id].imageExists ? 'bg-green-500' :
                              imageStatuses[template.id].hasExtractedFiles ? 'bg-yellow-500' : 'bg-gray-500'
                            }`}></div>
                            <span className="text-xs text-slate-400">
                              {imageStatuses[template.id].imageExists ? 'Built' :
                               imageStatuses[template.id].hasExtractedFiles ? 'Ready' : 'No Files'}
                            </span>
                          </>
                        ) : (
                          <>
                            <div className="w-2 h-2 rounded-full bg-gray-500 animate-pulse"></div>
                            <span className="text-xs text-slate-400">Checking...</span>
                          </>
                        )}
                      </div>
                    )}
                    
                    <button
                      onClick={() => handleToggleTemplate(template.id, template.isActive)}
                      className={`p-2 rounded-lg transition-colors ${
                        template.isActive
                          ? 'text-yellow-400 hover:bg-yellow-400/10'
                          : 'text-green-400 hover:bg-green-400/10'
                      }`}
                      title={template.isActive ? 'Deactivate' : 'Activate'}
                    >
                      {template.isActive ? <Square className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                    </button>

                    {template.isActive && (
                      <button
                        onClick={() => handleStartMachine(template)}
                        className="p-2 text-green-400 hover:bg-green-400/10 rounded-lg transition-colors border border-green-400/30 hover:border-green-400/50 bg-green-400/5"
                        title="Start Machine Instance (Show Details)"
                      >
                        <PlayCircle className="w-4 h-4" />
                      </button>
                    )}

                    <button
                      onClick={() => handleEditTemplate(template)}
                      className="p-2 text-blue-400 hover:bg-blue-400/10 rounded-lg transition-colors"
                      title="Edit"
                    >
                      <Edit className="w-4 h-4" />
                    </button>

                    {/* Build Status Button - All templates are Docker-based now */}
                    {(
                      <button
                        onClick={() => handleShowBuildStatus(template)}
                        className="p-2 text-orange-400 hover:bg-orange-400/10 rounded-lg transition-colors"
                        title="Build Status & Management"
                      >
                        <Settings className="w-4 h-4" />
                      </button>
                    )}

                    <button
                      onClick={() => handleManageTopics(template)}
                      className="p-2 text-purple-400 hover:bg-purple-400/10 rounded-lg transition-colors"
                      title="Manage Topics & Flags"
                    >
                      <BookOpen className="w-4 h-4" />
                    </button>

                    <button
                      onClick={() => handleDeleteTemplate(template.id)}
                      className="p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
                      title="Delete"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-slate-400" />
                    <span className="text-sm text-slate-400">
                      {template.solveCount || 0} solves
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Activity className="w-4 h-4 text-slate-400" />
                    <span className="text-sm text-slate-400">
                      {template.activeInstances || 0} active instances
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-slate-400" />
                    <span className="text-sm text-slate-400">
                      {template.flags?.length || 0} flags
                    </span>
                  </div>
                </div>

                {template.flags && template.flags.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-slate-700">
                    <h4 className="text-sm font-medium text-white mb-2">Flags:</h4>
                    <div className="flex flex-wrap gap-2">
                      {template.flags.map((flag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-slate-700/50 text-slate-300 rounded text-xs"
                        >
                          {flag.name} ({flag.points}pts)
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}            {templates.length === 0 && !loading && (
              <div className="text-center py-12">
                <Monitor className="w-16 h-16 text-slate-600 mx-auto mb-4" />
                <p className="text-slate-400 text-lg">No machine templates found.</p>
                <p className="text-slate-500 mt-2">Create your first template to get started.</p>
              </div>
            )}
          </div>
        </div>
      )}      {/* Active Instances Tab */}
      {activeTab === 'instances' && (
        <div className="space-y-6">
          {/* Search Controls for Instances */}
          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search instances by template, user, or IP..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                />
              </div>

              <div className="flex items-center space-x-2">
                {/* Auto-refresh Toggle */}
                <button
                  onClick={() => setAutoRefresh(!autoRefresh)}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    autoRefresh 
                      ? 'bg-green-600 text-white' 
                      : 'bg-slate-700 text-slate-400 hover:text-white'
                  }`}
                  title="Auto-refresh every 10 seconds"
                >
                  <RefreshCw className={`w-4 h-4 ${autoRefresh ? 'animate-spin' : ''}`} />
                </button>

                {/* Manual Refresh */}
                <button
                  onClick={handleRefresh}
                  className="px-3 py-2 bg-slate-700 text-slate-400 hover:text-white rounded-lg transition-colors"
                  title="Refresh now"
                >
                  <RefreshCw className="w-4 h-4" />
                </button>

                {/* Reset Search */}
                <button
                  onClick={() => setSearchQuery('')}
                  className="px-4 py-2 bg-slate-700 text-slate-400 hover:text-white rounded-lg transition-colors text-sm"
                >
                  Clear
                </button>
              </div>
            </div>

            {/* Results Count */}
            <div className="mt-4 text-sm text-slate-400">
              Showing {filteredInstances.length} of {instances.length} active instances
            </div>
          </div>

          <div className="grid gap-4">
            {Array.isArray(filteredInstances) && filteredInstances.map((instance) => (
              <div
                key={instance.id}
                className="bg-slate-800/50 rounded-xl border border-slate-700 p-6"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-slate-700/50 rounded-lg">
                      <Monitor className="w-6 h-6 text-white" />
                    </div>                    <div>
                      <h3 className="text-lg font-semibold text-white">{instance.template.name}</h3>
                      <p className="text-slate-400 text-sm">Owner: {instance.ownerId.username}</p>
                      <p className="text-slate-400 text-xs">ID: {instance.id}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className={`text-sm font-medium ${getStatusColor(instance.status)}`}>
                          {instance.status.toUpperCase()}
                        </span>
                        {instance.instanceIP && (
                          <span className="text-sm text-slate-400">IP: {instance.instanceIP}</span>
                        )}
                        {instance.isTeamShared && (
                          <span className="px-2 py-1 bg-purple-500/20 text-purple-400 rounded text-xs">
                            Team Shared
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <div className="text-right text-sm">
                      <div className="text-white">Started: {new Date(instance.startedAt).toLocaleString()}</div>
                      <div className="text-slate-400">
                        Expires: {formatTimeRemaining(instance.expiresAt)}
                      </div>
                    </div>
                    
                    <button
                      onClick={() => handleForceTerminate(instance.id)}
                      className="p-2 text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
                      title="Force Terminate"
                    >
                      <Square className="w-4 h-4" />
                    </button>
                  </div>
                </div>                {instance.exposedPorts && instance.exposedPorts.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-slate-700">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-slate-400">Exposed Ports:</span>
                      <div className="flex space-x-1">
                        {instance.exposedPorts.map((port: any) => (
                          <span key={port.external} className="px-2 py-1 bg-slate-700 text-slate-300 rounded text-xs">
                            {port.external}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}

            {instances.length === 0 && (
              <div className="text-center py-12">
                <Activity className="w-16 h-16 text-slate-600 mx-auto mb-4" />
                <p className="text-slate-400 text-lg">No active instances found.</p>
                <p className="text-slate-500 mt-2">Instances will appear here when users spawn machines.</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Statistics Tab */}
      {activeTab === 'statistics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Monitor className="w-6 h-6 text-blue-400" />
              <h3 className="font-semibold text-white">Templates</h3>
            </div>
            <div className="space-y-2">              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-400">Total</span>
                <span className="text-lg font-bold text-white">{Array.isArray(templates) ? templates.length : 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-400">Active</span>
                <span className="text-lg font-bold text-blue-400">
                  {Array.isArray(templates) ? templates.filter(t => t.isActive).length : 0}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Activity className="w-6 h-6 text-green-400" />
              <h3 className="font-semibold text-white">Instances</h3>
            </div>
            <div className="space-y-2">              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-400">Running</span>
                <span className="text-lg font-bold text-green-400">
                  {Array.isArray(instances) ? instances.filter(i => i.status === 'running').length : 0}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-400">Total Active</span>
                <span className="text-lg font-bold text-white">{Array.isArray(instances) ? instances.length : 0}</span>
              </div>
            </div>
          </div>

          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Users className="w-6 h-6 text-purple-400" />
              <h3 className="font-semibold text-white">Usage</h3>
            </div>
            <div className="space-y-2">              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-400">Total Solves</span>
                <span className="text-lg font-bold text-purple-400">
                  {Array.isArray(templates) ? templates.reduce((sum, t) => sum + (t.solveCount || 0), 0) : 0}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-400">Shared Instances</span>
                <span className="text-lg font-bold text-white">
                  {Array.isArray(instances) ? instances.filter(i => i.isTeamShared).length : 0}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}      {/* Create/Edit Modal */}
      <CreateTemplateModal
        template={editingTemplate}
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          setEditingTemplate(null);
        }}
        onSave={handleSaveTemplate}
      />

      {/* Topics & Flags Manager Modal */}
      {showTopicsManager && managingTemplate && (
        <MachineTopicsManager
          machine={managingTemplate}
          onUpdate={handleUpdateTopics}
          onClose={() => {
            setShowTopicsManager(false);
            setManagingTemplate(null);
          }}
        />
      )}

      {/* Machine Start Modal */}
      {showStartModal && startingTemplate && (
        <MachineStartModal
          machine={startingTemplate}
          isOpen={showStartModal}
          onStart={handleStartInstance}
          onClose={() => {
            setShowStartModal(false);
            setStartingTemplate(null);
          }}
        />
      )}

      {/* Build Status Modal */}
      {showBuildStatusModal && buildStatusTemplate && (
        <BuildStatusModal
          template={buildStatusTemplate}
          isOpen={showBuildStatusModal}
          onStatusUpdate={handleBuildStatusUpdate}
          onClose={() => {
            setShowBuildStatusModal(false);
            setBuildStatusTemplate(null);
          }}
        />
      )}
    </div>
  );
}
