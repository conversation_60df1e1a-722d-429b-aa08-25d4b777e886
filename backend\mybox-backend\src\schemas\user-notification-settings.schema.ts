import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type UserNotificationSettingsDocument = UserNotificationSettings & Document;

@Schema({ timestamps: true })
export class UserNotificationSettings {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true, unique: true })
  userId: Types.ObjectId;

  @Prop({ default: true })
  soundEnabled: boolean;

  @Prop({ type: Types.ObjectId, ref: 'NotificationSound' })
  selectedSoundId: Types.ObjectId;

  @Prop({ default: 0.7, min: 0, max: 1 })
  volume: number;

  @Prop({ default: true })
  desktopNotifications: boolean;

  @Prop({ default: true })
  emailNotifications: boolean;

  @Prop({
    type: Object,
    default: {
      admin_message: true,
      first_blood: true,
      challenge_solved: true,
      new_challenge: true,
      new_machine: true,
      machine_first_blood: true,
      system_announcement: true,
      team_invitation: true,
      competition_update: true,
    }
  })
  notificationTypes: {
    admin_message: boolean;
    first_blood: boolean;
    challenge_solved: boolean;
    new_challenge: boolean;
    new_machine: boolean;
    machine_first_blood: boolean;
    system_announcement: boolean;
    team_invitation: boolean;
    competition_update: boolean;
  };

  createdAt: Date;
  updatedAt: Date;
}

export const UserNotificationSettingsSchema = SchemaFactory.createForClass(UserNotificationSettings);