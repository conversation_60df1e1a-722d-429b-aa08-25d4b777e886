import Layout from '../components/layout.js';
import { HardDrive, Download } from 'lucide-react';
import { useEffect, useState } from 'react';

const DownloadItem = ({ name, type, size, date }) => {
    const handleDownload = () => {
        // Download the actual file from the public/pcaps folder
        const fileUrl = `/pcaps/${name}`;
        const element = document.createElement('a');
        element.href = fileUrl;
        element.download = name;
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    };

    return (
        <div className="flex items-center justify-between p-4 border-b border-gray-800 hover:bg-gray-900 transition-colors duration-200">
            <div className="flex items-center">
                <HardDrive className="w-8 h-8 mr-4 text-gray-500" />
                <div>
                    <p className="font-semibold text-white">{name}</p>
                    <p className="text-sm text-gray-400">{type} - {size}</p>
                </div>
            </div>
            <div className="flex items-center">
                <p className="text-sm text-gray-500 mr-6 hidden md:block">{date}</p>
                <button 
                    onClick={handleDownload}
                    className="group flex items-center justify-center py-2 px-4 font-semibold text-white bg-gray-800 border border-gray-700 rounded-lg hover:bg-white hover:text-black transition-all duration-200"
                >
                    <Download className="mr-2 h-4 w-4" />
                    Download
                </button>
            </div>
        </div>
    );
};

export default function SavedPage() {
    const [savedFiles, setSavedFiles] = useState([]);
    useEffect(() => {
        fetch('/pcaps/index.json')
            .then(res => res.json())
            .then(data => setSavedFiles(data));
    }, []);

    return (
        <Layout>
            <h1 className="text-4xl font-bold text-white mb-2">Saved Items</h1>
            <p className="text-gray-400 mb-8">Download saved network captures and log files for offline analysis.</p>

            <div className="bg-black border border-gray-800 rounded-lg">
                <div className="p-4 border-b border-gray-800">
                    <h2 className="text-lg font-semibold text-white">Available Files</h2>
                </div>
                <div>
                    {savedFiles.map((file, index) => (
                        <DownloadItem key={index} {...file} />
                    ))}
                </div>
            </div>
        </Layout>
    );
}
