import React, { useState, useEffect } from 'react';
import { useApp } from '../../contexts/AppContext';
import { TeamsService } from '../../services/teams';
import { 
  Users, 
  Plus, 
  Crown, 
  Search, 
  Settings, 
  UserPlus, 
  LogOut,
  Trophy,
  Target,
  Sparkles,
  Shield,
  UserMinus,
  UserCheck,
  Copy,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  MoreVertical,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface Team {
  id: string;
  name: string;
  description: string;
  isPublic: boolean;
  captainId: string;
  captain: {
    id: string;
    username: string;
    email: string;
  };
  members: Array<{
    userId: string;
    username: string;
    email: string;
    role: 'captain' | 'member';
    status: string;
    joinedAt: string;
  }>;
  maxMembers: number;
  teamScore: number;
  rank: number;
  inviteCode: string;
  createdAt: string;
  updatedAt: string;
}

interface CreateTeamData {
  name: string;
  description: string;
  isPublic: boolean;
  maxMembers: number;
}

interface JoinTeamData {
  teamIdentifier: string;
}

export function Teams() {
  const { state } = useApp();
  const [activeTab, setActiveTab] = useState<'my-team' | 'browse' | 'create' | 'manage'>('my-team');
  const [myTeam, setMyTeam] = useState<Team | null>(null);
  const [allTeams, setAllTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showJoinForm, setShowJoinForm] = useState(false);  const [showInviteCode, setShowInviteCode] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showEditForm, setShowEditForm] = useState(false);
  const [teamStats, setTeamStats] = useState<any>(null);

  // Form states
  const [createTeamData, setCreateTeamData] = useState<CreateTeamData>({
    name: '',
    description: '',
    isPublic: true,
    maxMembers: 5
  });  const [joinTeamData, setJoinTeamData] = useState<JoinTeamData>({
    teamIdentifier: ''
  });
  const [editTeamData, setEditTeamData] = useState<CreateTeamData>({
    name: '',
    description: '',
    isPublic: true,
    maxMembers: 5
  });

  useEffect(() => {
    loadTeamData();
  }, []);

  const loadTeamData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load user's team
      try {
        const userTeam = await TeamsService.getMyTeam();
        setMyTeam(userTeam);
      } catch (err: any) {
        console.error('Failed to load user team:', err);
      }

      // Load all public teams
      try {
        const teamsResponse = await TeamsService.getAllTeams(1, 20, true);
        setAllTeams(teamsResponse.teams);
      } catch (err: any) {
        console.error('Failed to load all teams:', err);
        throw err;
      }
    } catch (err: any) {
      console.error('Load team data error:', err);
      setError(err.message || 'Failed to load team data. Make sure the backend server is running.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTeam = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError(null);
      const newTeam = await TeamsService.createTeam(createTeamData);
      setMyTeam(newTeam);
      setShowCreateForm(false);
      setCreateTeamData({ name: '', description: '', isPublic: true, maxMembers: 5 });
      setActiveTab('my-team');
      setSuccess('Team created successfully!');
      setTimeout(() => setSuccess(null), 3000);
      await loadTeamData();
    } catch (err: any) {
      setError(err.message || 'Failed to create team');
    }
  };

  const handleJoinTeam = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError(null);
      const joinedTeam = await TeamsService.joinTeam(joinTeamData);
      setMyTeam(joinedTeam);
      setShowJoinForm(false);
      setJoinTeamData({ teamIdentifier: '' });
      setActiveTab('my-team');
      setSuccess('Successfully joined team!');
      setTimeout(() => setSuccess(null), 3000);
      await loadTeamData();
    } catch (err: any) {
      setError(err.message || 'Failed to join team');
    }
  };

  const handleLeaveTeam = async () => {
    if (!window.confirm('Are you sure you want to leave this team?')) return;
    
    try {
      setError(null);
      await TeamsService.leaveTeam();
      setMyTeam(null);
      setSuccess('Successfully left team');
      setTimeout(() => setSuccess(null), 3000);
      await loadTeamData();
    } catch (err: any) {
      setError(err.message || 'Failed to leave team');
    }
  };

  const handleRemoveMember = async (memberId: string, memberName: string) => {
    if (!window.confirm(`Are you sure you want to remove ${memberName} from the team?`)) return;
    
    try {
      setError(null);
      await TeamsService.removeMember(myTeam!.id, memberId);
      setSuccess(`${memberName} removed from team`);
      setTimeout(() => setSuccess(null), 3000);
      await loadTeamData();
    } catch (err: any) {
      setError(err.message || 'Failed to remove member');
    }
  };

  const handleRegenerateInviteCode = async () => {
    if (!window.confirm('Are you sure you want to regenerate the invite code? The old code will no longer work.')) return;
    
    try {
      setError(null);
      const result = await TeamsService.regenerateInviteCode(myTeam!.id);
      setMyTeam({ ...myTeam!, inviteCode: result.inviteCode });
      setSuccess('Invite code regenerated successfully');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message || 'Failed to regenerate invite code');
    }
  };

  const handleUpdateTeam = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError(null);
      const updatedTeam = await TeamsService.updateTeam(myTeam!.id, editTeamData);
      setMyTeam(updatedTeam);
      setShowEditForm(false);
      setSuccess('Team updated successfully!');
      setTimeout(() => setSuccess(null), 3000);
      await loadTeamData();
    } catch (err: any) {
      setError(err.message || 'Failed to update team');
    }
  };

  const loadTeamStats = async () => {
    if (!myTeam) return;
    try {
      const stats = await TeamsService.getTeamStats(myTeam.id);
      setTeamStats(stats);
    } catch (err: any) {
      console.error('Failed to load team stats:', err);
    }
  };

  useEffect(() => {
    if (myTeam && activeTab === 'manage') {
      loadTeamStats();
      setEditTeamData({
        name: myTeam.name,
        description: myTeam.description,
        isPublic: myTeam.isPublic,
        maxMembers: myTeam.maxMembers
      });
    }
  }, [myTeam, activeTab]);

  const filteredTeams = allTeams.filter(team =>
    team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    team.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const isTeamCaptain = myTeam && state.auth.user && myTeam.captainId === state.auth.user.id;
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent neon-text mb-4">
          Teams
        </h1>
        <p className="text-xl text-purple-200/80 mb-8">
          Join forces with fellow hackers and conquer challenges together
        </p>
      </div>

      {/* Success/Error Messages */}
      {error && (
        <div className="glass-card border-red-500/30 p-4 rounded-xl">
          <p className="text-red-400 text-center">{error}</p>
        </div>
      )}
      
      {success && (
        <div className="glass-card border-green-500/30 p-4 rounded-xl">
          <p className="text-green-400 text-center">{success}</p>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="flex justify-center mb-8">
        <div className="glass-card rounded-xl p-2 flex space-x-2">
          {[
            { id: 'my-team', label: 'My Team', icon: Shield },
            { id: 'browse', label: 'Browse Teams', icon: Search },
            { id: 'create', label: 'Create Team', icon: Plus },
            ...(isTeamCaptain ? [{ id: 'manage', label: 'Manage Team', icon: Settings }] : [])
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                    : 'text-purple-200 hover:text-white hover:bg-purple-500/20'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* My Team Tab */}
      {activeTab === 'my-team' && (
        <div className="space-y-6">
          {myTeam ? (
            <div className="glass-card p-8 rounded-xl border border-purple-500/30">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <div className="flex items-center space-x-3 mb-2">
                    <h2 className="text-3xl font-bold text-white">{myTeam.name}</h2>
                    {isTeamCaptain && <Crown className="w-6 h-6 text-yellow-400" />}
                  </div>
                  <p className="text-purple-200/80 mb-4">{myTeam.description}</p>
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center space-x-2">
                      <Trophy className="w-5 h-5 text-yellow-400" />
                      <span className="text-yellow-400 font-bold">{myTeam.teamScore} pts</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Target className="w-5 h-5 text-green-400" />
                      <span className="text-green-400 font-bold">Rank #{myTeam.rank}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="w-5 h-5 text-blue-400" />
                      <span className="text-blue-400">{myTeam.members.length}/{myTeam.maxMembers} members</span>
                    </div>
                  </div>
                </div>                <div className="flex space-x-2">
                  {isTeamCaptain && (
                    <button 
                      onClick={() => setActiveTab('manage')}
                      className="glass-card px-4 py-2 rounded-lg text-purple-200 hover:text-white transition-colors"
                    >
                      <Settings className="w-5 h-5" />
                    </button>
                  )}                  <button 
                    onClick={handleLeaveTeam}
                    className="glass-card px-4 py-2 rounded-lg text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-colors"
                  >
                    <LogOut className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Team Members */}
              <div>
                <h3 className="text-xl font-bold text-white mb-4">Team Members</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {myTeam.members.map(member => (
                    <div key={member.userId} className="glass-card-dark p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                            <span className="text-white font-bold">
                              {member.username.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-white">{member.username}</p>
                            <p className="text-sm text-purple-300/70">{member.email}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {member.role === 'captain' && <Crown className="w-4 h-4 text-yellow-400" />}
                          <span className="text-xs text-purple-300 capitalize">{member.role}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>              {/* Invite Code */}
              {isTeamCaptain && (
                <div className="mt-6 p-4 glass-card-dark rounded-lg">
                  <h4 className="font-medium text-white mb-2">Invite Code</h4>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 px-3 py-2 bg-slate-800 rounded text-green-400 font-mono select-none">
                      {showInviteCode ? myTeam.inviteCode : '••••••••••••'}
                    </code>
                    <button                      onClick={() => setShowInviteCode(!showInviteCode)}
                      className="p-2 glass-card hover:bg-purple-500/30 text-purple-300 hover:text-white transition-colors rounded"
                    >
                      {showInviteCode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>                    <button
                      onClick={() => navigator.clipboard.writeText(myTeam.inviteCode)}
                      className="p-3 glass-card hover:bg-purple-500/30 text-purple-300 hover:text-white transition-colors rounded"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                    <button
                      onClick={handleRegenerateInviteCode}                      className="px-4 py-2 bg-orange-500/20 text-orange-400 border border-orange-600/30 rounded-lg hover:bg-orange-500/30 transition-colors font-medium"
                    >
                      Regenerate
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center glass-card p-12 rounded-xl border border-purple-500/30">
              <Users className="w-16 h-16 text-purple-400 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">No Team Yet</h3>
              <p className="text-purple-200/80 mb-6">Join an existing team or create your own to get started!</p>              <div className="flex justify-center space-x-4">
                <button 
                  onClick={() => setShowJoinForm(true)}
                  className="flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg"
                >
                  <UserPlus className="w-5 h-5 mr-2" />
                  <span>Join Team</span>
                </button>
                <button 
                  onClick={() => setShowCreateForm(true)}
                  className="flex items-center justify-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  <span>Create Team</span>
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Browse Teams Tab */}
      {activeTab === 'browse' && (
        <div className="space-y-6">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-purple-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search teams..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 glass-card rounded-xl border border-purple-500/30 bg-slate-800/50 text-white placeholder-purple-300/50"
            />
          </div>

          {/* Teams Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTeams.map(team => (
              <div key={team.id} className="glass-card p-6 rounded-xl border border-purple-500/30 hover-lift">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-white mb-1">{team.name}</h3>
                    <p className="text-purple-200/80 text-sm">{team.description}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-yellow-400 font-bold">{team.teamScore} pts</p>
                    <p className="text-green-400 text-sm">Rank #{team.rank}</p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400 text-sm">{team.members.length}/{team.maxMembers}</span>
                  </div>
                  <button 
                    onClick={() => {
                      setJoinTeamData({ teamIdentifier: team.id });
                      setShowJoinForm(true);
                    }}                    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors text-sm"
                    disabled={!myTeam ? false : true}
                  >
                    {!myTeam ? 'Join' : 'In Team'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}      {/* Create Team Tab */}
      {activeTab === 'create' && (
        <div className="max-w-4xl mx-auto">
          {myTeam ? (
            <div className="glass-card p-12 rounded-xl border border-orange-500/30 text-center">
              <div className="w-20 h-20 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-10 h-10 text-orange-400" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Already in a Team</h3>
              <p className="text-purple-200/80 mb-6 max-w-md mx-auto">
                You're already a member of <span className="text-purple-400 font-semibold">"{myTeam.name}"</span>. 
                Leave your current team to create a new one.
              </p>              <button 
                onClick={() => setActiveTab('my-team')}
                className="flex items-center justify-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                <Shield className="w-5 h-5 mr-2" />
                <span>View My Team</span>
              </button>
            </div>
          ) : (
            <div className="glass-card p-10 rounded-xl border border-purple-500/30">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-white mb-2">Create Your Dream Team</h3>
                <p className="text-purple-200/80 max-w-2xl mx-auto">
                  Assemble a team of skilled hackers and compete together in cybersecurity challenges. 
                  Choose your team settings carefully to attract the right members.
                </p>
              </div>

              <form onSubmit={handleCreateTeam} className="space-y-8">
                {/* Team Identity Section */}
                <div className="bg-slate-800/30 rounded-xl p-6 border border-purple-500/20">
                  <h4 className="text-xl font-semibold text-white mb-4 flex items-center">
                    <Edit className="w-5 h-5 mr-2 text-purple-400" />
                    Team Identity
                  </h4>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-purple-200 mb-3 font-medium">Team Name *</label>
                      <input
                        type="text"
                        value={createTeamData.name}
                        onChange={(e) => setCreateTeamData({ ...createTeamData, name: e.target.value })}
                        className="w-full px-4 py-3 bg-slate-700/50 border border-purple-500/30 rounded-lg text-white placeholder-purple-300/50 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all"
                        placeholder="Enter an awesome team name..."
                        required
                        maxLength={50}
                      />
                      <p className="text-purple-400/60 text-sm mt-1">{createTeamData.name.length}/50 characters</p>
                    </div>

                    <div>
                      <label className="block text-purple-200 mb-3 font-medium">Team Size</label>
                      <select
                        value={createTeamData.maxMembers}
                        onChange={(e) => setCreateTeamData({ ...createTeamData, maxMembers: parseInt(e.target.value) })}
                        className="w-full px-4 py-3 bg-slate-700/50 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all"
                      >
                        {[3, 4, 5, 6, 7, 8].map(num => (
                          <option key={num} value={num}>{num} members maximum</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="mt-6">
                    <label className="block text-purple-200 mb-3 font-medium">Team Description</label>
                    <textarea
                      value={createTeamData.description}
                      onChange={(e) => setCreateTeamData({ ...createTeamData, description: e.target.value })}
                      className="w-full px-4 py-3 bg-slate-700/50 border border-purple-500/30 rounded-lg text-white placeholder-purple-300/50 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all resize-none h-32"
                      placeholder="Tell others about your team's goals, expertise areas, and what you're looking for in team members..."
                      maxLength={500}
                    />
                    <p className="text-purple-400/60 text-sm mt-1">{createTeamData.description.length}/500 characters</p>
                  </div>
                </div>

                {/* Team Privacy Section */}
                <div className="bg-slate-800/30 rounded-xl p-6 border border-purple-500/20">
                  <h4 className="text-xl font-semibold text-white mb-4 flex items-center">
                    <Eye className="w-5 h-5 mr-2 text-purple-400" />
                    Privacy Settings
                  </h4>
                  
                  <div className="space-y-4">
                    <div 
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                        createTeamData.isPublic 
                          ? 'border-green-500/50 bg-green-500/10' 
                          : 'border-slate-600 bg-slate-700/20 hover:border-green-500/30'
                      }`}
                      onClick={() => setCreateTeamData({ ...createTeamData, isPublic: true })}
                    >
                      <label className="flex items-start space-x-3 cursor-pointer">
                        <input
                          type="radio"
                          name="visibility"
                          checked={createTeamData.isPublic}
                          onChange={() => setCreateTeamData({ ...createTeamData, isPublic: true })}
                          className="w-5 h-5 text-green-500 mt-0.5"
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <Users className="w-5 h-5 text-green-400" />
                            <span className="text-green-400 font-semibold text-lg">Public Team</span>
                          </div>
                          <p className="text-slate-300 mt-1">
                            Anyone can discover and join your team. Great for meeting new people and growing quickly.
                          </p>
                          <div className="flex items-center space-x-4 mt-2 text-sm text-green-400/80">
                            <span>✓ Discoverable in team browser</span>
                            <span>✓ Easy to join</span>
                            <span>✓ Fast growth</span>
                          </div>
                        </div>
                      </label>
                    </div>

                    <div 
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                        !createTeamData.isPublic 
                          ? 'border-orange-500/50 bg-orange-500/10' 
                          : 'border-slate-600 bg-slate-700/20 hover:border-orange-500/30'
                      }`}
                      onClick={() => setCreateTeamData({ ...createTeamData, isPublic: false })}
                    >
                      <label className="flex items-start space-x-3 cursor-pointer">
                        <input
                          type="radio"
                          name="visibility"
                          checked={!createTeamData.isPublic}
                          onChange={() => setCreateTeamData({ ...createTeamData, isPublic: false })}
                          className="w-5 h-5 text-orange-500 mt-0.5"
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <Shield className="w-5 h-5 text-orange-400" />
                            <span className="text-orange-400 font-semibold text-lg">Private Team</span>
                          </div>
                          <p className="text-slate-300 mt-1">
                            Only people with your invite code can join. Perfect for close-knit groups and exclusive teams.
                          </p>
                          <div className="flex items-center space-x-4 mt-2 text-sm text-orange-400/80">
                            <span>✓ Invite-only access</span>
                            <span>✓ Selective membership</span>
                            <span>✓ More control</span>
                          </div>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Create Button */}
                <div className="text-center">                  <button 
                    type="submit" 
                    className="flex items-center justify-center px-12 py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 transform hover:scale-105 transition-all duration-200 shadow-lg text-white font-bold rounded-xl text-lg"
                  >
                    <Plus className="w-6 h-6 mr-3" />
                    <span>Create My Team</span>
                  </button>
                  <p className="text-purple-300/60 text-sm mt-3">
                    You can always modify these settings later as team captain
                  </p>
                </div>
              </form>
            </div>
          )}
        </div>
      )}      {/* Join Team Modal */}
      {showJoinForm && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-[99999] backdrop-blur-sm" style={{zIndex: 999999}}>
          <div className="glass-card p-8 rounded-xl border border-purple-500/30 max-w-lg w-full mx-4 shadow-2xl">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <UserPlus className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">Join a Team</h3>
              <p className="text-purple-200/80">
                Enter a team ID or invite code to join an existing team
              </p>
            </div>

            <form onSubmit={handleJoinTeam} className="space-y-6">
              <div>
                <label className="block text-purple-200 mb-3 font-medium">Team ID or Invite Code</label>
                <div className="relative">
                  <input
                    type="text"
                    value={joinTeamData.teamIdentifier}
                    onChange={(e) => setJoinTeamData({ teamIdentifier: e.target.value })}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-purple-500/30 rounded-lg text-white placeholder-purple-300/50 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all pl-12"
                    placeholder="team_abc123def456 or 507f1f77bcf86cd799439011"
                    required
                  />
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-purple-400" />
                </div>
                <div className="mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <div className="flex items-start space-x-2">
                    <div className="w-4 h-4 bg-blue-400 rounded-full mt-0.5 flex-shrink-0"></div>
                    <div className="text-sm text-blue-300">
                      <p className="font-medium mb-1">How to find team codes:</p>
                      <ul className="space-y-1 text-blue-300/80">
                        <li>• Public teams: Use the team ID from the Browse Teams tab</li>
                        <li>• Private teams: Ask the team captain for an invite code</li>
                        <li>• Team codes are case-sensitive</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-4">                <button 
                  type="submit" 
                  className="flex items-center justify-center flex-1 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105"
                >
                  <UserPlus className="w-4 h-4 mr-2" />
                  <span>Join Team</span>
                </button>
                <button 
                  type="button" 
                  onClick={() => setShowJoinForm(false)}
                  className="flex items-center justify-center flex-1 px-6 py-3 bg-slate-700/50 hover:bg-slate-700/70 text-white font-medium rounded-lg transition-colors border border-slate-600/50"
                >
                  <span>Cancel</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}      {/* Create Team Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-[9999] backdrop-blur-sm overflow-y-auto py-8">
          <div className="glass-card p-0 rounded-2xl border border-purple-500/40 max-w-3xl w-full mx-4 shadow-2xl relative animate-scale-in">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-purple-900/60 to-indigo-900/60 backdrop-blur-sm p-6 rounded-t-2xl border-b border-purple-500/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 p-0.5">
                    <div className="h-full w-full rounded-xl bg-slate-900/80 flex items-center justify-center">
                      <Sparkles className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-1 tracking-tight">Create New Team</h3>
                    <p className="text-purple-200/70 text-sm">Build your dream team of hackers</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="w-8 h-8 rounded-full bg-slate-800/80 hover:bg-slate-700 flex items-center justify-center text-purple-300 hover:text-white transition-all"
                >
                  <XCircle className="w-5 h-5" />
                </button>
              </div>
            </div>
            
            {/* Modal Body */}
            <div className="p-6">
              <form onSubmit={handleCreateTeam} className="space-y-6">
                {/* Team Name */}
                <div>
                  <label className="block text-purple-200 mb-2 font-medium">Team Name</label>
                  <div className="relative">
                    <input
                      type="text"
                      value={createTeamData.name}
                      onChange={(e) => setCreateTeamData({ ...createTeamData, name: e.target.value })}
                      className="w-full px-5 py-3.5 bg-slate-800/70 border border-purple-500/30 rounded-lg text-white placeholder-purple-300/50 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all pr-4 shadow-inner"
                      placeholder="Enter your team name..."
                      required
                      maxLength={50}
                    />
                  </div>
                  <p className="text-purple-400/60 text-xs mt-1.5 flex justify-between">
                    <span>Choose a memorable and creative name</span>
                    <span>{createTeamData.name.length}/50</span>
                  </p>
                </div>
                
                {/* Description */}
                <div>
                  <label className="block text-purple-200 mb-2 font-medium">Description</label>
                  <textarea
                    value={createTeamData.description}
                    onChange={(e) => setCreateTeamData({ ...createTeamData, description: e.target.value })}
                    className="w-full px-5 py-3.5 bg-slate-800/70 border border-purple-500/30 rounded-lg text-white placeholder-purple-300/50 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all resize-none h-28 shadow-inner"
                    placeholder="Describe your team's mission, goals, and the kinds of members you're looking for..."
                    maxLength={500}
                  />
                  <p className="text-purple-400/60 text-xs mt-1.5 flex justify-between">
                    <span>A great description helps attract the right teammates</span>
                    <span>{createTeamData.description.length}/500</span>
                  </p>
                </div>
                
                {/* Team Settings Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Max Members */}
                  <div>
                    <label className="block text-purple-200 mb-2 font-medium">Team Size</label>
                    <div className="relative">
                      <select
                        value={createTeamData.maxMembers}
                        onChange={(e) => setCreateTeamData({ ...createTeamData, maxMembers: parseInt(e.target.value) })}
                        className="w-full appearance-none px-5 py-3.5 bg-slate-800/70 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all shadow-inner"
                      >
                        {[3, 4, 5, 6, 7, 8].map(num => (
                          <option key={num} value={num}>{num} members maximum</option>
                        ))}
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-purple-300">
                        <svg className="w-4 h-4 fill-current" viewBox="0 0 20 20">
                          <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"></path>
                        </svg>
                      </div>
                    </div>
                    <p className="text-purple-400/60 text-xs mt-1.5">Choose how many members can join your team</p>
                  </div>
                  
                  {/* Team Visibility */}
                  <div>
                    <label className="block text-purple-200 mb-2 font-medium">Team Visibility</label>
                    <div className="relative">
                      <div className={`flex items-center p-3.5 border ${createTeamData.isPublic ? 'border-green-500/40 bg-green-500/10' : 'border-orange-500/40 bg-orange-500/10'} rounded-lg`}>
                        <div className="mr-3">
                          {createTeamData.isPublic ? (
                            <Users className="w-5 h-5 text-green-400" />
                          ) : (
                            <Shield className="w-5 h-5 text-orange-400" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <div className="font-medium text-sm">
                              {createTeamData.isPublic ? (
                                <span className="text-green-400">Public Team</span>
                              ) : (
                                <span className="text-orange-400">Private Team</span>
                              )}
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input 
                                type="checkbox"
                                checked={createTeamData.isPublic}
                                onChange={(e) => setCreateTeamData({ ...createTeamData, isPublic: e.target.checked })}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-slate-700 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
                            </label>
                          </div>
                          <p className="text-xs mt-1 text-slate-300">
                            {createTeamData.isPublic ? 
                              "Anyone can find and join your team" : 
                              "Only people with your invite code can join"}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Action Buttons */}
                <div className="flex space-x-4 pt-2">
                  <button 
                    type="submit" 
                    className="flex items-center justify-center flex-1 px-6 py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg"
                  >
                    <Sparkles className="w-5 h-5 mr-2" />
                    <span>Create My Team</span>
                  </button>
                  <button 
                    type="button" 
                    onClick={() => setShowCreateForm(false)}
                    className="flex items-center justify-center px-6 py-4 bg-slate-700/50 hover:bg-slate-700/70 text-white font-medium rounded-lg transition-colors border border-slate-600/50"
                  >
                    <span>Cancel</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Manage Team Tab (Captain Only) */}
      {activeTab === 'manage' && isTeamCaptain && myTeam && (
        <div className="space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-2">
              <Crown className="w-8 h-8 inline-block mr-3 text-yellow-400" />
              Team Management
            </h2>
            <p className="text-purple-200/80">Manage your team members and settings</p>
          </div>

          {/* Team Settings */}
          <div className="glass-card p-6 rounded-xl border border-purple-500/30">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">
                <Settings className="w-5 h-5 inline-block mr-2" />
                Team Settings
              </h3>              <button
                onClick={() => setShowEditForm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-emerald-600/20 text-emerald-400 rounded-lg hover:bg-emerald-600/30 transition-colors"
              >
                <Edit className="w-4 h-4" />
                <span>Edit Team</span>
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-white mb-2">Team Name</h4>
                <p className="text-purple-200/80">{myTeam.name}</p>
              </div>
              <div>
                <h4 className="font-medium text-white mb-2">Visibility</h4>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                  myTeam.isPublic ? 'bg-green-500/20 text-green-400' : 'bg-orange-500/20 text-orange-400'
                }`}>
                  {myTeam.isPublic ? 'Public' : 'Private'}
                </span>
              </div>
              <div>
                <h4 className="font-medium text-white mb-2">Max Members</h4>
                <p className="text-purple-200/80">{myTeam.maxMembers} members</p>
              </div>
              <div>
                <h4 className="font-medium text-white mb-2">Current Members</h4>
                <p className="text-purple-200/80">{myTeam.members.length} / {myTeam.maxMembers}</p>
              </div>
            </div>
          </div>

          {/* Team Statistics */}
          {teamStats && (
            <div className="glass-card p-6 rounded-xl border border-purple-500/30">
              <h3 className="text-xl font-bold text-white mb-4">
                <Trophy className="w-5 h-5 inline-block mr-2" />
                Team Statistics
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{teamStats.totalMembers}</div>
                  <div className="text-sm text-purple-300">Active Members</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{teamStats.solvedChallenges}</div>
                  <div className="text-sm text-purple-300">Solved Challenges</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-400">{teamStats.totalScore}</div>
                  <div className="text-sm text-purple-300">Total Points</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">#{teamStats.rank}</div>
                  <div className="text-sm text-purple-300">Global Rank</div>
                </div>
              </div>
            </div>
          )}

          {/* Invite Management */}
          {!myTeam.isPublic && (
            <div className="glass-card p-6 rounded-xl border border-orange-500/30">
              <h3 className="text-xl font-bold text-white mb-4">
                <UserPlus className="w-5 h-5 inline-block mr-2" />
                Invite Management
              </h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-white mb-2">Current Invite Code</h4>
                  <div className="flex items-center space-x-3">
                    <code className="flex-1 px-4 py-3 bg-gray-800 rounded text-green-400 font-mono select-none">
                      {showInviteCode ? myTeam.inviteCode : '••••••••••••••••••••'}
                    </code>
                    <button
                      onClick={() => setShowInviteCode(!showInviteCode)}
                      className="p-3 glass-card text-purple-300 hover:text-white transition-colors rounded"
                    >
                      {showInviteCode ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>                    <button
                      onClick={handleRegenerateInviteCode}
                      className="flex items-center justify-center px-4 py-3 bg-orange-500/20 text-orange-400 border border-orange-600/30 rounded-lg hover:bg-orange-500/30 transition-colors font-medium"
                    >
                      <span>Regenerate</span>
                    </button>
                  </div>
                  <p className="text-purple-300/70 text-sm mt-2">
                    Share this code with people you want to invite. Regenerating will invalidate the old code.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Member Management */}
          <div className="glass-card p-6 rounded-xl border border-purple-500/30">
            <h3 className="text-xl font-bold text-white mb-4">
              <Users className="w-5 h-5 inline-block mr-2" />
              Member Management
            </h3>
            <div className="space-y-4">
              {myTeam.members.map(member => (
                <div key={member.userId} className="glass-card-dark p-4 rounded-lg border border-purple-500/20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold text-lg">
                          {member.username.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-white">{member.username}</p>
                          {member.role === 'captain' && (
                            <Crown className="w-4 h-4 text-yellow-400" />
                          )}
                        </div>
                        <p className="text-sm text-purple-300/70">{member.email}</p>
                        <p className="text-xs text-purple-400/60">Joined {new Date(member.joinedAt).toLocaleDateString()}</p>
                      </div>
                    </div>
                    
                    {member.role !== 'captain' && (
                      <div className="flex items-center space-x-2">
                        <button                          onClick={() => handleRemoveMember(member.userId, member.username)}
                          className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors"
                          title="Remove member"
                        >
                          <UserMinus className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Edit Team Modal */}
      {showEditForm && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-[9999]">
          <div className="glass-card p-8 rounded-xl border border-purple-500/30 max-w-2xl w-full mx-4">
            <h3 className="text-2xl font-bold text-white mb-4">Edit Team</h3>
            <form onSubmit={handleUpdateTeam} className="space-y-4">
              <div>
                <label className="block text-purple-200 mb-2">Team Name</label>
                <input
                  type="text"
                  value={editTeamData.name}
                  onChange={(e) => setEditTeamData({ ...editTeamData, name: e.target.value })}
                  className="input-field"
                  placeholder="Enter team name..."
                  required
                />
              </div>
              <div>
                <label className="block text-purple-200 mb-2">Description</label>
                <textarea
                  value={editTeamData.description}
                  onChange={(e) => setEditTeamData({ ...editTeamData, description: e.target.value })}
                  className="input-field resize-none h-24"
                  placeholder="Describe your team..."
                />
              </div>
              <div className="flex space-x-4">
                <div className="flex-1">
                  <label className="block text-purple-200 mb-2">Max Members</label>
                  <select
                    value={editTeamData.maxMembers}
                    onChange={(e) => setEditTeamData({ ...editTeamData, maxMembers: parseInt(e.target.value) })}
                    className="input-field"
                  >
                    {[3, 4, 5, 6, 7, 8].map(num => (
                      <option key={num} value={num}>{num} members</option>
                    ))}
                  </select>
                </div>
                <div className="flex items-end">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={editTeamData.isPublic}
                      onChange={(e) => setEditTeamData({ ...editTeamData, isPublic: e.target.checked })}
                      className="w-5 h-5 text-purple-500"
                    />
                    <span className="text-purple-200">Public Team</span>
                  </label>
                </div>
              </div>
              <div className="flex space-x-4">                <button 
                  type="submit" 
                  className="flex items-center justify-center flex-1 px-6 py-3 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg transition-colors"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  <span>Update Team</span>
                </button>
                <button 
                  type="button" 
                  onClick={() => setShowEditForm(false)}
                  className="flex items-center justify-center flex-1 px-6 py-3 bg-slate-700/50 hover:bg-slate-700/70 text-white font-medium rounded-lg transition-colors border border-slate-600/50"
                >
                  <span>Cancel</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
