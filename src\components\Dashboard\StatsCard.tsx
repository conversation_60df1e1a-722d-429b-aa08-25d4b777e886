import React, { useState, useEffect } from 'react';
import { DivideIcon as LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  color: 'purple' | 'pink' | 'blue' | 'indigo';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  delay?: number;
}

const colorClasses = {
  purple: 'from-purple-500/20 to-purple-600/20 border-purple-500/30 text-purple-400',
  pink: 'from-pink-500/20 to-pink-600/20 border-pink-500/30 text-pink-400',
  blue: 'from-blue-500/20 to-blue-600/20 border-blue-500/30 text-blue-400',
  indigo: 'from-indigo-500/20 to-indigo-600/20 border-indigo-500/30 text-indigo-400'
};

export function StatsCard({ title, value, icon: Icon, color, trend, delay = 0 }: StatsCardProps) {
  const [mounted, setMounted] = useState(false);
  const [animatedValue, setAnimatedValue] = useState(0);
  const colorClass = colorClasses[color];

  useEffect(() => {
    const timer = setTimeout(() => setMounted(true), delay);
    return () => clearTimeout(timer);
  }, [delay]);

  useEffect(() => {
    if (mounted && typeof value === 'number') {
      let start = 0;
      const end = value;
      const duration = 2000;
      const increment = end / (duration / 16);

      const timer = setInterval(() => {
        start += increment;
        if (start >= end) {
          setAnimatedValue(end);
          clearInterval(timer);
        } else {
          setAnimatedValue(Math.floor(start));
        }
      }, 16);

      return () => clearInterval(timer);
    }
  }, [mounted, value]);

  return (
    <div 
      className={`glass-card-dark backdrop-blur-xl rounded-2xl border p-6 interactive-card neon-border shimmer-effect ${
        mounted ? 'animate-bounce-in' : 'opacity-0'
      }`}
      style={{ animationDelay: `${delay}ms` }}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-purple-200/80 text-sm font-medium mb-2">{title}</p>
          <p className="text-3xl font-bold text-white mb-1">
            {typeof value === 'number' ? animatedValue.toLocaleString() : value}
          </p>
          {trend && (
            <div className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${trend.isPositive ? 'bg-green-400' : 'bg-red-400'} animate-pulse`} />
              <p className={`text-sm font-medium ${trend.isPositive ? 'text-green-400' : 'text-red-400'}`}>
                {trend.isPositive ? '+' : '-'}{Math.abs(trend.value)}%
              </p>
            </div>
          )}
        </div>
        <div className={`p-4 rounded-2xl bg-gradient-to-br ${colorClass} animate-float`}>
          <Icon className="w-8 h-8" />
        </div>
      </div>

      {/* Animated progress bar */}
      <div className="mt-4 w-full h-1 bg-purple-900/50 rounded-full overflow-hidden">
        <div 
          className={`h-full bg-gradient-to-r ${colorClass.split(' ')[0]} ${colorClass.split(' ')[1]} transition-all duration-2000 ease-out`}
          style={{ 
            width: mounted ? '100%' : '0%',
            transitionDelay: `${delay + 500}ms`
          }}
        />
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-purple-400/60 rounded-full animate-float"
            style={{
              left: `${20 + i * 30}%`,
              top: `${20 + i * 20}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${3 + i}s`
            }}
          />
        ))}
      </div>
    </div>
  );
}