{% extends "base.html" %}
{% block title %}Login{% endblock %}
{% block content %}
<h2>Login</h2>
<form method="POST">
  <label>Username:</label>
  <input type="text" name="username" required>

  <label>Password:</label>
  <input type="password" name="password" required>

  <button type="submit">Login</button>
</form>

{% with messages = get_flashed_messages() %}
  {% if messages %}
    <div class="flash">
      <ul>
      {% for message in messages %}
        <li>{{ message }}</li>
      {% endfor %}
      </ul>
    </div>
  {% endif %}
{% endwith %}
{% endblock %}
