import { Controller, Post, Get, Delete, Req, Res, UseGuards, Body, Param, HttpException } from '@nestjs/common';
import { VpnService } from './vpn.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Response, Request } from 'express';
import { User } from '../schemas/user.schema';

interface AuthenticatedRequest extends Request {
  user: User;
}

@Controller('vpn')
export class VpnController {
  constructor(private readonly vpnService: VpnService) {}

  @Post('create')
  @UseGuards(JwtAuthGuard)
  async createVpnUser(@Req() req: AuthenticatedRequest, @Res() res: Response) {
    const username = req.user?.username;
    if (!username) throw new HttpException('No username', 400);
    
    const config = await this.vpnService.createUser(username);
    res.set({
      'Content-Type': 'application/x-wireguard-profile',
      'Content-Disposition': `attachment; filename="${username}.conf"`,
    });
    res.send(config);
  }

  @Get('config')
  @UseGuards(JwtAuthGuard)
  async getConfig(@Req() req: AuthenticatedRequest, @Res() res: Response) {
    const username = req.user?.username;
    if (!username) throw new HttpException('No username', 400);
    
    const config = await this.vpnService.getWireguardConfig(username);
    res.set({
      'Content-Type': 'application/x-wireguard-profile',
      'Content-Disposition': `attachment; filename="${username}.conf"`,
    });
    res.send(config);
  }

  @Delete('user')
  @UseGuards(JwtAuthGuard)
  async revokeUser(@Req() req: AuthenticatedRequest) {
    const username = req.user?.username;
    if (!username) throw new HttpException('No username', 400);
    return this.vpnService.revokeUser(username);
  }

  @Post('enable')
  @UseGuards(JwtAuthGuard)
  async enableUser(@Req() req: AuthenticatedRequest) {
    const username = req.user?.username;
    if (!username) throw new HttpException('No username', 400);
    return this.vpnService.enableClient(username);
  }

  @Post('disable')
  @UseGuards(JwtAuthGuard)
  async disableUser(@Req() req: AuthenticatedRequest) {
    const username = req.user?.username;
    if (!username) throw new HttpException('No username', 400);
    return this.vpnService.disableClient(username);
  }

  @Get('status')
  @UseGuards(JwtAuthGuard)
  async getStatus(@Req() req: AuthenticatedRequest) {
    return this.vpnService.getServerInfo();
  }

  @Get('users')
  @UseGuards(JwtAuthGuard)
  async listUsers(@Req() req: AuthenticatedRequest) {
    return this.vpnService.listUsers();
  }

  @Get('health')
  async healthCheck() {
    return this.vpnService.healthCheck();
  }
}
