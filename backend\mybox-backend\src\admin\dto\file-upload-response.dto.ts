import { ApiProperty } from '@nestjs/swagger';

export class FileUploadResponseDto {
  @ApiProperty({ description: 'Original filename provided by the client' })
  originalName: string;

  @ApiProperty({ description: 'Generated unique filename stored on server' })
  filename: string;

  @ApiProperty({ description: 'Relative path to the file' })
  filePath: string;

  @ApiProperty({ description: 'Size of file in bytes' })
  size: number;

  @ApiProperty({ description: 'MIME type of the file' })
  mimeType: string;

  @ApiProperty({ description: 'URL to access the file' })
  url: string;
}
