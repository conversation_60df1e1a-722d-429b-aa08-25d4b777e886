import { Module, NestModule, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { TeamsModule } from './teams/teams.module';
import { ChallengesModule } from './challenges/challenges.module';
import { CategoriesModule } from './categories/categories.module';
import { VirtualMachinesModule } from './virtual-machines/virtual-machines.module';
import { LeaderboardModule } from './leaderboard/leaderboard.module';
import { VpnModule } from './vpn/vpn.module';
import { AdminModule } from './admin/admin.module';
import { StatisticsModule } from './statistics/statistics.module';
import { NotificationsModule } from './notifications/notifications.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { MaintenanceMiddleware } from './middleware/maintenance.middleware';
import { AdminSettings, AdminSettingsSchema } from './schemas/admin-settings.schema';
import { User, UserSchema } from './schemas/user.schema';

@Module({
  imports: [
    // Global configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    
    // MongoDB connection
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
      }),
      inject: [ConfigService],
    }),

    // Add schemas globally for middleware
    MongooseModule.forFeature([
      { name: AdminSettings.name, schema: AdminSettingsSchema },
      { name: User.name, schema: UserSchema },
    ]),
    
    // JWT Global Module
    JwtModule.registerAsync({
      global: true,
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '7d'),
        },
      }),
      inject: [ConfigService],
    }),
    
    // Feature modules
    AuthModule,
    UsersModule,
    TeamsModule,
    ChallengesModule,
    CategoriesModule,
    VirtualMachinesModule,
    LeaderboardModule,
    VpnModule,
    AdminModule,
    StatisticsModule,
    NotificationsModule,
    DashboardModule
  ],
  controllers: [AppController],
  providers: [AppService, MaintenanceMiddleware],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(MaintenanceMiddleware)
      .exclude(
        { path: 'auth/(.*)', method: RequestMethod.ALL },
        { path: 'admin/(.*)', method: RequestMethod.ALL },
        { path: 'public-settings/(.*)', method: RequestMethod.ALL },
        { path: 'dashboard/config', method: RequestMethod.ALL },
      )
      .forRoutes('*'); // Apply to all routes except excluded ones
  }
}