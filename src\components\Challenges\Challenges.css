/* Challenges Page - Statistics Style Design */

/* Custom scrollbar for challenges page */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(139, 92, 246, 0.1);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(139, 92, 246, 0.1);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #8B5CF6, #EC4899);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #A78BFA, #F472B6);
}

/* Glass card effects - Always visible */
.glass-card {
  backdrop-filter: blur(20px) !important;
  background: rgba(139, 92, 246, 0.1) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(139, 92, 246, 0.1),
    inset 0 0 20px rgba(139, 92, 246, 0.05) !important;
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(139, 92, 246, 0.15) !important;
  border: 1px solid rgba(139, 92, 246, 0.4) !important;
  transform: translateY(-2px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(139, 92, 246, 0.2),
    inset 0 0 30px rgba(139, 92, 246, 0.1) !important;
}

.glass-card-dark {
  backdrop-filter: blur(20px) !important;
  background: rgba(139, 92, 246, 0.08) !important;
  border: 1px solid rgba(139, 92, 246, 0.25) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(139, 92, 246, 0.1),
    inset 0 0 20px rgba(139, 92, 246, 0.05) !important;
  transition: all 0.3s ease;
}

.glass-card-dark:hover {
  background: rgba(139, 92, 246, 0.12) !important;
  border: 1px solid rgba(139, 92, 246, 0.35) !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(139, 92, 246, 0.15),
    inset 0 0 30px rgba(139, 92, 246, 0.08) !important;
}

/* Override ChallengeCard styles with higher specificity */
.challenge-card-wrapper .group {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(236, 72, 153, 0.05)) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(139, 92, 246, 0.1),
    inset 0 0 20px rgba(139, 92, 246, 0.05) !important;
  transition: all 0.3s ease !important;
}

.challenge-card-wrapper .group:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(236, 72, 153, 0.1)) !important;
  border: 1px solid rgba(139, 92, 246, 0.4) !important;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(139, 92, 246, 0.2),
    inset 0 0 30px rgba(139, 92, 246, 0.1) !important;
  transform: translateY(-8px) !important;
}

/* Challenge card wrapper enhancements */
.challenge-card-wrapper {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.challenge-card-wrapper:hover {
  z-index: 10;
}

/* Enhanced challenge card styles - Always visible effects */
.challenge-card-wrapper .group::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(236, 72, 153, 0.05));
  opacity: 1;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.challenge-card-wrapper:hover .group::before {
  opacity: 0.3;
}

/* Floating particles on hover */
.challenge-card-wrapper:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.3) 2px, transparent 2px),
    radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.3) 2px, transparent 2px),
    radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.3) 2px, transparent 2px);
  background-size: 50px 50px, 60px 60px, 40px 40px;
  animation: float-particles 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 2;
}

@keyframes float-particles {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px);
    opacity: 0.7;
  }
}

/* Neon glow effects - Always visible */
.neon-glow {
  box-shadow: 
    0 0 5px rgba(139, 92, 246, 0.5),
    0 0 10px rgba(139, 92, 246, 0.3),
    0 0 15px rgba(139, 92, 246, 0.2),
    0 0 20px rgba(139, 92, 246, 0.1);
}

.neon-glow-pink {
  box-shadow: 
    0 0 5px rgba(236, 72, 153, 0.5),
    0 0 10px rgba(236, 72, 153, 0.3),
    0 0 15px rgba(236, 72, 153, 0.2),
    0 0 20px rgba(236, 72, 153, 0.1);
}

.neon-glow-blue {
  box-shadow: 
    0 0 5px rgba(6, 182, 212, 0.5),
    0 0 10px rgba(6, 182, 212, 0.3),
    0 0 15px rgba(6, 182, 212, 0.2),
    0 0 20px rgba(6, 182, 212, 0.1);
}

.neon-glow-green {
  box-shadow: 
    0 0 5px rgba(16, 185, 129, 0.5),
    0 0 10px rgba(16, 185, 129, 0.3),
    0 0 15px rgba(16, 185, 129, 0.2),
    0 0 20px rgba(16, 185, 129, 0.1);
}

.neon-glow-yellow {
  box-shadow: 
    0 0 5px rgba(245, 158, 11, 0.5),
    0 0 10px rgba(245, 158, 11, 0.3),
    0 0 15px rgba(245, 158, 11, 0.2),
    0 0 20px rgba(245, 158, 11, 0.1);
}

/* Floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Morph shapes */
.morph-shape {
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: translate3d(0, 0, 0) rotateZ(0.01deg);
  }
  34% {
    border-radius: 70% 60% 70% 30% / 50% 60% 30% 60%;
    transform: translate3d(5px, -10px, 0) rotateZ(0.01deg);
  }
  67% {
    border-radius: 100% 60% 60% 100% / 100% 100% 60% 60%;
    transform: translate3d(-5px, 10px, 0) rotateZ(0.01deg);
  }
}

/* Pulse animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text {
  background: linear-gradient(-45deg, #8B5CF6, #EC4899, #06B6D4, #10B981);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* First blood indicator enhancements */
.first-blood-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  animation: pulse-gold 2s infinite;
  z-index: 20;
}

@keyframes pulse-gold {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6);
  }
}

/* Solved indicator enhancements */
.solved-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #10B981, #059669);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  animation: pulse-green 2s infinite;
  z-index: 20;
}

@keyframes pulse-green {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6);
  }
}

/* Enhanced Modal Design - Statistics Style with Scrolling */
div[class*="fixed inset-0 bg-black/50"] {
  backdrop-filter: blur(20px) !important;
  background: rgba(0, 0, 0, 0.8) !important;
  animation: modal-fade-in 0.3s ease-out;
  z-index: 2147483647 !important;
  overflow-y: auto !important;
  overscroll-behavior: contain !important;
}

div[class*="bg-slate-900 rounded-xl"] {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(236, 72, 153, 0.05)) !important;
  backdrop-filter: blur(25px) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(139, 92, 246, 0.3),
    inset 0 0 30px rgba(139, 92, 246, 0.05) !important;
  position: relative;
  overflow: hidden;
  z-index: 2147483647 !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
  scroll-behavior: smooth !important;
  scrollbar-width: thin !important;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(139, 92, 246, 0.1) !important;
}

/* Enhanced Modal Scrollbar */
div[class*="bg-slate-900 rounded-xl"]::-webkit-scrollbar {
  width: 12px !important;
}

div[class*="bg-slate-900 rounded-xl"]::-webkit-scrollbar-track {
  background: rgba(139, 92, 246, 0.1) !important;
  border-radius: 6px !important;
  margin: 4px !important;
}

div[class*="bg-slate-900 rounded-xl"]::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #8B5CF6, #EC4899) !important;
  border-radius: 6px !important;
  border: 2px solid rgba(139, 92, 246, 0.1) !important;
}

div[class*="bg-slate-900 rounded-xl"]::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #A78BFA, #F472B6) !important;
}

/* Modal background overlay */
div[class*="bg-slate-900 rounded-xl"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(236, 72, 153, 0.03));
  pointer-events: none;
  z-index: 1;
}

/* Modal content wrapper */
div[class*="bg-slate-900 rounded-xl"] > div {
  position: relative;
  z-index: 2;
  max-height: none !important;
  overflow: visible !important;
}

/* Modal floating particles */
div[class*="bg-slate-900 rounded-xl"]::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(139, 92, 246, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 80% 80%, rgba(236, 72, 153, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.1) 2px, transparent 2px);
  background-size: 100px 100px, 120px 120px, 80px 80px;
  animation: modal-particles 8s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes modal-fade-in {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(20px);
  }
}

@keyframes modal-particles {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.6;
  }
}

/* Enhanced modal sections */
div[class*="bg-slate-900 rounded-xl"] div[class*="bg-slate-800/50"] {
  background: rgba(139, 92, 246, 0.08) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 10px rgba(139, 92, 246, 0.1) !important;
  transition: all 0.3s ease;
}

div[class*="bg-slate-900 rounded-xl"] div[class*="bg-slate-800/50"]:hover {
  background: rgba(139, 92, 246, 0.12) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(139, 92, 246, 0.15) !important;
  transform: translateY(-2px);
}

/* Modal input fields */
div[class*="bg-slate-900 rounded-xl"] input[class*="bg-slate-800"] {
  background: rgba(139, 92, 246, 0.05) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  box-shadow: inset 0 0 10px rgba(139, 92, 246, 0.05) !important;
  transition: all 0.3s ease;
}

div[class*="bg-slate-900 rounded-xl"] input[class*="bg-slate-800"]:focus {
  background: rgba(139, 92, 246, 0.1) !important;
  border: 1px solid rgba(139, 92, 246, 0.4) !important;
  box-shadow: 
    0 0 0 4px rgba(139, 92, 246, 0.1),
    inset 0 0 15px rgba(139, 92, 246, 0.1) !important;
  outline: none !important;
}

/* Modal buttons */
div[class*="bg-slate-900 rounded-xl"] button[class*="bg-emerald"] {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(6, 182, 212, 0.1)) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(16, 185, 129, 0.3) !important;
  box-shadow: 
    0 4px 16px rgba(16, 185, 129, 0.2),
    0 0 10px rgba(16, 185, 129, 0.1) !important;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

div[class*="bg-slate-900 rounded-xl"] button[class*="bg-emerald"]:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(6, 182, 212, 0.2)) !important;
  border: 1px solid rgba(16, 185, 129, 0.4) !important;
  box-shadow: 
    0 6px 20px rgba(16, 185, 129, 0.3),
    0 0 15px rgba(16, 185, 129, 0.2) !important;
  transform: translateY(-2px);
}

div[class*="bg-slate-900 rounded-xl"] button[class*="bg-emerald"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

div[class*="bg-slate-900 rounded-xl"] button[class*="bg-emerald"]:hover::before {
  left: 100%;
}

/* Modal download buttons */
div[class*="bg-slate-900 rounded-xl"] button[class*="bg-orange"] {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(251, 146, 60, 0.1)) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(245, 158, 11, 0.3) !important;
  box-shadow: 
    0 4px 16px rgba(245, 158, 11, 0.2),
    0 0 10px rgba(245, 158, 11, 0.1) !important;
  transition: all 0.3s ease;
}

div[class*="bg-slate-900 rounded-xl"] button[class*="bg-orange"]:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.3), rgba(251, 146, 60, 0.2)) !important;
  border: 1px solid rgba(245, 158, 11, 0.4) !important;
  box-shadow: 
    0 6px 20px rgba(245, 158, 11, 0.3),
    0 0 15px rgba(245, 158, 11, 0.2) !important;
  transform: translateY(-2px);
}

/* Modal close button */
div[class*="bg-slate-900 rounded-xl"] button[class*="text-slate-400"] {
  background: rgba(139, 92, 246, 0.05) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease;
}

div[class*="bg-slate-900 rounded-xl"] button[class*="text-slate-400"]:hover {
  background: rgba(139, 92, 246, 0.1) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.2) !important;
  transform: scale(1.1) rotate(90deg);
}

/* Modal status indicators */
div[class*="bg-slate-900 rounded-xl"] div[class*="bg-emerald-500/20"] {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(6, 182, 212, 0.1)) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(16, 185, 129, 0.3) !important;
  box-shadow: 
    0 4px 16px rgba(16, 185, 129, 0.2),
    0 0 10px rgba(16, 185, 129, 0.1) !important;
}

div[class*="bg-slate-900 rounded-xl"] div[class*="bg-red-500/20"] {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(244, 63, 94, 0.1)) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(239, 68, 68, 0.3) !important;
  box-shadow: 
    0 4px 16px rgba(239, 68, 68, 0.2),
    0 0 10px rgba(239, 68, 68, 0.1) !important;
}

div[class*="bg-slate-900 rounded-xl"] div[class*="bg-yellow-500/20"] {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(251, 191, 36, 0.1)) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(245, 158, 11, 0.3) !important;
  box-shadow: 
    0 4px 16px rgba(245, 158, 11, 0.2),
    0 0 10px rgba(245, 158, 11, 0.1) !important;
}

div[class*="bg-slate-900 rounded-xl"] div[class*="bg-blue-500/20"] {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(6, 182, 212, 0.1)) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  box-shadow: 
    0 4px 16px rgba(59, 130, 246, 0.2),
    0 0 10px rgba(59, 130, 246, 0.1) !important;
}

/* Modal file download sections */
div[class*="bg-slate-900 rounded-xl"] div[class*="bg-orange-500/20"] {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(251, 146, 60, 0.08)) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(245, 158, 11, 0.25) !important;
  box-shadow: 
    0 4px 16px rgba(245, 158, 11, 0.15),
    0 0 10px rgba(245, 158, 11, 0.08) !important;
}

/* Modal gradient sections */
div[class*="bg-slate-900 rounded-xl"] div[class*="bg-gradient-to-r"] {
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 10px rgba(139, 92, 246, 0.1) !important;
}

/* Modal header enhancements */
div[class*="bg-slate-900 rounded-xl"] h2 {
  background: linear-gradient(-45deg, #8B5CF6, #EC4899, #06B6D4);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

div[class*="bg-slate-900 rounded-xl"] h3 {
  color: rgba(139, 92, 246, 0.9) !important;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

/* Modal animation entrance */
div[class*="bg-slate-900 rounded-xl"] {
  animation: modal-scale-in 0.3s ease-out;
}

@keyframes modal-scale-in {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Modal content spacing */
div[class*="bg-slate-900 rounded-xl"] .space-y-6 {
  padding-bottom: 2rem !important;
}

/* Enhanced button styles */
.challenge-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.challenge-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.challenge-button:hover::before {
  left: 100%;
}

.challenge-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Search input enhancements */
.search-input {
  position: relative;
  transition: all 0.3s ease;
}

.search-input:focus-within {
  transform: scale(1.02);
}

.search-input input {
  transition: all 0.3s ease;
}

.search-input input:focus {
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
}

/* Stagger animation delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }
.stagger-7 { animation-delay: 0.7s; }
.stagger-8 { animation-delay: 0.8s; }

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, rgba(139, 92, 246, 0.1) 25%, rgba(139, 92, 246, 0.2) 50%, rgba(139, 92, 246, 0.1) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Progress bars */
.progress-bar {
  position: relative;
  overflow: hidden;
  border-radius: 9999px;
  background: rgba(139, 92, 246, 0.1);
}

.progress-fill {
  height: 100%;
  border-radius: 9999px;
  transition: width 1s ease-in-out;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Hover lift effect */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Force challenge modal to appear above everything */
.fixed.inset-0 {
  z-index: 2147483647 !important;
}

/* Override any header/nav z-index */
header,
nav,
.header,
.navbar {
  z-index: 50 !important;
}

/* Mobile scrolling improvements */
@media (max-width: 768px) {
  div[class*="bg-slate-900 rounded-xl"] {
    max-height: 95vh !important;
    margin: 0.5rem !important;
  }
  
  div[class*="bg-slate-900 rounded-xl"] > div {
    padding: 1rem !important;
  }
  
  .challenge-card-wrapper {
    margin-bottom: 1rem;
  }
  
  .glass-card-dark {
    padding: 1rem;
  }
  
  .search-input {
    margin-bottom: 1rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .glass-card,
  .glass-card-dark {
    backdrop-filter: blur(25px);
  }
}

/* Text clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}