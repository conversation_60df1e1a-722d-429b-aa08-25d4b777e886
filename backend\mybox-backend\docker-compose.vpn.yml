services:
  openvpn:
    image: kylemanna/openvpn
    container_name: openvpn
    ports:
      - "1194:1194/udp"
    restart: always
    cap_add:
      - NET_ADMIN
    volumes:
      - ./openvpn-data:/etc/openvpn
    networks:
      - vpn-network

  vpn-api:
    image: jadolg/vpn2go
    container_name: vpn-api
    ports:
      - "8080:5000"
    restart: always
    environment:
      - SERVICE_USER=admin
      - SERVICE_PASSWORD=admin123
    volumes:
      - ./openvpn-data:/etc/openvpn
    depends_on:
      - openvpn
    networks:
      - vpn-network

networks:
  vpn-network:
    driver: bridge
    ipam:
      config:
        - subnet: ********/24