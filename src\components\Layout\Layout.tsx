import React from 'react';
import { Header } from './Header';

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 animate-gradient relative overflow-hidden">
      {/* Animated background elements */}
      <div className="particles-bg">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 8}s`,
              animationDuration: `${6 + Math.random() * 6}s`
            }}
          />
        ))}
      </div>

      {/* Floating geometric shapes */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full morph-shape animate-float" />
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-pink-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '4s' }} />
        <div className="absolute bottom-40 right-1/3 w-28 h-28 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '6s' }} />
      </div>

      {/* Main content */}
      <Header />
      <main className="pt-24 pb-8 relative z-10">
        <div className="container mx-auto px-4">
          {children}
        </div>
      </main>

      {/* Gradient overlay for depth */}
      <div className="fixed inset-0 bg-gradient-to-t from-purple-900/20 via-transparent to-purple-900/20 pointer-events-none" />
    </div>
  );
}