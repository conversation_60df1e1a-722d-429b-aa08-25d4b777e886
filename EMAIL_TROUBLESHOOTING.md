# Email Verification Troubleshooting Guide

## Common SMTP Error: "Greeting never received"

This error typically indicates a connection timeout or configuration issue. Here are the most common solutions:

### 1. Check SMTP Settings

#### Gmail SMTP Settings:
```
SMTP Host: smtp.gmail.com
SMTP Port: 587 (TLS) or 465 (SSL)
SMTP Secure: false for 587, true for 465
SMTP Username: <EMAIL>
SMTP Password: App Password (not regular password)
From Email: <EMAIL>
```

**Important for Gmail**: You need to use an "App Password", not your regular Gmail password:
1. Enable 2-Factor Authentication on your Google account
2. Go to Google Account Settings > Security > App passwords
3. Generate an app password for "Mail"
4. Use this app password in SMTP settings

#### Outlook/Hotmail SMTP Settings:
```
SMTP Host: smtp-mail.outlook.com
SMTP Port: 587
SMTP Secure: false
SMTP Username: <EMAIL>
SMTP Password: your-password
From Email: <EMAIL>
```

#### Custom SMTP Server:
```
SMTP Host: mail.yourdomain.com
SMTP Port: 587 (TLS) or 465 (SSL) or 25 (unsecured)
SMTP Secure: false for 587/25, true for 465
SMTP Username: <EMAIL>
SMTP Password: your-password
From Email: <EMAIL>
```

### 2. Network and Firewall Issues

#### Check if port is accessible:
```bash
# Test SMTP connection (Windows)
telnet smtp.gmail.com 587

# Test SMTP connection (Linux/Mac)
nc -zv smtp.gmail.com 587
```

#### Common port blocking:
- Port 25: Often blocked by ISPs
- Port 587: Usually allowed (TLS)
- Port 465: Usually allowed (SSL)

### 3. Configuration Testing

The system now includes enhanced error handling and logging. Check the backend console for detailed error messages:

```
Creating SMTP transporter with settings: { host: 'smtp.gmail.com', port: 587, secure: false, hasAuth: true }
Verifying SMTP connection...
Email configuration test successful
```

### 4. Common Solutions

#### Solution 1: Try different ports
- If 587 doesn't work, try 465 with `smtpSecure: true`
- If both fail, try 25 (if not blocked)

#### Solution 2: Disable security temporarily
For testing purposes, you can try:
```javascript
tls: {
  rejectUnauthorized: false
}
```

#### Solution 3: Check authentication
- Ensure username/password are correct
- For Gmail, use App Password
- For corporate email, check with IT department

#### Solution 4: Test with external tools
Use tools like:
- Telnet to test basic connectivity
- Online SMTP testers
- Email client configuration

### 5. Debugging Steps

1. **Enable detailed logging**: Check backend console for SMTP connection logs
2. **Test email settings**: Use the admin panel's "Test Email Settings" button
3. **Check network**: Ensure server can reach SMTP host
4. **Verify credentials**: Double-check username/password
5. **Try different provider**: Test with Gmail or Outlook first

### 6. Production Recommendations

#### For Production Use:
1. **Use dedicated email service**: SendGrid, Mailgun, AWS SES
2. **Configure SPF/DKIM**: Prevent emails from going to spam
3. **Monitor email delivery**: Track bounces and failures
4. **Use environment variables**: Don't hardcode credentials

#### Example with SendGrid:
```
SMTP Host: smtp.sendgrid.net
SMTP Port: 587
SMTP Secure: false
SMTP Username: apikey
SMTP Password: your-sendgrid-api-key
From Email: <EMAIL>
```

### 7. Error Code Reference

- `ETIMEDOUT`: Connection timeout - check host/port
- `EAUTH`: Authentication failed - check credentials
- `ECONNREFUSED`: Connection refused - check host/port/firewall
- `ESOCKET`: Socket error - network connectivity issue

### 8. Testing Commands

Test your SMTP configuration:

```bash
# Test Gmail SMTP
telnet smtp.gmail.com 587

# Test Outlook SMTP  
telnet smtp-mail.outlook.com 587

# Check if port is open
nmap -p 587 smtp.gmail.com
```

If you continue to have issues, please check:
1. Your email provider's SMTP documentation
2. Server firewall settings
3. Network connectivity to SMTP server
4. Email provider's security settings (2FA, app passwords, etc.)
