import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type TeamInvitationDocument = TeamInvitation & Document;

@Schema({ timestamps: true })
export class TeamInvitation {
  @Prop({ type: Types.ObjectId, ref: 'Team', required: true })
  teamId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  invitedUserId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  invitedByUserId: Types.ObjectId;

  @Prop({ required: true })
  invitedUserEmail: string;

  @Prop({ 
    type: String, 
    enum: ['pending', 'accepted', 'declined', 'cancelled'], 
    default: 'pending' 
  })
  status: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date })
  respondedAt?: Date;

  @Prop({ type: Date, default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) }) // 7 days from now
  expiresAt: Date;
}

export const TeamInvitationSchema = SchemaFactory.createForClass(TeamInvitation);
