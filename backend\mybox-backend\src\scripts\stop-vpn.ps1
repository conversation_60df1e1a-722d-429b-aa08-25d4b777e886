# PowerShell script to stop OpenVPN infrastructure
Write-Host "Stopping OpenVPN infrastructure..." -ForegroundColor Yellow

# Navigate to backend directory
$backendPath = Split-Path -Parent $PSScriptRoot
Set-Location $backendPath

# Stop VPN services
docker compose -f docker-compose.vpn.yml down

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ VPN infrastructure stopped successfully!" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to stop VPN infrastructure" -ForegroundColor Red
    exit 1
}