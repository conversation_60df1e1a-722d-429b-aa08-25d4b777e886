/**
 * Converts a relative image URL to a full backend URL
 * @param imageUrl - The image URL (can be relative or absolute)
 * @returns Full URL pointing to the backend server
 */
export function getImageUrl(imageUrl: string): string {
  if (!imageUrl) return '';

  // If it's already a full URL (starts with http), return as is
  if (imageUrl.startsWith('http')) {
    return imageUrl;
  }

  // If it's a relative URL starting with /uploads, prepend backend URL
  if (imageUrl.startsWith('/uploads')) {
    // Use the same base URL as the API but without /api suffix
    const backendUrl = 'http://localhost:3001';
    return `${backendUrl}${imageUrl}`;
  }

  // For any other relative URLs, return as is
  return imageUrl;
}

/**
 * Checks if an image URL is accessible
 * @param imageUrl - The image URL to check
 * @returns Promise that resolves to true if image is accessible
 */
export async function isImageAccessible(imageUrl: string): Promise<boolean> {
  try {
    const response = await fetch(getImageUrl(imageUrl), { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
}