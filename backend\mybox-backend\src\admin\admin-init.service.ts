import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcryptjs';
import { ConfigService } from '@nestjs/config';
import { CategoriesService } from '../categories/categories.service';

@Injectable()
export class AdminInitService implements OnModuleInit {
  private readonly logger = new Logger(AdminInitService.name);

  constructor(
    @InjectModel('User') private userModel: Model<any>,
    private configService: ConfigService,
    private categoriesService: CategoriesService,
  ) {}

  async onModuleInit() {
    await this.initializeAdmin();
    await this.initializeCategories();
  }
  private async initializeAdmin(): Promise<void> {
    try {
      this.logger.log('Checking if admin user exists...');
      
      // Check if admin users exist and verify their emails
      const adminUsers = await this.userModel.find({ role: 'admin' }).exec();

      if (adminUsers.length > 0) {
        this.logger.log(`Found ${adminUsers.length} admin user(s)`);

        // Verify email for any admin users that aren't verified
        for (const admin of adminUsers) {
          if (!admin.isEmailVerified) {
            this.logger.log(`Admin email not verified for: ${admin.email}. Verifying now...`);
            await this.userModel.updateOne(
              { _id: admin._id },
              {
                $set: {
                  isEmailVerified: true,
                  emailVerificationCode: undefined,
                  emailVerificationExpires: undefined
                }
              }
            );
            this.logger.log(`✅ Admin email verified successfully for: ${admin.email}`);
          } else {
            this.logger.log(`Admin email already verified for: ${admin.email}`);
          }
        }

        return;
      }

      // Admin user doesn't exist, create one
      this.logger.log('Admin user does not exist. Creating admin user...');
      
      // Get admin credentials from environment variables or use defaults
      const adminUsername = this.configService.get<string>('ADMIN_USERNAME') || 'admin';
      const adminEmail = this.configService.get<string>('ADMIN_EMAIL') || '<EMAIL>';
      const adminPassword = this.configService.get<string>('ADMIN_PASSWORD') || 'Admin@123';
      
      // Hash the password
      const salt = await bcrypt.genSalt();
      const hashedPassword = await bcrypt.hash(adminPassword, salt);      // Create the admin user
      const admin = new this.userModel({
        username: adminUsername,
        email: adminEmail,
        passwordHash: hashedPassword,
        role: 'admin',
        score: 0,
        rank: 0,
        isActive: true,
        isEmailVerified: true, // Admin email is verified by default
        lastActive: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      });

      await admin.save();

      this.logger.log(`Admin user created successfully with username: ${adminUsername} and email: ${adminEmail}`);
      this.logger.log('Admin email is automatically verified and ready to use.');
      this.logger.log('You can log in with these credentials and change the password for security purposes.');
    } catch (error) {
      this.logger.error('Error initializing admin user:', error.message);
      // We don't want to crash the application if admin creation fails
    }
  }

  private async initializeCategories(): Promise<void> {
    try {
      this.logger.log('Initializing default categories...');
      await this.categoriesService.initializeDefaultCategories();
      this.logger.log('Default categories initialized successfully');
    } catch (error) {
      this.logger.error('Error initializing categories:', error.message);
      // We don't want to crash the application if category initialization fails
    }
  }
}
