import React, { useState, useEffect } from 'react';
import {
  Users,
  Target,
  Monitor,
  Activity,
  Plus,
  Edit,
  Bell,
  Layout,
  Settings,
  TrendingUp,
  Clock,
  Shield,
  RefreshCw,
  AlertCircle,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { AdminOverviewService, SystemStats, ActivityLogsResponse, MachineStats, DashboardStats } from '../../../services/adminOverview';

export function AdminOverview() {
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [machineStats, setMachineStats] = useState<MachineStats | null>(null);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [activityPagination, setActivityPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    hasNext: false,
    hasPrev: false
  });
  const [loading, setLoading] = useState(true);
  const [activityLoading, setActivityLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const loadData = async (resetActivity = true) => {
    try {
      setLoading(true);
      setError(null);

      const [statsData, machineData, dashData] = await Promise.all([
        AdminOverviewService.getSystemStats(),
        AdminOverviewService.getMachineStats(),
        AdminOverviewService.getDashboardStats()
      ]);

      setSystemStats(statsData);
      setMachineStats(machineData);
      setDashboardStats(dashData);

      if (resetActivity) {
        await loadActivityData(1);
      }

      setLastRefresh(new Date());
    } catch (err) {
      console.error('Failed to load admin overview data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadActivityData = async (page: number) => {
    try {
      setActivityLoading(true);

      const activityData = await AdminOverviewService.getActivityLogs({
        page,
        limit: 10,
        sortBy: 'submittedAt',
        sortDirection: 'desc'
      });

      setRecentActivity(AdminOverviewService.formatActivityLogs(activityData.activities));
      setActivityPagination({
        currentPage: activityData.page,
        totalPages: activityData.pages,
        totalItems: activityData.total,
        hasNext: activityData.page < activityData.pages,
        hasPrev: activityData.page > 1
      });
    } catch (err) {
      console.error('Failed to load activity data:', err);
      // Don't show error for activity loading failure, just keep existing data
    } finally {
      setActivityLoading(false);
    }
  };

  useEffect(() => {
    loadData();

    // Auto-refresh every 30 seconds (but don't reset activity pagination)
    const interval = setInterval(() => loadData(false), 30000);
    return () => clearInterval(interval);
  }, []);

  // Keyboard navigation for activity pagination
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Only handle if no input is focused
      if (document.activeElement?.tagName === 'INPUT' || document.activeElement?.tagName === 'TEXTAREA') {
        return;
      }

      if (e.key === 'ArrowLeft' && activityPagination.hasPrev && !activityLoading) {
        e.preventDefault();
        handlePreviousPage();
      } else if (e.key === 'ArrowRight' && activityPagination.hasNext && !activityLoading) {
        e.preventDefault();
        handleNextPage();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [activityPagination.hasPrev, activityPagination.hasNext, activityLoading]);

  const handleRefresh = () => {
    loadData();
  };

  const handlePreviousPage = () => {
    if (activityPagination.hasPrev) {
      loadActivityData(activityPagination.currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (activityPagination.hasNext) {
      loadActivityData(activityPagination.currentPage + 1);
    }
  };

  const handleRefreshActivity = () => {
    loadActivityData(activityPagination.currentPage);
  };

  if (loading && !systemStats) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-3 text-slate-400">
          <RefreshCw className="w-6 h-6 animate-spin" />
          <span>Loading dashboard data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Failed to Load Data</h3>
          <p className="text-slate-400 mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_registered':
        return <Users className="w-4 h-4 text-blue-400" />;
      case 'challenge_solved':
        return <Target className="w-4 h-4 text-emerald-400" />;
      case 'vm_started':
        return <Monitor className="w-4 h-4 text-purple-400" />;
      case 'flag_submitted':
        return <Shield className="w-4 h-4 text-orange-400" />;
      default:
        return <Activity className="w-4 h-4 text-slate-400" />;
    }
  };

  return (
    <div className="space-y-8 p-6">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Admin Overview</h1>
          <p className="text-slate-400">
            Last updated: {AdminOverviewService.formatTimeAgo(lastRefresh.toISOString())}
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="flex items-center space-x-2 px-4 py-2 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 text-white rounded-lg transition-colors"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="group bg-gradient-to-br from-slate-800/80 to-slate-900/80 backdrop-blur-sm rounded-2xl border border-slate-700/50 p-6 hover:border-blue-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10">
          <div className="flex items-center space-x-4 mb-6">
            <div className="p-3 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-xl group-hover:from-blue-500/30 group-hover:to-blue-600/30 transition-all duration-300">
              <Users className="w-7 h-7 text-blue-400" />
            </div>
            <div>
              <h3 className="font-semibold text-white text-lg">Users</h3>
              <p className="text-sm text-slate-400">Platform members</p>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Total</span>
              <span className="text-3xl font-bold text-white">
                {systemStats?.users?.total?.toLocaleString() || dashboardStats?.totalUsers?.toLocaleString() || '0'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Active</span>
              <span className="text-xl font-bold text-blue-400">
                {systemStats?.users?.active || dashboardStats?.onlineUsers || '0'}
              </span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-emerald-400 bg-emerald-500/10 px-3 py-1 rounded-full">
              <TrendingUp className="w-4 h-4" />
              <span>+12% this week</span>
            </div>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-emerald-500/20 rounded-lg">
              <Target className="w-6 h-6 text-emerald-400" />
            </div>
            <div>
              <h3 className="font-semibold text-white">Challenges</h3>
              <p className="text-xs text-slate-400">CTF challenges</p>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Total</span>
              <span className="text-2xl font-bold text-white">
                {systemStats?.challenges?.total || dashboardStats?.totalChallenges || '0'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Active</span>
              <span className="text-lg font-bold text-emerald-400">
                {systemStats?.challenges?.active || dashboardStats?.totalChallenges || '0'}
              </span>
            </div>
            <div className="flex items-center space-x-1 text-xs text-emerald-400">
              <TrendingUp className="w-3 h-3" />
              <span>+5 new this week</span>
            </div>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-purple-500/20 rounded-lg">
              <Monitor className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h3 className="font-semibold text-white">Machines</h3>
              <p className="text-xs text-slate-400">Virtual machines</p>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Total</span>
              <span className="text-2xl font-bold text-white">
                {machineStats?.totalMachines || dashboardStats?.activeVMs || '0'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Running</span>
              <span className="text-lg font-bold text-purple-400">
                {machineStats?.runningMachines || machineStats?.activeInstances || '0'}
              </span>
            </div>
            <div className="flex items-center space-x-1 text-xs text-orange-400">
              <Clock className="w-3 h-3" />
              <span>{Math.round(machineStats?.utilizationRate || 0)}% utilization</span>
            </div>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-orange-500/20 rounded-lg">
              <Activity className="w-6 h-6 text-orange-400" />
            </div>
            <div>
              <h3 className="font-semibold text-white">Teams</h3>
              <p className="text-xs text-slate-400">Active teams</p>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Total</span>
              <span className="text-2xl font-bold text-white">
                {systemStats?.teams?.total || dashboardStats?.platformStats?.totalTeams || '0'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Active</span>
              <span className="text-lg font-bold text-orange-400">
                {systemStats?.teams?.active || dashboardStats?.platformStats?.activeTeams || '0'}
              </span>
            </div>
            <div className="flex items-center space-x-1 text-xs text-emerald-400">
              <TrendingUp className="w-3 h-3" />
              <span>+3 new teams</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <div className="lg:col-span-2 bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-semibold text-white">Recent Activity</h3>
              {activityPagination.totalItems > 0 && (
                <p className="text-sm text-slate-400 mt-1">
                  {activityPagination.totalItems} total activities
                </p>
              )}
            </div>
            <div className="flex items-center space-x-3">
              {/* Activity Refresh Button */}
              <button
                onClick={handleRefreshActivity}
                disabled={activityLoading}
                className="p-2 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 text-white rounded-lg transition-colors"
                title="Refresh activity"
              >
                <RefreshCw className={`w-4 h-4 ${activityLoading ? 'animate-spin' : ''}`} />
              </button>

              {/* Pagination Controls */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={handlePreviousPage}
                  disabled={!activityPagination.hasPrev || activityLoading}
                  className="p-2 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:text-slate-600 text-white rounded-lg transition-colors"
                  title="Previous page"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>

                <span className="text-sm text-slate-400 px-2">
                  {activityPagination.currentPage} / {activityPagination.totalPages}
                </span>

                <button
                  onClick={handleNextPage}
                  disabled={!activityPagination.hasNext || activityLoading}
                  className="p-2 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:text-slate-600 text-white rounded-lg transition-colors"
                  title="Next page"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          <div className="space-y-4 relative">
            {activityLoading && (
              <div className="absolute inset-0 bg-slate-800/50 backdrop-blur-sm rounded-lg flex items-center justify-center z-10">
                <div className="flex items-center space-x-2 text-slate-400">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Loading activities...</span>
                </div>
              </div>
            )}

            {recentActivity.length > 0 ? (
              recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center p-4 bg-slate-900/50 rounded-lg border border-slate-700">
                  <div className="rounded-full bg-slate-800 p-3 mr-4">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1">
                    <p className="text-white text-sm">
                      <span className="font-medium text-emerald-400">{activity.user}</span> {activity.details}
                    </p>
                    <p className="text-xs text-slate-500 mt-1">{AdminOverviewService.formatTimeAgo(activity.timestamp)}</p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Activity className="w-12 h-12 text-slate-600 mx-auto mb-4" />
                <p className="text-slate-400">No recent activity</p>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
          <h3 className="text-xl font-semibold text-white mb-6">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full flex items-center justify-between px-4 py-3 bg-slate-900/70 rounded-lg border border-slate-700 text-white hover:bg-slate-900 hover:border-emerald-500/50 transition-all duration-200 group">
              <span className="flex items-center">
                <Target className="w-4 h-4 mr-3 text-emerald-400 group-hover:text-emerald-300" />
                Add Challenge
              </span>
              <Plus className="w-4 h-4 text-slate-400 group-hover:text-emerald-400" />
            </button>
            
            <button className="w-full flex items-center justify-between px-4 py-3 bg-slate-900/70 rounded-lg border border-slate-700 text-white hover:bg-slate-900 hover:border-blue-500/50 transition-all duration-200 group">
              <span className="flex items-center">
                <Users className="w-4 h-4 mr-3 text-blue-400 group-hover:text-blue-300" />
                Add User
              </span>
              <Plus className="w-4 h-4 text-slate-400 group-hover:text-blue-400" />
            </button>
            
            <button className="w-full flex items-center justify-between px-4 py-3 bg-slate-900/70 rounded-lg border border-slate-700 text-white hover:bg-slate-900 hover:border-purple-500/50 transition-all duration-200 group">
              <span className="flex items-center">
                <Monitor className="w-4 h-4 mr-3 text-purple-400 group-hover:text-purple-300" />
                Add Machine
              </span>
              <Plus className="w-4 h-4 text-slate-400 group-hover:text-purple-400" />
            </button>
            
            <button className="w-full flex items-center justify-between px-4 py-3 bg-slate-900/70 rounded-lg border border-slate-700 text-white hover:bg-slate-900 hover:border-orange-500/50 transition-all duration-200 group">
              <span className="flex items-center">
                <Bell className="w-4 h-4 mr-3 text-orange-400 group-hover:text-orange-300" />
                Send Notification
              </span>
              <Plus className="w-4 h-4 text-slate-400 group-hover:text-orange-400" />
            </button>
            
            <button className="w-full flex items-center justify-between px-4 py-3 bg-slate-900/70 rounded-lg border border-slate-700 text-white hover:bg-slate-900 hover:border-pink-500/50 transition-all duration-200 group">
              <span className="flex items-center">
                <Layout className="w-4 h-4 mr-3 text-pink-400 group-hover:text-pink-300" />
                Configure Dashboard
              </span>
              <Edit className="w-4 h-4 text-slate-400 group-hover:text-pink-400" />
            </button>
            
            <button className="w-full flex items-center justify-between px-4 py-3 bg-slate-900/70 rounded-lg border border-slate-700 text-white hover:bg-slate-900 hover:border-slate-500/50 transition-all duration-200 group">
              <span className="flex items-center">
                <Settings className="w-4 h-4 mr-3 text-slate-400 group-hover:text-slate-300" />
                System Settings
              </span>
              <Edit className="w-4 h-4 text-slate-400 group-hover:text-slate-300" />
            </button>
          </div>
        </div>
      </div>

      {/* System Health */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
        <h3 className="text-xl font-semibold text-white mb-6">System Health</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-emerald-400 mb-2">99.9%</div>
            <div className="text-sm text-slate-400">Uptime</div>
            <div className="text-xs text-slate-500 mt-1">Last 30 days</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400 mb-2">2.3s</div>
            <div className="text-sm text-slate-400">Avg Response</div>
            <div className="text-xs text-slate-500 mt-1">API endpoints</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">45GB</div>
            <div className="text-sm text-slate-400">Storage Used</div>
            <div className="text-xs text-slate-500 mt-1">of 100GB available</div>
          </div>
        </div>
      </div>
    </div>
  );
}