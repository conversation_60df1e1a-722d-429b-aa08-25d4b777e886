import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class ChallengeSubmission extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Challenge', required: true })
  challengeId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Team' })
  teamId?: Types.ObjectId;

  @Prop({ required: true })
  flagSubmitted: string;

  @Prop({ required: true })
  isCorrect: boolean;

  @Prop({ required: true, default: 0 })
  pointsAwarded: number;

  @Prop({ default: 0 })
  teamPointsAwarded: number;

  @Prop({ default: false })
  isFirstTeamSolve: boolean;
  @Prop({ default: false })
  isFirstBlood: boolean;
  
  @Prop({ default: -1 })
  flagIndex: number;

  @Prop({ required: true, default: Date.now })
  submittedAt: Date;
}

export const ChallengeSubmissionSchema = SchemaFactory.createForClass(ChallengeSubmission);

// Create indexes
ChallengeSubmissionSchema.index({ userId: 1, challengeId: 1 });
ChallengeSubmissionSchema.index({ teamId: 1, challengeId: 1 });
ChallengeSubmissionSchema.index({ challengeId: 1, isCorrect: 1 });
ChallengeSubmissionSchema.index({ isFirstBlood: 1 });
ChallengeSubmissionSchema.index({ submittedAt: -1 });
