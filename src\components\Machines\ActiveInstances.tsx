import { useState } from 'react';
import { MachineInstance, MachineTemplate } from '../../services/machines';
import machineService from '../../services/machines';
import { 
  Monitor, 
  Clock, 
  Wifi, 
  Square, 
  RotateCcw, 
  Terminal, 
  Eye, 
  AlertTriangle,
  RefreshCw,
  Users
} from 'lucide-react';

interface ActiveInstancesProps {
  instances: MachineInstance[];
  templates: MachineTemplate[];
  onRefresh: () => void;
}

export function ActiveInstances({ instances, templates, onRefresh }: ActiveInstancesProps) {
  const [loadingInstances, setLoadingInstances] = useState<Set<string>>(new Set());

  const formatTimeRemaining = (minutes: number): string => {
    if (minutes <= 0) return 'Expired';
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getTimeColor = (minutes: number): string => {
    if (minutes <= 0) return 'text-red-400';
    if (minutes <= 30) return 'text-orange-400';
    if (minutes <= 60) return 'text-yellow-400';
    return 'text-emerald-400';
  };

  const getTemplate = (instance: MachineInstance): MachineTemplate | undefined => {
    return templates.find(t => t.id === instance.template?.id);
  };

  const handleAction = async (instanceId: string, action: 'stop' | 'restart') => {
    setLoadingInstances(prev => new Set(prev.add(instanceId)));
    
    try {
      if (action === 'stop') {
        await machineService.terminateInstance(instanceId);
      } else {
        await machineService.restartInstance(instanceId);
      }
      onRefresh();
    } catch (error) {
      console.error(`Error ${action}ing instance:`, error);
    } finally {
      setLoadingInstances(prev => {
        const newSet = new Set(prev);
        newSet.delete(instanceId);
        return newSet;
      });
    }
  };

  if (instances.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Monitor className="w-5 h-5 text-emerald-400" />
          <h2 className="text-xl font-semibold text-white">Active Instances</h2>
          <span className="px-2 py-1 bg-emerald-500/20 text-emerald-400 rounded-full text-xs font-medium">
            {instances.length}
          </span>
        </div>
        
        <button
          onClick={onRefresh}
          className="flex items-center space-x-2 px-3 py-2 bg-blue-600/20 text-blue-400 border border-blue-600/30 rounded-lg hover:bg-blue-600/30 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          <span className="text-sm">Refresh</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
        {instances.map(instance => {
          const template = getTemplate(instance);
          const isLoading = loadingInstances.has(instance.id);
          const timeColor = getTimeColor(instance.timeRemaining);
          
          return (
            <div
              key={instance.id}
              className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-4 hover:border-slate-700 transition-all duration-300"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="text-lg font-semibold text-white">
                    {template?.name || 'Unknown Machine'}
                  </h3>
                  <p className="text-sm text-slate-400 capitalize">
                    {template?.category || 'Unknown'}
                  </p>
                </div>
                
                <div className="flex items-center space-x-2">
                  {instance.isTeamShared && (
                    <div className="p-1 bg-blue-500/20 rounded">
                      <Users className="w-3 h-3 text-blue-400" />
                    </div>
                  )}
                  <div className="px-2 py-1 bg-emerald-500/20 text-emerald-400 rounded text-xs font-medium">
                    {instance.status}
                  </div>
                </div>
              </div>

              {/* Connection Info */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-400">IP Address</span>
                  <div className="flex items-center space-x-2">
                    <Wifi className="w-3 h-3 text-emerald-400" />
                    <span className="text-sm font-mono text-emerald-400">
                      {instance.instanceIP}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-400">Time Remaining</span>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-3 h-3" />
                    <span className={`text-sm font-medium ${timeColor}`}>
                      {formatTimeRemaining(instance.timeRemaining)}
                    </span>
                  </div>
                </div>

                {instance.exposedPorts && instance.exposedPorts.length > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-400">Ports</span>
                    <div className="flex space-x-1">
                      {instance.exposedPorts.slice(0, 3).map((port, idx) => (
                        <span 
                          key={idx} 
                          className="px-2 py-1 bg-slate-800 text-slate-300 rounded text-xs font-mono"
                        >
                          {port.external}
                        </span>
                      ))}
                      {instance.exposedPorts.length > 3 && (
                        <span className="px-2 py-1 bg-slate-800 text-slate-300 rounded text-xs">
                          +{instance.exposedPorts.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-slate-800 rounded-full h-2 mb-4">
                <div 
                  className={`h-2 rounded-full transition-all duration-1000 ${
                    instance.timeRemaining <= 30 ? 'bg-gradient-to-r from-red-500 to-orange-500' :
                    instance.timeRemaining <= 60 ? 'bg-gradient-to-r from-orange-500 to-yellow-500' :
                    'bg-gradient-to-r from-emerald-500 to-green-500'
                  }`}
                  style={{ 
                    width: `${Math.max(0, Math.min(100, (instance.timeRemaining / 120) * 100))}%` 
                  }}
                />
              </div>

              {/* Warning for low time */}
              {instance.timeRemaining <= 30 && instance.timeRemaining > 0 && (
                <div className="mb-4 p-2 bg-orange-500/10 border border-orange-500/20 rounded-lg flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4 text-orange-400" />
                  <span className="text-orange-400 text-sm">
                    Instance will expire soon
                  </span>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={() => handleAction(instance.id, 'restart')}
                  disabled={isLoading}
                  className="flex-1 flex items-center justify-center space-x-2 py-2 px-3 bg-orange-600/20 text-orange-400 border border-orange-600/30 rounded-lg hover:bg-orange-600/30 disabled:opacity-50 transition-colors"
                >
                  {isLoading ? (
                    <div className="w-4 h-4 border-2 border-orange-400/20 border-t-orange-400 rounded-full animate-spin" />
                  ) : (
                    <RotateCcw className="w-4 h-4" />
                  )}
                  <span className="text-sm">Restart</span>
                </button>

                <button
                  onClick={() => handleAction(instance.id, 'stop')}
                  disabled={isLoading}
                  className="flex-1 flex items-center justify-center space-x-2 py-2 px-3 bg-red-600/20 text-red-400 border border-red-600/30 rounded-lg hover:bg-red-600/30 disabled:opacity-50 transition-colors"
                >
                  {isLoading ? (
                    <div className="w-4 h-4 border-2 border-red-400/20 border-t-red-400 rounded-full animate-spin" />
                  ) : (
                    <Square className="w-4 h-4" />
                  )}
                  <span className="text-sm">Stop</span>
                </button>
              </div>

              {/* Connection Options */}
              <div className="flex space-x-2 mt-2">
                <button className="flex-1 flex items-center justify-center space-x-1 py-1.5 px-2 bg-blue-600/10 text-blue-400 border border-blue-600/20 rounded text-xs hover:bg-blue-600/20 transition-colors">
                  <Terminal className="w-3 h-3" />
                  <span>SSH</span>
                </button>
                <button className="flex-1 flex items-center justify-center space-x-1 py-1.5 px-2 bg-purple-600/10 text-purple-400 border border-purple-600/20 rounded text-xs hover:bg-purple-600/20 transition-colors">
                  <Eye className="w-3 h-3" />
                  <span>VNC</span>
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
