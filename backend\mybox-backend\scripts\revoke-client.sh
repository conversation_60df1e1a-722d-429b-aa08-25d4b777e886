#!/bin/bash

if [ -z "$1" ]; then
  echo "Usage: $0 <username>"
  exit 1
fi

USERNAME=$1
EASY_RSA="/etc/openvpn/easy-rsa"
CONFIG_PATH="/etc/openvpn"

# Revoke client certificate
echo "Revoking client certificate for $USERNAME..."
cd "$EASY_RSA" || exit 1
./easyrsa revoke "$USERNAME"
./easyrsa gen-crl

# Remove client config file
rm -f "${CONFIG_PATH}/${USERNAME}.ovpn"

# Reload OpenVPN to apply changes
systemctl reload openvpn

echo "VPN access for $USERNAME has been revoked"
