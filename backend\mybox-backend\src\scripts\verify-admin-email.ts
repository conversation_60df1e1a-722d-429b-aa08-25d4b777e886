import * as mongoose from 'mongoose';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function verifyAdminEmails() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/mybox');
    console.log('✅ Connected to MongoDB successfully');

    // Define user schema
    const userSchema = new mongoose.Schema({
      username: { type: String, required: true, unique: true },
      email: { type: String, required: true, unique: true },
      passwordHash: { type: String, required: true },
      role: { type: String, enum: ['user', 'admin', 'moderator'], default: 'user' },
      score: { type: Number, default: 0 },
      rank: { type: Number, default: 0 },
      teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'Team', default: null },
      avatarUrl: { type: String },
      apiToken: { type: String },
      isActive: { type: Boolean, default: true },
      isEmailVerified: { type: Boolean, default: false },
      emailVerificationCode: { type: String },
      emailVerificationExpires: { type: Date },
      lastActive: { type: Date, default: Date.now },
      country: { type: String },
      bio: { type: String },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });
    
    // Create model
    const UserModel = mongoose.model('User', userSchema);
    
    // Find all admin users
    console.log('🔍 Looking for admin users...');
    const adminUsers = await UserModel.find({ role: 'admin' });
    
    if (adminUsers.length === 0) {
      console.log('❌ No admin users found');
      return;
    }
    
    console.log(`📋 Found ${adminUsers.length} admin user(s)`);
    
    // Check and verify each admin user's email
    for (const admin of adminUsers) {
      console.log(`\n👤 Processing admin: ${admin.username} (${admin.email})`);
      
      if (admin.isEmailVerified) {
        console.log('✅ Email already verified');
      } else {
        console.log('❌ Email not verified. Verifying now...');
        
        // Update the admin user to verify email
        await UserModel.updateOne(
          { _id: admin._id },
          { 
            $set: { 
              isEmailVerified: true,
              emailVerificationCode: undefined,
              emailVerificationExpires: undefined,
              updatedAt: new Date()
            } 
          }
        );
        
        console.log('✅ Email verified successfully!');
      }
    }
    
    console.log('\n🎉 All admin emails have been verified!');
    console.log('🔐 Admin users can now log in without email verification.');
    
  } catch (error) {
    console.error('❌ Error verifying admin emails:', error.message);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  console.log('🚀 Starting admin email verification script...');
  verifyAdminEmails();
}

export { verifyAdminEmails };
