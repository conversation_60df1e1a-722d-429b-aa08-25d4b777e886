import { api } from './api';

export interface UserLeaderboardEntry {
  userId: string;
  username: string;
  email: string;
  avatar?: string;
  score: number;
  rank: number;
  solvedChallenges: number;
  lastSolved?: string;
  totalSubmissions: number;
}

export interface TeamLeaderboardEntry {
  teamId: string;
  teamName: string;
  teamScore: number;
  rank: number;
  memberCount: number;
  solvedChallenges: number;
  lastSolved?: string;
  captain: {
    id: string;
    username: string;
  };
}

export interface UserLeaderboardResponse {
  users: UserLeaderboardEntry[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface TeamLeaderboardResponse {
  teams: TeamLeaderboardEntry[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface UserRanking {
  userId: string;
  username: string;
  score: number;
  rank: number;
  solvedChallenges: number;
  lastSolved?: string;
  percentile: number;
}

export interface TeamRanking {
  teamId: string;
  teamName: string;
  teamScore: number;
  rank: number;
  memberCount: number;
  solvedChallenges: number;
  lastSolved?: string;
  percentile: number;
}

export interface LeaderboardQuery {
  page?: number;
  limit?: number;
}

class LeaderboardService {
  async getUserLeaderboard(query: LeaderboardQuery = {}): Promise<UserLeaderboardResponse> {
    const params = new URLSearchParams();
    if (query.page) params.append('page', query.page.toString());
    if (query.limit) params.append('limit', query.limit.toString());
    
    const response = await api.get(`/leaderboard?${params.toString()}`);
    return response.data;
  }

  async getTeamLeaderboard(query: LeaderboardQuery = {}): Promise<TeamLeaderboardResponse> {
    const params = new URLSearchParams();
    if (query.page) params.append('page', query.page.toString());
    if (query.limit) params.append('limit', query.limit.toString());
    
    const response = await api.get(`/leaderboard/teams?${params.toString()}`);
    return response.data;
  }

  async getTopUsers(limit: number = 10): Promise<UserLeaderboardEntry[]> {
    const response = await api.get(`/leaderboard/top/${limit}`);
    return response.data;
  }

  async getTopTeams(limit: number = 10): Promise<TeamLeaderboardEntry[]> {
    const response = await api.get(`/leaderboard/teams/top/${limit}`);
    return response.data;
  }

  async getUserRanking(userId: string): Promise<UserRanking> {
    const response = await api.get(`/leaderboard/user/${userId}`);
    return response.data;
  }

  async getTeamRanking(teamId: string): Promise<TeamRanking> {
    const response = await api.get(`/leaderboard/team/${teamId}`);
    return response.data;
  }
}

export const leaderboardService = new LeaderboardService();
