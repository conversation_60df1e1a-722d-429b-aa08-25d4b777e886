{% extends "base.html" %}
{% block title %}Upload File{% endblock %}
{% block content %}
<h2>Upload New File</h2>
<form method="POST" enctype="multipart/form-data">
  {% if is_admin %}
  <label>Upload from URL (Admins only):</label>
  <input type="text" name="file_url" placeholder="http://example.com/file.txt">
  {% endif %}

  <label>Select File:</label>
  <input type="file" name="file" required>

  <button type="submit">Upload</button>
</form>
<p><a href="{{ url_for('list_files') }}">Back to Files</a></p>

{% with messages = get_flashed_messages() %}
  {% if messages %}
    <div class="flash">
      <ul>
      {% for message in messages %}
        <li>{{ message }}</li>
      {% endfor %}
      </ul>
    </div>
  {% endif %}
{% endwith %}
{% endblock %}
