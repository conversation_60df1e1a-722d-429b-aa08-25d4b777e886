import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type NotificationDocument = Notification & Document;

export enum NotificationType {
  ADMIN_MESSAGE = 'admin_message',
  FIRST_BLOOD = 'first_blood',
  CHALLENGE_SOLVED = 'challenge_solved',
  NEW_CHALLENGE = 'new_challenge',
  NEW_MACHINE = 'new_machine',
  MACHINE_FIRST_BLOOD = 'machine_first_blood',
  SYSTEM_ANNOUNCEMENT = 'system_announcement',
  TEAM_INVITATION = 'team_invitation',
  COMPETITION_UPDATE = 'competition_update',
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Schema({ timestamps: true })
export class Notification {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  message: string;

  @Prop({ 
    type: String, 
    enum: NotificationType, 
    required: true 
  })
  type: NotificationType;

  @Prop({ 
    type: String, 
    enum: NotificationPriority, 
    default: NotificationPriority.MEDIUM 
  })
  priority: NotificationPriority;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  sender: Types.ObjectId;

  @Prop({ type: [Types.ObjectId], ref: 'User', default: [] })
  recipients: Types.ObjectId[];

  @Prop({ type: [Types.ObjectId], ref: 'User', default: [] })
  readBy: Types.ObjectId[];

  @Prop({ default: false })
  isGlobal: boolean;

  @Prop({ type: Object, default: {} })
  metadata: {
    challengeId?: string;
    challengeName?: string;
    challengeCategory?: string;
    challengeDifficulty?: string;
    challengePoints?: number;
    machineId?: string;
    machineName?: string;
    machineOs?: string;
    machineDifficulty?: string;
    machinePoints?: number;
    username?: string;
    teamName?: string;
    points?: number;
    icon?: string;
    color?: string;
    actionUrl?: string;
    [key: string]: any;
  };

  @Prop({ default: Date.now })
  expiresAt: Date;

  @Prop({ default: true })
  isActive: boolean;

  createdAt: Date;
  updatedAt: Date;
}

export const NotificationSchema = SchemaFactory.createForClass(Notification);