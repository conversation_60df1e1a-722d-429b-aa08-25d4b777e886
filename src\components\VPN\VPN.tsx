import React, { useState, useEffect } from 'react';
import { useApp } from '../../contexts/AppContext';
import { 
  Shield, 
  Download, 
  Wifi, 
  WifiOff, 
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  AlertTriangle,
  Loader2,
  Power,
  PowerOff
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { VpnService, VPNStatus, VPNConnectionLog, VPNSetupInstructions } from '../../services/vpn';

export function VPN() {
  const { state } = useApp();
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [vpnStatus, setVpnStatus] = useState<VPNStatus | null>(null);
  const [connectionLogs, setConnectionLogs] = useState<VPNConnectionLog[]>([]);
  const [setupInstructions, setSetupInstructions] = useState<VPNSetupInstructions | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showSetupInstructions, setShowSetupInstructions] = useState(false);

  // Fetch VPN status from the backend using new WireGuard API
  const fetchVpnStatus = async () => {
    try {
      const status = await VpnService.getVPNStatus();
      setVpnStatus(status);
      setError(null);

      return status;
    } catch (err: any) {
      setError('Failed to fetch VPN status');
      console.error('Error fetching VPN status:', err);
      toast.error(err.message || 'Failed to fetch VPN status');
      throw err;
    }
  };

  // Load setup instructions
  const loadSetupInstructions = async () => {
    try {
      const instructions = await VpnService.getSetupInstructions();
      setSetupInstructions(instructions);
    } catch (err: any) {
      console.error('Error loading setup instructions:', err);
    }
  };

  // Load connection logs
  const loadConnectionLogs = async () => {
    try {
      const logs = await VpnService.getConnectionLogs();
      setConnectionLogs(logs);
    } catch (err: any) {
      console.error('Error loading connection logs:', err);
    }
  };

  // Handle downloading VPN config
  const handleDownloadConfig = async () => {
    try {
      setIsLoading(true);

      if (vpnStatus?.hasVPNConfig) {
        // Download existing config
        await VpnService.downloadVPNConfig();
        toast.success('VPN configuration downloaded successfully');
      } else {
        // Generate new config first, then download
        await VpnService.generateVPNConfig();
        await VpnService.downloadVPNConfig();
        toast.success('VPN configuration created and downloaded successfully');
      }

      // Refresh status after download
      await fetchVpnStatus();
    } catch (err: any) {
      setError('Failed to download VPN configuration');
      console.error('Error downloading VPN config:', err);
      toast.error(err.message || 'Failed to download VPN configuration');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle revoking VPN access
  const handleRevokeAccess = async () => {
    if (!window.confirm('Are you sure you want to revoke your VPN access? This will disconnect you immediately.')) {
      return;
    }

    try {
      setIsLoading(true);
      const result = await VpnService.revokeVPNAccess();

      toast.success(result.message || 'VPN access revoked successfully');
      await fetchVpnStatus();
    } catch (err: any) {
      setError('Failed to revoke VPN access');
      console.error('Error revoking VPN access:', err);
      toast.error(err.message || 'Failed to revoke VPN access');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle regenerating VPN config
  const handleRegenerateConfig = async () => {
    if (!window.confirm('Are you sure you want to regenerate your VPN configuration? This will create new keys and disconnect any active connections.')) {
      return;
    }

    try {
      setIsLoading(true);
      const result = await VpnService.regenerateVPNConfig();

      toast.success(result.message || 'VPN configuration regenerated successfully');
      await fetchVpnStatus();
    } catch (err: any) {
      setError('Failed to regenerate VPN configuration');
      console.error('Error regenerating VPN config:', err);
      toast.error(err.message || 'Failed to regenerate VPN configuration');
    } finally {
      setIsLoading(false);
    }
  };



  // Handle manual refresh
  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await fetchVpnStatus();
    } finally {
      setIsRefreshing(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchVpnStatus();
    loadSetupInstructions();

    // Set up interval for auto-refresh
    const interval = setInterval(fetchVpnStatus, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Format connection time
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Format relative time
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  // Determine connection status based on server status and active connections
  const isConnected = (vpnStatus?.serverStatus?.activeConnections || 0) > 0;
  const ipAddress = vpnStatus?.vpnInfo?.ipAddress || 'Not configured';
  const hasVPNConfig = vpnStatus?.hasVPNConfig || false;

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">VPN Access</h1>
        <p className="text-slate-400 mb-6">
          Connect to the MyBox network to access machines and challenges
        </p>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/20 text-red-300 px-4 py-3 rounded-lg flex items-center">
          <AlertTriangle className="w-5 h-5 mr-2" />
          <span>{error}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <Shield className="w-6 h-6 text-emerald-400" />
                <h2 className="text-xl font-semibold text-white">Connection Status</h2>
              </div>
              <button 
                onClick={handleRefresh}
                disabled={isRefreshing}
                className={`p-1.5 rounded-full ${isRefreshing ? 'text-slate-500' : 'text-slate-400 hover:text-white hover:bg-slate-700'}`}
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </button>
            </div>

            <div className="space-y-6">
              <div className="flex items-center justify-between p-4 bg-slate-800/50 rounded-lg">
                <div className="flex items-center space-x-3">
                  {isLoading ? (
                    <Loader2 className="w-6 h-6 text-slate-400 animate-spin" />
                  ) : isConnected ? (
                    <Wifi className="w-6 h-6 text-emerald-400" />
                  ) : (
                    <WifiOff className="w-6 h-6 text-red-400" />
                  )}
                  <div>
                    <p className="font-medium text-white">
                      {isLoading ? 'Loading...' : isConnected ? 'Connected' : 'Disconnected'}
                    </p>
                    <p className="text-sm text-slate-400">
                      {isLoading ? 'Checking connection status...' : 
                       isConnected ? 'VPN tunnel is active' : 'No active connection'}
                    </p>
                  </div>
                </div>
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  isLoading ? 'bg-slate-500/20 text-slate-400' :
                  isConnected ? 'bg-emerald-500/20 text-emerald-400' : 'bg-red-500/20 text-red-400'
                }`}>
                  {isLoading ? '...' : isConnected ? 'ACTIVE' : 'INACTIVE'}
                </div>
              </div>

              {isConnected && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-slate-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Clock className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-slate-400">Connection Time</span>
                    </div>
                    <p className="text-xl font-mono text-emerald-400">
                      {isConnected ? 'Connected' : 'Disconnected'}
                    </p>
                  </div>
                  <div className="bg-slate-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Wifi className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-slate-400">Assigned IP</span>
                    </div>
                    <p className="text-xl font-mono text-emerald-400">
                      {isConnected ? ipAddress : 'Not connected'}
                    </p>
                  </div>
                </div>
              )}

              <div className="flex space-x-4">
                <button
                  onClick={handleDownloadConfig}
                  disabled={isLoading || isConnected}
                  className={`flex items-center space-x-2 px-6 py-3 font-medium rounded-lg transition-colors ${
                    isConnected 
                      ? 'bg-slate-700 text-slate-400 cursor-not-allowed' 
                      : 'bg-emerald-600 hover:bg-emerald-700 text-white'
                  }`}
                >
                  {isLoading ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    <Download className="w-5 h-5" />
                  )}
                  <span>{isConnected ? 'Already Connected' : 'Download Config'}</span>
                </button>
                
                <button
                  onClick={handleRevokeAccess}
                  disabled={isLoading || !isConnected}
                  className={`flex items-center space-x-2 px-6 py-3 font-medium rounded-lg transition-colors ${
                    isConnected 
                      ? 'bg-red-600 hover:bg-red-700 text-white' 
                      : 'bg-slate-700 text-slate-400 cursor-not-allowed'
                  }`}
                >
                  {isLoading ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    <WifiOff className="w-5 h-5" />
                  )}
                  <span>Revoke Access</span>
                </button>
              </div>
            </div>
          </div>

          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <CheckCircle className="w-6 h-6 text-blue-400" />
              <h2 className="text-xl font-semibold text-white">Setup Instructions</h2>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-emerald-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  1
                </div>
                <div>
                  <p className="font-medium text-white">Download WireGuard Client</p>
                  <p className="text-sm text-slate-400">
                    Install WireGuard client on your system (Windows, macOS, Linux, iOS, Android)
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-emerald-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  2
                </div>
                <div>
                  <p className="font-medium text-white">Download Configuration</p>
                  <p className="text-sm text-slate-400">
                    Click "Download Config" to get your personalized WireGuard configuration
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-emerald-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  3
                </div>
                <div>
                  <p className="font-medium text-white">Connect to VPN</p>
                  <p className="text-sm text-slate-400">
                    Import the .conf file into your WireGuard client and activate the tunnel
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-emerald-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  4
                </div>
                <div>
                  <p className="font-medium text-white">Access Machines</p>
                  <p className="text-sm text-slate-400">
                    Once connected, you can access machines using their private IP addresses
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-white">Connected Users</h2>
              <button 
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="text-slate-400 hover:text-white p-1 -mr-2"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </button>
            </div>
            <div className="bg-slate-900/50 rounded-lg border border-slate-800 overflow-hidden">
              <div className="grid grid-cols-12 gap-4 p-4 border-b border-slate-800 text-xs text-slate-400 font-medium">
                <div className="col-span-6">USER</div>
                <div className="col-span-3">IP ADDRESS</div>
                <div className="col-span-3 text-right">CONNECTED</div>
              </div>
              {isRefreshing ? (
                <div className="p-8 text-center text-slate-400">
                  <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2" />
                  <p>Loading connected users...</p>
                </div>
              ) : connectedUsers.length > 0 ? (
                connectedUsers.map((user) => (
                  <div key={user.id} className="grid grid-cols-12 gap-4 p-4 border-b border-slate-800/50 hover:bg-slate-800/20 transition-colors">
                    <div className="col-span-6 flex items-center space-x-3">
                      <div className="w-2 h-2 rounded-full bg-emerald-400"></div>
                      <span className="font-medium text-white">{user.username}</span>
                    </div>
                    <div className="col-span-3 text-slate-400">{user.ip}</div>
                    <div className="col-span-3 text-right text-slate-400">
                      {formatTimeAgo(user.connectedAt)}
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-8 text-center text-slate-400">
                  <WifiOff className="w-6 h-6 mx-auto mb-2" />
                  <p>No active connections</p>
                </div>
              )}
            </div>
          </div>

          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <AlertCircle className="w-6 h-6 text-orange-400" />
              <h2 className="text-xl font-semibold text-white">Important Notes</h2>
            </div>

            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-orange-400 rounded-full mt-2"></div>
                <p className="text-sm text-slate-300">
                  VPN sessions automatically disconnect after 4 hours of inactivity
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-orange-400 rounded-full mt-2"></div>
                <p className="text-sm text-slate-300">
                  Only connect to machines you have permission to access
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-orange-400 rounded-full mt-2"></div>
                <p className="text-sm text-slate-300">
                  Report any connection issues to the support team
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}