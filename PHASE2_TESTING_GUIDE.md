# Phase 2 Testing Guide
## Archive Upload & Docker Build Integration

This guide will help you test the new archive upload and Docker build functionality in the admin interface.

## 🎯 **What's New in Phase 2**

### ✅ **Backend Features**
- Archive upload endpoint (`POST /admin/machines/:id/upload-archive`)
- Docker image building endpoint (`POST /admin/machines/:id/build-image`)
- Build status checking endpoint (`GET /admin/machines/:id/image-status`)
- Build logs retrieval endpoint (`GET /admin/machines/:id/build-logs`)
- Image removal endpoint (`DELETE /admin/machines/:id/image`)

### ✅ **Frontend Features**
- Archive upload section in Create/Edit Template Modal
- Build status indicators in machine list
- Build Status Modal with detailed management
- Real-time build status tracking

## 🧪 **Testing Steps**

### Step 1: Access Admin Interface
1. Navigate to your admin dashboard
2. Go to the "Machines" section
3. You should see the machine templates list

### Step 2: Create a New Machine Template
1. Click "Create Template"
2. Fill in the basic information:
   - **Name**: Test Archive Machine
   - **Description**: Testing archive upload functionality
   - **Category**: web
   - **Difficulty**: easy
   - **OS**: linux
   - **Note**: Machine Type and Docker Image fields have been removed - all templates are now archive-based!

3. Add at least one flag:
   - **Name**: user.txt
   - **Value**: test_flag_123
   - **Type**: user
   - **Points**: 10

4. Save the template

### Step 3: Test Archive Upload
1. After saving, edit the template again
2. You should now see an "Archive Upload" section (only for Docker machines)
3. Click "Choose File" and select a ZIP or 7z archive containing:
   ```
   your-archive.zip
   ├── Dockerfile
   ├── docker-compose.yml
   └── machine/
       └── [your application files]
   ```
4. Click "Upload" to upload the archive
5. The system should extract and validate the archive

### Step 4: Test Docker Build
1. After successful upload, you should see build controls
2. Click "Build Image" to start the Docker build process
3. Monitor the build progress
4. Click "Show Logs" to view build logs
5. The status should change from "Ready" to "Built" when complete

### Step 5: Test Build Status Modal
1. In the machine list, look for Docker machines
2. You should see build status indicators:
   - 🔴 Gray dot: No files uploaded
   - 🟡 Yellow dot: Files extracted, ready to build
   - 🟢 Green dot: Image built successfully
3. Click the orange "Settings" button (⚙️) on Docker machines
4. This opens the detailed Build Status Modal with:
   - Current image status
   - Build/Remove buttons
   - Build logs viewer
   - Status summary

### Step 6: Test Build Management
1. In the Build Status Modal, try:
   - **Build Image**: Start a new build
   - **Remove Image**: Delete the built image
   - **Refresh**: Update status information
   - **Show/Hide Logs**: Toggle build logs visibility

## 📋 **Expected Results**

### ✅ **Successful Upload**
- Archive is extracted to `uploads/machines/dockers/{template-slug}/`
- Status shows "Files extracted, ready to build"
- Build button becomes available

### ✅ **Successful Build**
- Docker image is created with name `rakcha/{template-slug}:latest`
- Status shows "Image built and ready"
- Build logs are available
- Remove button becomes available

### ✅ **Status Indicators**
- Machine list shows correct build status
- Status updates in real-time
- Build Status Modal provides detailed information

## 🐛 **Common Issues & Solutions**

### Issue: "Only ZIP and 7z files are allowed"
**Solution**: Ensure your archive has `.zip` or `.7z` extension

### Issue: "Invalid machine template structure"
**Solution**: Your archive must contain:
- `Dockerfile` (required)
- `docker-compose.yml` (required)
- `machine/` directory (recommended)

### Issue: "Template files not found"
**Solution**: Upload an archive first before trying to build

### Issue: Build fails
**Solution**: 
1. Check build logs for specific errors
2. Ensure Dockerfile is valid
3. Check if base images are available

## 📁 **Sample Archive Structure**

Create a test archive with this structure:

```
test-machine.zip
├── Dockerfile
├── docker-compose.yml
└── machine/
    ├── index.html
    ├── flag.txt
    └── app/
        └── [your application files]
```

### Sample Dockerfile:
```dockerfile
FROM nginx:alpine
COPY machine/ /usr/share/nginx/html/
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Sample docker-compose.yml:
```yaml
version: '3.9'
services:
  web:
    build: .
    ports:
      - "80:80"
    restart: unless-stopped
```

## 🔍 **Verification Checklist**

- [ ] Can create Docker machine templates
- [ ] Archive upload section appears for Docker machines only
- [ ] Can upload ZIP and 7z files
- [ ] Archive validation works (rejects invalid files)
- [ ] Build status indicators show in machine list
- [ ] Can build Docker images from uploaded archives
- [ ] Build logs are captured and displayable
- [ ] Can remove built images
- [ ] Build Status Modal opens and functions correctly
- [ ] Status updates reflect changes in real-time

## 🚀 **Next Steps**

Once Phase 2 testing is complete and confirmed working:

1. ✅ **Phase 2 Complete**: Archive upload and Docker build integration
2. 🔄 **Phase 3 Ready**: VPN integration system
   - WireGuard service integration
   - VPN configuration management
   - Network routing setup
   - User VPN access interface

## 📞 **Support**

If you encounter any issues during testing:

1. Check browser console for JavaScript errors
2. Check backend logs for API errors
3. Verify file permissions in uploads directory
4. Ensure Docker is running and accessible
5. Test with a simple archive first

The system is now ready for comprehensive testing of the archive upload and Docker build functionality!
