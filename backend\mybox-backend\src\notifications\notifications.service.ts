import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Notification, NotificationDocument, NotificationType, NotificationPriority } from '../schemas/notification.schema';
import { User } from '../schemas/user.schema';
import { CreateNotificationDto, UpdateNotificationDto, NotificationQueryDto } from './dto/notification.dto';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectModel(Notification.name) private notificationModel: Model<NotificationDocument>,
    @InjectModel(User.name) private userModel: Model<User>,
  ) {}

  async createNotification(createNotificationDto: CreateNotificationDto, senderId?: string): Promise<Notification> {
    const notificationData = {
      ...createNotificationDto,
      sender: senderId ? new Types.ObjectId(senderId) : undefined,
      recipients: createNotificationDto.recipients?.map(id => new Types.ObjectId(id)) || [],
      expiresAt: createNotificationDto.expiresAt ? new Date(createNotificationDto.expiresAt) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days default
    };

    const notification = new this.notificationModel(notificationData);
    return await notification.save();
  }

  async createFirstBloodNotification(challengeId: string, challengeName: string, username: string, points: number): Promise<Notification> {
    const notification = await this.createNotification({
      title: '🏆 First Blood Achievement!',
      message: `${username} just scored the first blood on "${challengeName}"!`,
      type: NotificationType.FIRST_BLOOD,
      priority: NotificationPriority.HIGH,
      isGlobal: true,
      metadata: {
        challengeId,
        challengeName,
        username,
        points,
        icon: '🏆',
        color: '#FFD700',
        actionUrl: `/challenges/${challengeId}`,
      },
    });

    return notification;
  }

  async createNewChallengeNotification(challengeId: string, challengeName: string, category: string, difficulty: string, points: number): Promise<Notification> {
    const difficultyEmojis = {
      easy: '🟢',
      medium: '🟡', 
      hard: '🔴',
      insane: '🟣'
    };

    const categoryEmojis = {
      web: '🌐',
      crypto: '🔐',
      pwn: '💥',
      reverse: '🔄',
      forensics: '🔍',
      misc: '🎯'
    };

    const emoji = difficultyEmojis[difficulty] || '⭐';
    const categoryEmoji = categoryEmojis[category] || '🎯';

    const notification = await this.createNotification({
      title: `${emoji} New Challenge Available!`,
      message: `${categoryEmoji} "${challengeName}" (${difficulty.toUpperCase()}) is now available! Worth ${points} points.`,
      type: NotificationType.NEW_CHALLENGE,
      priority: NotificationPriority.MEDIUM,
      isGlobal: true,
      metadata: {
        challengeId,
        challengeName,
        challengeCategory: category,
        challengeDifficulty: difficulty,
        challengePoints: points,
        icon: emoji,
        color: '#10B981', // Green color for new challenges
        actionUrl: `/challenges/${challengeId}`,
      },
    });

    return notification;
  }

  async createNewMachineNotification(machineId: string, machineName: string, os: string, difficulty: string, points: number): Promise<Notification> {
    const difficultyEmojis = {
      easy: '🟢',
      medium: '🟡', 
      hard: '🔴',
      insane: '🟣'
    };

    const osEmojis = {
      linux: '🐧',
      windows: '🪟',
      freebsd: '👹',
      other: '💻'
    };

    const emoji = difficultyEmojis[difficulty] || '⭐';
    const osEmoji = osEmojis[os.toLowerCase()] || '💻';

    const notification = await this.createNotification({
      title: `${emoji} New Machine Available!`,
      message: `${osEmoji} "${machineName}" (${difficulty.toUpperCase()}) is now available! Worth ${points} points.`,
      type: NotificationType.NEW_MACHINE,
      priority: NotificationPriority.MEDIUM,
      isGlobal: true,
      metadata: {
        machineId,
        machineName,
        machineOs: os,
        machineDifficulty: difficulty,
        machinePoints: points,
        icon: emoji,
        color: '#3B82F6', // Blue color for new machines
        actionUrl: `/machines/${machineId}`,
      },
    });

    return notification;
  }

  async createMachineFirstBloodNotification(machineId: string, machineName: string, username: string, points: number): Promise<Notification> {
    const notification = await this.createNotification({
      title: '🏆 Machine First Blood!',
      message: `${username} just scored the first blood on machine "${machineName}"!`,
      type: NotificationType.MACHINE_FIRST_BLOOD,
      priority: NotificationPriority.HIGH,
      isGlobal: true,
      metadata: {
        machineId,
        machineName,
        username,
        points,
        icon: '🏆',
        color: '#FFD700', // Gold color for first blood
        actionUrl: `/machines/${machineId}`,
      },
    });

    return notification;
  }

  async createAdminNotification(title: string, message: string, senderId: string, recipients?: string[]): Promise<Notification> {
    return await this.createNotification({
      title,
      message,
      type: NotificationType.ADMIN_MESSAGE,
      priority: NotificationPriority.HIGH,
      recipients,
      isGlobal: !recipients || recipients.length === 0,
      metadata: {
        icon: '📢',
        color: '#8B5CF6',
      },
    }, senderId);
  }

  async createSystemAnnouncement(title: string, message: string, priority: NotificationPriority = NotificationPriority.MEDIUM): Promise<Notification> {
    return await this.createNotification({
      title,
      message,
      type: NotificationType.SYSTEM_ANNOUNCEMENT,
      priority,
      isGlobal: true,
      metadata: {
        icon: '🔔',
        color: '#06B6D4',
      },
    });
  }

  async getUserNotifications(userId: string, query: NotificationQueryDto): Promise<{
    notifications: Notification[];
    total: number;
    unreadCount: number;
  }> {
    const page = parseInt(query.page?.toString() || '1') || 1;
    const limit = parseInt(query.limit?.toString() || '20') || 20;
    const skip = (page - 1) * limit;

    const filter: any = {
      $and: [
        {
          $or: [
            { isGlobal: true },
            { recipients: new Types.ObjectId(userId) },
          ],
        },
        { isActive: true },
        { expiresAt: { $gt: new Date() } },
      ],
    };

    if (query.type) {
      filter.$and.push({ type: query.type });
    }

    if (query.priority) {
      filter.$and.push({ priority: query.priority });
    }

    if (query.unreadOnly) {
      filter.$and.push({ readBy: { $ne: new Types.ObjectId(userId) } });
    }

    const [notifications, total, unreadCount] = await Promise.all([
      this.notificationModel
        .find(filter)
        .populate('sender', 'username email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      this.notificationModel.countDocuments(filter),
      this.notificationModel.countDocuments({
        ...filter,
        readBy: { $ne: new Types.ObjectId(userId) },
      }),
    ]);

    return {
      notifications: notifications.map(notification => ({
        ...notification,
        isRead: notification.readBy.some(id => id.toString() === userId),
      })) as any,
      total,
      unreadCount,
    };
  }

  async markAsRead(userId: string, notificationIds: string[]): Promise<void> {
    const objectIds = notificationIds.map(id => new Types.ObjectId(id));
    
    await this.notificationModel.updateMany(
      {
        _id: { $in: objectIds },
        $or: [
          { isGlobal: true },
          { recipients: new Types.ObjectId(userId) },
        ],
      },
      {
        $addToSet: { readBy: new Types.ObjectId(userId) },
      },
    );
  }

  async markAllAsRead(userId: string): Promise<void> {
    await this.notificationModel.updateMany(
      {
        $or: [
          { isGlobal: true },
          { recipients: new Types.ObjectId(userId) },
        ],
        readBy: { $ne: new Types.ObjectId(userId) },
        isActive: true,
        expiresAt: { $gt: new Date() },
      },
      {
        $addToSet: { readBy: new Types.ObjectId(userId) },
      },
    );
  }

  async getNotificationById(id: string, userId?: string): Promise<Notification> {
    const notification = await this.notificationModel
      .findById(id)
      .populate('sender', 'username email')
      .lean();

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    // Check if user has access to this notification
    if (userId && !notification.isGlobal && !notification.recipients.some(recipientId => recipientId.toString() === userId)) {
      throw new ForbiddenException('Access denied to this notification');
    }

    return {
      ...notification,
      isRead: userId ? notification.readBy.some(id => id.toString() === userId) : false,
    } as any;
  }

  async updateNotification(id: string, updateNotificationDto: UpdateNotificationDto, userId: string): Promise<Notification> {
    const notification = await this.notificationModel.findById(id);

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    // Check if user is the sender or admin
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.role !== 'admin' && notification.sender?.toString() !== userId) {
      throw new ForbiddenException('You can only update your own notifications');
    }

    Object.assign(notification, updateNotificationDto);
    return await notification.save();
  }

  async deleteNotification(id: string, userId: string): Promise<void> {
    const notification = await this.notificationModel.findById(id);

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    // Check if user is the sender or admin
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.role !== 'admin' && notification.sender?.toString() !== userId) {
      throw new ForbiddenException('You can only delete your own notifications');
    }

    await this.notificationModel.findByIdAndDelete(id);
  }

  async getAdminNotifications(query: NotificationQueryDto): Promise<{
    notifications: Notification[];
    total: number;
  }> {
    const page = parseInt(query.page?.toString() || '1') || 1;
    const limit = parseInt(query.limit?.toString() || '20') || 20;
    const skip = (page - 1) * limit;

    const filter: any = {};

    if (query.type) {
      filter.type = query.type;
    }

    if (query.priority) {
      filter.priority = query.priority;
    }

    const [notifications, total] = await Promise.all([
      this.notificationModel
        .find(filter)
        .populate('sender', 'username email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      this.notificationModel.countDocuments(filter),
    ]);

    return {
      notifications: notifications as any,
      total,
    };
  }

  async cleanupExpiredNotifications(): Promise<void> {
    await this.notificationModel.deleteMany({
      expiresAt: { $lt: new Date() },
    });
  }
}