import React, { useState } from 'react';
import { Upload } from 'lucide-react';
import { dashboardService } from '../../services/dashboard';
import { getImageUrl } from '../../utils/imageUtils';

interface SponsorModalProps {
  sponsor?: any;
  onSave: (sponsorData: any) => void;
  onClose: () => void;
}

export function SponsorModal({ sponsor, onSave, onClose }: SponsorModalProps) {
  const [formData, setFormData] = useState({
    name: sponsor?.name || '',
    website: sponsor?.website || '',
    description: sponsor?.description || '',
    tier: sponsor?.tier || 'bronze'
  });
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string>(
    sponsor?.logo ? getImageUrl(sponsor.logo) : ''
  );
  const [uploading, setUploading] = useState(false);

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setLogoFile(file);
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setLogoPreview(previewUrl);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setUploading(true);

    try {
      let logoUrl = sponsor?.logo || '';

      // If there's a new logo file, upload it first
      if (logoFile) {
        if (sponsor?.id) {
          // For existing sponsor, upload to specific sponsor
          const uploadResponse = await dashboardService.uploadSponsorLogo(sponsor.id, logoFile);
          logoUrl = uploadResponse.logoUrl;
        } else {
          // For new sponsor, we need to upload to a temporary location first
          const formDataForUpload = new FormData();
          formDataForUpload.append('file', logoFile);
          
          // Upload to temporary location with proper authentication
          const token = localStorage.getItem('mybox_token');
          const response = await fetch('http://localhost:3001/api/dashboard/sponsors/upload-temp-logo', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
            },
            body: formDataForUpload,
          });
          
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({ message: 'Failed to upload logo' }));
            throw new Error(errorData.message || 'Failed to upload logo');
          }
          
          const uploadResult = await response.json();
          logoUrl = uploadResult.logoUrl;
        }
      }

      // Save sponsor with logo URL
      onSave({
        ...formData,
        logo: logoUrl
      });
    } catch (error) {
      console.error('Error uploading logo:', error);
      // Show error message to user
      alert(`Failed to upload logo: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[9999]">
      <div className="bg-slate-800 rounded-xl border border-purple-500/30 p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white">
            {sponsor ? 'Edit Sponsor' : 'Add Sponsor'}
          </h3>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-white"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-purple-200 mb-2">
              Sponsor Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 bg-slate-900 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:outline-none"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-purple-200 mb-2">
              Logo
            </label>
            <div className="space-y-3">
              {/* Logo Preview */}
              {logoPreview && (
                <div className="flex items-center justify-center p-4 bg-slate-900/50 rounded-lg border border-slate-700">
                  <img
                    src={logoPreview}
                    alt="Logo preview"
                    className="max-w-32 max-h-20 object-contain"
                    onError={(e) => {
                      console.error('Failed to load image:', logoPreview);
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}
              
              {/* File Upload */}
              <div className="flex items-center space-x-3">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoChange}
                  className="hidden"
                  id="logo-upload"
                />
                <label
                  htmlFor="logo-upload"
                  className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg cursor-pointer transition-colors text-white"
                >
                  <Upload className="w-4 h-4" />
                  <span>{logoPreview ? 'Change Logo' : 'Upload Logo'}</span>
                </label>
                {logoFile && (
                  <span className="text-sm text-purple-300">{logoFile.name}</span>
                )}
              </div>
              <p className="text-xs text-slate-400">
                Supported formats: PNG, JPG, SVG. Max size: 2MB
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-purple-200 mb-2">
              Website (Optional)
            </label>
            <input
              type="url"
              value={formData.website}
              onChange={(e) => setFormData({ ...formData, website: e.target.value })}
              className="w-full px-3 py-2 bg-slate-900 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:outline-none"
              placeholder="https://sponsor-website.com"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-purple-200 mb-2">
              Tier
            </label>
            <select
              value={formData.tier}
              onChange={(e) => setFormData({ ...formData, tier: e.target.value })}
              className="w-full px-3 py-2 bg-slate-900 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:outline-none"
            >
              <option value="bronze">Bronze</option>
              <option value="silver">Silver</option>
              <option value="gold">Gold</option>
              <option value="platinum">Platinum</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-purple-200 mb-2">
              Description (Optional)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 bg-slate-900 border border-purple-500/30 rounded-lg text-white focus:border-purple-400 focus:outline-none"
              placeholder="Brief description of the sponsor..."
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 bg-slate-700 text-white rounded-lg hover:bg-slate-600 transition-colors"
              disabled={uploading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={uploading || (!logoFile && !sponsor?.logo)}
              className="flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading ? 'Uploading...' : sponsor ? 'Update' : 'Add'} Sponsor
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}