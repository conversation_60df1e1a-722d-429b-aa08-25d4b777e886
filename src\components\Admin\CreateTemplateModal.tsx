import { useState, useEffect } from 'react';
import { MachineTemplate } from '../../services/machines';
import machineService from '../../services/machines';
import { getImageUrl } from '../../utils/imageUtils';
import {
  X,
  Plus,
  Trash2,
  AlertCircle,
  Crown,
  User,
  Globe,
  Flag,
  Upload,
  BookOpen
} from 'lucide-react';

interface CreateTemplateModalProps {
  template: MachineTemplate | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (templateData: Partial<MachineTemplate>) => Promise<void>;
}

interface Flag {
  name: string;
  value: string;
  type: 'root' | 'user' | 'www-data' | 'custom';
  description?: string;
  points: number;
  isRequired: boolean;
}

interface Topic {
  title: string;
  description: string;
  imageUrl?: string;
  order: number;
}

export function CreateTemplateModal({ template, isOpen, onClose, onSave }: CreateTemplateModalProps) {
  const flagTypeIcons = {
    root: Crown,
    user: User,
    'www-data': Globe,
    custom: Flag
  };

  const flagTypeColors = {
    root: 'text-red-400 bg-red-500/20 border-red-500/30',
    user: 'text-blue-400 bg-blue-500/20 border-blue-500/30',
    'www-data': 'text-green-400 bg-green-500/20 border-green-500/30',
    custom: 'text-purple-400 bg-purple-500/20 border-purple-500/30'
  };
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    category: 'web' as 'web' | 'crypto' | 'pwn' | 'reverse' | 'forensics' | 'misc' | 'osint',
    difficulty: 'easy' as 'easy' | 'medium' | 'hard' | 'insane',
    os: 'linux' as 'linux' | 'windows' | 'other',
    isActive: true,
    requiredRAM: 512,
    requiredCPU: 1,
    maxInstances: 10,
    authorName: '',
  });

  const [flags, setFlags] = useState<Flag[]>([]);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [hints, setHints] = useState<string[]>(['']);
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Archive upload states
  const [archiveFile, setArchiveFile] = useState<File | null>(null);
  const [archiveUploading, setArchiveUploading] = useState(false);
  const [archiveUploaded, setArchiveUploaded] = useState(false);
  const [imageStatus, setImageStatus] = useState<{
    imageExists: boolean;
    hasExtractedFiles: boolean;
    imageName: string;
    canBuild: boolean;
  } | null>(null);
  const [buildLogs, setBuildLogs] = useState<string[]>([]);
  const [showBuildLogs, setShowBuildLogs] = useState(false);
  const [building, setBuilding] = useState(false);
  const [systemStatus, setSystemStatus] = useState<{
    platform: string;
    zipSupported: boolean;
    sevenZipSupported: boolean;
    installationInstructions?: string;
  } | null>(null);

  // Categories and difficulties
  const categories = ['web', 'crypto', 'pwn', 'reverse', 'forensics', 'misc', 'osint'];
  const difficulties = ['easy', 'medium', 'hard', 'insane'];
  const osOptions = ['linux', 'windows', 'other'];
  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name || '',
        slug: template.slug || '',
        description: template.description || '',
        category: template.category || 'web',
        difficulty: template.difficulty || 'easy',
        os: template.os || 'linux',
        isActive: template.isActive ?? true,
        requiredRAM: template.requiredRAM || 512,
        requiredCPU: template.requiredCPU || 1,
        maxInstances: template.maxInstances || 10,
        authorName: template.authorName || '',
      });
      setFlags(template.flags?.map(flag => ({
        name: flag.name,
        value: flag.value || '',
        type: flag.type || 'custom',
        description: flag.description || '',
        points: flag.points,
        isRequired: flag.isRequired || false
      })) || []);
      setTopics(template.topics || []);
      setHints(template.hints || ['']);
      setTags(template.tags || []);

      // Check image status for existing templates
      if (template.id) {
        checkImageStatus();
      }

      // Load system status
      loadSystemStatus();
    } else {
      // Reset form for new template
      setFormData({
        name: '',
        slug: '',
        description: '',
        category: 'web',
        difficulty: 'easy',
        os: 'linux',
        isActive: true,
        requiredRAM: 512,
        requiredCPU: 1,
        maxInstances: 10,
        authorName: '',
      });
      setFlags([]);
      setHints(['']);
      setTags([]);

      // Reset archive states
      setImageStatus(null);
      setArchiveFile(null);
      setArchiveUploaded(false);
      setBuildLogs([]);
      setShowBuildLogs(false);
    }
    setErrors({});
  }, [template, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.slug.trim()) newErrors.slug = 'Slug is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.authorName.trim()) newErrors.authorName = 'Author name is required';
    if (flags.length === 0) newErrors.flags = 'At least one flag is required';

    // Validate flags
    flags.forEach((flag, index) => {
      if (!flag.name.trim()) newErrors[`flag_${index}_name`] = 'Flag name is required';
      if (!flag.value.trim()) newErrors[`flag_${index}_value`] = 'Flag value is required';
      if (flag.points <= 0) newErrors[`flag_${index}_points`] = 'Points must be greater than 0';
    });

    // Validate that at least one flag is required
    const hasRequiredFlag = flags.some(flag => flag.isRequired);
    if (flags.length > 0 && !hasRequiredFlag) {
      newErrors.flags = 'At least one flag must be marked as required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: prev.slug || generateSlug(name)
    }));
  };

  const addFlag = () => {
    setFlags([...flags, {
      name: '',
      value: '',
      type: 'custom',
      description: '',
      points: 100,
      isRequired: false
    }]);
  };

  const updateFlag = (index: number, field: keyof Flag, value: string | number | boolean) => {
    const newFlags = [...flags];
    newFlags[index] = { ...newFlags[index], [field]: value };
    setFlags(newFlags);
  };

  const removeFlag = (index: number) => {
    setFlags(flags.filter((_, i) => i !== index));
  };

  const addTopic = () => {
    setTopics([...topics, {
      title: '',
      description: '',
      imageUrl: '',
      order: topics.length
    }]);
  };

  const updateTopic = (index: number, field: keyof Topic, value: string | number) => {
    const updatedTopics = [...topics];
    updatedTopics[index] = { ...updatedTopics[index], [field]: value };
    setTopics(updatedTopics);
  };

  const removeTopic = (index: number) => {
    setTopics(topics.filter((_, i) => i !== index));
  };

  const handleTopicImageUpload = async (e: React.ChangeEvent<HTMLInputElement>, topicIndex: number) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setLoading(true);
      const result = await machineService.uploadTopicImage(file);
      updateTopic(topicIndex, 'imageUrl', result.imageUrl);
    } catch (error) {
      console.error('Error uploading topic image:', error);
      setErrors({ ...errors, [`topic_${topicIndex}_image`]: 'Failed to upload image' });
    } finally {
      setLoading(false);
    }
  };

  // Archive upload functions
  const handleArchiveUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['.zip', '.7z'];
    const fileExt = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    if (!allowedTypes.includes(fileExt)) {
      setErrors({ ...errors, archive: 'Only ZIP and 7z files are allowed' });
      return;
    }

    // Validate file size (500MB limit)
    if (file.size > 500 * 1024 * 1024) {
      setErrors({ ...errors, archive: 'File size must be less than 500MB' });
      return;
    }

    setArchiveFile(file);
    setErrors({ ...errors, archive: '' });
  };

  const uploadArchive = async () => {
    if (!archiveFile || !template?.id) return;

    try {
      setArchiveUploading(true);
      setErrors({ ...errors, archive: '' });

      await machineService.uploadMachineArchive(template.id, archiveFile);
      setArchiveUploaded(true);

      // Check image status after upload
      await checkImageStatus();
    } catch (error: any) {
      console.error('Error uploading archive:', error);
      setErrors({ ...errors, archive: error.response?.data?.message || 'Failed to upload archive' });
    } finally {
      setArchiveUploading(false);
    }
  };

  const checkImageStatus = async () => {
    if (!template?.id) return;

    try {
      const status = await machineService.getImageStatus(template.id);
      setImageStatus(status);
    } catch (error) {
      console.error('Error checking image status:', error);
    }
  };

  const loadSystemStatus = async () => {
    try {
      const status = await machineService.getSystemStatus();
      setSystemStatus(status);
    } catch (error) {
      console.error('Error loading system status:', error);
    }
  };

  const buildImage = async () => {
    if (!template?.id) return;

    try {
      setBuilding(true);
      setBuildLogs([]);
      setErrors({ ...errors, build: '' });

      const result = await machineService.buildMachineImage(template.id);
      setBuildLogs(result.buildLogs || ['Build completed successfully']);
      setShowBuildLogs(true);

      // Refresh image status
      await checkImageStatus();
    } catch (error: any) {
      console.error('Error building image:', error);
      setErrors({ ...errors, build: error.response?.data?.message || 'Failed to build image' });
      setBuildLogs([`Build failed: ${error.response?.data?.message || error.message}`]);
      setShowBuildLogs(true);
    } finally {
      setBuilding(false);
    }
  };

  const removeImage = async () => {
    if (!template?.id) return;

    try {
      await machineService.removeImage(template.id);
      await checkImageStatus();
    } catch (error: any) {
      console.error('Error removing image:', error);
      setErrors({ ...errors, build: error.response?.data?.message || 'Failed to remove image' });
    }
  };

  const addHint = () => {
    setHints([...hints, '']);
  };

  const updateHint = (index: number, value: string) => {
    const newHints = [...hints];
    newHints[index] = value;
    setHints(newHints);
  };

  const removeHint = (index: number) => {
    if (hints.length > 1) {
      setHints(hints.filter((_, i) => i !== index));
    }
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    try {
      const templateData: Partial<MachineTemplate> = {
        ...formData,
        machineType: 'docker', // All templates are Docker-based now
        dockerImage: '', // Will be built from uploaded archive
        flags: flags.filter(flag => flag.name.trim() !== ''),
        topics: topics.filter(topic => topic.title.trim() !== ''),
        hints: hints.filter(hint => hint.trim()),
        tags,
        exposedPorts: [], // Default empty for now
        rating: 0,
        solveCount: 0,
        releaseDate: new Date().toISOString(),
      };

      await onSave(templateData);
      onClose();
    } catch (error) {
      console.error('Error saving template:', error);
      setErrors({ submit: 'Failed to save template. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-xl border border-slate-700 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-700">
          <h2 className="text-2xl font-bold text-white">
            {template ? 'Edit Template' : 'Create New Template'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Machine Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  className={`w-full px-4 py-3 bg-slate-900/50 border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.name ? 'border-red-500' : 'border-slate-600'
                  }`}
                  placeholder="Enter machine name"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-400">{errors.name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Slug *
                </label>
                <input
                  type="text"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  className={`w-full px-4 py-3 bg-slate-900/50 border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.slug ? 'border-red-500' : 'border-slate-600'
                  }`}
                  placeholder="machine-slug"
                />
                {errors.slug && (
                  <p className="mt-1 text-sm text-red-400">{errors.slug}</p>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className={`w-full px-4 py-3 bg-slate-900/50 border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.description ? 'border-red-500' : 'border-slate-600'
                }`}
                placeholder="Describe the machine and what skills it tests"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-400">{errors.description}</p>
              )}
            </div>

            {/* Category, Difficulty, OS */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Category
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value as any }))}
                  className="w-full px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat.toUpperCase()}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Difficulty
                </label>
                <select
                  value={formData.difficulty}
                  onChange={(e) => setFormData(prev => ({ ...prev, difficulty: e.target.value as any }))}
                  className="w-full px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {difficulties.map(diff => (
                    <option key={diff} value={diff}>{diff.toUpperCase()}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Operating System
                </label>
                <select
                  value={formData.os}
                  onChange={(e) => setFormData(prev => ({ ...prev, os: e.target.value as any }))}
                  className="w-full px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {osOptions.map(os => (
                    <option key={os} value={os}>{os.toUpperCase()}</option>
                  ))}
                </select>
              </div>
            </div>



            {/* Docker Template Info - For new templates */}
            {!template?.id && (
              <div className="bg-blue-900/20 rounded-lg p-4 border border-blue-500/30">
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-blue-500/20 rounded-lg">
                    <Upload className="w-5 h-5 text-blue-400" />
                  </div>
                  <div>
                    <h4 className="text-blue-300 font-medium mb-1">Docker Template Setup</h4>
                    <p className="text-blue-200 text-sm mb-2">
                      After creating this template, you'll be able to upload a ZIP/7z archive containing your Dockerfile and application files.
                    </p>
                    <div className="text-xs text-blue-300">
                      <strong>Required files in archive:</strong>
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        <li>Dockerfile (container build instructions)</li>
                        <li>docker-compose.yml (service configuration)</li>
                        <li>machine/ directory (your application files)</li>
                      </ul>

                      {systemStatus && (
                        <div className="mt-3 p-2 bg-slate-800/50 rounded">
                          <strong>System Support:</strong>
                          <div className="flex items-center space-x-4 mt-1">
                            <span className={`text-xs ${systemStatus.zipSupported ? 'text-green-400' : 'text-red-400'}`}>
                              ZIP: {systemStatus.zipSupported ? '✓ Supported' : '✗ Not supported'}
                            </span>
                            <span className={`text-xs ${systemStatus.sevenZipSupported ? 'text-green-400' : 'text-yellow-400'}`}>
                              7z: {systemStatus.sevenZipSupported ? '✓ Supported' : '⚠ Requires installation'}
                            </span>
                          </div>
                          {!systemStatus.sevenZipSupported && systemStatus.installationInstructions && (
                            <details className="mt-2">
                              <summary className="text-xs text-yellow-400 cursor-pointer">Installation Instructions</summary>
                              <pre className="text-xs text-slate-300 mt-1 whitespace-pre-wrap">{systemStatus.installationInstructions}</pre>
                            </details>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Archive Upload Section - Only for existing templates */}
            {template?.id && (
              <div className="bg-slate-900/30 rounded-lg p-6 border border-slate-700">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-slate-200">Machine Template Archive</h3>
                  {imageStatus && (
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        imageStatus.imageExists ? 'bg-green-500' :
                        imageStatus.hasExtractedFiles ? 'bg-yellow-500' : 'bg-gray-500'
                      }`}></div>
                      <span className="text-sm text-slate-400">
                        {imageStatus.imageExists ? 'Image Built' :
                         imageStatus.hasExtractedFiles ? 'Files Extracted' : 'No Files'}
                      </span>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {/* File Upload */}
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Upload Archive (ZIP/7z)
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="file"
                        accept=".zip,.7z"
                        onChange={handleArchiveUpload}
                        className="hidden"
                        id="archive-upload"
                      />
                      <label
                        htmlFor="archive-upload"
                        className="flex items-center space-x-2 px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg cursor-pointer hover:bg-slate-700 transition-colors"
                      >
                        <Upload className="w-4 h-4 text-slate-400" />
                        <span className="text-slate-300">Choose File</span>
                      </label>
                      {archiveFile && (
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-slate-400">{archiveFile.name}</span>
                          <button
                            type="button"
                            onClick={uploadArchive}
                            disabled={archiveUploading}
                            className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {archiveUploading ? 'Uploading...' : 'Upload'}
                          </button>
                        </div>
                      )}
                    </div>
                    {errors.archive && (
                      <p className="mt-1 text-sm text-red-400">{errors.archive}</p>
                    )}
                  </div>

                  {/* Build Controls */}
                  {imageStatus && (
                    <div className="flex items-center justify-between p-4 bg-slate-800/50 rounded-lg">
                      <div>
                        <p className="text-sm text-slate-300">
                          {imageStatus.imageName}
                        </p>
                        <p className="text-xs text-slate-500">
                          {imageStatus.imageExists ? 'Image is built and ready' :
                           imageStatus.hasExtractedFiles ? 'Files extracted, ready to build' :
                           'Upload an archive to get started'}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {imageStatus.canBuild && (
                          <button
                            type="button"
                            onClick={buildImage}
                            disabled={building}
                            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {building ? 'Building...' : 'Build Image'}
                          </button>
                        )}
                        {imageStatus.imageExists && (
                          <button
                            type="button"
                            onClick={removeImage}
                            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                          >
                            Remove Image
                          </button>
                        )}
                        {buildLogs.length > 0 && (
                          <button
                            type="button"
                            onClick={() => setShowBuildLogs(!showBuildLogs)}
                            className="px-4 py-2 bg-slate-600 text-white rounded hover:bg-slate-700"
                          >
                            {showBuildLogs ? 'Hide Logs' : 'Show Logs'}
                          </button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Build Logs */}
                  {showBuildLogs && buildLogs.length > 0 && (
                    <div className="bg-black/50 rounded-lg p-4 max-h-64 overflow-y-auto">
                      <h4 className="text-sm font-medium text-slate-300 mb-2">Build Logs</h4>
                      <pre className="text-xs text-green-400 font-mono whitespace-pre-wrap">
                        {buildLogs.join('\n')}
                      </pre>
                    </div>
                  )}

                  {errors.build && (
                    <p className="text-sm text-red-400">{errors.build}</p>
                  )}
                </div>
              </div>
            )}

            {/* Resource Requirements */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  RAM (MB)
                </label>
                <input
                  type="number"
                  value={formData.requiredRAM}
                  onChange={(e) => setFormData(prev => ({ ...prev, requiredRAM: parseInt(e.target.value) }))}
                  min="128"
                  max="8192"
                  className="w-full px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  CPU Cores
                </label>
                <input
                  type="number"
                  value={formData.requiredCPU}
                  onChange={(e) => setFormData(prev => ({ ...prev, requiredCPU: parseInt(e.target.value) }))}
                  min="1"
                  max="8"
                  className="w-full px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Max Instances
                </label>
                <input
                  type="number"
                  value={formData.maxInstances}
                  onChange={(e) => setFormData(prev => ({ ...prev, maxInstances: parseInt(e.target.value) }))}
                  min="1"
                  max="100"
                  className="w-full px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Author */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Author Name *
              </label>
              <input
                type="text"
                value={formData.authorName}
                onChange={(e) => setFormData(prev => ({ ...prev, authorName: e.target.value }))}
                className={`w-full px-4 py-3 bg-slate-900/50 border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.authorName ? 'border-red-500' : 'border-slate-600'
                }`}
                placeholder="Author or creator name"
              />
              {errors.authorName && (
                <p className="mt-1 text-sm text-red-400">{errors.authorName}</p>
              )}
            </div>

            {/* Flags */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <label className="block text-sm font-medium text-slate-300">
                  Flags *
                </label>
                <button
                  type="button"
                  onClick={addFlag}
                  className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  <span>Add Flag</span>
                </button>
              </div>
              
              {errors.flags && (
                <p className="mb-4 text-sm text-red-400">{errors.flags}</p>
              )}

              <div className="space-y-4">
                {flags.map((flag, index) => {
                  const IconComponent = flagTypeIcons[flag.type];
                  return (
                    <div key={index} className="bg-slate-900/30 rounded-lg p-4 border border-slate-700">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-xs text-slate-400 mb-1">Flag Name</label>
                          <input
                            type="text"
                            value={flag.name}
                            onChange={(e) => updateFlag(index, 'name', e.target.value)}
                            className={`w-full px-3 py-2 bg-slate-800 border rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 ${
                              errors[`flag_${index}_name`] ? 'border-red-500' : 'border-slate-600'
                            }`}
                            placeholder="user.txt, root.txt, etc."
                          />
                          {errors[`flag_${index}_name`] && (
                            <p className="mt-1 text-xs text-red-400">{errors[`flag_${index}_name`]}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-xs text-slate-400 mb-1">Flag Value</label>
                          <input
                            type="text"
                            value={flag.value}
                            onChange={(e) => updateFlag(index, 'value', e.target.value)}
                            className={`w-full px-3 py-2 bg-slate-800 border rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 ${
                              errors[`flag_${index}_value`] ? 'border-red-500' : 'border-slate-600'
                            }`}
                            placeholder="HTB{example_flag_value}"
                          />
                          {errors[`flag_${index}_value`] && (
                            <p className="mt-1 text-xs text-red-400">{errors[`flag_${index}_value`]}</p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <label className="block text-xs text-slate-400 mb-1">Flag Type</label>
                          <div className="relative">
                            <select
                              value={flag.type}
                              onChange={(e) => updateFlag(index, 'type', e.target.value as Flag['type'])}
                              className="w-full px-3 py-2 pl-10 bg-slate-800 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none"
                            >
                              <option value="custom">Custom</option>
                              <option value="user">User Flag</option>
                              <option value="root">Root Flag</option>
                              <option value="www-data">WWW-Data Flag</option>
                            </select>
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                              <IconComponent className="w-4 h-4 text-slate-400" />
                            </div>
                          </div>
                        </div>

                        <div>
                          <label className="block text-xs text-slate-400 mb-1">Points</label>
                          <input
                            type="number"
                            value={flag.points}
                            onChange={(e) => updateFlag(index, 'points', parseInt(e.target.value))}
                            min="1"
                            className={`w-full px-3 py-2 bg-slate-800 border rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 ${
                              errors[`flag_${index}_points`] ? 'border-red-500' : 'border-slate-600'
                            }`}
                          />
                          {errors[`flag_${index}_points`] && (
                            <p className="mt-1 text-xs text-red-400">{errors[`flag_${index}_points`]}</p>
                          )}
                        </div>

                        <div className="flex items-center">
                          <label className="flex items-center space-x-2 mt-6">
                            <input
                              type="checkbox"
                              checked={flag.isRequired}
                              onChange={(e) => updateFlag(index, 'isRequired', e.target.checked)}
                              className="w-4 h-4 text-blue-600 bg-slate-800 border-slate-600 rounded focus:ring-blue-500"
                            />
                            <span className="text-sm text-slate-300">Required</span>
                          </label>
                        </div>

                        <div className="flex items-end justify-end">
                          <button
                            type="button"
                            onClick={() => removeFlag(index)}
                            className="p-2 text-red-400 hover:bg-red-400/10 rounded transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs text-slate-400 mb-1">Description (Optional)</label>
                        <textarea
                          value={flag.description}
                          onChange={(e) => updateFlag(index, 'description', e.target.value)}
                          rows={2}
                          className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                          placeholder="Optional description for this flag"
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Topics */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <label className="block text-sm font-medium text-slate-300">
                  Topics & Hints (Optional)
                </label>
                <button
                  type="button"
                  onClick={addTopic}
                  className="flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  <span>Add Topic</span>
                </button>
              </div>

              <div className="space-y-4">
                {topics.map((topic, index) => (
                  <div key={index} className="bg-slate-900/30 rounded-lg p-4 border border-slate-700">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-xs text-slate-400 mb-1">Topic Title</label>
                        <input
                          type="text"
                          value={topic.title}
                          onChange={(e) => updateTopic(index, 'title', e.target.value)}
                          className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="e.g., Initial Enumeration, Privilege Escalation"
                        />
                      </div>

                      <div>
                        <label className="block text-xs text-slate-400 mb-1">Order</label>
                        <input
                          type="number"
                          value={topic.order}
                          onChange={(e) => updateTopic(index, 'order', parseInt(e.target.value) || 0)}
                          min="0"
                          className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                    </div>

                    <div className="mb-4">
                      <label className="block text-xs text-slate-400 mb-1">Description</label>
                      <textarea
                        value={topic.description}
                        onChange={(e) => updateTopic(index, 'description', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                        placeholder="Detailed description or hint for this topic"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <label className="block text-xs text-slate-400 mb-1">Image (Optional)</label>
                        <div className="flex items-center space-x-3">
                          <label className="flex items-center space-x-2 px-3 py-2 bg-slate-700 hover:bg-slate-600 rounded cursor-pointer transition-colors">
                            <Upload className="w-4 h-4" />
                            <span className="text-sm">Upload Image</span>
                            <input
                              type="file"
                              accept="image/*"
                              onChange={(e) => handleTopicImageUpload(e, index)}
                              className="hidden"
                            />
                          </label>
                          {topic.imageUrl && (
                            <div className="flex items-center space-x-2">
                              <img
                                src={getImageUrl(topic.imageUrl)}
                                alt="Topic preview"
                                className="w-8 h-8 object-cover rounded"
                              />
                              <button
                                type="button"
                                onClick={() => updateTopic(index, 'imageUrl', '')}
                                className="text-red-400 hover:text-red-300 text-sm"
                              >
                                Remove
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeTopic(index)}
                        className="ml-4 p-2 text-red-400 hover:bg-red-400/10 rounded transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Hints */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <label className="block text-sm font-medium text-slate-300">
                  Hints (Optional)
                </label>
                <button
                  type="button"
                  onClick={addHint}
                  className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  <span>Add Hint</span>
                </button>
              </div>

              <div className="space-y-3">
                {hints.map((hint, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="flex-1">
                      <input
                        type="text"
                        value={hint}
                        onChange={(e) => updateHint(index, e.target.value)}
                        className="w-full px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder={`Hint ${index + 1}`}
                      />
                    </div>
                    {hints.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeHint(index)}
                        className="p-2 text-red-400 hover:bg-red-400/10 rounded transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Tags
              </label>
              <div className="flex items-center space-x-3 mb-3">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  className="flex-1 px-4 py-3 bg-slate-900/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add a tag and press Enter"
                />
                <button
                  type="button"
                  onClick={addTag}
                  className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Add
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-sm"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-2 text-blue-400 hover:text-blue-300"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>

            {/* Active Status */}
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                className="w-4 h-4 text-blue-600 bg-slate-900 border-slate-600 rounded focus:ring-blue-500 focus:ring-2"
              />
              <label htmlFor="isActive" className="text-sm text-slate-300">
                Make this template active and available to users
              </label>
            </div>

            {errors.submit && (
              <div className="flex items-center space-x-2 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                <AlertCircle className="w-5 h-5 text-red-400" />
                <span className="text-red-400">{errors.submit}</span>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-4 p-6 border-t border-slate-700 bg-slate-800/50">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 text-slate-400 hover:text-white transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading && <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />}
              <span>{template ? 'Update Template' : 'Create Template'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
