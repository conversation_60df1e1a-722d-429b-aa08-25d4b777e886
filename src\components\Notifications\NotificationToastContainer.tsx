import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NotificationToast } from './NotificationToast';
import { useNotifications } from '../../contexts/NotificationsContext';
import { Notification } from '../../services/notifications';

interface ToastNotification extends Notification {
  id: string;
  timestamp: number;
}

export function NotificationToastContainer() {
  const { notifications } = useNotifications();
  const [toastNotifications, setToastNotifications] = useState<ToastNotification[]>([]);
  const [lastNotificationCount, setLastNotificationCount] = useState(0);

  // Monitor for new notifications
  useEffect(() => {
    if (notifications.length > lastNotificationCount && lastNotificationCount > 0) {
      // New notification(s) received
      const newNotifications = notifications.slice(0, notifications.length - lastNotificationCount);
      
      newNotifications.forEach((notification, index) => {
        // Only show toast for unread notifications
        if (!notification.isRead) {
          const toastNotification: ToastNotification = {
            ...notification,
            id: `${notification._id}-${Date.now()}-${index}`,
            timestamp: Date.now(),
          };

          setToastNotifications(prev => {
            // Limit to 5 toasts maximum
            const updated = [toastNotification, ...prev].slice(0, 5);
            return updated;
          });
        }
      });
    }
    
    setLastNotificationCount(notifications.length);
  }, [notifications, lastNotificationCount]);

  // Listen for real-time notifications from the context
  useEffect(() => {
    const handleNewNotification = (event: CustomEvent<Notification>) => {
      const notification = event.detail;
      const toastNotification: ToastNotification = {
        ...notification,
        id: `${notification._id}-${Date.now()}`,
        timestamp: Date.now(),
      };

      setToastNotifications(prev => {
        // Limit to 5 toasts maximum
        const updated = [toastNotification, ...prev].slice(0, 5);
        return updated;
      });
    };

    // Listen for custom events from the notifications context
    window.addEventListener('new-notification-toast', handleNewNotification as EventListener);
    window.addEventListener('first-blood-notification-toast', handleNewNotification as EventListener);

    return () => {
      window.removeEventListener('new-notification-toast', handleNewNotification as EventListener);
      window.removeEventListener('first-blood-notification-toast', handleNewNotification as EventListener);
    };
  }, []);

  const removeToast = (id: string) => {
    setToastNotifications(prev => prev.filter(toast => toast.id !== id));
  };

  return (
    <div className="fixed top-4 right-4 z-[9999] space-y-3 pointer-events-none">
      <AnimatePresence mode="popLayout">
        {toastNotifications.map((toast, index) => (
          <motion.div
            key={toast.id}
            layout
            initial={{ opacity: 0, y: -50, scale: 0.8 }}
            animate={{ 
              opacity: 1, 
              y: 0, 
              scale: 1,
              transition: { delay: index * 0.1 }
            }}
            exit={{ 
              opacity: 0, 
              x: 400, 
              scale: 0.8,
              transition: { duration: 0.3 }
            }}
            className="pointer-events-auto"
          >
            <NotificationToast
              notification={toast}
              onClose={() => removeToast(toast.id)}
              autoClose={true}
              duration={toast.type === 'first_blood' ? 8000 : 5000} // First blood stays longer
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}