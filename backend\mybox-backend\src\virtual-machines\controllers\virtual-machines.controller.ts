import { 
  Controller, 
  Get, 
  Post, 
  Delete, 
  Patch,
  Param, 
  Body, 
  Query,
  UseGuards,
  Request,
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { MachineInstanceService, CreateInstanceDto, ExtendInstanceDto } from '../services/machine-instance.service';
import { FlagSubmissionService, FlagSubmissionDto } from '../services/flag-submission.service';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('Virtual Machines')
@ApiBearerAuth()
@Controller('machines')
@UseGuards(JwtAuthGuard)
export class VirtualMachinesController {
  private readonly logger = new Logger(VirtualMachinesController.name);

  constructor(
    private readonly machineInstanceService: MachineInstanceService,
    private readonly flagSubmissionService: FlagSubmissionService,
  ) {}

  /**
   * Get available machine templates
   */
  @Get()
  @ApiOperation({ summary: 'Get available machine templates' })
  @ApiResponse({ status: 200, description: 'List of available machines' })
  async getAvailableTemplates(
    @Request() req: any,
    @Query('category') category?: string,
    @Query('difficulty') difficulty?: string,
    @Query('search') search?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.machineInstanceService.getAvailableTemplates(req.user.userId, {
      category,
      difficulty,
      search,
      page: page ? parseInt(page.toString()) : undefined,
      limit: limit ? parseInt(limit.toString()) : undefined,
    });
  }

  /**
   * Get machine template details
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get machine template details' })
  @ApiResponse({ status: 200, description: 'Machine template details' })
  async getTemplateDetails(@Request() req: any, @Param('id') templateId: string) {
    return this.machineInstanceService.getTemplateDetails(templateId, req.user.userId);
  }

  /**
   * Spawn a new machine instance
   */
  @Post(':id/spawn')
  @ApiOperation({ summary: 'Spawn a new machine instance' })
  @ApiResponse({ status: 201, description: 'Machine instance created successfully' })
  @HttpCode(HttpStatus.CREATED)
  async spawnMachine(
    @Request() req: any,
    @Param('id') templateId: string,
    @Body() body: { teamId?: string; extendedRuntime?: number } = {}
  ) {
    const createDto: CreateInstanceDto = {
      templateId,
      teamId: body.teamId,
      extendedRuntime: body.extendedRuntime,
    };

    this.logger.log(`Spawning machine ${templateId} for user ${req.user.userId}`);
    return this.machineInstanceService.createInstance(req.user.userId, createDto);
  }

  /**
   * Get user's active instances
   */
  @Get('instances/my')
  @ApiOperation({ summary: 'Get user\'s active machine instances' })
  @ApiResponse({ status: 200, description: 'List of user\'s active instances' })
  async getUserInstances(@Request() req: any) {
    return this.machineInstanceService.getUserInstances(req.user.userId);
  }

  /**
   * Get team instances
   */
  @Get('teams/:teamId/instances')
  @ApiOperation({ summary: 'Get team\'s shared machine instances' })
  @ApiResponse({ status: 200, description: 'List of team\'s shared instances' })
  async getTeamInstances(@Request() req: any, @Param('teamId') teamId: string) {
    return this.machineInstanceService.getTeamInstances(req.user.userId, teamId);
  }

  /**
   * Get instance details
   */
  @Get('instances/:id')
  @ApiOperation({ summary: 'Get machine instance details' })
  @ApiResponse({ status: 200, description: 'Machine instance details' })
  async getInstanceDetails(@Request() req: any, @Param('id') instanceId: string) {
    return this.machineInstanceService.getInstanceDetails(instanceId, req.user.userId);
  }

  /**
   * Get instance connection information
   */
  @Get('instances/:id/connection')
  @ApiOperation({ summary: 'Get machine instance connection information' })
  @ApiResponse({ status: 200, description: 'Connection details for the instance' })
  async getConnectionInfo(@Request() req: any, @Param('id') instanceId: string) {
    return this.machineInstanceService.getConnectionInfo(instanceId, req.user.userId);
  }

  /**
   * Terminate a machine instance
   */
  @Delete('instances/:id')
  @ApiOperation({ summary: 'Terminate a machine instance' })
  @ApiResponse({ status: 200, description: 'Machine instance terminated successfully' })
  async terminateInstance(@Request() req: any, @Param('id') instanceId: string) {
    await this.machineInstanceService.terminateInstance(instanceId, req.user.userId);
    return { message: 'Machine instance terminated successfully' };
  }

  /**
   * Restart a machine instance
   */
  @Post('instances/:id/restart')
  @ApiOperation({ summary: 'Restart a machine instance' })
  @ApiResponse({ status: 200, description: 'Machine instance restarted successfully' })
  async restartInstance(@Request() req: any, @Param('id') instanceId: string) {
    await this.machineInstanceService.restartInstance(instanceId, req.user.userId);
    return { message: 'Machine instance restarted successfully' };
  }

  /**
   * Extend machine instance runtime
   */
  @Patch('instances/:id/extend')
  @ApiOperation({ summary: 'Extend machine instance runtime' })
  @ApiResponse({ status: 200, description: 'Machine instance runtime extended successfully' })
  async extendInstance(
    @Request() req: any,
    @Param('id') instanceId: string,
    @Body() extendDto: ExtendInstanceDto
  ) {
    await this.machineInstanceService.extendInstance(instanceId, req.user.userId, extendDto);
    return { message: 'Machine instance runtime extended successfully' };
  }

  /**
   * Share instance with team
   */
  @Post('instances/:id/share')
  @ApiOperation({ summary: 'Share machine instance with team' })
  @ApiResponse({ status: 200, description: 'Machine instance shared with team successfully' })
  async shareInstanceWithTeam(
    @Request() req: any,
    @Param('id') instanceId: string,
    @Body() body: { teamId: string }
  ) {
    await this.machineInstanceService.shareInstanceWithTeam(instanceId, req.user.userId, body.teamId);
    return { message: 'Machine instance shared with team successfully' };
  }

  /**
   * Submit a flag for a machine instance
   */
  @Post('instances/:id/submit')
  @ApiOperation({ summary: 'Submit a flag for a machine instance' })
  @ApiResponse({ status: 200, description: 'Flag submission result' })
  async submitFlag(
    @Request() req: any,
    @Param('id') instanceId: string,
    @Body() submissionDto: { flagName: string; flag: string }
  ) {
    const flagSubmission: FlagSubmissionDto = {
      instanceId,
      flagName: submissionDto.flagName,
      flag: submissionDto.flag,
    };

    return this.flagSubmissionService.submitFlag(req.user.userId, flagSubmission);
  }

  /**
   * Get available flags for an instance
   */
  @Get('instances/:id/flags')
  @ApiOperation({ summary: 'Get available flags for a machine instance' })
  @ApiResponse({ status: 200, description: 'List of available flags and their status' })
  async getInstanceFlags(@Request() req: any, @Param('id') instanceId: string) {
    return this.flagSubmissionService.getInstanceFlags(instanceId, req.user.userId);
  }

  /**
   * Get user's submission history
   */
  @Get('submissions/my')
  @ApiOperation({ summary: 'Get user\'s flag submission history' })
  @ApiResponse({ status: 200, description: 'User\'s submission history' })  async getUserSubmissionHistory(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('correctOnly') correctOnly?: string,
  ) {
    return this.flagSubmissionService.getUserSubmissionHistory(req.user.userId, {
      page: page ? parseInt(page.toString()) : undefined,
      limit: limit ? parseInt(limit.toString()) : undefined,
      correctOnly: correctOnly === 'true',
    });
  }

  /**
   * Get team submission statistics
   */
  @Get('teams/:teamId/stats')
  @ApiOperation({ summary: 'Get team submission statistics' })
  @ApiResponse({ status: 200, description: 'Team submission statistics' })
  async getTeamSubmissionStats(@Param('teamId') teamId: string) {
    return this.flagSubmissionService.getTeamSubmissionStats(teamId);
  }

  /**
   * Get machine solve statistics
   */
  @Get(':id/stats')
  @ApiOperation({ summary: 'Get machine solve statistics' })
  @ApiResponse({ status: 200, description: 'Machine solve statistics' })
  async getMachineSolveStats(@Param('id') templateId: string) {
    return this.flagSubmissionService.getMachineSolveStats(templateId);
  }

  /**
   * Get global machine leaderboard
   */
  @Get('leaderboard/global')
  @ApiOperation({ summary: 'Get global machine solving leaderboard' })
  @ApiResponse({ status: 200, description: 'Global leaderboard data' })
  async getGlobalLeaderboard(@Query('timeframe') timeframe?: 'daily' | 'weekly' | 'monthly' | 'all') {
    return this.flagSubmissionService.getGlobalLeaderboard(timeframe);
  }

  /**
   * Get machine management statistics (for admin dashboard)
   */
  @Get('admin/statistics')
  @ApiOperation({ summary: 'Get machine management statistics' })
  @ApiResponse({ status: 200, description: 'Machine management statistics' })
  async getMachineStatistics() {
    return this.machineInstanceService.getMachineStatistics();
  }
}
