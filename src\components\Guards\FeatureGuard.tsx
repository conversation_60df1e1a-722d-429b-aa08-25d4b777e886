import React, { ReactNode } from 'react';
import { usePlatformSettings } from '../../contexts/PlatformSettingsContext';
import { useApp } from '../../contexts/AppContext';
import { PublicSettings } from '../../services/publicSettings';
import { MaintenancePage } from '../Maintenance/MaintenancePage';
import { Shield, Users, AlertTriangle } from 'lucide-react';

interface FeatureGuardProps {
  children: ReactNode;
  feature?: keyof PublicSettings;
  fallback?: ReactNode;
  showMaintenanceForAll?: boolean;
}

interface FeatureDisabledPageProps {
  feature: string;
  icon: ReactNode;
  title: string;
  description: string;
}

function FeatureDisabledPage({ feature, icon, title, description }: FeatureDisabledPageProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Icon */}
        <div className="w-32 h-32 mx-auto bg-gradient-to-r from-red-500/20 to-orange-500/20 rounded-full flex items-center justify-center border border-red-500/30 mb-8">
          {icon}
        </div>

        {/* Main message */}
        <div className="mb-8">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-red-400 via-orange-400 to-red-600 bg-clip-text text-transparent mb-4">
            {title}
          </h1>
          <p className="text-xl text-red-200/80 mb-6">
            {description}
          </p>
          <p className="text-lg text-red-300/70">
            This feature has been temporarily disabled by the administrator.
          </p>
        </div>

        {/* Status card */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6 mb-8">
          <div className="flex items-center justify-center mb-3">
            <AlertTriangle className="w-8 h-8 text-orange-400" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">Feature Status</h3>
          <p className="text-orange-200">Currently Disabled</p>
        </div>

        {/* Additional info */}
        <div className="text-sm text-red-300/60">
          <p className="mb-2">
            Please contact the administrator if you believe this is an error.
          </p>
          <p>
            You can continue using other features of the platform.
          </p>
        </div>

        {/* Back button */}
        <div className="mt-8">
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-medium rounded-xl transition-all duration-300"
          >
            <span>Go Back</span>
          </button>
        </div>
      </div>
    </div>
  );
}

export function FeatureGuard({
  children,
  feature,
  fallback,
  showMaintenanceForAll = true
}: FeatureGuardProps) {
  const { settings, loading, refreshSettings } = usePlatformSettings();
  const { state } = useApp();

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-3 text-purple-200/80">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500/30 border-t-purple-400"></div>
          <span className="text-lg">Loading...</span>
        </div>
      </div>
    );
  }

  // Show maintenance page if enabled and showMaintenanceForAll is true
  // But allow admin users to bypass maintenance mode
  if (showMaintenanceForAll && settings?.maintenanceMode) {
    const isAdmin = state.auth.user?.role === 'admin';

    if (!isAdmin) {
      return <MaintenancePage onRetry={refreshSettings} />;
    }
  }

  // If no specific feature check is required, render children
  if (!feature) {
    return <>{children}</>;
  }

  // Check if the specific feature is enabled
  const isFeatureEnabled = settings?.[feature];

  if (!isFeatureEnabled) {
    // Show custom fallback if provided
    if (fallback) {
      return <>{fallback}</>;
    }

    // Show default disabled page based on feature
    switch (feature) {
      case 'enableTeams':
        return (
          <FeatureDisabledPage
            feature="teams"
            icon={<Users className="w-16 h-16 text-red-400" />}
            title="Teams Disabled"
            description="Team functionality is currently unavailable."
          />
        );
      
      case 'allowRegistration':
        return (
          <FeatureDisabledPage
            feature="registration"
            icon={<Shield className="w-16 h-16 text-red-400" />}
            title="Registration Disabled"
            description="New user registration is currently disabled."
          />
        );
      
      default:
        return (
          <FeatureDisabledPage
            feature={feature}
            icon={<AlertTriangle className="w-16 h-16 text-red-400" />}
            title="Feature Disabled"
            description={`The ${feature} feature is currently disabled.`}
          />
        );
    }
  }

  // Feature is enabled, render children
  return <>{children}</>;
}

// Convenience components for specific features
export function TeamsGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <FeatureGuard feature="enableTeams" fallback={fallback}>
      {children}
    </FeatureGuard>
  );
}

export function RegistrationGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <FeatureGuard feature="allowRegistration" fallback={fallback}>
      {children}
    </FeatureGuard>
  );
}

export function MaintenanceGuard({ children }: { children: ReactNode }) {
  return (
    <FeatureGuard showMaintenanceForAll={true}>
      {children}
    </FeatureGuard>
  );
}