import React, { useState } from 'react';
import { Challenge } from '../../types';
import { useApp } from '../../contexts/AppContext';
import { 
  Flag, 
  Users, 
  Clock, 
  Award, 
  CheckCircle, 
  X,
  Lightbulb,
  Send,
  Calendar,
  User,
  Server,
  Download,
  Trophy,
  Star,
  FileText,
  Zap
} from 'lucide-react';

interface ChallengeCardProps {
  challenge: Challenge;
}

const difficultyColors = {
  easy: 'text-emerald-400 bg-emerald-500/20 border-emerald-500/30',
  medium: 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30',
  hard: 'text-orange-400 bg-orange-500/20 border-orange-500/30',
  insane: 'text-red-400 bg-red-500/20 border-red-500/30'
};

// Remove hardcoded category colors - will use dynamic categories from context

export function ChallengeCard({ challenge }: ChallengeCardProps) {
  const { submitFlag, state } = useApp();
  const currentUser = state.auth.user;
  const categories = state.categories;
  const [showDetails, setShowDetails] = useState(false);
  const [flag, setFlag] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showHints, setShowHints] = useState(false);
  const [submitResult, setSubmitResult] = useState<'success' | 'error' | null>(null);

  // Helper function to get category style
  const getCategoryStyle = (categoryName: string) => {
    const category = categories.find(cat => cat.name === categoryName);
    if (category) {
      return {
        backgroundColor: `${category.color}20`, // Add 20% opacity
        color: category.color,
        borderColor: `${category.color}40`
      };
    }
    // Fallback for unknown categories
    return {
      backgroundColor: '#6B728020',
      color: '#9CA3AF',
      borderColor: '#6B728040'
    };
  };

  // Helper function to get category display name
  const getCategoryDisplayName = (categoryName: string) => {
    const category = categories.find(cat => cat.name === categoryName);
    return category?.displayName || categoryName;
  };

  // Check if challenge is solved by teammate and if they're still in team
  const solvedByTeammate = challenge.solvedByTeammate;
  const canSubmit = !challenge.isSolved && (!solvedByTeammate || !solvedByTeammate.isStillInTeam);

  const handleSubmitFlag = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitResult(null);

    try {
      const isCorrect = await submitFlag(challenge.id, flag);
      setSubmitResult(isCorrect ? 'success' : 'error');
      if (isCorrect) {
        setFlag('');
        setTimeout(() => setShowDetails(false), 2000);
      }
    } catch (error) {
      setSubmitResult('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDownload = async (filePath: string, fileName: string) => {
    try {
      const token = localStorage.getItem('mybox_token');
      
      // Clean the file path - the backend expects path relative to uploads directory
      let cleanPath = filePath.startsWith('/') ? filePath.substring(1) : filePath;
      
      // If the path starts with 'uploads/', remove it since backend adds it automatically
      if (cleanPath.startsWith('uploads/')) {
        cleanPath = cleanPath.substring('uploads/'.length);
      }
      
      // The backend expects /admin/files/* for downloads (relative to uploads directory)
      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/files/${cleanPath}`, {
        method: 'GET',
        headers: {
          ...(token && { Authorization: `Bearer ${token}` }),
        },
      });

      if (!response.ok) {
        throw new Error('Failed to download file');
      }

      // Create blob from response
      const blob = await response.blob();
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
      // You could add a toast notification here
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Unknown';
    }
  };

  const hasMultipleFlags = challenge.flags && challenge.flags.length > 0;
  const hasFiles = challenge.files && challenge.files.length > 0;

  // Check if current user got first blood
  const isCurrentUserFirstBlood = () => {
    if (!currentUser) return false;

    // Debug first blood data
    console.log(`🔍 Checking first blood for challenge "${challenge.title}":`, {
      challengeFirstBlood: challenge.firstBlood,
      challengeFlagsFirstBlood: challenge.flagsFirstBlood,
      currentUserId: currentUser.id
    });

    // Check single flag first blood
    if (challenge.firstBlood) {
      console.log(`🔍 Comparing single flag first blood:`, {
        firstBloodUserId: challenge.firstBlood.userId,
        firstBloodUserIdType: typeof challenge.firstBlood.userId,
        currentUserId: currentUser.id,
        currentUserIdType: typeof currentUser.id,
        areEqual: challenge.firstBlood.userId === currentUser.id,
        stringComparison: String(challenge.firstBlood.userId) === String(currentUser.id)
      });

      // Handle both string and ObjectId formats for backward compatibility
      let firstBloodUserId: string;

      // If it's an ObjectId object, extract the string value
      if (typeof challenge.firstBlood.userId === 'object' && challenge.firstBlood.userId !== null) {
        firstBloodUserId = (challenge.firstBlood.userId as any).toString();
      } else {
        firstBloodUserId = String(challenge.firstBlood.userId);
      }

      const currentUserId = String(currentUser.id);

      console.log(`🔍 Final comparison: "${firstBloodUserId}" === "${currentUserId}" = ${firstBloodUserId === currentUserId}`);

      if (firstBloodUserId === currentUserId) {
        console.log(`✅ User ${currentUser.id} has first blood on challenge "${challenge.title}"`);
        return true;
      }
    }

    // Check multi-flag first blood
    if (challenge.flagsFirstBlood) {
      console.log(`🔍 Checking multi-flag first blood:`, challenge.flagsFirstBlood);
      const hasFirstBlood = Object.values(challenge.flagsFirstBlood).some(
        firstBlood => {
          console.log(`🔍 Comparing multi-flag first blood:`, {
            firstBloodUserId: firstBlood.userId,
            firstBloodUserIdType: typeof firstBlood.userId,
            currentUserId: currentUser.id,
            currentUserIdType: typeof currentUser.id,
            areEqual: firstBlood.userId === currentUser.id,
            stringComparison: String(firstBlood.userId) === String(currentUser.id)
          });
          // Handle both string and ObjectId formats for backward compatibility
          const firstBloodUserId = String(firstBlood.userId);
          const currentUserId = String(currentUser.id);
          return firstBloodUserId === currentUserId;
        }
      );
      if (hasFirstBlood) {
        console.log(`✅ User ${currentUser.id} has first blood on multi-flag challenge "${challenge.title}"`);
        return true;
      }
    }

    return false;
  };

  const hasFirstBlood = challenge.firstBlood || (challenge.flagsFirstBlood && Object.keys(challenge.flagsFirstBlood).length > 0);
  const showFirstBloodBadge = hasFirstBlood && isCurrentUserFirstBlood();

  return (
    <>
      <div className={`group relative backdrop-blur-sm rounded-2xl p-6 transition-all duration-300 transform hover:-translate-y-1 ${
        showFirstBloodBadge
          ? 'bg-gradient-to-br from-yellow-900/40 to-amber-900/30 border-2 border-yellow-500/60 hover:border-yellow-400/80 shadow-lg shadow-yellow-500/20 hover:shadow-yellow-500/30'
          : 'bg-gradient-to-br from-slate-900/80 to-slate-800/50 border border-slate-700/50 hover:border-emerald-500/30 hover:shadow-lg hover:shadow-emerald-500/10'
      }`}>
        {/* Challenge solved indicator */}
        {challenge.isSolved && (
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center z-10">
            <CheckCircle className="w-5 h-5 text-white" />
          </div>
        )}

        {/* First blood indicator - only show if current user got first blood */}
        {showFirstBloodBadge && (
          <div className="absolute top-3 right-3 z-20">
            <div className="flex items-center space-x-1 px-3 py-1.5 bg-gradient-to-r from-yellow-500/30 to-amber-500/30 border border-yellow-400/50 rounded-full shadow-lg shadow-yellow-500/20 animate-pulse">
              <Trophy className="w-4 h-4 text-yellow-300" />
              <span className="text-sm text-yellow-200 font-bold">First Blood!</span>
            </div>
          </div>
        )}

        {/* Category icon and header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div
              className={`p-3 rounded-xl border ${
                showFirstBloodBadge
                  ? 'bg-yellow-500/20 text-yellow-400 shadow-lg shadow-yellow-500/20 border-yellow-500/20'
                  : 'border-current/20'
              }`}
              style={!showFirstBloodBadge ? getCategoryStyle(challenge.category) : undefined}
            >
              <Flag className="w-6 h-6" />
            </div>
            <div className="flex-1">
              <h3 className={`font-bold text-lg leading-tight transition-colors ${
                showFirstBloodBadge
                  ? 'text-yellow-300 group-hover:text-yellow-200 drop-shadow-[0_0_8px_rgba(251,191,36,0.5)]'
                  : 'text-white group-hover:text-emerald-400'
              }`}>
                {challenge.title}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-sm text-slate-400 capitalize">{challenge.category}</span>
                {hasMultipleFlags && (
                  <div className="flex items-center space-x-1 px-2 py-0.5 bg-purple-500/20 rounded-full">
                    <Zap className="w-3 h-3 text-purple-400" />
                    <span className="text-xs text-purple-400 font-medium">Multi-Flag</span>
                  </div>
                )}
                {challenge.requiresServer && (
                  <div className="flex items-center space-x-1 px-2 py-0.5 bg-blue-500/20 rounded-full">
                    <Server className="w-3 h-3 text-blue-400" />
                    <span className="text-xs text-blue-400 font-medium">Server</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Description */}
        <p className="text-sm text-slate-300 mb-4 line-clamp-3 leading-relaxed">
          {challenge.description}
        </p>

        {/* Challenge stats row */}
        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className={`flex items-center justify-between p-3 rounded-lg border ${
            showFirstBloodBadge
              ? 'bg-yellow-500/10 border-yellow-500/30'
              : 'bg-slate-800/30 border-slate-700/50'
          }`}>
            <div className="flex items-center space-x-2">
              <Award className={`w-4 h-4 ${showFirstBloodBadge ? 'text-yellow-400' : 'text-emerald-400'}`} />
              <span className="text-xs text-slate-400">Points</span>
            </div>
            <span className={`text-sm font-bold ${showFirstBloodBadge ? 'text-yellow-400' : 'text-emerald-400'}`}>
              {challenge.points}
            </span>
          </div>
          <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg border border-slate-700/50">
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-slate-400" />
              <span className="text-xs text-slate-400">Solves</span>
            </div>
            <span className="text-sm font-bold text-white">{challenge.solves}</span>
          </div>
        </div>

        {/* Difficulty and release info */}
        <div className="flex items-center justify-between mb-4">
          <div className={`px-3 py-1.5 rounded-lg text-xs font-medium border ${difficultyColors[challenge.difficulty]}`}>
            <Star className="w-3 h-3 inline mr-1" />
            {challenge.difficulty.toUpperCase()}
          </div>
          
          <div className="flex items-center space-x-1 text-xs text-slate-400">
            <Calendar className="w-3 h-3" />
            <span>{formatDate(challenge.releaseDate)}</span>
          </div>
        </div>

        {/* Author and additional info */}
        <div className="flex items-center justify-between mb-4 text-xs">
          <div className="flex items-center space-x-1 text-slate-400">
            <User className="w-3 h-3" />
            <span>by {challenge.authorName || challenge.author || 'Anonymous'}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            {hasFiles && (
              <div className="flex items-center space-x-1 px-2 py-1 bg-orange-500/20 rounded-full">
                <Download className="w-3 h-3 text-orange-400" />
                <span className="text-orange-400 font-medium">{challenge.files?.length}</span>
              </div>
            )}
            {challenge.hints.length > 0 && (
              <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 rounded-full">
                <Lightbulb className="w-3 h-3 text-yellow-400" />
                <span className="text-yellow-400 font-medium">{challenge.hints.length}</span>
              </div>
            )}
          </div>
        </div>

        {/* Tags */}
        {challenge.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {challenge.tags.slice(0, 3).map(tag => (
              <span 
                key={tag} 
                className="px-2 py-1 bg-slate-700/50 text-slate-300 rounded-md text-xs border border-slate-600/50"
              >
                #{tag}
              </span>
            ))}
            {challenge.tags.length > 3 && (
              <span className="px-2 py-1 bg-slate-700/50 text-slate-400 rounded-md text-xs border border-slate-600/50">
                +{challenge.tags.length - 3} more
              </span>
            )}
          </div>
        )}

        {/* Action button */}
        <button
          onClick={() => setShowDetails(true)}
          className="w-full px-4 py-3 bg-gradient-to-r from-emerald-600/20 to-blue-600/20 text-emerald-400 border border-emerald-600/30 rounded-xl hover:from-emerald-600/30 hover:to-blue-600/30 hover:border-emerald-500/50 transition-all duration-300 text-sm font-medium group/btn"
        >
          <div className="flex items-center justify-center space-x-2">
            <FileText className="w-4 h-4 group-hover/btn:scale-110 transition-transform" />
            <span>{challenge.isSolved ? 'View Details' : 'Start Challenge'}</span>
          </div>
        </button>
      </div>

      {/* Challenge Details Modal */}
      {showDetails && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-\[99999\]">
          <div className="bg-slate-900 rounded-xl border border-slate-800 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* First Blood Achievement Banner */}
              {showFirstBloodBadge && (
                <div className="mb-6 p-4 bg-gradient-to-r from-yellow-500/20 to-amber-500/20 border border-yellow-500/40 rounded-xl">
                  <div className="flex items-center justify-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <Trophy className="w-6 h-6 text-yellow-400 animate-bounce" />
                      <Star className="w-5 h-5 text-yellow-300" />
                    </div>
                    <div className="text-center">
                      <h3 className="text-lg font-bold text-yellow-300 mb-1">🎉 First Blood Achievement! 🎉</h3>
                      <p className="text-sm text-yellow-200">You were the first to solve this challenge!</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Star className="w-5 h-5 text-yellow-300" />
                      <Trophy className="w-6 h-6 text-yellow-400 animate-bounce" />
                    </div>
                  </div>
                </div>
              )}

              <div className="flex items-start justify-between mb-6">
                <div className="flex-1">
                  <h2 className="text-2xl font-bold text-white mb-2">{challenge.title}</h2>
                  <div className="flex items-center flex-wrap gap-2 mb-3">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium border ${difficultyColors[challenge.difficulty]}`}>
                      {challenge.difficulty}
                    </span>
                    <span
                      className="px-3 py-1 rounded-full text-sm font-medium border"
                      style={getCategoryStyle(challenge.category)}
                    >
                      {getCategoryDisplayName(challenge.category)}
                    </span>
                    <span className="text-emerald-400 font-medium">{challenge.points} points</span>
                    
                    {hasMultipleFlags && (
                      <div className="flex items-center space-x-1 px-2 py-1 bg-purple-500/20 border border-purple-500/30 rounded-full">
                        <Zap className="w-3 h-3 text-purple-400" />
                        <span className="text-xs text-purple-400 font-medium">Multi-Flag</span>
                      </div>
                    )}
                    
                    {challenge.requiresServer && (
                      <div className="flex items-center space-x-1 px-2 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full">
                        <Server className="w-3 h-3 text-blue-400" />
                        <span className="text-xs text-blue-400 font-medium">Server Required</span>
                      </div>
                    )}
                    
                    {showFirstBloodBadge && (
                      <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 border border-yellow-500/30 rounded-full">
                        <Trophy className="w-3 h-3 text-yellow-400" />
                        <span className="text-xs text-yellow-400 font-medium">First Blood</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Author and Release Info */}
                  <div className="flex items-center space-x-4 text-sm text-slate-400">
                    <div className="flex items-center space-x-1">
                      <User className="w-4 h-4" />
                      <span>by {challenge.authorName || challenge.author || 'Anonymous'}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>Released {formatDate(challenge.releaseDate)}</span>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => setShowDetails(false)}
                  className="text-slate-400 hover:text-white transition-colors flex-shrink-0"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Description</h3>
                  <p className="text-slate-300 leading-relaxed">{challenge.description}</p>
                </div>

                {/* Challenge Stats Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-slate-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="w-4 h-4 text-slate-400" />
                      <span className="text-sm font-medium text-slate-300">Solves</span>
                    </div>
                    <p className="text-xl font-bold text-white">{challenge.solves}</p>
                  </div>
                  <div className="bg-slate-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Award className="w-4 h-4 text-emerald-400" />
                      <span className="text-sm font-medium text-slate-300">Points</span>
                    </div>
                    <p className="text-xl font-bold text-emerald-400">{challenge.points}</p>
                  </div>
                  <div className="bg-slate-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Clock className="w-4 h-4 text-slate-400" />
                      <span className="text-sm font-medium text-slate-300">Released</span>
                    </div>
                    <p className="text-sm font-bold text-white">{formatDate(challenge.releaseDate)}</p>
                  </div>
                  <div className="bg-slate-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Flag className="w-4 h-4 text-blue-400" />
                      <span className="text-sm font-medium text-slate-300">Flags</span>
                    </div>
                    <p className="text-xl font-bold text-blue-400">{hasMultipleFlags ? challenge.flags?.length : 1}</p>
                  </div>
                </div>

                {/* Files Section */}
                {hasFiles && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-3 flex items-center space-x-2">
                      <Download className="w-5 h-5 text-orange-400" />
                      <span>Challenge Files ({challenge.files?.length})</span>
                    </h3>
                    <div className="space-y-2">
                      {challenge.files?.map((file, index) => (
                        <div key={index} className="bg-slate-800/50 rounded-lg p-4 border border-slate-700/50">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="p-2 bg-orange-500/20 rounded-lg">
                                <Download className="w-4 h-4 text-orange-400" />
                              </div>
                              <div>
                                <p className="text-white font-medium">{file.name}</p>
                                {file.description && (
                                  <p className="text-sm text-slate-400 mt-1">{file.description}</p>
                                )}
                                <div className="flex items-center space-x-3 mt-1 text-xs text-slate-500">
                                  {file.type && <span>Type: {file.type}</span>}
                                  {file.size && <span>Size: {(file.size / 1024).toFixed(1)}KB</span>}
                                </div>
                              </div>
                            </div>
                            <button 
                              onClick={() => handleDownload(file.path, file.name)}
                              className="px-3 py-2 bg-orange-600/20 text-orange-400 border border-orange-600/30 rounded-lg hover:bg-orange-600/30 transition-colors text-sm"
                            >
                              Download
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* First Blood Section */}
                {hasFirstBlood && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-3 flex items-center space-x-2">
                      <Trophy className="w-5 h-5 text-yellow-400" />
                      <span>First Blood</span>
                    </h3>
                    <div className="bg-gradient-to-r from-yellow-500/10 to-amber-500/10 border border-yellow-500/20 rounded-lg p-4">
                      {challenge.firstBlood && (
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-yellow-400 font-medium">{challenge.firstBlood.username}</p>
                            <p className="text-sm text-yellow-300">
                              First to solve • {formatDate(challenge.firstBlood.timestamp)}
                            </p>
                          </div>
                          <div className="flex items-center space-x-1 text-yellow-400">
                            <Trophy className="w-5 h-5" />
                            <span className="font-bold">First Blood!</span>
                          </div>
                        </div>
                      )}
                      
                      {challenge.flagsFirstBlood && Object.keys(challenge.flagsFirstBlood).length > 0 && (
                        <div className="space-y-2 mt-3">
                          <p className="text-sm text-yellow-300 font-medium">Flag-specific First Bloods:</p>
                          {Object.entries(challenge.flagsFirstBlood).map(([flagId, firstBlood]) => (
                            <div key={flagId} className="flex items-center justify-between bg-yellow-500/5 rounded-lg p-2">
                              <span className="text-yellow-200 text-sm">{firstBlood.username}</span>
                              <span className="text-xs text-yellow-400">{formatDate(firstBlood.timestamp)}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Tags Section */}
                {challenge.tags.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-3">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {challenge.tags.map(tag => (
                        <span 
                          key={tag} 
                          className="px-3 py-1 bg-slate-700/50 text-slate-300 rounded-full text-sm border border-slate-600/50"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Hints Section */}
                {challenge.hints.length > 0 && (
                  <div>
                    <button
                      onClick={() => setShowHints(!showHints)}
                      className="flex items-center space-x-2 text-yellow-400 hover:text-yellow-300 transition-colors mb-3"
                    >
                      <Lightbulb className="w-5 h-5" />
                      <span className="font-medium">Hints ({challenge.hints.length})</span>
                    </button>
                    {showHints && (
                      <div className="space-y-2">
                        {challenge.hints.map((hint, index) => (
                          <div key={index} className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
                            <p className="text-sm text-yellow-200">{hint}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Server Configuration */}
                {challenge.requiresServer && challenge.dockerImage && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-3 flex items-center space-x-2">
                      <Server className="w-5 h-5 text-blue-400" />
                      <span>Server Information</span>
                    </h3>
                    <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-slate-300">Docker Image:</span>
                        <span className="text-blue-400 font-mono text-sm">{challenge.dockerImage}</span>
                      </div>
                      {challenge.serverConfig && (
                        <div className="mt-3">
                          <span className="text-sm text-slate-300 block mb-2">Configuration:</span>
                          <pre className="bg-slate-800/50 rounded-lg p-3 text-xs text-slate-300 overflow-x-auto">
                            {JSON.stringify(challenge.serverConfig, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Flag Submission Section */}
                {!challenge.isSolved && canSubmit && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-3">Submit Flag</h3>
                    {hasMultipleFlags && challenge.flags && challenge.flags.length > 1 ? (
                      <div className="space-y-4">
                        <p className="text-sm text-slate-400 mb-3">
                          This challenge has multiple flags. You can submit them individually.
                        </p>
                        {challenge.flags.map((flagInfo, index) => (
                          <div key={index} className="bg-slate-800/30 border border-slate-700/50 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-slate-300">{flagInfo.description || `Flag ${index + 1}`}</span>
                              <span className="text-emerald-400 text-sm font-medium">{flagInfo.points} pts</span>
                            </div>
                            <form onSubmit={handleSubmitFlag} className="flex space-x-3">
                              <input
                                type="text"
                                value={flag}
                                onChange={(e) => setFlag(e.target.value)}
                                placeholder={`Enter ${(flagInfo.description || 'flag').toLowerCase()}...`}
                                className="flex-1 px-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                                disabled={isSubmitting}
                              />
                              <button
                                type="submit"
                                disabled={isSubmitting || !flag.trim()}
                                className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors flex items-center space-x-2"
                              >
                                {isSubmitting ? (
                                  <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                                ) : (
                                  <Send className="w-4 h-4" />
                                )}
                                <span>Submit</span>
                              </button>
                            </form>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <form onSubmit={handleSubmitFlag} className="space-y-4">
                        <div className="flex space-x-3">
                          <input
                            type="text"
                            value={flag}
                            onChange={(e) => setFlag(e.target.value)}
                            placeholder="Enter flag..."
                            className="flex-1 px-4 py-3 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                            disabled={isSubmitting}
                          />
                          <button
                            type="submit"
                            disabled={isSubmitting || !flag.trim()}
                            className="px-6 py-3 bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors flex items-center space-x-2"
                          >
                            {isSubmitting ? (
                              <div className="w-5 h-5 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                            ) : (
                              <Send className="w-5 h-5" />
                            )}
                            <span>Submit</span>
                          </button>
                        </div>
                      </form>
                    )}
                    
                    {submitResult && (
                      <div className={`p-3 rounded-lg mt-3 ${submitResult === 'success' ? 'bg-emerald-500/20 text-emerald-400' : 'bg-red-500/20 text-red-400'}`}>
                        {submitResult === 'success' ? 'Correct! Flag accepted.' : 'Incorrect flag. Try again.'}
                      </div>
                    )}
                  </div>
                )}

                {/* Solved by Teammate Section */}
                {!challenge.isSolved && solvedByTeammate && solvedByTeammate.isStillInTeam && (
                  <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <Users className="w-5 h-5 text-blue-400" />
                      <span className="font-medium text-blue-400">Solved by Teammate</span>
                    </div>
                    <p className="text-sm text-blue-300 mt-1">
                      This challenge has been solved by your teammate <span className="font-medium">{solvedByTeammate.username}</span>.
                    </p>
                    <p className="text-xs text-blue-400 mt-1">
                      Solved on {new Date(solvedByTeammate.solvedAt).toLocaleDateString()}
                    </p>
                  </div>
                )}

                {/* Former Teammate Solved - Can Resubmit */}
                {!challenge.isSolved && solvedByTeammate && !solvedByTeammate.isStillInTeam && (
                  <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <Users className="w-5 h-5 text-yellow-400" />
                      <span className="font-medium text-yellow-400">Previously Solved by Former Teammate</span>
                    </div>
                    <p className="text-sm text-yellow-300 mt-1">
                      This challenge was solved by <span className="font-medium">{solvedByTeammate.username}</span> who is no longer on your team.
                    </p>
                    <p className="text-xs text-yellow-400 mt-1">
                      You can now submit the flag to earn points for your current team.
                    </p>
                  </div>
                )}

                {/* Challenge Solved Section */}
                {challenge.isSolved && (
                  <div className="bg-emerald-500/20 border border-emerald-500/30 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-5 h-5 text-emerald-400" />
                      <span className="font-medium text-emerald-400">Challenge Solved!</span>
                    </div>
                    <p className="text-sm text-emerald-300 mt-1">
                      You earned {challenge.points} points for solving this challenge.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
