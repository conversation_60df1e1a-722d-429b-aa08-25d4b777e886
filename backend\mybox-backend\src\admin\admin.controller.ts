import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  ValidationPipe,
  BadRequestException,
  NotFoundException,
  UseInterceptors,
  UploadedFile,
  Req,
  Res,
  Header
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import * as fs from 'fs';
import * as path from 'path';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Roles } from '../auth/roles.decorator';
import { RolesGuard } from '../auth/roles.guard';
import { AdminService } from './admin.service';
import { AdminSettingsService, UpdateAdminSettingsDto } from './admin-settings.service';
import { AdminPaginationQueryDto } from './dto/admin-pagination.dto';
import { UpdateUserStatusDto } from './dto/update-user-status.dto';
import { UpdateChallengeStatusDto } from './dto/update-challenge-status.dto';
import { UpdateTeamStatusDto } from './dto/update-team-status.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UpdateUserRoleDto } from './dto/update-user-role.dto';
import { CreateChallengeDto } from './dto/create-challenge.dto';
import { UpdateChallengeDto } from './dto/update-challenge.dto';
import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
import { FileUploadResponseDto } from './dto/file-upload-response.dto';
import { CategoriesService } from '../categories/categories.service';
import { ApiTags, ApiBearerAuth, ApiConsumes, ApiBody, ApiOperation } from '@nestjs/swagger';

@ApiTags('admin')
@ApiBearerAuth()
@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin')
export class AdminController {
  constructor(
    private readonly adminService: AdminService,
    private readonly adminSettingsService: AdminSettingsService,
    private readonly categoriesService: CategoriesService,
  ) {}

  // Admin Settings endpoints
  @Get('settings')
  async getAdminSettings() {
    return this.adminSettingsService.getAdminSettings();
  }

  @Put('settings')
  async updateAdminSettings(
    @Body() updateDto: UpdateAdminSettingsDto,
    @Req() req: any
  ) {
    return this.adminSettingsService.updateAdminSettings(updateDto, req.user.userId);
  }

  @Post('settings/backup')
  async createBackup() {
    return this.adminSettingsService.createBackup();
  }

  @Get('settings/backups')
  async listBackups() {
    return this.adminSettingsService.listBackups();
  }

  @Get('settings/backup/:filename')
  async downloadBackup(
    @Param('filename') filename: string,
    @Res() res: Response
  ) {
    try {
      const backupDir = path.join(process.cwd(), 'backups');
      const backupPath = path.join(backupDir, filename);

      // Security check
      if (!filename.startsWith('mybox-backup-') || (!filename.endsWith('.tar.gz') && !filename.endsWith('.zip'))) {
        return res.status(400).json({ error: 'Invalid backup filename' });
      }

      if (!fs.existsSync(backupPath)) {
        return res.status(404).json({ error: 'Backup file not found' });
      }

      res.setHeader('Content-Type', 'application/gzip');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      const fileStream = fs.createReadStream(backupPath);
      fileStream.pipe(res);
    } catch (error) {
      console.error('Download backup error:', error);
      res.status(500).json({ error: 'Failed to download backup' });
    }
  }

  @Delete('settings/backup/:filename')
  async deleteBackup(@Param('filename') filename: string) {
    return this.adminSettingsService.deleteBackup(filename);
  }

  @Post('settings/backup/:filename/restore')
  async restoreBackup(@Param('filename') filename: string) {
    return this.adminSettingsService.restoreBackup(filename);
  }

  @Post('settings/backup/upload')
  @UseInterceptors(FileInterceptor('backup'))
  async uploadBackup(@UploadedFile() file: Express.Multer.File) {
    try {
      if (!file) {
        throw new BadRequestException('Backup file is required');
      }

      // Validate file type
      if (!file.originalname.endsWith('.tar.gz') && !file.originalname.endsWith('.zip')) {
        throw new BadRequestException('Invalid file type. Only .tar.gz and .zip files are allowed');
      }

      const backupDir = path.join(process.cwd(), 'backups');
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      // Generate unique filename with correct extension
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const extension = file.originalname.endsWith('.zip') ? '.zip' : '.tar.gz';
      const filename = `mybox-backup-uploaded-${timestamp}${extension}`;
      const backupPath = path.join(backupDir, filename);

      // Save uploaded file
      fs.writeFileSync(backupPath, file.buffer);

      return {
        success: true,
        message: 'Backup uploaded successfully',
        data: {
          filename,
          size: file.size,
          originalName: file.originalname
        }
      };
    } catch (error) {
      console.error('Upload backup error:', error);
      throw new BadRequestException(`Failed to upload backup: ${error.message}`);
    }
  }

  @Get('settings/logs')
  async downloadSystemLogs() {
    return this.adminSettingsService.downloadSystemLogs();
  }

  @Post('settings/clear-cache')
  async clearCache() {
    return this.adminSettingsService.clearCache();
  }

  @Get('settings/system-stats')
  async getSystemStats() {
    return this.adminSettingsService.getSystemStats();
  }

  @Post('settings/test-email')
  async testEmailSettings(@Body() settings: Partial<UpdateAdminSettingsDto>) {
    return this.adminSettingsService.testEmailSettings(settings);
  }

  // User management endpoints
  @Get('users')
  async getUsers(@Query(ValidationPipe) query: AdminPaginationQueryDto) {
    return this.adminService.getUsers(query);
  }

  @Post('users')
  async createUser(@Body(ValidationPipe) createUserDto: CreateUserDto) {
    try {
      return await this.adminService.createUser(createUserDto);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('users/:id')
  async getUserById(@Param('id') id: string) {
    const user = await this.adminService.getUserById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }

  @Put('users/:id')
  async updateUser(
    @Param('id') id: string, 
    @Body(ValidationPipe) updateUserDto: UpdateUserDto
  ) {
    try {
      return await this.adminService.updateUser(id, updateUserDto);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  @Put('users/:id/status')
  async updateUserStatus(
    @Param('id') id: string, 
    @Body(ValidationPipe) updateStatusDto: UpdateUserStatusDto
  ) {
    try {
      return await this.adminService.updateUserStatus(id, updateStatusDto.isActive);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  @Put('users/:id/role')
  async updateUserRole(
    @Param('id') id: string,
    @Body(ValidationPipe) updateRoleDto: UpdateUserRoleDto
  ) {
    try {
      return await this.adminService.updateUserRole(id, updateRoleDto.role);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  @Put('users/:id/verify-email')
  async verifyUserEmail(@Param('id') id: string) {
    try {
      return await this.adminService.verifyUserEmail(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  @Delete('users/:id')
  async deleteUser(@Param('id') id: string) {
    try {
      return await this.adminService.deleteUser(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  // Team management endpoints
  @Get('teams')
  async getTeams(@Query(ValidationPipe) query: AdminPaginationQueryDto) {
    return this.adminService.getTeams(query);
  }

  @Get('teams/:id')
  async getTeamById(@Param('id') id: string) {
    const team = await this.adminService.getTeamById(id);
    if (!team) {
      throw new NotFoundException('Team not found');
    }
    return team;
  }

  @Put('teams/:id/status')
  async updateTeamStatus(
    @Param('id') id: string, 
    @Body(ValidationPipe) updateStatusDto: UpdateTeamStatusDto
  ) {
    try {
      return await this.adminService.updateTeamStatus(id, updateStatusDto.isActive);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  @Delete('teams/:id')
  async deleteTeam(@Param('id') id: string) {
    try {
      return await this.adminService.deleteTeam(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }
  // Challenge management endpoints
  @Get('challenges')
  async getChallenges(@Query(ValidationPipe) query: AdminPaginationQueryDto) {
    return this.adminService.getChallenges(query);
  }

  @Post('challenges')
  async createChallenge(@Body(ValidationPipe) createChallengeDto: CreateChallengeDto) {
    try {
      return await this.adminService.createChallenge(createChallengeDto);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('challenges/:id')
  async getChallengeById(@Param('id') id: string) {
    const challenge = await this.adminService.getChallengeById(id);
    if (!challenge) {
      throw new NotFoundException('Challenge not found');
    }
    return challenge;
  }

  @Put('challenges/:id')
  async updateChallenge(
    @Param('id') id: string,
    @Body(ValidationPipe) updateChallengeDto: UpdateChallengeDto
  ) {
    try {
      return await this.adminService.updateChallenge(id, updateChallengeDto);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  @Put('challenges/:id/status')
  async updateChallengeStatus(
    @Param('id') id: string, 
    @Body(ValidationPipe) updateStatusDto: UpdateChallengeStatusDto
  ) {
    try {
      return await this.adminService.updateChallengeStatus(id, updateStatusDto.isActive);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  @Delete('challenges/:id')
  async deleteChallenge(@Param('id') id: string) {
    try {
      return await this.adminService.deleteChallenge(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  // System statistics endpoint
  @Get('stats')
  async getSystemStatsOld() {
    return this.adminService.getSystemStats();
  }  // Activity logs endpoint
  @Get('activity-logs')
  async getActivityLogs(@Query(ValidationPipe) query: AdminPaginationQueryDto) {
    return this.adminService.getActivityLogs(query);
  }

  // File upload endpoint
  @Post('upload')
  @ApiOperation({ summary: 'Upload a file for challenge resources' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The file to upload',
        },
        challengeName: {
          type: 'string',
          description: 'The name of the challenge to organize files',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('file')
  )  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body('challengeName') challengeName?: string
  ): Promise<FileUploadResponseDto> {
    try {
      return await this.adminService.uploadFile(file, challengeName);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }  // File download endpoint
  @Get('files/:folder/:subfolder/:filename')
  @ApiOperation({ summary: 'Download a challenge file' })
  async downloadFile(
    @Param('folder') folder: string,
    @Param('subfolder') subfolder: string,
    @Param('filename') filename: string,
    @Res() res: Response
  ) {
    try {
      const filePath = `${folder}/${subfolder}/${filename}`;
      console.log('=== DOWNLOAD CONTROLLER ===');
      console.log('Constructed filePath:', filePath);
      console.log('Request URL:', res.req.url);
      console.log('Request method:', res.req.method);
      console.log('=========================');
      
      return await this.adminService.downloadFile(filePath, res);
    } catch (error) {
      console.log('Controller download error:', error.message);
      throw new NotFoundException('File not found');
    }
  }

  // Category management endpoints
  @Get('categories')
  @ApiOperation({ summary: 'Get all categories (including inactive)' })
  async getCategories(@Query('includeInactive') includeInactive?: string) {
    const includeInactiveFlag = includeInactive === 'true';
    const categories = await this.categoriesService.getAllCategories(includeInactiveFlag);

    return categories.map(category => ({
      id: (category._id as any).toString(),
      name: category.name,
      displayName: category.displayName,
      description: category.description,
      color: category.color,
      icon: category.icon,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt
    }));
  }

  @Post('categories')
  @ApiOperation({ summary: 'Create a new category' })
  async createCategory(@Body(ValidationPipe) createCategoryDto: CreateCategoryDto) {
    try {
      const category = await this.categoriesService.createCategory(createCategoryDto);
      return {
        id: (category._id as any).toString(),
        name: category.name,
        displayName: category.displayName,
        description: category.description,
        color: category.color,
        icon: category.icon,
        isActive: category.isActive,
        sortOrder: category.sortOrder,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('categories/:id')
  @ApiOperation({ summary: 'Get category by ID' })
  async getCategoryById(@Param('id') id: string) {
    try {
      const category = await this.categoriesService.getCategoryById(id);
      return {
        id: (category._id as any).toString(),
        name: category.name,
        displayName: category.displayName,
        description: category.description,
        color: category.color,
        icon: category.icon,
        isActive: category.isActive,
        sortOrder: category.sortOrder,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  @Put('categories/:id')
  @ApiOperation({ summary: 'Update category' })
  async updateCategory(@Param('id') id: string, @Body(ValidationPipe) updateCategoryDto: UpdateCategoryDto) {
    try {
      const category = await this.categoriesService.updateCategory(id, updateCategoryDto);
      return {
        id: (category._id as any).toString(),
        name: category.name,
        displayName: category.displayName,
        description: category.description,
        color: category.color,
        icon: category.icon,
        isActive: category.isActive,
        sortOrder: category.sortOrder,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  @Delete('categories/:id')
  @ApiOperation({ summary: 'Delete category' })
  async deleteCategory(@Param('id') id: string) {
    try {
      await this.categoriesService.deleteCategory(id);
      return { message: 'Category deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }
}