import React, { useState, useEffect } from 'react';
import { AdminService, AdminUserListItem, AdminPaginationParams, CreateUserRequest, UpdateUserRequest } from '../../services/admin';
import { Edit, Trash2, Shield, Search, ChevronLeft, ChevronRight, UserX, UserCheck, Plus, X, Mail, MailCheck } from 'lucide-react';

// Modal component for reuse
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;    return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[99999]" style={{zIndex: 999999}}>
      <div className="bg-slate-800 rounded-xl border border-slate-700 p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-white">{title}</h3>
          <button 
            onClick={onClose} 
            className="text-slate-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

export function UserMonitoring() {
  const [users, setUsers] = useState<AdminUserListItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalUsers, setTotalUsers] = useState<number>(0);
  const [limit] = useState<number>(10);
  
  // Modal states
  const [isAddModalOpen, setIsAddModalOpen] = useState<boolean>(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [isStatusModalOpen, setIsStatusModalOpen] = useState<boolean>(false);
  const [isRoleModalOpen, setIsRoleModalOpen] = useState<boolean>(false);
  
  // Form states
  const [selectedUser, setSelectedUser] = useState<AdminUserListItem | null>(null);
  const [newUserData, setNewUserData] = useState<CreateUserRequest>({
    username: '',
    email: '',
    password: '',
    role: 'user'
  });
  const [editUserData, setEditUserData] = useState<UpdateUserRequest>({});

  const fetchUsers = async (params: AdminPaginationParams = {}) => {
    setLoading(true);
    try {
      const response = await AdminService.getUsers({
        page: params.page || currentPage,
        limit,
        search: params.search || searchTerm,
        sortBy: 'createdAt',
        sortDirection: 'desc'
      });
      setUsers(response.users);
      setTotalPages(response.pages);
      setTotalUsers(response.total);
      setCurrentPage(response.page);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch users');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchUsers({ page: 1, search: searchTerm });
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      fetchUsers({ page: newPage });
    }
  };
  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    if (!userId) {
      setError('Invalid user ID');
      return;
    }
    
    try {
      await AdminService.updateUserStatus(userId, !currentStatus);
      fetchUsers();
    } catch (err: any) {
      console.error('Error toggling user status:', err);
      setError(err.message || 'Failed to update user status');
    }
  };
  const handleMakeAdmin = async (userId: string) => {
    if (!userId) {
      setError('Invalid user ID');
      return;
    }
    
    try {
      await AdminService.updateUserRole(userId, 'admin');
      fetchUsers();
    } catch (err: any) {
      console.error('Error making user admin:', err);
      setError(err.message || 'Failed to update user role');
    }
  };
  const handleDeleteUser = async (userId: string) => {
    if (!userId) {
      setError('Invalid user ID');
      return;
    }

    try {
      await AdminService.deleteUser(userId);
      fetchUsers();
      setIsDeleteModalOpen(false);
    } catch (err: any) {
      console.error('Error deleting user:', err);
      setError(err.message || 'Failed to delete user');
    }
  };

  const handleVerifyUserEmail = async (userId: string) => {
    if (!userId) {
      setError('Invalid user ID');
      return;
    }

    try {
      await AdminService.verifyUserEmail(userId);
      fetchUsers(); // Refresh the user list to show updated verification status
      console.log('✅ User email verified successfully');
    } catch (err: any) {
      console.error('Error verifying user email:', err);
      setError(err.message || 'Failed to verify user email');
    }
  };
  
  const handleCreateUser = async () => {
    try {
      await AdminService.createUser(newUserData);
      setIsAddModalOpen(false);
      setNewUserData({
        username: '',
        email: '',
        password: '',
        role: 'user'
      });
      fetchUsers();
    } catch (err: any) {
      console.error('Error creating user:', err);
      setError(err.message || 'Failed to create user');
    }
  };
    const handleUpdateUser = async () => {
    if (!selectedUser || !selectedUser.id) {
      setError('No user selected or invalid user ID');
      return;
    }
    
    try {
      await AdminService.updateUser(selectedUser.id, editUserData);
      setIsEditModalOpen(false);
      setSelectedUser(null);
      setEditUserData({});
      fetchUsers();
    } catch (err: any) {
      console.error('Error updating user:', err);
      setError(err.message || 'Failed to update user');
    }
  };
    const openEditModal = (user: AdminUserListItem) => {
    if (!user || !user.id) {
      setError('Invalid user data');
      return;
    }
    setSelectedUser(user);
    setEditUserData({
      username: user.username,
      email: user.email,
      role: user.role
    });
    setIsEditModalOpen(true);
  };
  
  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setSelectedUser(null);
    setEditUserData({});
  };
  
  const openDeleteModal = (user: AdminUserListItem) => {
    setSelectedUser(user);
    setIsDeleteModalOpen(true);
  };
  
  const openStatusModal = (user: AdminUserListItem) => {
    setSelectedUser(user);
    setIsStatusModalOpen(true);
  };
  
  const openRoleModal = (user: AdminUserListItem) => {
    setSelectedUser(user);
    setIsRoleModalOpen(true);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold text-white">User Management</h3>
        <div className="flex items-center gap-4">
          <div className="text-sm text-slate-400">
            {totalUsers} total users
          </div>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg flex items-center gap-2"
          >
            <Plus className="w-4 h-4" /> Add User
          </button>
        </div>
      </div>
      
      <form onSubmit={handleSearchSubmit} className="relative">
        <Search className="absolute left-3 top-3 w-5 h-5 text-slate-400" />
        <input
          type="text"
          placeholder="Search users by username or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-3 bg-slate-900/50 border border-slate-800 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400 backdrop-blur-sm"
        />
      </form>
      
      {error && (
        <div className="p-4 bg-red-900/30 border border-red-800 rounded-xl text-red-400">
          {error}
          <button 
            onClick={() => setError(null)} 
            className="ml-2 text-red-400 hover:text-red-300"
          >
            <X className="inline w-4 h-4" />
          </button>
        </div>
      )}
      
      <div className="bg-slate-800/50 rounded-xl border border-slate-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-slate-900/50">
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">User</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Email</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Role</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Score</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Status</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Email Verified</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Last Active</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Created</th>
                <th className="px-4 py-3 text-right text-xs font-medium text-slate-400 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-slate-700">
              {loading && Array(5).fill(0).map((_, i) => (
                <tr key={`skeleton-${i}`} className="animate-pulse">
                  <td colSpan={9} className="px-4 py-4">
                    <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                  </td>
                </tr>
              ))}

              {!loading && users.length === 0 && (
                <tr>
                  <td colSpan={9} className="px-4 py-6 text-center text-slate-400">
                    No users found. Try adjusting your search.
                  </td>
                </tr>
              )}
              
              {!loading && users.map((user) => (
                <tr key={user.id} className="hover:bg-slate-800/50 transition-colors">
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-white">{user.username}</div>
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">{user.email}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      user.role === 'admin' 
                        ? 'bg-purple-500/20 text-purple-400'
                        : user.role === 'moderator'
                          ? 'bg-blue-500/20 text-blue-400'
                          : 'bg-slate-500/20 text-slate-400'
                    }`}>
                      {user.role}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">{user.score}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      user.isActive
                        ? 'bg-emerald-500/20 text-emerald-400'
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      {user.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      user.isEmailVerified
                        ? 'bg-emerald-500/20 text-emerald-400'
                        : 'bg-amber-500/20 text-amber-400'
                    }`}>
                      {user.isEmailVerified ? 'Verified' : 'Unverified'}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">{formatDate(user.lastActive)}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-slate-300">{formatDate(user.createdAt)}</div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => openStatusModal(user)}
                        className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-white transition-colors"
                        title={user.isActive ? "Deactivate User" : "Activate User"}
                      >
                        {user.isActive ? <UserX className="w-4 h-4" /> : <UserCheck className="w-4 h-4" />}
                      </button>
                      {!user.isEmailVerified && (
                        <button
                          onClick={() => handleVerifyUserEmail(user.id)}
                          className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-emerald-400 transition-colors"
                          title="Verify Email"
                        >
                          <MailCheck className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => openEditModal(user)}
                        className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-blue-400 transition-colors"
                        title="Edit User"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button 
                        onClick={() => openDeleteModal(user)}
                        className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-red-400 transition-colors"
                        title="Delete User"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                      {user.role !== 'admin' && (
                        <button 
                          onClick={() => openRoleModal(user)}
                          className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-purple-400 transition-colors"
                          title="Make Admin"
                        >
                          <Shield className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {totalPages > 1 && (
          <div className="px-4 py-3 flex items-center justify-between border-t border-slate-700">
            <div className="flex-1 flex justify-between items-center">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`flex items-center px-4 py-2 border rounded-md ${
                  currentPage === 1
                    ? 'border-slate-700 bg-slate-800/30 text-slate-500 cursor-not-allowed'
                    : 'border-slate-700 bg-slate-800 text-slate-300 hover:bg-slate-700'
                }`}
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Previous
              </button>
              <div className="text-sm text-slate-400">
                Page {currentPage} of {totalPages}
              </div>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`flex items-center px-4 py-2 border rounded-md ${
                  currentPage === totalPages
                    ? 'border-slate-700 bg-slate-800/30 text-slate-500 cursor-not-allowed'
                    : 'border-slate-700 bg-slate-800 text-slate-300 hover:bg-slate-700'
                }`}
              >
                Next
                <ChevronRight className="w-4 h-4 ml-2" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Add User Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add New User"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-1">Username</label>
            <input
              type="text"
              value={newUserData.username}
              onChange={(e) => setNewUserData({...newUserData, username: e.target.value})}
              className="w-full px-4 py-2 bg-slate-900/50 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-1">Email</label>
            <input
              type="email"
              value={newUserData.email}
              onChange={(e) => setNewUserData({...newUserData, email: e.target.value})}
              className="w-full px-4 py-2 bg-slate-900/50 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-1">Password</label>
            <input
              type="password"
              value={newUserData.password}
              onChange={(e) => setNewUserData({...newUserData, password: e.target.value})}
              className="w-full px-4 py-2 bg-slate-900/50 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-1">Role</label>
            <select
              value={newUserData.role}
              onChange={(e) => setNewUserData({...newUserData, role: e.target.value as 'user' | 'admin' | 'moderator'})}
              className="w-full px-4 py-2 bg-slate-900/50 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400"
            >
              <option value="user">User</option>
              <option value="admin">Admin</option>
              <option value="moderator">Moderator</option>
            </select>
          </div>
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setIsAddModalOpen(false)}
              className="px-4 py-2 border border-slate-600 text-slate-300 rounded-lg hover:bg-slate-700"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateUser}
              className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700"
            >
              Create User
            </button>
          </div>
        </div>
      </Modal>      {/* Edit User Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        title="Edit User"
      >
        {selectedUser && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-1">Username</label>
              <input
                type="text"
                value={editUserData.username}
                onChange={(e) => setEditUserData({...editUserData, username: e.target.value})}
                className="w-full px-4 py-2 bg-slate-900/50 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-1">Email</label>
              <input
                type="email"
                value={editUserData.email}
                onChange={(e) => setEditUserData({...editUserData, email: e.target.value})}
                className="w-full px-4 py-2 bg-slate-900/50 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-1">Role</label>
              <select
                value={editUserData.role}
                onChange={(e) => setEditUserData({...editUserData, role: e.target.value as 'user' | 'admin' | 'moderator'})}
                className="w-full px-4 py-2 bg-slate-900/50 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400"
              >
                <option value="user">User</option>
                <option value="admin">Admin</option>
                <option value="moderator">Moderator</option>
              </select>
            </div>
            <div className="flex justify-end space-x-3 mt-6">              <button
                onClick={closeEditModal}
                className="px-4 py-2 border border-slate-600 text-slate-300 rounded-lg hover:bg-slate-700"
              >
                Cancel
              </button>
              <button
                onClick={handleUpdateUser}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Update User
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete User Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete User"
      >
        {selectedUser && (
          <div className="space-y-4">
            <p className="text-slate-300">
              Are you sure you want to delete user <span className="text-white font-semibold">{selectedUser.username}</span>? 
              This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 border border-slate-600 text-slate-300 rounded-lg hover:bg-slate-700"
              >
                Cancel
              </button>
              <button
                onClick={() => selectedUser && selectedUser.id ? handleDeleteUser(selectedUser.id) : setError('Invalid user ID')}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Delete User
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Toggle Status Confirmation Modal */}
      <Modal
        isOpen={isStatusModalOpen}
        onClose={() => setIsStatusModalOpen(false)}
        title={selectedUser?.isActive ? "Deactivate User" : "Activate User"}
      >
        {selectedUser && (
          <div className="space-y-4">
            <p className="text-slate-300">
              Are you sure you want to {selectedUser.isActive ? "deactivate" : "activate"} user 
              <span className="text-white font-semibold"> {selectedUser.username}</span>?
            </p>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setIsStatusModalOpen(false)}
                className="px-4 py-2 border border-slate-600 text-slate-300 rounded-lg hover:bg-slate-700"
              >
                Cancel
              </button>
              <button                onClick={() => {
                  if (selectedUser && selectedUser.id) {
                    handleToggleUserStatus(selectedUser.id, selectedUser.isActive);
                  }
                  setIsStatusModalOpen(false);
                }}
                className={`px-4 py-2 ${selectedUser.isActive ? 'bg-orange-600 hover:bg-orange-700' : 'bg-emerald-600 hover:bg-emerald-700'} text-white rounded-lg`}
              >
                {selectedUser.isActive ? "Deactivate" : "Activate"}
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Make Admin Confirmation Modal */}
      <Modal
        isOpen={isRoleModalOpen}
        onClose={() => setIsRoleModalOpen(false)}
        title="Make User Admin"
      >
        {selectedUser && (
          <div className="space-y-4">
            <p className="text-slate-300">
              Are you sure you want to make <span className="text-white font-semibold">{selectedUser.username}</span> an admin? 
              This will give them full access to all administration features.
            </p>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setIsRoleModalOpen(false)}
                className="px-4 py-2 border border-slate-600 text-slate-300 rounded-lg hover:bg-slate-700"
              >
                Cancel
              </button>
              <button                onClick={() => {
                  if (selectedUser && selectedUser.id) {
                    handleMakeAdmin(selectedUser.id);
                  }
                  setIsRoleModalOpen(false);
                }}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
              >
                Make Admin
              </button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}
