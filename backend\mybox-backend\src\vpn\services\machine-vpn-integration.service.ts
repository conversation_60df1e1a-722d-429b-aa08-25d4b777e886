import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { VPNConfigService } from './vpn-config.service';
import { NetworkIsolationService } from './network-isolation.service';
import { MachineTemplate, MachineTemplateDocument } from '../../schemas/machine-template.schema';
import { MachineInstance, MachineInstanceDocument } from '../../schemas/machine-instance.schema';
import { MachineNetwork, MachineNetworkDocument } from '../../schemas/machine-network.schema';

export interface MachineDeploymentConfig {
  templateId: string;
  userId: string;
  exposedPorts?: { internal: number; protocol: 'tcp' | 'udp'; description?: string }[];
  environmentVariables?: Record<string, string>;
  resourceLimits?: {
    memory?: string;
    cpu?: string;
  };
}

export interface DeployedMachine {
  machineId: string;
  containerId: string;
  internalIP: string;
  networkName: string;
  vpnAccessible: boolean;
  accessInstructions: {
    vpnRequired: boolean;
    connectionInfo: string;
    exposedServices: Array<{
      port: number;
      protocol: string;
      description: string;
      accessUrl?: string;
    }>;
  };
}

@Injectable()
export class MachineVPNIntegrationService {
  private readonly logger = new Logger(MachineVPNIntegrationService.name);

  constructor(
    @InjectModel(MachineTemplate.name) private machineTemplateModel: Model<MachineTemplateDocument>,
    @InjectModel(MachineInstance.name) private machineInstanceModel: Model<MachineInstanceDocument>,
    private readonly vpnConfigService: VPNConfigService,
    private readonly networkIsolationService: NetworkIsolationService,
  ) {}

  /**
   * Deploy machine with VPN integration
   */
  async deployMachineWithVPN(config: MachineDeploymentConfig): Promise<DeployedMachine> {
    try {
      const { templateId, userId, exposedPorts = [], environmentVariables = {}, resourceLimits = {} } = config;

      // Validate template exists
      const template = await this.machineTemplateModel.findById(templateId);
      if (!template) {
        throw new BadRequestException('Machine template not found');
      }

      // Ensure user has VPN configuration
      let vpnConfig = await this.vpnConfigService.getVPNConfig(userId);
      if (!vpnConfig) {
        this.logger.log(`Creating VPN configuration for user ${userId}`);
        vpnConfig = await this.vpnConfigService.createVPNConfig(userId);
      }

      // Create user's isolated network
      const networkConfig = await this.networkIsolationService.createUserNetwork(userId);
      const subnetBase = networkConfig.subnet.split('/')[0].split('.').slice(0, 3).join('.');

      // Find next available IP (simplified for now)
      const internalIP = `${subnetBase}.10`; // This should be improved to find actual next available IP

      // Prepare machine deployment configuration
      const machineConfig = {
        machineId: new Types.ObjectId().toString(),
        userId,
        templateId,
        internalIP,
        networkName: networkConfig.networkName,
        exposedPorts: exposedPorts.map(port => ({
          internal: port.internal,
          protocol: port.protocol,
          description: port.description || `${port.protocol.toUpperCase()} service on port ${port.internal}`
        }))
      };

      // Connect machine to isolated network
      const machineNetwork = await this.networkIsolationService.connectMachineToNetwork(machineConfig);

      // Create machine instance record
      const machineInstance = new this.machineInstanceModel({
        _id: new Types.ObjectId(machineConfig.machineId),
        templateId: new Types.ObjectId(templateId),
        userId: new Types.ObjectId(userId),
        status: 'deploying',
        networkConfig: {
          networkName: machineNetwork.networkName,
          internalIP: machineNetwork.internalIP,
          subnet: machineNetwork.subnetCIDR,
          vpnRequired: true
        },
        deploymentConfig: {
          exposedPorts: machineConfig.exposedPorts,
          environmentVariables,
          resourceLimits
        },
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours default
      });

      await machineInstance.save();

      // Generate access instructions
      const accessInstructions = this.generateAccessInstructions(
        machineNetwork,
        vpnConfig,
        template
      );

      this.logger.log(`Deployed machine ${machineConfig.machineId} for user ${userId} with VPN integration`);

      return {
        machineId: machineConfig.machineId,
        containerId: '', // Will be set when container is actually created
        internalIP: machineNetwork.internalIP,
        networkName: machineNetwork.networkName,
        vpnAccessible: true,
        accessInstructions
      };
    } catch (error) {
      this.logger.error(`Failed to deploy machine with VPN: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate access instructions for a deployed machine
   */
  private generateAccessInstructions(
    machineNetwork: MachineNetworkDocument,
    vpnConfig: any,
    template: MachineTemplateDocument
  ) {
    const exposedServices = machineNetwork.exposedPorts.map(port => {
      let accessUrl = '';
      let description = port.description || `${port.protocol.toUpperCase()} service`;

      // Generate access URLs for common services
      if (port.internal === 80 || port.internal === 8080) {
        accessUrl = `http://${machineNetwork.internalIP}:${port.internal}`;
        description = 'Web interface';
      } else if (port.internal === 443 || port.internal === 8443) {
        accessUrl = `https://${machineNetwork.internalIP}:${port.internal}`;
        description = 'Secure web interface';
      } else if (port.internal === 22) {
        accessUrl = `ssh user@${machineNetwork.internalIP}`;
        description = 'SSH access';
      } else if (port.internal === 3389) {
        accessUrl = `rdp://${machineNetwork.internalIP}:${port.internal}`;
        description = 'Remote Desktop';
      }

      return {
        port: port.internal,
        protocol: port.protocol,
        description,
        accessUrl
      };
    });

    const connectionInfo = `
🔐 VPN Connection Required

1. Download and install WireGuard VPN client
2. Download your VPN configuration from the VPN section
3. Connect to the VPN
4. Access your machine at: ${machineNetwork.internalIP}

📡 Your VPN Details:
• VPN IP: ${vpnConfig.ipAddress}
• Machine IP: ${machineNetwork.internalIP}
• Network: ${machineNetwork.subnetCIDR}

🎯 Machine: ${template.name}
📝 Description: ${template.description}
`;

    return {
      vpnRequired: true,
      connectionInfo: connectionInfo.trim(),
      exposedServices
    };
  }

  /**
   * Get machine access information
   */
  async getMachineAccessInfo(machineId: string, userId: string): Promise<{
    machine: any;
    network: MachineNetworkDocument;
    vpnConfig: any;
    accessInstructions: any;
  } | null> {
    try {
      // Get machine instance
      const machine = await this.machineInstanceModel.findOne({
        _id: new Types.ObjectId(machineId),
        userId: new Types.ObjectId(userId)
      }).populate('templateId');

      if (!machine) {
        return null;
      }

      // Get network configuration
      const network = await this.networkIsolationService.getMachineNetwork(machineId);
      if (!network) {
        throw new BadRequestException('Machine network configuration not found');
      }

      // Get VPN configuration
      const vpnConfig = await this.vpnConfigService.getVPNConfig(userId);
      if (!vpnConfig) {
        throw new BadRequestException('VPN configuration not found');
      }

      // Generate current access instructions
      const accessInstructions = this.generateAccessInstructions(
        network,
        vpnConfig,
        machine.templateId as any
      );

      return {
        machine,
        network,
        vpnConfig,
        accessInstructions
      };
    } catch (error) {
      this.logger.error(`Failed to get machine access info: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cleanup machine and network resources
   */
  async cleanupMachine(machineId: string, userId: string): Promise<void> {
    try {
      // Remove machine from network
      await this.networkIsolationService.disconnectMachineFromNetwork(machineId);

      // Remove machine instance
      await this.machineInstanceModel.deleteOne({
        _id: new Types.ObjectId(machineId),
        userId: new Types.ObjectId(userId)
      });

      // Check if user has any other machines, if not, optionally cleanup network
      const remainingMachines = await this.machineInstanceModel.countDocuments({
        userId: new Types.ObjectId(userId)
      });

      if (remainingMachines === 0) {
        this.logger.log(`User ${userId} has no remaining machines, keeping VPN/network for future use`);
        // Note: We keep the VPN config and network for future machine deployments
      }

      this.logger.log(`Cleaned up machine ${machineId} for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup machine: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get user's deployed machines with VPN info
   */
  async getUserMachinesWithVPN(userId: string): Promise<Array<{
    machine: MachineInstanceDocument;
    network: MachineNetworkDocument;
    accessInfo: any;
  }>> {
    try {
      const machines = await this.machineInstanceModel.find({
        userId: new Types.ObjectId(userId)
      }).populate('templateId');

      const machinesWithVPN: Array<{
        machine: MachineInstanceDocument;
        network: MachineNetworkDocument;
        accessInfo: any;
      }> = [];

      for (const machine of machines) {
        const machineId = (machine._id as Types.ObjectId).toString();
        const network = await this.networkIsolationService.getMachineNetwork(machineId);
        if (network) {
          const accessInfo = this.generateAccessInstructions(
            network,
            { ipAddress: network.allowedSourceIPs[0] }, // Simplified
            machine.templateId as any
          );

          machinesWithVPN.push({
            machine,
            network,
            accessInfo
          });
        }
      }

      return machinesWithVPN;
    } catch (error) {
      this.logger.error(`Failed to get user machines with VPN: ${error.message}`);
      throw error;
    }
  }

  /**
   * Test VPN connectivity to a machine
   */
  async testMachineConnectivity(machineId: string, userId: string): Promise<{
    reachable: boolean;
    latency?: number;
    services: Array<{ port: number; accessible: boolean }>;
  }> {
    try {
      const network = await this.networkIsolationService.getMachineNetwork(machineId);
      if (!network) {
        throw new BadRequestException('Machine network not found');
      }

      // This would implement actual connectivity testing
      // For now, return a mock response
      return {
        reachable: true,
        latency: 25, // ms
        services: network.exposedPorts.map(port => ({
          port: port.internal,
          accessible: true
        }))
      };
    } catch (error) {
      this.logger.error(`Failed to test machine connectivity: ${error.message}`);
      throw error;
    }
  }
}
