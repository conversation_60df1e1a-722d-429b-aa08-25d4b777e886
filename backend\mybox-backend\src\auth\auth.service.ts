import { Injectable, UnauthorizedException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { LoginDto, RegisterDto, AuthResponseDto, VerifyEmailDto, ResendVerificationDto } from './dto/auth.dto';
import { User } from '../schemas/user.schema';
import { AdminSettings } from '../schemas/admin-settings.schema';
import { EmailService } from '../services/email.service';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private emailService: EmailService,
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(AdminSettings.name) private adminSettingsModel: Model<AdminSettings>,
  ) {}
  async validateUser(email: string, password: string): Promise<any> {
    console.log('🔐 Validating user login for email:', email);

    const user = await this.usersService.findByEmail(email);
    if (!user) {
      console.log('❌ User not found for email:', email);
      return null;
    }

    if (!user.isActive) {
      console.log('❌ User account is inactive for email:', email);
      return null;
    }

    console.log('✅ User found, validating password for:', email);
    const isPasswordValid = await this.usersService.validatePassword(user, password);
    if (!isPasswordValid) {
      console.log('❌ Password validation failed for email:', email);
      return null;
    }

    console.log('✅ Password validation successful for email:', email);    // Update last active
    await this.usersService.updateLastActive((user._id as any).toString());

    const { passwordHash, ...result } = user.toObject();
    return result;
  }

  async login(loginDto: LoginDto): Promise<AuthResponseDto> {
    console.log('🚀 Login attempt for email:', loginDto.email);

    const user = await this.validateUser(loginDto.email, loginDto.password);
    if (!user) {
      console.log('❌ Login failed - validateUser returned null for email:', loginDto.email);
      throw new UnauthorizedException('Invalid credentials');
    }

    console.log('✅ User validation successful, proceeding with login for:', loginDto.email);

    // Check if email verification is required
    const settings = await this.adminSettingsModel.findOne({ configId: 'default' });
    if (settings?.emailVerification && !user.isEmailVerified) {
      // Special handling for admin users
      if (user.role === 'admin') {
        console.log(`⚠️  Admin user ${user.email} email not verified. This should be auto-verified.`);
        console.log('💡 Run "npm run verify-admin" to fix this issue.');
        return {
          requiresEmailVerification: true,
          message: 'Admin email verification required. Please contact system administrator or run the admin verification script.',
        };
      }

      return {
        requiresEmailVerification: true,
        message: 'Please verify your email address before logging in.',
      };
    }

    return this.generateAuthResponse(user);
  }

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    try {
      // Check if email verification is enabled
      const settings = await this.adminSettingsModel.findOne({ configId: 'default' });
      const emailVerificationEnabled = settings?.emailVerification || false;

      // Check if user already exists
      const existingUser = await this.userModel.findOne({
        $or: [
          { username: registerDto.username },
          { email: registerDto.email }
        ]
      });

      if (existingUser) {
        throw new ConflictException('Username or email already exists');
      }

      // Hash password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(registerDto.password, saltRounds);

      // Generate verification code if email verification is enabled
      let verificationCode: string | undefined;
      let verificationExpires: Date | undefined;

      if (emailVerificationEnabled) {
        verificationCode = this.emailService.generateVerificationCode();
        verificationExpires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
      }

      // Create user
      const user = new this.userModel({
        username: registerDto.username,
        email: registerDto.email,
        passwordHash,
        isEmailVerified: !emailVerificationEnabled, // If verification disabled, mark as verified
        emailVerificationCode: verificationCode,
        emailVerificationExpires: verificationExpires,
        lastActive: new Date(),
      });

      const savedUser = await user.save();

      // Send verification email if enabled
      if (emailVerificationEnabled && verificationCode) {
        try {
          await this.emailService.sendVerificationEmail(
            registerDto.email,
            registerDto.username,
            verificationCode
          );
        } catch (emailError) {
          console.error('Failed to send verification email:', emailError);
          // Don't fail registration if email sending fails
        }

        return {
          requiresEmailVerification: true,
          message: 'Registration successful! Please check your email for a verification code.',
        };
      }

      // If email verification is disabled, return auth response
      return this.generateAuthResponse(savedUser);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new Error('Registration failed');
    }
  }

  private generateAuthResponse(user: User): AuthResponseDto {    const payload = {
      sub: (user._id as any).toString(),
      username: user.username,
      email: user.email,
      role: user.role,
    };

    const access_token = this.jwtService.sign(payload);

    return {
      access_token,
      user: {
        id: (user._id as any).toString(),
        username: user.username,
        email: user.email,
        role: user.role,
        score: user.score,
        rank: user.rank,
        teamId: user.teamId?.toString(),
        isEmailVerified: user.isEmailVerified,
      },
    };
  }

  async verifyEmail(verifyEmailDto: VerifyEmailDto): Promise<AuthResponseDto> {
    const user = await this.userModel.findOne({ email: verifyEmailDto.email });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.isEmailVerified) {
      throw new BadRequestException('Email is already verified');
    }

    if (!user.emailVerificationCode || !user.emailVerificationExpires) {
      throw new BadRequestException('No verification code found. Please request a new one.');
    }

    if (user.emailVerificationExpires < new Date()) {
      throw new BadRequestException('Verification code has expired. Please request a new one.');
    }

    if (user.emailVerificationCode !== verifyEmailDto.code) {
      throw new BadRequestException('Invalid verification code');
    }

    // Mark email as verified and clear verification fields
    user.isEmailVerified = true;
    user.emailVerificationCode = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();

    return this.generateAuthResponse(user);
  }

  async resendVerificationCode(resendDto: ResendVerificationDto): Promise<{ message: string }> {
    const user = await this.userModel.findOne({ email: resendDto.email });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.isEmailVerified) {
      throw new BadRequestException('Email is already verified');
    }

    // Generate new verification code
    const verificationCode = this.emailService.generateVerificationCode();
    const verificationExpires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    user.emailVerificationCode = verificationCode;
    user.emailVerificationExpires = verificationExpires;
    await user.save();

    // Send verification email
    try {
      await this.emailService.sendVerificationEmail(
        user.email,
        user.username,
        verificationCode
      );
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      throw new BadRequestException('Failed to send verification email. Please try again later.');
    }

    return {
      message: 'Verification code sent successfully. Please check your email.',
    };
  }

  async refreshToken(user: any): Promise<AuthResponseDto> {
    const fullUser = await this.usersService.findById(user.userId);
    if (!fullUser || !fullUser.isActive) {
      throw new UnauthorizedException('User not found or inactive');
    }

    // Update last active
    await this.usersService.updateLastActive((fullUser._id as any).toString());

    return this.generateAuthResponse(fullUser);
  }

  // Debug method to get all users
  async debugGetUsers(): Promise<User[]> {
    console.log('🔧 Fetching all users for debugging');
    try {
      const users = await this.userModel.find({}).select('-passwordHash').limit(10).exec();
      console.log('✅ Found', users.length, 'users in database');
      return users;
    } catch (error) {
      console.error('❌ Error fetching users:', error);
      throw error;
    }
  }
}
