import { api, API_BASE_URL } from './api';

export interface NotificationSound {
  _id: string;
  name: string;
  displayName: string;
  description?: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  duration: number;
  isDefault: boolean;
  isActive: boolean;
  uploadedBy: {
    _id: string;
    username: string;
  };
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface UserNotificationSettings {
  _id: string;
  userId: string;
  soundEnabled: boolean;
  selectedSoundId?: NotificationSound;
  volume: number;
  desktopNotifications: boolean;
  emailNotifications: boolean;
  notificationTypes: {
    admin_message: boolean;
    first_blood: boolean;
    challenge_solved: boolean;
    new_challenge: boolean;
    new_machine: boolean;
    machine_first_blood: boolean;
    system_announcement: boolean;
    team_invitation: boolean;
    competition_update: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateNotificationSoundDto {
  name: string;
  displayName: string;
  description?: string;
  isDefault?: boolean;
}

export interface UpdateNotificationSoundDto {
  displayName?: string;
  description?: string;
  isDefault?: boolean;
  isActive?: boolean;
}

export interface UpdateUserNotificationSettingsDto {
  soundEnabled?: boolean;
  selectedSoundId?: string;
  volume?: number;
  desktopNotifications?: boolean;
  emailNotifications?: boolean;
  notificationTypes?: {
    admin_message?: boolean;
    first_blood?: boolean;
    challenge_solved?: boolean;
    new_challenge?: boolean;
    new_machine?: boolean;
    machine_first_blood?: boolean;
    system_announcement?: boolean;
    team_invitation?: boolean;
    competition_update?: boolean;
  };
}

export interface NotificationSoundStats {
  totalSounds: number;
  totalUsers: number;
  soundUsageStats: Array<{
    soundName: string;
    usageCount: number;
    userCount: number;
  }>;
}

class NotificationSoundsService {
  // Notification Sounds Management
  async getAllNotificationSounds(): Promise<NotificationSound[]> {
    const response = await api.get('/notifications/sounds');
    return response.data;
  }

  async getNotificationSound(id: string): Promise<NotificationSound> {
    const response = await api.get(`/notifications/sounds/${id}`);
    return response.data;
  }

  async createNotificationSound(
    data: CreateNotificationSoundDto,
    file: File
  ): Promise<{ message: string; sound: NotificationSound }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', data.name);
    formData.append('displayName', data.displayName);
    if (data.description) formData.append('description', data.description);
    if (data.isDefault !== undefined) formData.append('isDefault', data.isDefault.toString());

    const response = await api.post('/notifications/sounds', formData);
    return response.data;
  }

  async updateNotificationSound(
    id: string,
    data: UpdateNotificationSoundDto
  ): Promise<{ message: string; sound: NotificationSound }> {
    const response = await api.put(`/notifications/sounds/${id}`, data);
    return response.data;
  }

  async deleteNotificationSound(id: string): Promise<{ message: string }> {
    const response = await api.delete(`/notifications/sounds/${id}`);
    return response.data;
  }

  async getNotificationSoundFile(id: string): Promise<string> {
    try {
      // Fetch the audio file with proper authentication headers
      const token = localStorage.getItem('mybox_token');
      const response = await fetch(`${API_BASE_URL}/notifications/sounds/${id}/file`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch audio file: ${response.status}`);
      }

      // Create a blob from the response and return a blob URL
      const blob = await response.blob();
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('Error fetching notification sound file:', error);
      throw error;
    }
  }

  async getNotificationSoundStats(): Promise<NotificationSoundStats> {
    const response = await api.get('/notifications/admin/sounds/stats');
    return response.data;
  }

  // User Notification Settings
  async getUserNotificationSettings(): Promise<UserNotificationSettings> {
    const response = await api.get('/notifications/settings');
    return response.data;
  }

  async updateUserNotificationSettings(
    data: UpdateUserNotificationSettingsDto
  ): Promise<{ message: string; settings: UserNotificationSettings }> {
    const response = await api.put('/notifications/settings', data);
    return response.data;
  }

  // Audio playback utilities
  async playNotificationSound(soundId: string, volume: number = 0.7): Promise<void> {
    try {
      const soundUrl = await this.getNotificationSoundFile(soundId);
      const audio = new Audio(soundUrl);
      audio.volume = Math.max(0, Math.min(1, volume));
      
      return new Promise((resolve, reject) => {
        audio.onended = () => {
          // Clean up blob URL to prevent memory leaks
          URL.revokeObjectURL(soundUrl);
          resolve();
        };
        audio.onerror = () => {
          // Clean up blob URL to prevent memory leaks
          URL.revokeObjectURL(soundUrl);
          reject(new Error('Failed to play notification sound'));
        };
        audio.play().catch((error) => {
          // Clean up blob URL to prevent memory leaks
          URL.revokeObjectURL(soundUrl);
          reject(error);
        });
      });
    } catch (error) {
      console.error('Error playing notification sound:', error);
      throw error;
    }
  }

  async preloadNotificationSound(soundId: string): Promise<HTMLAudioElement> {
    const soundUrl = await this.getNotificationSoundFile(soundId);
    const audio = new Audio(soundUrl);
    
    return new Promise((resolve, reject) => {
      audio.oncanplaythrough = () => resolve(audio);
      audio.onerror = () => {
        // Clean up blob URL to prevent memory leaks
        URL.revokeObjectURL(soundUrl);
        reject(new Error('Failed to preload notification sound'));
      };
      audio.load();
    });
  }

  // Browser notification utilities
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      throw new Error('This browser does not support notifications');
    }

    if (Notification.permission === 'granted') {
      return 'granted';
    }

    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      return permission;
    }

    return Notification.permission;
  }

  async showDesktopNotification(
    title: string,
    options: NotificationOptions = {}
  ): Promise<Notification | null> {
    const permission = await this.requestNotificationPermission();
    
    if (permission === 'granted') {
      return new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        ...options,
      });
    }
    
    return null;
  }

  // Notification sound manager for real-time notifications
  private audioCache = new Map<string, HTMLAudioElement>();

  async initializeNotificationSounds(settings: UserNotificationSettings): Promise<void> {
    // Clear existing cache first to ensure we use the new sound
    this.clearAudioCache();
    
    if (!settings.soundEnabled || !settings.selectedSoundId) {
      return;
    }

    try {
      const audio = await this.preloadNotificationSound(settings.selectedSoundId._id);
      audio.volume = settings.volume;
      this.audioCache.set(settings.selectedSoundId._id, audio);
    } catch (error) {
      console.error('Failed to initialize notification sounds:', error);
    }
  }

  async playNotificationForType(
    notificationType: keyof UserNotificationSettings['notificationTypes'],
    settings: UserNotificationSettings
  ): Promise<void> {
    // Check if sound is enabled for this notification type
    if (!settings.soundEnabled || 
        !settings.notificationTypes[notificationType] || 
        !settings.selectedSoundId) {
      return;
    }

    try {
      const soundId = settings.selectedSoundId._id;
      let audio = this.audioCache.get(soundId);

      // If no cached audio or if the cached audio doesn't match current settings, reload it
      if (!audio) {
        audio = await this.preloadNotificationSound(soundId);
        this.audioCache.set(soundId, audio);
      }

      // Always update volume to match current settings
      audio.volume = Math.max(0, Math.min(1, settings.volume));

      // Reset audio to beginning and play
      audio.currentTime = 0;
      await audio.play();
    } catch (error) {
      console.error('Failed to play notification sound:', error);
    }
  }

  clearAudioCache(): void {
    this.audioCache.clear();
  }
}

export const notificationSoundsService = new NotificationSoundsService();