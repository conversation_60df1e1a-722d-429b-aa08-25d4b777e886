import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useApp } from '../../contexts/AppContext';
import {
  Trophy,
  Medal,
  Award,
  Crown,
  Users,
  TrendingUp,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  Star,
  Zap,
  Target,
  Activity,
  Flame,
  Shield,
  Hexagon,
  Download,
  Camera
} from 'lucide-react';
import './Leaderboard.css';

export function Leaderboard() {
  const { state, fetchUserLeaderboard, fetchTeamLeaderboard, fetchTopUsers, fetchTopTeams, fetchUserRanking } = useApp();
  const [activeTab, setActiveTab] = useState<'users' | 'teams'>('users');
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const downloadRef = useRef<HTMLDivElement>(null);
  const pageSize = 20;

  useEffect(() => {
    const loadInitialData = async () => {
      setIsLoading(true);
      try {
        await Promise.all([
          fetchUserLeaderboard(1, pageSize),
          fetchTeamLeaderboard(1, pageSize),
          fetchTopUsers(10),
          fetchTopTeams(10)
        ]);
        
        // Fetch current user's ranking if logged in
        if (state.auth.user?.id) {
          await fetchUserRanking(state.auth.user.id);
        }
      } catch (error) {
        console.error('Failed to load leaderboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, []);

  useEffect(() => {
    const loadPageData = async () => {
      setIsLoading(true);
      try {
        if (activeTab === 'users') {
          await fetchUserLeaderboard(currentPage, pageSize);
        } else {
          await fetchTeamLeaderboard(currentPage, pageSize);
        }
      } catch (error) {
        console.error('Failed to load page data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPageData();
  }, [activeTab, currentPage]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-400" />;
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />;
      case 3:
        return <Award className="w-6 h-6 text-amber-600" />;
      default:
        return <Trophy className="w-6 h-6 text-slate-400" />;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border-yellow-500/30';
      case 2:
        return 'bg-gradient-to-r from-gray-500/20 to-gray-600/20 border-gray-500/30';
      case 3:
        return 'bg-gradient-to-r from-amber-500/20 to-amber-600/20 border-amber-500/30';
      default:
        return 'bg-slate-900/50 border-slate-800';
    }
  };

  const formatTimeAgo = (timestamp?: string) => {
    if (!timestamp) return 'Never';
    
    const now = new Date();
    const date = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Less than an hour ago';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  // Enhanced avatar generation function using API
  const generateAvatar = (username: string, rank?: number, isTeam: boolean = false) => {
    const isTop3 = rank && rank <= 3;
    
    // Generate avatar URL using DiceBear API
    const getAvatarUrl = () => {
      if (isTeam) {
        // Use initials style for teams with team icon
        return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(username)}&backgroundColor=6366f1,8b5cf6,a855f7,ec4899,f97316&fontSize=40&fontWeight=600`;
      } else {
        // Use avataaars style for users
        return `https://api.dicebear.com/7.x/avataaars/svg?seed=${encodeURIComponent(username)}&backgroundColor=6366f1,8b5cf6,a855f7,ec4899,f97316`;
      }
    };
    
    // Special ring colors for top 3
    const getRingClass = () => {
      if (!isTop3) return '';
      switch (rank) {
        case 1: return 'ring-2 ring-yellow-400/60 ring-offset-2 ring-offset-transparent';
        case 2: return 'ring-2 ring-gray-400/60 ring-offset-2 ring-offset-transparent';
        case 3: return 'ring-2 ring-amber-500/60 ring-offset-2 ring-offset-transparent';
        default: return '';
      }
    };
    
    return (
      <motion.div 
        className={`relative w-12 h-12 rounded-full overflow-hidden shadow-lg ${getRingClass()}`}
        whileHover={{ scale: 1.1, rotate: 5 }}
        animate={isTop3 ? { 
          scale: [1, 1.05, 1]
        } : {}}
        transition={isTop3 ? { 
          duration: 2, 
          repeat: Infinity, 
          ease: "easeInOut" 
        } : { type: "spring", stiffness: 300 }}
      >
        {/* Avatar Image */}
        <img
          src={getAvatarUrl()}
          alt={`${username} avatar`}
          className="w-full h-full object-cover rounded-full"
          onError={(e) => {
            // Fallback to a different API if DiceBear fails
            const target = e.target as HTMLImageElement;
            target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&size=48&background=6366f1&color=ffffff&bold=true&format=svg`;
          }}
        />
        
        {/* Top 3 special effects overlay */}
        {isTop3 && (
          <>
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            />
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
              <Crown className="w-2 h-2 text-white" />
            </div>
          </>
        )}
        
        {/* Team icon overlay for teams */}
        {isTeam && (
          <div className="absolute inset-0 bg-black/20 rounded-full flex items-center justify-center">
            <Users className="w-5 h-5 text-white drop-shadow-lg" />
          </div>
        )}
      </motion.div>
    );
  };

  // Enhanced download function for top performers with avatars and special design
  const downloadTopPerformers = async (type: 'users' | 'teams') => {
    const data = type === 'users' ? state.topUsers.slice(0, 10) : state.topTeams.slice(0, 10);
    
    // Create canvas for image generation
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions
    canvas.width = 900;
    canvas.height = 1200;
    
    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, '#0f0f23');
    gradient.addColorStop(0.3, '#1e1b4b');
    gradient.addColorStop(0.7, '#312e81');
    gradient.addColorStop(1, '#0f0f23');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add decorative elements and particles
    ctx.fillStyle = 'rgba(139, 92, 246, 0.08)';
    for (let i = 0; i < 30; i++) {
      const x = Math.random() * canvas.width;
      const y = Math.random() * canvas.height;
      const radius = Math.random() * 25 + 8;
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fill();
    }
    
    // Add some sparkle effects
    ctx.fillStyle = 'rgba(251, 191, 36, 0.3)';
    for (let i = 0; i < 15; i++) {
      const x = Math.random() * canvas.width;
      const y = Math.random() * canvas.height;
      const size = Math.random() * 3 + 1;
      ctx.fillRect(x, y, size, size);
    }
    
    // Title with enhanced styling
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 42px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`🏆 Top 10 ${type.charAt(0).toUpperCase() + type.slice(1)}`, canvas.width / 2, 70);
    
    // Subtitle
    ctx.fillStyle = '#a78bfa';
    ctx.font = 'bold 20px Arial';
    ctx.fillText('Kyubisec Leaderboard', canvas.width / 2, 105);
    
    // Date
    ctx.fillStyle = '#94a3b8';
    ctx.font = '16px Arial';
    ctx.fillText(new Date().toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }), canvas.width / 2, 130);
    
    // Function to load and draw avatar
    const loadAvatar = (username: string, isTeam: boolean = false): Promise<HTMLImageElement> => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        
        const getAvatarUrl = () => {
          if (isTeam) {
            return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(username)}&backgroundColor=6366f1,8b5cf6,a855f7,ec4899,f97316&fontSize=40&fontWeight=600`;
          } else {
            return `https://api.dicebear.com/7.x/avataaars/svg?seed=${encodeURIComponent(username)}&backgroundColor=6366f1,8b5cf6,a855f7,ec4899,f97316`;
          }
        };
        
        img.onload = () => resolve(img);
        img.onerror = () => {
          // Fallback to UI Avatars
          const fallbackImg = new Image();
          fallbackImg.crossOrigin = 'anonymous';
          fallbackImg.onload = () => resolve(fallbackImg);
          fallbackImg.onerror = () => reject(new Error('Failed to load avatar'));
          fallbackImg.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&size=64&background=6366f1&color=ffffff&bold=true&format=svg`;
        };
        img.src = getAvatarUrl();
      });
    };
    
    // Draw leaderboard entries with avatars
    const drawEntries = async () => {
      for (let index = 0; index < data.length; index++) {
        const item = data[index];
        const y = 180 + index * 90;
        const isTop3 = index < 3;
        
        // Enhanced background for top 3
        if (isTop3) {
          // Outer glow effect
          const glowGradient = ctx.createRadialGradient(canvas.width / 2, y, 0, canvas.width / 2, y, 400);
          if (index === 0) {
            glowGradient.addColorStop(0, 'rgba(251, 191, 36, 0.15)');
            glowGradient.addColorStop(1, 'rgba(251, 191, 36, 0)');
          } else if (index === 1) {
            glowGradient.addColorStop(0, 'rgba(156, 163, 175, 0.15)');
            glowGradient.addColorStop(1, 'rgba(156, 163, 175, 0)');
          } else {
            glowGradient.addColorStop(0, 'rgba(245, 158, 11, 0.15)');
            glowGradient.addColorStop(1, 'rgba(245, 158, 11, 0)');
          }
          ctx.fillStyle = glowGradient;
          ctx.fillRect(0, y - 40, canvas.width, 80);
          
          // Main background
          const entryGradient = ctx.createLinearGradient(60, y - 35, 840, y + 35);
          if (index === 0) {
            entryGradient.addColorStop(0, 'rgba(251, 191, 36, 0.25)');
            entryGradient.addColorStop(0.5, 'rgba(245, 158, 11, 0.3)');
            entryGradient.addColorStop(1, 'rgba(251, 191, 36, 0.25)');
          } else if (index === 1) {
            entryGradient.addColorStop(0, 'rgba(156, 163, 175, 0.25)');
            entryGradient.addColorStop(0.5, 'rgba(107, 114, 128, 0.3)');
            entryGradient.addColorStop(1, 'rgba(156, 163, 175, 0.25)');
          } else {
            entryGradient.addColorStop(0, 'rgba(245, 158, 11, 0.25)');
            entryGradient.addColorStop(0.5, 'rgba(180, 83, 9, 0.3)');
            entryGradient.addColorStop(1, 'rgba(245, 158, 11, 0.25)');
          }
          ctx.fillStyle = entryGradient;
          ctx.fillRect(60, y - 35, 780, 70);
          
          // Border effect for top 3
          ctx.strokeStyle = index === 0 ? '#fbbf24' : index === 1 ? '#9ca3af' : '#f59e0b';
          ctx.lineWidth = 3;
          ctx.strokeRect(60, y - 35, 780, 70);
        }
        
        // Enhanced rank circle
        const rankRadius = isTop3 ? 28 : 22;
        const rankColor = isTop3 ? 
          (index === 0 ? '#fbbf24' : index === 1 ? '#9ca3af' : '#f59e0b') : 
          '#8b5cf6';
        
        // Rank circle with glow for top 3
        if (isTop3) {
          ctx.shadowColor = rankColor;
          ctx.shadowBlur = 15;
          ctx.shadowOffsetX = 0;
          ctx.shadowOffsetY = 0;
        }
        
        ctx.fillStyle = rankColor;
        ctx.beginPath();
        ctx.arc(120, y, rankRadius, 0, Math.PI * 2);
        ctx.fill();
        
        // Reset shadow
        ctx.shadowBlur = 0;
        
        // Rank number
        ctx.fillStyle = '#ffffff';
        ctx.font = `bold ${isTop3 ? '20px' : '16px'} Arial`;
        ctx.textAlign = 'center';
        ctx.fillText((index + 1).toString(), 120, y + (isTop3 ? 7 : 5));
        
        // Load and draw avatar
        try {
          const username = type === 'users' ? (item as any).username : (item as any).teamName;
          const avatar = await loadAvatar(username, type === 'teams');
          
          // Avatar circle with enhanced styling for top 3
          const avatarSize = isTop3 ? 56 : 48;
          const avatarX = 180;
          const avatarY = y - avatarSize / 2;
          
          // Avatar glow for top 3
          if (isTop3) {
            ctx.shadowColor = rankColor;
            ctx.shadowBlur = 12;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
          }
          
          // Clip to circle
          ctx.save();
          ctx.beginPath();
          ctx.arc(avatarX + avatarSize / 2, y, avatarSize / 2, 0, Math.PI * 2);
          ctx.clip();
          
          // Draw avatar
          ctx.drawImage(avatar, avatarX, avatarY, avatarSize, avatarSize);
          ctx.restore();
          
          // Reset shadow
          ctx.shadowBlur = 0;
          
          // Avatar border for top 3
          if (isTop3) {
            ctx.strokeStyle = rankColor;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(avatarX + avatarSize / 2, y, avatarSize / 2 + 2, 0, Math.PI * 2);
            ctx.stroke();
          }
          
        } catch (error) {
          console.warn('Failed to load avatar, using fallback');
          // Fallback: draw colored circle with initial
          const avatarSize = isTop3 ? 56 : 48;
          const avatarX = 180;
          const username = type === 'users' ? (item as any).username : (item as any).teamName;
          
          ctx.fillStyle = '#6366f1';
          ctx.beginPath();
          ctx.arc(avatarX + avatarSize / 2, y, avatarSize / 2, 0, Math.PI * 2);
          ctx.fill();
          
          ctx.fillStyle = '#ffffff';
          ctx.font = `bold ${avatarSize / 2}px Arial`;
          ctx.textAlign = 'center';
          ctx.fillText(username.charAt(0).toUpperCase(), avatarX + avatarSize / 2, y + avatarSize / 6);
        }
        
        // Name/Team name with enhanced styling
        ctx.fillStyle = isTop3 ? '#ffffff' : '#e2e8f0';
        ctx.font = isTop3 ? 'bold 26px Arial' : 'bold 22px Arial';
        ctx.textAlign = 'left';
        const name = type === 'users' ? (item as any).username : (item as any).teamName;
        ctx.fillText(name, 250, y + 8);
        
        // Special badges for top 3
        if (isTop3) {
          const badgeText = index === 0 ? '👑 CHAMPION' : index === 1 ? '🥈 RUNNER-UP' : '🥉 THIRD PLACE';
          ctx.fillStyle = rankColor;
          ctx.font = 'bold 14px Arial';
          ctx.fillText(badgeText, 250, y + 30);
        }
        
        // Score with enhanced styling
        ctx.fillStyle = isTop3 ? '#ffffff' : '#a78bfa';
        ctx.font = `bold ${isTop3 ? '28px' : '22px'} Arial`;
        ctx.textAlign = 'right';
        const score = type === 'users' ? (item as any).score : (item as any).teamScore;
        ctx.fillText(`${score.toLocaleString()}`, 780, y + 8);
        
        // Points label
        ctx.fillStyle = isTop3 ? '#ffffff' : '#94a3b8';
        ctx.font = `${isTop3 ? '16px' : '14px'} Arial`;
        ctx.fillText('points', 780, y + (isTop3 ? 28 : 25));
        
        // Additional info for teams
        if (type === 'teams') {
          ctx.fillStyle = '#94a3b8';
          ctx.font = '16px Arial';
          ctx.textAlign = 'left';
          ctx.fillText(`${(item as any).memberCount} members`, 250, y - 15);
        }
        
        // Crown emoji for top 3 (positioned near rank)
        if (isTop3) {
          ctx.font = '24px Arial';
          ctx.textAlign = 'left';
          ctx.fillText(index === 0 ? '👑' : index === 1 ? '🥈' : '🥉', 85, y + 8);
        }
        
        // Add sparkle effects for #1
        if (index === 0) {
          ctx.fillStyle = '#fbbf24';
          ctx.font = '16px Arial';
          ctx.fillText('✨', 820, y - 10);
          ctx.fillText('✨', 820, y + 20);
        }
      }
    };
    
    // Draw all entries
    await drawEntries();
    
    // Enhanced footer
    ctx.fillStyle = '#64748b';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('🚀 Generated by Kyubisec Platform', canvas.width / 2, canvas.height - 40);
    
    ctx.fillStyle = '#94a3b8';
    ctx.font = '12px Arial';
    ctx.fillText('Elite Cybersecurity Competition', canvas.width / 2, canvas.height - 20);
    
    // Convert canvas to blob and download
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `kyubisec-top-10-${type}-${new Date().toISOString().split('T')[0]}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    }, 'image/png');
  };

  const currentLeaderboard = activeTab === 'users' ? state.userLeaderboard : state.teamLeaderboard;
  const totalPages = currentLeaderboard ? Math.ceil(currentLeaderboard.total / pageSize) : 0;

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-pink-900/20"></div>
        {[...Array(40)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-purple-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 1, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Floating geometric shapes */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <motion.div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full morph-shape animate-float" />
        <motion.div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '2s' }} />
        <motion.div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-pink-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '4s' }} />
        <motion.div className="absolute bottom-40 right-1/3 w-28 h-28 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '6s' }} />
      </div>

      <div className="relative z-10 space-y-12 p-8">
        {/* Hero Header */}
        <motion.div
          className="text-center space-y-6"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          <div className="flex items-center justify-center space-x-4">
            <motion.div
              className="relative p-4 glass-card rounded-2xl"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Trophy className="w-12 h-12 text-yellow-400 trophy-glow" />
              <motion.div
                className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center"
                animate={{ rotate: 360 }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <Sparkles className="w-3 h-3 text-white" />
              </motion.div>
            </motion.div>
            <motion.h1
              className="text-6xl font-black bg-gradient-to-r from-yellow-400 via-orange-400 to-red-600 bg-clip-text text-transparent"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              Hall of Fame
            </motion.h1>
          </div>
          <motion.p
            className="text-xl text-purple-200/80 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            Compete with the best hackers and teams in the cybersecurity arena
          </motion.p>
        </motion.div>

      {/* Tab Navigation */}
      <motion.div
        className="flex justify-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.6 }}
      >
        <div className="glass-card p-1 rounded-2xl border border-purple-500/30">
          <motion.button
            onClick={() => {
              setActiveTab('users');
              setCurrentPage(1);
            }}
            className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
              activeTab === 'users'
                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg shadow-purple-500/25'
                : 'text-purple-200 hover:text-white hover:bg-purple-500/20'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="flex items-center space-x-3">
              <Trophy className="w-5 h-5" />
              <span>Users</span>
            </div>
          </motion.button>
          <motion.button
            onClick={() => {
              setActiveTab('teams');
              setCurrentPage(1);
            }}
            className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
              activeTab === 'teams'
                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg shadow-purple-500/25'
                : 'text-purple-200 hover:text-white hover:bg-purple-500/20'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="flex items-center space-x-3">
              <Users className="w-5 h-5" />
              <span>Teams</span>
            </div>
          </motion.button>
        </div>
      </motion.div>

      {/* Statistics Cards */}
      {state.userRanking && activeTab === 'users' && (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          <motion.div
            className="glass-card p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-300"
            whileHover={{ scale: 1.02, y: -5 }}
          >
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-lg">
                <Trophy className="w-6 h-6 text-yellow-400 trophy-glow" />
              </div>
              <h3 className="text-lg font-semibold text-white">Your Rank</h3>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                #{state.userRanking.rank}
              </p>
              <p className="text-sm text-purple-200/80">out of {state.userLeaderboard?.total || 0} users</p>
              <p className="text-xs text-purple-300/60 mt-1">{state.userRanking.percentile}th percentile</p>
            </div>
          </motion.div>

          <motion.div
            className="glass-card p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-300"
            whileHover={{ scale: 1.02, y: -5 }}
          >
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg">
                <TrendingUp className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="text-lg font-semibold text-white">Your Score</h3>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                {state.userRanking.score.toLocaleString()}
              </p>
              <p className="text-sm text-purple-200/80">points</p>
              <p className="text-xs text-purple-300/60 mt-1">{state.userRanking.solvedChallenges} challenges solved</p>
            </div>
          </motion.div>

          <motion.div
            className="glass-card p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-300"
            whileHover={{ scale: 1.02, y: -5 }}
          >
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-lg">
                <Calendar className="w-6 h-6 text-pink-400" />
              </div>
              <h3 className="text-lg font-semibold text-white">Last Solve</h3>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-white">{formatTimeAgo(state.userRanking.lastSolved)}</p>
              <p className="text-xs text-purple-300/60 mt-1">Keep the momentum going!</p>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Main Leaderboard */}
      <motion.div
        className="glass-card border border-purple-500/30 overflow-hidden"
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0, duration: 0.6 }}
      >
        <div className="p-6 border-b border-purple-500/20 bg-gradient-to-r from-purple-500/10 to-pink-500/10">
          <div className="flex items-center justify-between">
            <motion.h2
              className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.2, duration: 0.5 }}
            >
              {activeTab === 'users' ? 'Global User Rankings' : 'Team Rankings'}
            </motion.h2>
            <motion.div
              className="flex items-center space-x-2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.2, duration: 0.5 }}
            >
              <div className="p-1 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg">
                <Trophy className="w-5 h-5 text-purple-400" />
              </div>
              <span className="text-sm text-purple-200/80">Updated in real-time</span>
            </motion.div>
          </div>
        </div>

        {isLoading ? (
          <div className="p-12 text-center">
            <div className="inline-flex items-center space-x-3 text-purple-200/80">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500/30 border-t-purple-400"></div>
              <span className="text-lg">Loading leaderboard...</span>
            </div>
          </div>
        ) : (
          <>
            <div className="divide-y divide-purple-500/10">
              {activeTab === 'users' && state.userLeaderboard?.users.map((entry, index) => {
                const isTop3 = entry.rank <= 3;
                
                // Enhanced styling for top 3
                const getTop3Styling = () => {
                  switch (entry.rank) {
                    case 1:
                      return {
                        bg: 'bg-gradient-to-r from-yellow-400/30 via-yellow-500/20 to-orange-500/30',
                        border: 'border-l-8 border-yellow-400',
                        glow: 'shadow-2xl shadow-yellow-500/30',
                        textGlow: 'drop-shadow-lg',
                        particles: 'from-yellow-400 to-orange-500'
                      };
                    case 2:
                      return {
                        bg: 'bg-gradient-to-r from-gray-300/30 via-gray-400/20 to-gray-500/30',
                        border: 'border-l-8 border-gray-400',
                        glow: 'shadow-2xl shadow-gray-400/30',
                        textGlow: 'drop-shadow-lg',
                        particles: 'from-gray-400 to-gray-600'
                      };
                    case 3:
                      return {
                        bg: 'bg-gradient-to-r from-amber-400/30 via-amber-500/20 to-orange-600/30',
                        border: 'border-l-8 border-amber-500',
                        glow: 'shadow-2xl shadow-amber-500/30',
                        textGlow: 'drop-shadow-lg',
                        particles: 'from-amber-500 to-orange-600'
                      };
                    default:
                      return null;
                  }
                };
                
                const top3Style = getTop3Styling();
                
                return (
                  <motion.div
                    key={entry.userId}
                    className={`relative overflow-hidden transition-all duration-500 ${
                      entry.userId === state.auth.user?.id
                        ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-l-4 border-purple-500 p-6'
                        : isTop3
                          ? `${top3Style?.bg} ${top3Style?.border} ${top3Style?.glow} p-6 m-1 rounded-xl hover:scale-[1.02]`
                          : 'hover:bg-purple-500/10 border-l-4 border-transparent p-6'
                    }`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05, duration: 0.3 }}
                    whileHover={{ 
                      scale: isTop3 ? 1.02 : 1.01, 
                      x: isTop3 ? 8 : 5,
                      transition: { type: "spring", stiffness: 300 }
                    }}
                  >
                    {/* Animated background particles for top 3 - reduced count */}
                    {isTop3 && (
                      <div className="absolute inset-0 overflow-hidden rounded-xl">
                        {[...Array(4)].map((_, i) => (
                          <motion.div
                            key={i}
                            className={`absolute w-1.5 h-1.5 bg-gradient-to-r ${top3Style?.particles} rounded-full opacity-50`}
                            style={{
                              left: `${Math.random() * 100}%`,
                              top: `${Math.random() * 100}%`,
                            }}
                            animate={{
                              y: [0, -20, 0],
                              x: [0, Math.random() * 10 - 5, 0],
                              opacity: [0.5, 0.8, 0.5],
                              scale: [1, 1.2, 1],
                            }}
                            transition={{
                              duration: 3 + Math.random() * 2,
                              repeat: Infinity,
                              delay: Math.random() * 2,
                            }}
                          />
                        ))}
                      </div>
                    )}

                    {/* Subtle glowing border effect for top 3 */}
                    {isTop3 && (
                      <motion.div
                        className={`absolute inset-0 rounded-xl bg-gradient-to-r ${top3Style?.particles} opacity-15`}
                        animate={{
                          opacity: [0.15, 0.25, 0.15],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                    )}

                    <div className="relative z-10 flex items-center space-x-4">
                      {/* Compact rank icon with effects */}
                      <div className="flex-shrink-0 relative">
                        <motion.div
                          animate={isTop3 ? { 
                            rotate: [0, 5, -5, 0],
                            scale: [1, 1.05, 1]
                          } : {}}
                          transition={{ 
                            duration: 3, 
                            repeat: Infinity, 
                            repeatDelay: 3,
                            ease: "easeInOut"
                          }}
                        >
                          {getRankIcon(entry.rank)}
                          
                          {/* Subtle glow effect for rank 1 */}
                          {entry.rank === 1 && (
                            <motion.div
                              className="absolute inset-0 bg-yellow-400 rounded-full blur-lg opacity-20"
                              animate={{
                                scale: [1, 1.3, 1],
                                opacity: [0.2, 0.4, 0.2],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                              }}
                            />
                          )}
                        </motion.div>
                      </div>
                      
                      {/* Compact rank badge */}
                      <div className="flex-shrink-0 relative">
                        <motion.div
                          className={`${isTop3 ? 'w-14 h-14' : 'w-12 h-12'} rounded-full flex items-center justify-center font-bold ${isTop3 ? 'text-xl' : 'text-lg'} ${getRankColor(entry.rank)} relative overflow-hidden`}
                          animate={isTop3 ? { 
                            scale: [1, 1.08, 1],
                          } : {}}
                          transition={{ 
                            duration: 2.5, 
                            repeat: Infinity, 
                            repeatDelay: 2,
                            ease: "easeInOut"
                          }}
                        >
                          <span className={`relative z-10 ${top3Style?.textGlow}`}>#{entry.rank}</span>
                          
                          {/* Subtle rotating gradient overlay for top 3 */}
                          {isTop3 && (
                            <motion.div
                              className={`absolute inset-0 bg-gradient-to-r ${top3Style?.particles} opacity-20 rounded-full`}
                              animate={{ rotate: 360 }}
                              transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                            />
                          )}
                        </motion.div>
                        
                        {/* Smaller floating crown for rank 1 */}
                        {entry.rank === 1 && (
                          <motion.div
                            className="absolute -top-1 -right-1"
                            animate={{
                              y: [0, -3, 0],
                            }}
                            transition={{
                              duration: 3,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          >
                            <div className="w-5 h-5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                              <Crown className="w-2.5 h-2.5 text-white" />
                            </div>
                          </motion.div>
                        )}
                      </div>

                      {/* Avatar with subtle effects */}
                      <div className="flex-shrink-0 relative">
                        <motion.div
                          whileHover={{ scale: 1.15, rotate: 8 }}
                          transition={{ type: "spring", stiffness: 300 }}
                          className="relative"
                        >
                          {generateAvatar(entry.username, entry.rank)}
                          
                          {/* Subtle avatar glow effect for top 3 */}
                          {isTop3 && (
                            <motion.div
                              className={`absolute inset-0 rounded-full bg-gradient-to-r ${top3Style?.particles} opacity-30 blur-sm`}
                              animate={{
                                scale: [1, 1.2, 1],
                                opacity: [0.3, 0.5, 0.3],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                              }}
                            />
                          )}
                        </motion.div>
                      </div>

                      {/* Compact user info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <motion.h3 
                            className={`font-bold truncate ${isTop3 ? 'text-xl text-white' : 'text-lg text-white'} ${top3Style?.textGlow}`}
                          >
                            {entry.username}
                          </motion.h3>
                          
                          {entry.userId === state.auth.user?.id && (
                            <span className="px-2 py-1 bg-purple-500/30 text-purple-300 rounded text-xs font-medium border border-purple-400/50">
                              You
                            </span>
                          )}
                          
                          {/* Compact effects for top 3 */}
                          {isTop3 && (
                            <div className="flex items-center space-x-1">
                              <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ duration: 3, repeat: Infinity }}
                              >
                                <Sparkles className="w-4 h-4 text-yellow-400" />
                              </motion.div>
                              
                              {entry.rank === 1 && (
                                <motion.div
                                  animate={{ 
                                    scale: [1, 1.2, 1],
                                    opacity: [0.7, 1, 0.7]
                                  }}
                                  transition={{ duration: 1.5, repeat: Infinity }}
                                >
                                  <Flame className="w-3 h-3 text-orange-400" />
                                </motion.div>
                              )}
                              
                              {entry.rank === 2 && (
                                <Shield className="w-3 h-3 text-gray-300" />
                              )}
                              
                              {entry.rank === 3 && (
                                <Zap className="w-3 h-3 text-amber-400" />
                              )}
                            </div>
                          )}
                        </div>
                        
                        <p className="text-sm text-slate-300 mt-0.5">
                          {entry.solvedChallenges} challenges solved • {entry.totalSubmissions} total submissions
                        </p>
                        
                        {/* Compact achievement badge for top 3 */}
                        {isTop3 && (
                          <motion.div
                            className={`inline-block px-2 py-0.5 rounded-full text-xs font-bold bg-gradient-to-r ${top3Style?.particles} text-white shadow-sm mt-1`}
                            animate={{ opacity: [0.8, 1, 0.8] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          >
                            {entry.rank === 1 ? '🏆 CHAMPION' : entry.rank === 2 ? '🥈 RUNNER-UP' : '🥉 THIRD PLACE'}
                          </motion.div>
                        )}
                      </div>

                      {/* Compact score display */}
                      <div className="flex-shrink-0 text-right">
                        <motion.div 
                          className={`${isTop3 ? 'text-3xl' : 'text-2xl'} font-black bg-gradient-to-r ${isTop3 ? top3Style?.particles : 'from-purple-400 to-pink-400'} bg-clip-text text-transparent`}
                          animate={isTop3 ? {
                            scale: [1, 1.03, 1],
                          } : {}}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          {entry.score.toLocaleString()}
                        </motion.div>
                        <div className={`text-xs ${isTop3 ? 'text-white/80' : 'text-purple-300/60'} font-medium`}>
                          points
                        </div>
                        
                        {/* Compact activity indicator for top 3 */}
                        {isTop3 && (
                          <div className="flex items-center justify-end space-x-1 mt-0.5">
                            <Activity className="w-2.5 h-2.5 text-green-400" />
                            <span className="text-xs text-green-400 font-medium">Hot</span>
                          </div>
                        )}
                      </div>

                      {/* Compact time display */}
                      <div className="flex-shrink-0 text-right">
                        <div className={`text-sm ${isTop3 ? 'text-white' : 'text-slate-400'} font-medium`}>
                          Last solve
                        </div>
                        <div className={`text-xs ${isTop3 ? 'text-white/70' : 'text-slate-500'}`}>
                          {formatTimeAgo(entry.lastSolved)}
                        </div>
                        
                        {/* Compact activity dot for top 3 */}
                        {isTop3 && (
                          <div className="flex items-center justify-end space-x-1 mt-0.5">
                            <motion.div 
                              className={`w-1.5 h-1.5 rounded-full bg-gradient-to-r ${top3Style?.particles}`}
                              animate={{ opacity: [0.6, 1, 0.6] }}
                              transition={{ duration: 2, repeat: Infinity }}
                            />
                            <span className="text-xs text-white/60">Live</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                );
              })}

              {activeTab === 'teams' && state.teamLeaderboard?.teams.map((entry, index) => {
                const isTop3 = entry.rank <= 3;
                const specialBg = isTop3 ? {
                  1: 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border-yellow-500/50',
                  2: 'bg-gradient-to-r from-gray-500/20 to-gray-600/20 border-gray-500/50',
                  3: 'bg-gradient-to-r from-amber-500/20 to-amber-600/20 border-amber-500/50'
                }[entry.rank] : '';
                
                const teamColors = [
                  'from-purple-500 to-blue-500',
                  'from-green-500 to-teal-500',
                  'from-pink-500 to-purple-500',
                  'from-orange-500 to-red-500',
                  'from-indigo-500 to-purple-500',
                  'from-cyan-500 to-blue-500'
                ];
                
                const teamColorClass = teamColors[entry.teamName.length % teamColors.length];
                
                return (
                  <motion.div
                    key={entry.teamId}
                    className={`p-6 transition-all duration-300 border-l-4 ${
                      isTop3
                        ? `${specialBg} hover:scale-[1.02]`
                        : 'hover:bg-purple-500/10 border-transparent'
                    }`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05, duration: 0.3 }}
                    whileHover={{ scale: isTop3 ? 1.02 : 1.01, x: 5 }}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <motion.div
                          animate={isTop3 ? { rotate: [0, 5, -5, 0] } : {}}
                          transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                        >
                          {getRankIcon(entry.rank)}
                        </motion.div>
                      </div>
                      
                      <div className="flex-shrink-0">
                        <motion.div
                          className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${getRankColor(entry.rank)}`}
                          animate={isTop3 ? { scale: [1, 1.1, 1] } : {}}
                          transition={{ duration: 2, repeat: Infinity, repeatDelay: 2 }}
                        >
                          #{entry.rank}
                        </motion.div>
                      </div>

                      <div className="flex-shrink-0">
                        {generateAvatar(entry.teamName, entry.rank, true)}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <h3 className={`font-semibold truncate ${isTop3 ? 'text-white text-lg' : 'text-white'}`}>
                            {entry.teamName}
                          </h3>
                          {isTop3 && (
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 3, repeat: Infinity }}
                            >
                              <Sparkles className="w-4 h-4 text-yellow-400" />
                            </motion.div>
                          )}
                        </div>
                        <p className="text-sm text-slate-400">
                          {entry.memberCount} members • {entry.solvedChallenges} challenges solved
                        </p>
                        <p className="text-xs text-slate-500">
                          Captain: {entry.captain.username}
                        </p>
                      </div>

                      <div className="flex-shrink-0 text-right">
                        <div className={`text-2xl font-bold ${isTop3 ? 'text-3xl' : ''} bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent`}>
                          {entry.teamScore.toLocaleString()}
                        </div>
                        <div className="text-xs text-purple-300/60">
                          team points
                        </div>
                      </div>

                      <div className="flex-shrink-0 text-right">
                        <div className="text-sm text-slate-400">
                          Last solve
                        </div>
                        <div className="text-xs text-slate-500">
                          {formatTimeAgo(entry.lastSolved)}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {/* Pagination */}
            {currentLeaderboard && totalPages > 1 && (
              <div className="p-6 border-t border-purple-500/20 bg-gradient-to-r from-purple-500/5 to-pink-500/5">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-purple-200/80">
                    Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, currentLeaderboard.total)} of {currentLeaderboard.total} entries
                  </p>
                  <div className="flex items-center space-x-2">
                    <motion.button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="p-2 text-purple-300 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <ChevronLeft className="w-5 h-5" />
                    </motion.button>
                    
                    <div className="flex space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <motion.button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`px-3 py-1 rounded text-sm transition-all duration-200 ${
                              currentPage === page
                                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                                : 'text-purple-300 hover:text-white hover:bg-purple-500/20'
                            }`}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            {page}
                          </motion.button>
                        );
                      })}
                    </div>
                    
                    <motion.button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="p-2 text-purple-300 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <ChevronRight className="w-5 h-5" />
                    </motion.button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </motion.div>

      {/* Top Performers Section */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 gap-6"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.4, duration: 0.6 }}
      >
        {/* Top Users */}
        <motion.div
          className="glass-card p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-300"
          whileHover={{ scale: 1.02, y: -5 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-lg">
                <Crown className="w-6 h-6 text-yellow-400 trophy-glow" />
              </div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">Top 10 Users</h3>
            </div>
            <motion.button
              onClick={() => downloadTopPerformers('users')}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 hover:from-yellow-500/30 hover:to-orange-500/30 border border-yellow-500/30 rounded-lg text-yellow-400 hover:text-yellow-300 transition-all duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Download className="w-4 h-4" />
              <span className="text-sm font-medium">Download</span>
            </motion.button>
          </div>
          <div className="space-y-4">
            {state.topUsers.slice(0, 10).map((user, index) => (
              <motion.div
                key={user.userId}
                className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
                  index < 3
                    ? `${
                        index === 0 ? 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border border-yellow-500/30' :
                        index === 1 ? 'bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-500/30' :
                        'bg-gradient-to-r from-amber-500/20 to-amber-600/20 border border-amber-500/30'
                      } hover:scale-[1.02]`
                    : 'hover:bg-purple-500/10'
                }`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.6 + index * 0.05, duration: 0.3 }}
                whileHover={{ x: 5, scale: index < 3 ? 1.02 : 1 }}
              >
                <div className="flex-shrink-0 relative">
                  {generateAvatar(user.username, index + 1)}
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg">
                    {index + 1}
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className={`font-semibold truncate ${index < 3 ? 'text-white text-lg' : 'text-white'}`}>
                      {user.username}
                    </p>
                    {index < 3 && (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 3, repeat: Infinity }}
                      >
                        <Sparkles className="w-4 h-4 text-yellow-400" />
                      </motion.div>
                    )}
                  </div>
                  <p className="text-sm text-purple-200/80">{user.score.toLocaleString()} points</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Top Teams */}
        <motion.div
          className="glass-card p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-300"
          whileHover={{ scale: 1.02, y: -5 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg">
                <Users className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Top 10 Teams</h3>
            </div>
            <motion.button
              onClick={() => downloadTopPerformers('teams')}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 hover:from-purple-500/30 hover:to-pink-500/30 border border-purple-500/30 rounded-lg text-purple-400 hover:text-purple-300 transition-all duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Download className="w-4 h-4" />
              <span className="text-sm font-medium">Download</span>
            </motion.button>
          </div>
          <div className="space-y-4">
            {state.topTeams.slice(0, 10).map((team, index) => (
              <motion.div
                key={team.teamId}
                className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
                  index < 3
                    ? `${
                        index === 0 ? 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border border-yellow-500/30' :
                        index === 1 ? 'bg-gradient-to-r from-gray-500/20 to-gray-600/20 border border-gray-500/30' :
                        'bg-gradient-to-r from-amber-500/20 to-amber-600/20 border border-amber-500/30'
                      } hover:scale-[1.02]`
                    : 'hover:bg-purple-500/10'
                }`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.6 + index * 0.05, duration: 0.3 }}
                whileHover={{ x: 5, scale: index < 3 ? 1.02 : 1 }}
              >
                <div className="flex-shrink-0 relative">
                  <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${
                    index < 3 ?
                      index === 0 ? 'from-yellow-400 to-yellow-600' :
                      index === 1 ? 'from-gray-400 to-gray-600' :
                      'from-amber-500 to-amber-700'
                    : 'from-purple-500 to-pink-500'
                  } flex items-center justify-center shadow-lg ${index < 3 ? 'ring-2 ring-white/30' : ''}`}>
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg">
                    {index + 1}
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className={`font-semibold truncate ${index < 3 ? 'text-white text-lg' : 'text-white'}`}>
                      {team.teamName}
                    </p>
                    {index < 3 && (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 3, repeat: Infinity }}
                      >
                        <Sparkles className="w-4 h-4 text-yellow-400" />
                      </motion.div>
                    )}
                  </div>
                  <p className="text-sm text-purple-200/80">{team.teamScore.toLocaleString()} points • {team.memberCount} members</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </motion.div>
      
      {/* Gradient overlay for depth */}
      <div className="fixed inset-0 bg-gradient-to-t from-purple-900/20 via-transparent to-purple-900/20 pointer-events-none" />
      </div>
    </div>
  );
}