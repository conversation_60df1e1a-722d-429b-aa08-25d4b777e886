import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, Lock, Zap, Code, Terminal, Eye, Wifi, Bug } from 'lucide-react';

interface LoadingScreenProps {
  isLoading: boolean;
  onComplete?: () => void;
}

const FloatingIcon = ({ icon: Icon, delay = 0, duration = 4 }: { icon: any, delay?: number, duration?: number }) => (
  <motion.div
    className="absolute text-purple-400/30 will-change-transform"
    initial={{ opacity: 0, scale: 0 }}
    animate={{
      opacity: [0, 0.6, 0],
      scale: [0, 1, 0],
      rotate: [0, 180]
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      repeatDelay: 2,
      ease: "easeInOut"
    }}
    style={{
      left: `${Math.random() * 100}%`,
      top: `${Math.random() * 100}%`
    }}
  >
    <Icon className="w-5 h-5" />
  </motion.div>
);

const MatrixRain = () => {
  const chars = '01';
  const columns = 25; // Reduced for better performance

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {Array.from({ length: columns }).map((_, i) => (
        <motion.div
          key={i}
          className="absolute top-0 text-purple-400/20 font-mono text-xs will-change-transform"
          style={{ left: `${(i / columns) * 100}%` }}
          animate={{
            y: ['0vh', '100vh'],
            opacity: [0, 0.8, 0]
          }}
          transition={{
            duration: Math.random() * 2 + 3,
            repeat: Infinity,
            delay: Math.random() * 3,
            ease: 'linear'
          }}
        >
          {Array.from({ length: 15 }).map((_, j) => (
            <div key={j} className="block">
              {chars[Math.floor(Math.random() * chars.length)]}
            </div>
          ))}
        </motion.div>
      ))}
    </div>
  );
};

const GlitchText = ({ text, className = "" }: { text: string, className?: string }) => (
  <div className={`relative ${className}`}>
    <motion.div
      className="absolute inset-0 text-red-500"
      animate={{
        x: [0, -2, 2, 0],
        opacity: [0, 1, 0, 1]
      }}
      transition={{
        duration: 0.2,
        repeat: Infinity,
        repeatDelay: Math.random() * 3 + 1
      }}
    >
      {text}
    </motion.div>
    <motion.div
      className="absolute inset-0 text-blue-500"
      animate={{
        x: [0, 2, -2, 0],
        opacity: [0, 1, 0, 1]
      }}
      transition={{
        duration: 0.2,
        repeat: Infinity,
        repeatDelay: Math.random() * 3 + 1.5
      }}
    >
      {text}
    </motion.div>
    <div className="relative z-10 text-white">
      {text}
    </div>
  </div>
);

const DrivingTunnel = ({ isExiting }: { isExiting: boolean }) => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none">
    {/* Highway tunnel rings - like driving through a tunnel at high speed */}
    {Array.from({ length: 15 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute border-2 border-purple-400/60 rounded-full will-change-transform"
        style={{
          left: '50%',
          top: '50%',
          width: `${(i + 1) * 60}px`,
          height: `${(i + 1) * 60}px`,
          marginLeft: `${-(i + 1) * 30}px`,
          marginTop: `${-(i + 1) * 30}px`,
        }}
        animate={isExiting ? {
          scale: [1, 20], // Massive zoom like driving at light speed
          opacity: [0.8, 0],
          borderWidth: [2, 6, 0]
        } : {
          scale: [0.1, 4],
          opacity: [1, 0]
        }}
        transition={isExiting ? {
          duration: 1.8,
          delay: i * 0.03, // Very fast succession for speed effect
          ease: [0.6, 0, 0.4, 1] // Aggressive acceleration curve
        } : {
          duration: 2.5,
          delay: i * 0.1,
          repeat: Infinity,
          ease: "easeOut"
        }}
      />
    ))}

    {/* Road lane dividers - like highway markings */}
    {Array.from({ length: 8 }).map((_, i) => (
      <motion.div
        key={`lane-${i}`}
        className="absolute bg-purple-300/50 will-change-transform"
        style={{
          left: '49%',
          top: `${15 + i * 8}%`,
          width: '2%',
          height: '3px',
          transformOrigin: 'center'
        }}
        animate={isExiting ? {
          scaleY: [1, 30], // Stretch like speed lines
          scaleX: [1, 0.1],
          opacity: [0.8, 0]
        } : {
          scaleY: [0.5, 3, 0.5],
          opacity: [0.3, 0.8, 0.3]
        }}
        transition={isExiting ? {
          duration: 1.2,
          delay: i * 0.05,
          ease: "easeIn"
        } : {
          duration: 2,
          delay: i * 0.15,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    ))}
  </div>
);

const HexGrid = () => (
  <div className="absolute inset-0 opacity-3">
    <svg className="w-full h-full" viewBox="0 0 800 600">
      {Array.from({ length: 4 }).map((_, row) =>
        Array.from({ length: 6 }).map((_, col) => {
          const x = col * 120 + (row % 2) * 60;
          const y = row * 100;
          return (
            <motion.polygon
              key={`${row}-${col}`}
              points="40,0 80,23.09 80,69.28 40,92.38 0,69.28 0,23.09"
              transform={`translate(${x}, ${y})`}
              stroke="currentColor"
              strokeWidth="1"
              fill="none"
              className="text-purple-400 will-change-transform"
              animate={{
                opacity: [0.1, 0.4, 0.1]
              }}
              transition={{
                duration: 6,
                delay: (row + col) * 0.3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          );
        })
      )}
    </svg>
  </div>
);

const SpeedLines = ({ isExiting }: { isExiting: boolean }) => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none">
    {/* Horizontal speed lines - like car motion blur */}
    {Array.from({ length: 40 }).map((_, i) => (
      <motion.div
        key={`speed-${i}`}
        className="absolute bg-gradient-to-r from-transparent via-purple-400/80 to-transparent will-change-transform"
        style={{
          left: '-20%',
          top: `${Math.random() * 100}%`,
          width: '140%',
          height: `${Math.random() * 3 + 1}px`,
          transformOrigin: 'left center'
        }}
        animate={isExiting ? {
          scaleX: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 100]
        } : {
          scaleX: [0, 0.3, 0],
          opacity: [0, 0.4, 0]
        }}
        transition={isExiting ? {
          duration: 0.6,
          delay: i * 0.01,
          ease: "easeOut"
        } : {
          duration: 3,
          delay: Math.random() * 4,
          repeat: Infinity,
          ease: "linear"
        }}
      />
    ))}

    {/* Vertical data streams */}
    {Array.from({ length: 15 }).map((_, i) => (
      <motion.div
        key={`data-${i}`}
        className="absolute w-1 h-8 bg-gradient-to-b from-purple-400/60 to-transparent rounded-full will-change-transform"
        style={{
          left: `${Math.random() * 100}%`,
          top: '-40px'
        }}
        animate={{
          y: ['0vh', '110vh'],
          opacity: [0, 0.8, 0]
        }}
        transition={{
          duration: Math.random() * 2 + 3,
          repeat: Infinity,
          delay: Math.random() * 5,
          ease: "linear"
        }}
      />
    ))}
  </div>
);

const ParticleSystem = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none">
    {Array.from({ length: 25 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-purple-400/80 rounded-full will-change-transform"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`
        }}
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 0.8, 0],
          y: [0, -80]
        }}
        transition={{
          duration: Math.random() * 3 + 4,
          repeat: Infinity,
          delay: Math.random() * 8,
          ease: "easeOut"
        }}
      />
    ))}
  </div>
);

const CircuitBoard = () => (
  <div className="absolute inset-0 opacity-10">
    <svg className="w-full h-full" viewBox="0 0 800 600">
      {/* Circuit paths */}
      <motion.path
        d="M100,100 L200,100 L200,200 L300,200 L300,300"
        stroke="currentColor"
        strokeWidth="2"
        fill="none"
        className="text-purple-400"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 3, repeat: Infinity, repeatDelay: 1 }}
      />
      <motion.path
        d="M500,100 L600,100 L600,200 L700,200 L700,300"
        stroke="currentColor"
        strokeWidth="2"
        fill="none"
        className="text-pink-400"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 3, delay: 0.5, repeat: Infinity, repeatDelay: 1 }}
      />
      <motion.path
        d="M100,400 L300,400 L300,500 L500,500"
        stroke="currentColor"
        strokeWidth="2"
        fill="none"
        className="text-purple-300"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 3, delay: 1, repeat: Infinity, repeatDelay: 1 }}
      />

      {/* Circuit nodes */}
      {[
        { x: 200, y: 100 }, { x: 300, y: 200 }, { x: 600, y: 100 },
        { x: 700, y: 200 }, { x: 300, y: 400 }, { x: 500, y: 500 }
      ].map((node, i) => (
        <motion.circle
          key={i}
          cx={node.x}
          cy={node.y}
          r="4"
          className="fill-purple-400"
          animate={{
            r: [4, 8, 4],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 2,
            delay: i * 0.3,
            repeat: Infinity
          }}
        />
      ))}
    </svg>
  </div>
);

const LoadingProgress = ({ progress }: { progress: number }) => (
  <div className="w-80 mx-auto">
    <div className="relative">
      {/* Progress bar background */}
      <div className="h-2 bg-purple-900/30 rounded-full overflow-hidden">
        <motion.div
          className="h-full bg-gradient-to-r from-purple-500 via-pink-500 to-purple-600"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        />
      </div>

      {/* Glowing effect */}
      <motion.div
        className="absolute inset-0 h-2 bg-gradient-to-r from-purple-500/50 via-pink-500/50 to-purple-600/50 rounded-full blur-sm"
        initial={{ width: 0 }}
        animate={{ width: `${progress}%` }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      />

      {/* Progress text */}
      <div className="text-center mt-4 text-purple-400 font-mono">
        <motion.span
          key={progress}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {progress}%
        </motion.span>
      </div>
    </div>
  </div>
);

const Cube3D = () => (
  <div className="relative w-28 h-28 mx-auto mb-8" style={{ perspective: '800px' }}>
    <motion.div
      className="relative w-full h-full will-change-transform"
      animate={{
        rotateX: [0, 360],
        rotateY: [0, 360],
        scale: [1, 1.1, 1]
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: "linear"
      }}
      style={{ transformStyle: 'preserve-3d' }}
    >
      {/* Cube faces */}
      {[
        { transform: 'rotateY(0deg) translateZ(64px)', bg: 'bg-purple-500/20' },
        { transform: 'rotateY(90deg) translateZ(64px)', bg: 'bg-pink-500/20' },
        { transform: 'rotateY(180deg) translateZ(64px)', bg: 'bg-purple-600/20' },
        { transform: 'rotateY(-90deg) translateZ(64px)', bg: 'bg-purple-400/20' },
        { transform: 'rotateX(90deg) translateZ(64px)', bg: 'bg-pink-400/20' },
        { transform: 'rotateX(-90deg) translateZ(64px)', bg: 'bg-purple-300/20' }
      ].map((face, i) => (
        <div
          key={i}
          className={`absolute w-32 h-32 border border-purple-400/30 ${face.bg}`}
          style={{ transform: face.transform }}
        >
          <div className="w-full h-full flex items-center justify-center">
            <Shield className="w-8 h-8 text-purple-400/60" />
          </div>
        </div>
      ))}
    </motion.div>
  </div>
);

export function LoadingScreen({ isLoading, onComplete }: LoadingScreenProps) {
  const [progress, setProgress] = useState(0);
  const [loadingStage, setLoadingStage] = useState(0);
  const [isExiting, setIsExiting] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  
  const loadingStages = [
    "Starting the Engine...",
    "Accelerating into Cyberspace...",
    "Navigating Digital Highways...",
    "Loading Penetration Testing Arsenal...",
    "Establishing Secure Tunnels...",
    "Synchronizing Attack Vectors...",
    "Preparing Advanced Scanners...",
    "Calibrating Exploit Frameworks...",
    "Entering the Platform Zone...",
    "Welcome to Rakcha Pentest V2!"
  ];

  useEffect(() => {
    if (!isLoading) return;

    // Try to start background audio automatically
    const playAudio = async () => {
      if (audioRef.current) {
        try {
          audioRef.current.volume = 0.3; // Set volume to 30%
          audioRef.current.loop = true; // Loop the audio
          await audioRef.current.play();
        } catch (error) {
          // Silently handle autoplay restriction - audio will just not play
          console.log('Audio autoplay prevented - continuing without sound');
        }
      }
    };

    // Small delay to ensure audio element is ready
    setTimeout(playAudio, 100);

    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + Math.random() * 8 + 2; // Slower progress
        if (newProgress >= 100) {
          clearInterval(interval);
          // Start exit animation
          setTimeout(() => {
            setIsExiting(true);
            // Fade out and stop audio
            if (audioRef.current) {
              const fadeOut = setInterval(() => {
                if (audioRef.current && audioRef.current.volume > 0.1) {
                  audioRef.current.volume = Math.max(0, audioRef.current.volume - 0.1);
                } else {
                  if (audioRef.current) {
                    audioRef.current.pause();
                    audioRef.current.currentTime = 0;
                  }
                  clearInterval(fadeOut);
                }
              }, 100);
            }
            // Complete after exit animation
            setTimeout(() => onComplete?.(), 2000);
          }, 1000);
          return 100;
        }
        return Math.min(newProgress, 100);
      });
    }, 600); // Slower interval

    const stageInterval = setInterval(() => {
      setLoadingStage(prev => (prev + 1) % loadingStages.length);
    }, 2000); // Slower stage changes

    return () => {
      clearInterval(interval);
      clearInterval(stageInterval);
      // Stop audio when component unmounts
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };
  }, [isLoading, onComplete]);

  const icons = [Shield, Lock, Zap, Code, Terminal, Eye, Wifi, Bug];

  return (
    <AnimatePresence>
      {isLoading && (
        <>
          {/* Performance optimization styles */}
          <style>{`
            .will-change-transform {
              will-change: transform;
              transform: translateZ(0);
              backface-visibility: hidden;
            }
          `}</style>

          {/* Background Audio */}
          <audio
            ref={audioRef}
            preload="auto"
            style={{ display: 'none' }}
          >
            <source src="/audio/hacking-sound.mp3" type="audio/mpeg" />
            Your browser does not support the audio element.
          </audio>

          {/* Main Loading Screen */}
          <motion.div
            className="fixed inset-0 z-50 bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center overflow-hidden will-change-transform"
            initial={{ opacity: 0 }}
            animate={isExiting ? {
              opacity: 1,
              scale: [1, 1.2, 3],
              rotateZ: [0, 0, 10]
            } : { opacity: 1 }}
            exit={{
              opacity: 0,
              scale: 5,
              rotateZ: 15
            }}
            transition={isExiting ? {
              duration: 2,
              ease: [0.25, 0.46, 0.45, 0.94]
            } : { duration: 0.5 }}
          >
          {/* Central Vortex Glow */}
          <motion.div
            className="absolute inset-0 flex items-center justify-center pointer-events-none will-change-transform"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <div className="w-80 h-80 bg-gradient-to-r from-purple-500/15 via-purple-400/8 to-transparent rounded-full blur-2xl" />
          </motion.div>

          {/* Background Effects - Driving into Platform */}
          <DrivingTunnel isExiting={isExiting} />
          <SpeedLines isExiting={isExiting} />
          <HexGrid />
          <MatrixRain />
          <CircuitBoard />
          <ParticleSystem />
          
          {/* Floating Icons - Reduced for performance */}
          {icons.slice(0, 4).map((icon, i) => (
            <FloatingIcon
              key={i}
              icon={icon}
              delay={i * 1}
              duration={4 + i * 0.5}
            />
          ))}

          {/* Main Content with Car Acceleration Effect */}
          <motion.div
            className="relative z-10 text-center will-change-transform"
            animate={isExiting ? {
              scale: [1, 0.5, 0.05], // Shrink dramatically like driving away
              opacity: [1, 0.8, 0],
              z: [0, 200, 1000], // Move far into distance
              rotateX: [0, 15, 45], // Tilt like looking down a road
              y: [0, -50, -200] // Move up like going over a hill
            } : {
              scale: [0.95, 1, 0.95],
              y: [0, -5, 0] // Subtle floating
            }}
            transition={isExiting ? {
              duration: 2,
              ease: [0.6, 0, 0.4, 1] // Fast acceleration curve
            } : {
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            style={isExiting ? {
              perspective: '1200px',
              transformStyle: 'preserve-3d'
            } : {}}
          >
            {/* 3D Cube */}
            <Cube3D />

            {/* Main Title */}
            <motion.div
              className="mb-8 relative"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              {/* Glow effect behind title */}
              <motion.div
                className="absolute inset-0 blur-xl"
                animate={{
                  opacity: [0.3, 0.6, 0.3],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity
                }}
              >
                <div className="text-6xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent">
                  RAKCHA PENTEST
                </div>
              </motion.div>

              <GlitchText
                text="RAKCHA PENTEST"
                className="relative z-10 text-6xl font-bold mb-2 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent"
              />
              <motion.div
                className="text-2xl font-light text-purple-300 relative z-10"
                animate={{
                  opacity: [0.5, 1, 0.5],
                  textShadow: [
                    '0 0 10px rgba(168,85,247,0.5)',
                    '0 0 20px rgba(168,85,247,0.8)',
                    '0 0 10px rgba(168,85,247,0.5)'
                  ]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity
                }}
              >
                V2.0
              </motion.div>
            </motion.div>

            {/* Loading Progress */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1 }}
            >
              <LoadingProgress progress={progress} />
            </motion.div>

            {/* Loading Stage Text with Terminal Effect */}
            <motion.div
              className="mt-8 text-purple-200/80 font-mono text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.5 }}
            >
              <div className="flex items-center justify-center space-x-2">
                <span className="text-purple-400">$</span>
                <motion.span
                  key={loadingStage}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.5 }}
                >
                  {loadingStages[loadingStage]}
                </motion.span>
                <motion.span
                  className="text-purple-400"
                  animate={{ opacity: [1, 0, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                >
                  _
                </motion.span>
              </div>
            </motion.div>

            {/* Scanning Effect */}
            <motion.div
              className="absolute inset-0 pointer-events-none"
              animate={{
                background: [
                  'linear-gradient(90deg, transparent 0%, rgba(168,85,247,0.1) 50%, transparent 100%)',
                  'linear-gradient(90deg, transparent 100%, rgba(168,85,247,0.1) 150%, transparent 200%)'
                ]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </motion.div>
        </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
