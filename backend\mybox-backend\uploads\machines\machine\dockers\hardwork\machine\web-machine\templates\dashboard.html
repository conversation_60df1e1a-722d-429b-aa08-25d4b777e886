{% extends "base.html" %}
{% block title %}Dashboard{% endblock %}
{% block content %}
<h2>Employee Dashboard</h2>

<p>
  <a href="{{ url_for('logout') }}">Logout</a>
  {% if is_admin %}
    | <a href="{{ url_for('add_employee') }}">Add Employee</a>
  {% endif %}
</p>

<table>
  <thead>
    <tr>
      <th>Name</th>
      <th>Salary</th>
      <th>Hire Date</th>
      <th>Post</th>
      {% if is_admin %}
      <th>Actions</th>
      {% endif %}
    </tr>
  </thead>
  <tbody>
    {% for emp in employees %}
    <tr>
      <td>{{ emp.name }}</td>
      <td>{{ "%.2f"|format(emp.salary) }}</td>
      <td>{{ emp.hire_date }}</td>
      <td>{{ emp.post }}</td>
      {% if is_admin %}
      <td>
        <a href="{{ url_for('edit_employee', emp_id=emp.id) }}">Edit</a>
        <form action="{{ url_for('delete_employee', emp_id=emp.id) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure?');">
          <button type="submit">Delete</button>
        </form>
      </td>
      {% endif %}
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endblock %}
