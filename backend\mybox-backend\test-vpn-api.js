const axios = require('axios');

async function testVpnApi() {
  try {
    console.log('Testing VPN API endpoints...');
    
    // Test health endpoint (no auth)
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get('http://localhost:3001/api/vpn/health');
    console.log('✓ Health:', healthResponse.data);
    
    // Test status endpoint (requires auth)
    console.log('2. Testing status endpoint...');
    try {
      const statusResponse = await axios.get('http://localhost:3001/api/vpn/status', {
        headers: {
          'Authorization': 'Bearer fake-token-for-testing'
        }
      });
      console.log('✓ Status:', statusResponse.data);
    } catch (error) {
      console.log('✗ Status error:', error.response?.status, error.response?.data || error.message);
    }
    
    // Test WireGuard Easy directly
    console.log('3. Testing WireGuard Easy directly...');
    try {
      // First authenticate
      const loginResponse = await axios.post('http://localhost:51821/api/session', {
        password: 'yourpassword'
      }, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      const sessionCookie = loginResponse.headers['set-cookie'][0].split(';')[0];
      console.log('✓ WireGuard Easy authentication successful');
      
      // Test client list
      const clientsResponse = await axios.get('http://localhost:51821/api/wireguard/client', {
        headers: {
          'Content-Type': 'application/json',
          'Cookie': sessionCookie
        }
      });
      console.log('✓ WireGuard Easy clients:', clientsResponse.data.length, 'clients found');
      
    } catch (error) {
      console.log('✗ WireGuard Easy error:', error.response?.status, error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testVpnApi();