import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AdminSettings } from '../schemas/admin-settings.schema';

export interface PublicSettings {
  allowRegistration: boolean;
  emailVerification: boolean;
  maintenanceMode: boolean;
  enableTeams: boolean;
  maxTeamSize: number;
  maxConcurrentVMs: number;
  defaultSessionTime: number;
  minPasswordLength: number;
  requirePasswordComplexity: boolean;
}

@Injectable()
export class PublicSettingsService {
  constructor(
    @InjectModel(AdminSettings.name) private adminSettingsModel: Model<AdminSettings>,
  ) {}

  async getPublicSettings(): Promise<PublicSettings> {
    const settings = await this.adminSettingsModel.findOne({ configId: 'default' });
    
    if (!settings) {
      // Return default settings if none exist
      return {
        allowRegistration: true,
        emailVerification: false,
        maintenanceMode: false,
        enableTeams: true,
        maxTeamSize: 5,
        maxConcurrentVMs: 1,
        defaultSessionTime: 2,
        minPasswordLength: 8,
        requirePasswordComplexity: true,
      };
    }

    return {
      allowRegistration: settings.allowRegistration,
      emailVerification: settings.emailVerification,
      maintenanceMode: settings.maintenanceMode,
      enableTeams: settings.enableTeams,
      maxTeamSize: settings.maxTeamSize,
      maxConcurrentVMs: settings.maxConcurrentVMs,
      defaultSessionTime: settings.defaultSessionTime,
      minPasswordLength: settings.minPasswordLength,
      requirePasswordComplexity: settings.requirePasswordComplexity,
    };
  }

  async isMaintenanceMode(): Promise<boolean> {
    const settings = await this.adminSettingsModel.findOne({ configId: 'default' });
    return settings ? settings.maintenanceMode : false;
  }

  async areTeamsEnabled(): Promise<boolean> {
    const settings = await this.adminSettingsModel.findOne({ configId: 'default' });
    return settings ? settings.enableTeams : true;
  }

  async getMaxTeamSize(): Promise<number> {
    const settings = await this.adminSettingsModel.findOne({ configId: 'default' });
    return settings ? settings.maxTeamSize : 5;
  }
}