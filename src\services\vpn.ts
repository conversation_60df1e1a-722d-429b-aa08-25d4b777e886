import { api, API_BASE_URL } from './api';

// VPN Types - Updated for WireGuard Integration
export interface VPNStatus {
  hasVPNConfig: boolean;
  isConnected: boolean; // Add connection status
  vpnInfo?: {
    ipAddress: string;
    subnet: string;
    serverEndpoint: string;
    serverPort: number;
  };
  serverStatus: {
    isRunning: boolean;
    totalUsers: number;
    activeConnections: number;
  };
  userNetworks: Array<{
    machineId: string;
    internalIP: string;
    exposedPorts: Array<{
      internal: number;
      protocol: string;
    }>;
    status: string;
  }>;
}

export interface VPNConnectionLog {
  action: 'connect' | 'disconnect' | 'handshake' | 'error';
  timestamp: string;
  clientIP: string;
  vpnIP: string;
  disconnectedAt?: string;
  connectionStats?: {
    bytesReceived: number;
    bytesSent: number;
    packetsReceived: number;
    packetsSent: number;
    duration: number;
  };
}

export interface VPNSetupInstructions {
  [key: string]: {
    title: string;
    steps: string[];
  };
}

// Legacy types for backward compatibility
export interface VpnStatus {
  connected: boolean;
  ipAddress?: string;
  lastConnection?: string;
  error?: string;
}

export interface VpnServerInfo {
  totalClients: number;
  clients: VpnClient[];
}

export interface VpnClient {
  id: string;
  name: string;
  enabled: boolean;
  address: string;
  latestHandshakeAt?: string;
  transferRx: number;
  transferTx: number;
}

export interface VpnHealthCheck {
  status: 'healthy' | 'unhealthy';
  message: string;
}

export interface ConnectedUser {
  id: string;
  username: string;
  ip: string;
  connectedAt: string;
}

export class VpnService {
  // ===== NEW WIREGUARD API METHODS =====

  /**
   * Generate VPN configuration for the user
   */
  static async generateVPNConfig(): Promise<{ success: boolean; message: string; data: any }> {
    const response = await api.post('/vpn/generate');
    return response.data as { success: boolean; message: string; data: any };
  }

  /**
   * Get VPN status and configuration info
   */
  static async getVPNStatus(): Promise<VPNStatus> {
    const response = await api.get('/vpn/status');
    return (response.data as any).data;
  }

  /**
   * Download VPN configuration file
   */
  static async downloadVPNConfig(): Promise<void> {
    const token = localStorage.getItem('mybox_token');
    const response = await fetch(`${API_BASE_URL}/vpn/config`, {
      method: 'GET',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    // Handle file download
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // Get filename from Content-Disposition header or use default
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = 'mybox-vpn.conf';

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  }

  /**
   * Regenerate VPN configuration with new keys
   */
  static async regenerateVPNConfig(): Promise<{ success: boolean; message: string; data: any }> {
    const response = await api.put('/vpn/regenerate');
    return response.data as { success: boolean; message: string; data: any };
  }

  /**
   * Revoke VPN access and delete configuration
   */
  static async revokeVPNAccess(): Promise<{ success: boolean; message: string }> {
    const response = await api.delete('/vpn/revoke');
    return response.data as { success: boolean; message: string };
  }

  /**
   * Get user's VPN connection logs
   */
  static async getConnectionLogs(): Promise<VPNConnectionLog[]> {
    const response = await api.get('/vpn/logs');
    return (response.data as any).data;
  }

  /**
   * Get VPN setup instructions for different operating systems
   */
  static async getSetupInstructions(): Promise<VPNSetupInstructions> {
    const response = await api.get('/vpn/setup-instructions');
    return (response.data as any).data;
  }

  // ===== LEGACY API METHODS (for backward compatibility) =====

  // Get VPN server status and connected clients
  static async getStatus(): Promise<VpnServerInfo> {
    const response = await api.get<VpnServerInfo>('/vpn/status');
    return response.data;
  }

  // Create and download VPN configuration
  static async createAndDownloadConfig(): Promise<void> {
    const token = localStorage.getItem('mybox_token');
    const response = await fetch(`${API_BASE_URL}/vpn/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    // Handle file download
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    // Get filename from Content-Disposition header or use default
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = 'wireguard-config.conf';
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }
    
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  }

  // Download existing VPN configuration
  static async downloadConfig(): Promise<void> {
    const token = localStorage.getItem('mybox_token');
    const response = await fetch(`${API_BASE_URL}/vpn/config`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    // Handle file download
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    // Get filename from Content-Disposition header or use default
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = 'wireguard-config.conf';
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }
    
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  }

  // Revoke VPN access
  static async revokeAccess(): Promise<{ success: boolean; message: string }> {
    const response = await api.delete<{ success: boolean; message: string }>('/vpn/user');
    return response.data;
  }

  // Enable VPN client
  static async enableClient(): Promise<{ success: boolean; message: string }> {
    const response = await api.post<{ success: boolean; message: string }>('/vpn/enable');
    return response.data;
  }

  // Disable VPN client
  static async disableClient(): Promise<{ success: boolean; message: string }> {
    const response = await api.post<{ success: boolean; message: string }>('/vpn/disable');
    return response.data;
  }

  // List all VPN users (admin only)
  static async listUsers(): Promise<VpnClient[]> {
    const response = await api.get<VpnClient[]>('/vpn/users');
    return response.data;
  }

  // Health check (no auth required)
  static async healthCheck(): Promise<VpnHealthCheck> {
    const response = await fetch(`${API_BASE_URL}/vpn/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Helper method to check if user has VPN access
  static async hasVpnAccess(): Promise<boolean> {
    try {
      const serverInfo = await this.getStatus();
      const currentUser = JSON.parse(localStorage.getItem('mybox_user') || '{}');
      
      // Check if current user has a client configured
      return serverInfo.clients.some(client => client.name === currentUser.username);
    } catch (error) {
      console.error('Error checking VPN access:', error);
      return false;
    }
  }

  // Helper method to get current user's VPN client info
  static async getCurrentUserClient(): Promise<VpnClient | null> {
    try {
      const serverInfo = await this.getStatus();
      const currentUser = JSON.parse(localStorage.getItem('mybox_user') || '{}');
      
      return serverInfo.clients.find(client => client.name === currentUser.username) || null;
    } catch (error) {
      console.error('Error getting current user client:', error);
      return null;
    }
  }

  // Convert VPN clients to connected users format for UI
  static convertClientsToConnectedUsers(clients: VpnClient[]): ConnectedUser[] {
    return clients
      .filter(client => client.enabled && client.latestHandshakeAt)
      .map(client => ({
        id: client.id,
        username: client.name,
        ip: client.address,
        connectedAt: client.latestHandshakeAt || new Date().toISOString(),
      }));
  }

  // Format transfer data
  static formatTransferData(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}