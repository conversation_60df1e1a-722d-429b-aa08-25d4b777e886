# Machine Deployment and VPN Access System

## 🎯 OBJECTIVE
Create a comprehensive system where administrators can upload machine templates as compressed files (ZIP/7z), and users can launch isolated instances of these machines with secure VPN access through WireGuard.

## 📋 SYSTEM OVERVIEW

### Current Architecture
The platform already has:
- ✅ Machine template management system
- ✅ Docker container orchestration
- ✅ User-specific network isolation (10.10.x.0/24 subnets)
- ✅ WireGuard Easy integration scripts
- ✅ File upload infrastructure
- ✅ Instance management with auto-shutdown

### Target Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Admin Panel   │───▶│  File Processor  │───▶│ Docker Registry │
│  (Upload ZIP)   │    │ (Extract & Build)│    │   (Templates)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  User Request   │───▶│ Instance Manager │───▶│ Docker Network  │
│ (Launch Machine)│    │ (Spawn Container)│    │  (Isolated IP)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   VPN Service   │◀───│  Network Bridge  │───▶│ User Container  │
│ (WireGuard Cfg) │    │ (Route Traffic)  │    │ (10.10.x.y/24)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🏗️ IMPLEMENTATION PLAN

### Phase 1: Enhanced File Upload System

#### 1.1 Archive Upload Handler
**Location**: `backend/mybox-backend/src/virtual-machines/services/machine-template.service.ts`

**New Method**: `uploadMachineArchive(templateId, file, extractPath)`
```typescript
async uploadMachineArchive(
  templateId: string,
  file: Express.Multer.File,
  options: { extractToDockers?: boolean } = {}
): Promise<{ extractPath: string; files: string[] }> {
  // Validate archive format (ZIP/7z)
  // Extract to uploads/machines/dockers/{templateSlug}/
  // Validate required files (Dockerfile, docker-compose.yml)
  // Return extraction results
}
```

#### 1.2 Archive Extraction Service
**New Service**: `backend/mybox-backend/src/virtual-machines/services/archive-extractor.service.ts`

**Features**:
- Support ZIP and 7z formats using `node-7z` or `yauzl`
- Validate archive structure matches expected pattern
- Extract to `uploads/machines/dockers/{templateSlug}/`
- Preserve directory structure and permissions

#### 1.3 Template Structure Validation
**Expected Structure** (following hardwork example):
```
template-archive.zip
├── Dockerfile                    # Container build configuration
├── docker-compose.yml           # Service orchestration
├── entrypoint.sh               # Container startup script
└── machine/                    # Application components directory
    ├── web-app/               # Web applications
    ├── services/              # Additional services
    ├── configs/               # Configuration files
    └── data/                  # Static data files
```

### Phase 2: Enhanced Container Management

#### 2.1 Template-Based Container Building
**Enhancement**: `backend/mybox-backend/src/virtual-machines/services/docker-machine.service.ts`

**New Method**: `buildFromTemplate(templatePath, imageName)`
```typescript
async buildFromTemplate(templatePath: string, imageName: string): Promise<string> {
  // Build Docker image from extracted template
  // Tag with consistent naming: rakcha/{templateSlug}:latest
  // Store build logs for debugging
  // Return image ID
}
```

#### 2.2 Dynamic Port Management
**Enhancement**: Existing `findAvailablePort()` method
- Implement port range reservation per user
- Track port usage in database
- Ensure no conflicts between user instances

#### 2.3 Network Isolation Enhancement
**Current**: User-specific networks (10.10.x.0/24)
**Enhancement**: 
- Reserve IP ranges per user: 10.10.{userId}.0/24
- Implement network policies for isolation
- Add routing rules for VPN access

### Phase 3: VPN Integration System

#### 3.1 WireGuard Service Integration
**New Service**: `backend/mybox-backend/src/vpn/wireguard.service.ts`

**Core Methods**:
```typescript
async createUserVPN(userId: string): Promise<WireGuardConfig>
async getUserVPNConfig(userId: string): Promise<string>
async updateUserRoutes(userId: string, routes: string[]): Promise<void>
async revokeUserVPN(userId: string): Promise<void>
```

#### 3.2 VPN Configuration Management
**Features**:
- Generate unique WireGuard configs per user
- Include routes to user's container subnets
- Automatic peer management via WireGuard Easy API
- Config download endpoint for users

#### 3.3 Network Routing
**Implementation**:
- Configure WireGuard to route user subnets (10.10.{userId}.0/24)
- Add iptables rules for traffic forwarding
- Implement network policies for security

### Phase 4: API Endpoints

#### 4.1 Admin Endpoints
**Controller**: `backend/mybox-backend/src/virtual-machines/controllers/machine-admin.controller.ts`

**New Endpoints**:
```typescript
POST /admin/machines/:id/upload-archive  // Upload ZIP/7z template
POST /admin/machines/:id/build-image     // Build Docker image from template
GET  /admin/machines/:id/build-logs      // Get build logs
DELETE /admin/machines/:id/image         // Remove built image
```

#### 4.2 User VPN Endpoints
**New Controller**: `backend/mybox-backend/src/vpn/vpn.controller.ts`

**Endpoints**:
```typescript
POST /vpn/setup                    // Initialize user VPN
GET  /vpn/config                   // Download WireGuard config
GET  /vpn/status                   // Check VPN connection status
POST /vpn/refresh-routes           // Update routing for new instances
DELETE /vpn/revoke                 // Revoke VPN access
```

### Phase 5: Frontend Integration

#### 5.1 Admin Interface Enhancement
**Component**: `src/components/Admin/CreateTemplateModal.tsx`

**New Features**:
- Archive upload field (ZIP/7z)
- Build progress indicator
- Template validation status
- Build logs viewer

#### 5.2 User VPN Interface
**New Component**: `src/components/VPN/VPNManager.tsx`

**Features**:
- VPN setup wizard
- Config file download
- Connection status indicator
- Troubleshooting guide

#### 5.3 Machine Access Enhancement
**Component**: `src/components/Machines/MachineCard.tsx`

**New Features**:
- VPN requirement indicator
- Direct IP access (when VPN connected)
- Connection instructions
- Network diagnostics

## 🔧 TECHNICAL SPECIFICATIONS

### File Structure
```
uploads/machines/dockers/
├── {template-slug-1}/
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── entrypoint.sh
│   └── machine/
│       └── [application files]
├── {template-slug-2}/
│   └── [similar structure]
└── .../
```

### Network Architecture
```
WireGuard Server: ********/24
├── User 1 VPN: ********/32 → Routes to *********/24
├── User 2 VPN: ********/32 → Routes to *********/24
└── User N VPN: 10.6.0.N+1/32 → Routes to 10.10.N.0/24

Container Networks:
├── user-1-network: *********/24
├── user-2-network: *********/24
└── user-N-network: 10.10.N.0/24
```

### Security Considerations
1. **Container Isolation**: Each user gets isolated Docker networks
2. **VPN Authentication**: WireGuard key-based authentication
3. **Network Policies**: iptables rules prevent cross-user access
4. **File Validation**: Archive content validation before extraction
5. **Resource Limits**: CPU/Memory limits per container
6. **Auto-cleanup**: Automatic instance shutdown and cleanup

## 📦 DEPENDENCIES

### Backend Dependencies
```json
{
  "node-7z": "^3.0.0",           // 7z archive extraction
  "yauzl": "^2.10.0",            // ZIP archive extraction
  "dockerode": "^3.3.4",         // Docker API client (existing)
  "axios": "^1.4.0"              // HTTP client for WireGuard Easy API
}
```

### System Dependencies
- Docker Engine (existing)
- WireGuard Easy container (existing)
- 7zip command line tools (for 7z support)

## 🚀 DEPLOYMENT STEPS

### Step 1: Install Dependencies
```bash
cd backend/mybox-backend
npm install node-7z yauzl
```

### Step 2: Initialize WireGuard
```bash
# Linux/macOS
./scripts/init-vpn.sh

# Windows
scripts\init-vpn.bat
```

### Step 3: Configure Environment
```env
# Add to .env
WG_EASY_URL=http://localhost:51821
WG_PASSWORD=yourpassword
DOCKER_UPLOADS_PATH=./uploads/machines/dockers
```

### Step 4: Database Migration
```typescript
// Add VPN fields to User schema
vpnEnabled: boolean
vpnPublicKey: string
vpnPrivateKey: string
vpnIPAddress: string
vpnConfigPath: string
```

## 🔍 TESTING STRATEGY

### Unit Tests
- Archive extraction validation
- Network configuration generation
- VPN config creation
- Container build process

### Integration Tests
- End-to-end machine deployment
- VPN connectivity testing
- Network isolation verification
- Multi-user scenario testing

### Security Tests
- Cross-user network isolation
- VPN authentication bypass attempts
- Container escape prevention
- File upload validation

## 📈 MONITORING & LOGGING

### Metrics to Track
- Machine deployment success rate
- VPN connection statistics
- Container resource usage
- Network traffic patterns
- Build time performance

### Log Categories
- Archive extraction logs
- Docker build logs
- VPN connection logs
- Network routing logs
- Security event logs

## 🔄 MAINTENANCE

### Regular Tasks
- Clean up expired containers
- Rotate VPN keys periodically
- Monitor disk usage in uploads directory
- Update WireGuard Easy container
- Backup machine templates

### Troubleshooting
- VPN connectivity issues
- Container networking problems
- Build failures
- Port conflicts
- Resource exhaustion

## 🌐 DETAILED NETWORK TOPOLOGY

### WireGuard VPN Configuration
```
WireGuard Server (wg-easy container)
├── Interface: wg0
├── Listen Port: 51820/udp
├── Server IP: ********/24
├── Web UI: http://localhost:51821
└── API: http://localhost:51821/api

User VPN Assignments:
├── User 1: ********/32 → Container Network: *********/24
├── User 2: ********/32 → Container Network: *********/24
├── User 3: ********/32 → Container Network: *********/24
└── User N: 10.6.0.N+1/32 → Container Network: 10.10.N.0/24
```

### Container Network Isolation
```
Docker Networks per User:
├── user-1-network (*********/24)
│   ├── Gateway: *********
│   ├── Machine 1: **********
│   ├── Machine 2: **********
│   └── Machine N: 10.10.1.N+9
├── user-2-network (*********/24)
│   ├── Gateway: *********
│   └── [similar structure]
└── [additional user networks]
```

### Routing Configuration
```bash
# WireGuard server routes (added automatically)
ip route add *********/24 dev wg0 peer ********
ip route add *********/24 dev wg0 peer ********
ip route add 10.10.N.0/24 dev wg0 peer 10.6.0.N+1

# iptables rules for forwarding
iptables -A FORWARD -i wg0 -o docker0 -j ACCEPT
iptables -A FORWARD -i docker0 -o wg0 -j ACCEPT
iptables -t nat -A POSTROUTING -s ********/24 -j MASQUERADE
```

## 🔐 SECURITY ARCHITECTURE

### Multi-Layer Security Model
```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   JWT Auth      │  │  Role-Based     │  │   API Rate   │ │
│  │  Validation     │  │  Access Control │  │   Limiting   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Network Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   WireGuard     │  │   Docker        │  │   iptables   │ │
│  │  Encryption     │  │   Networks      │  │   Firewall   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Container Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Resource      │  │   Namespace     │  │   AppArmor/  │ │
│  │   Limits        │  │   Isolation     │  │   SELinux    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Security Policies
1. **Network Isolation**: Users cannot access other users' containers
2. **VPN Authentication**: Cryptographic key-based authentication
3. **Container Sandboxing**: Limited system access and resource constraints
4. **File Validation**: Archive content scanning before extraction
5. **Audit Logging**: All actions logged for security monitoring

## 📊 IMPLEMENTATION TIMELINE

### Week 1-2: Foundation
- [ ] Set up development environment
- [ ] Install and configure dependencies
- [ ] Create archive extraction service
- [ ] Implement basic file upload validation

### Week 3-4: Core Services
- [ ] Develop WireGuard integration service
- [ ] Enhance Docker machine service
- [ ] Create VPN configuration management
- [ ] Implement network routing logic

### Week 5-6: API Development
- [ ] Create admin upload endpoints
- [ ] Develop user VPN endpoints
- [ ] Add machine build automation
- [ ] Implement status monitoring APIs

### Week 7-8: Frontend Integration
- [ ] Enhance admin interface
- [ ] Create VPN management UI
- [ ] Update machine cards with VPN info
- [ ] Add connection diagnostics

### Week 9-10: Testing & Security
- [ ] Comprehensive security testing
- [ ] Performance optimization
- [ ] Load testing with multiple users
- [ ] Documentation and training

### Week 11-12: Deployment & Monitoring
- [ ] Production deployment
- [ ] Monitoring setup
- [ ] User training and onboarding
- [ ] Performance tuning

## 🛠️ DEVELOPMENT TOOLS & SCRIPTS

### Build Scripts
```bash
# scripts/build-machine-template.sh
#!/bin/bash
TEMPLATE_PATH=$1
IMAGE_NAME=$2
docker build -t "rakcha/${IMAGE_NAME}:latest" "${TEMPLATE_PATH}"
docker tag "rakcha/${IMAGE_NAME}:latest" "rakcha/${IMAGE_NAME}:$(date +%Y%m%d)"
```

### VPN Management Scripts
```bash
# scripts/setup-user-vpn.sh
#!/bin/bash
USER_ID=$1
VPN_IP="10.6.0.$((USER_ID + 1))"
CONTAINER_SUBNET="10.10.${USER_ID}.0/24"

# Create WireGuard peer via API
curl -X POST "http://localhost:51821/api/clients" \
  -H "Content-Type: application/json" \
  -d "{\"name\": \"user-${USER_ID}\", \"address\": \"${VPN_IP}\"}"
```

### Monitoring Scripts
```bash
# scripts/monitor-containers.sh
#!/bin/bash
# Monitor container resource usage
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

# Check VPN connections
wg show wg0
```

## 📋 CONFIGURATION TEMPLATES

### WireGuard Client Configuration Template
```ini
[Interface]
PrivateKey = {USER_PRIVATE_KEY}
Address = 10.6.0.{USER_ID+1}/32
DNS = *******

[Peer]
PublicKey = {SERVER_PUBLIC_KEY}
Endpoint = {SERVER_IP}:51820
AllowedIPs = 10.10.{USER_ID}.0/24
PersistentKeepalive = 25
```

### Docker Compose Template for Machines
```yaml
version: '3.9'
services:
  {MACHINE_NAME}:
    container_name: {CONTAINER_NAME}
    build: .
    ports:
      - "{EXTERNAL_PORT_1}:{INTERNAL_PORT_1}"
      - "{EXTERNAL_PORT_2}:{INTERNAL_PORT_2}"
    networks:
      - {USER_NETWORK}
    restart: unless-stopped
    mem_limit: {MEMORY_LIMIT}
    cpus: {CPU_LIMIT}

networks:
  {USER_NETWORK}:
    external: true
```

---

## 📞 NEXT STEPS

1. **Review and approve** this comprehensive implementation plan
2. **Set up development environment** with all required dependencies
3. **Begin Phase 1 implementation** (Enhanced File Upload System)
4. **Establish testing protocols** for each development phase
5. **Create development timeline** with specific milestones
6. **Set up monitoring and logging infrastructure**

This system will transform your platform into a enterprise-grade machine deployment solution with secure, isolated VPN access for each user, following industry best practices for security and scalability.
