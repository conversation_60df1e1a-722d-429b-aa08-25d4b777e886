# Admin Portal

A simple PHP web application with a beautiful frontend for managing admin profile settings.

## Features

- Modern and responsive design
- Home page with welcome message
- Dashboard with profile settings
- Profile photo upload functionality
- Email and coordinates management
- Clean and intuitive user interface

## Requirements

- PHP 7.4 or higher
- Web server (Apache/Nginx)
- Write permissions for the `uploads` directory

## Installation

1. Clone or download this repository to your web server's document root
2. Make sure the `uploads` directory has write permissions:
   ```bash
   chmod 777 uploads
   ```
3. Access the application through your web browser

## File Structure

- `index.php` - Home page
- `dashboard.php` - Dashboard with profile settings
- `update-profile.php` - Handles form submissions
- `style.css` - Stylesheet for the application
- `uploads/` - Directory for storing uploaded profile photos

## Security Notes

This is a basic implementation. In a production environment, you should:

1. Implement proper user authentication
2. Add input validation and sanitization
3. Use a database to store user data
4. Implement CSRF protection
5. Use secure file upload handling
6. Configure proper file permissions

## License

MIT License 