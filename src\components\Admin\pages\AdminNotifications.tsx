import { useState } from 'react';
import { Bell, Volume2 } from 'lucide-react';
import { NotificationManager } from '../NotificationManager';
import { NotificationSoundsAdmin } from '../NotificationSoundsAdmin';

export function AdminNotifications() {
  const [activeSubTab, setActiveSubTab] = useState('notifications');

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Sub-navigation */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 overflow-hidden">
        <div className="flex border-b border-slate-700">
          <button
            onClick={() => setActiveSubTab('notifications')}
            className={`flex items-center space-x-2 px-6 py-3 font-medium transition-colors ${
              activeSubTab === 'notifications'
                ? 'text-emerald-400 bg-emerald-500/10 border-b-2 border-emerald-400'
                : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
            }`}
          >
            <Bell className="w-4 h-4" />
            <span>Send Notifications</span>
          </button>
          <button
            onClick={() => setActiveSubTab('sounds')}
            className={`flex items-center space-x-2 px-6 py-3 font-medium transition-colors ${
              activeSubTab === 'sounds'
                ? 'text-emerald-400 bg-emerald-500/10 border-b-2 border-emerald-400'
                : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
            }`}
          >
            <Volume2 className="w-4 h-4" />
            <span>Notification Sounds</span>
          </button>
        </div>
        
        <div className="p-6">
          {activeSubTab === 'notifications' && <NotificationManager />}
          {activeSubTab === 'sounds' && <NotificationSoundsAdmin />}
        </div>
      </div>
    </div>
  );
}