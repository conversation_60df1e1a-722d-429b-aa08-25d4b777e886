import { api } from './api';

// Admin VPN Types
export interface AdminVPNServerStatus {
  server: {
    isRunning: boolean;
    activeConnections: number;
    totalUsers: number;
    serverEndpoint: string;
    serverPort: number;
  };
  networks: {
    totalNetworks: number;
    activeConnections: number;
    totalMachines: number;
  };
  timestamp: string;
}

export interface AdminVPNConfig {
  id: string;
  userId: string;
  user?: any; // Will be populated with user data
  ipAddress: string;
  subnet: string;
  isActive: boolean;
  createdAt: string;
  lastConnected?: string;
  totalConnections: number;
  totalBytesTransferred: number;
}

export interface AdminVPNConnectionLog {
  id: string;
  userId: string;
  user?: any; // Will be populated with user data
  action: 'connect' | 'disconnect' | 'handshake' | 'error';
  timestamp: string;
  clientIP: string;
  vpnIP: string;
  disconnectedAt?: string;
  connectionStats?: {
    bytesReceived: number;
    bytesSent: number;
    packetsReceived: number;
    packetsSent: number;
    duration: number;
  };
  errorMessage?: string;
  accessedMachines: string[];
}

export interface AdminNetworkOverview {
  statistics: {
    totalNetworks: number;
    activeConnections: number;
    totalMachines: number;
  };
  networks: Array<{
    id: string;
    machineId: string;
    userId: string;
    templateId: string;
    networkName: string;
    internalIP: string;
    subnetCIDR: string;
    exposedPorts: Array<{
      internal: number;
      protocol: string;
    }>;
    status: string;
    vpnAccessible: boolean;
    createdAt: string;
    lastAccessedAt?: string;
  }>;
}

export interface AdminServerConfig {
  serverConfig: string;
  totalPeers: number;
  generatedAt: string;
}

export class AdminVpnService {
  /**
   * Get VPN server status and statistics
   */
  static async getVPNServerStatus(): Promise<AdminVPNServerStatus> {
    const response = await api.get('/admin/vpn/status');
    return (response.data as any).data;
  }

  /**
   * Get all user VPN configurations
   */
  static async getAllVPNConfigs(): Promise<AdminVPNConfig[]> {
    const response = await api.get('/admin/vpn/configs');
    return (response.data as any).data;
  }

  /**
   * Get VPN configuration for a specific user
   */
  static async getUserVPNConfig(userId: string): Promise<{
    vpnConfig: {
      ipAddress: string;
      subnet: string;
      serverConfig: string;
    };
    networks: Array<{
      machineId: string;
      templateId: string;
      networkName: string;
      internalIP: string;
      exposedPorts: Array<{
        internal: number;
        protocol: string;
      }>;
      status: string;
      createdAt: string;
    }>;
  }> {
    const response = await api.get(`/admin/vpn/user/${userId}`);
    return (response.data as any).data;
  }

  /**
   * Reset VPN configuration for a user (regenerate keys)
   */
  static async resetUserVPNConfig(userId: string): Promise<{ success: boolean; message: string; data: any }> {
    const response = await api.post(`/admin/vpn/user/${userId}/reset`);
    return response.data as { success: boolean; message: string; data: any };
  }

  /**
   * Revoke VPN access for a user
   */
  static async revokeUserVPNAccess(userId: string): Promise<{ success: boolean; message: string }> {
    const response = await api.delete(`/admin/vpn/user/${userId}`);
    return response.data as { success: boolean; message: string };
  }

  /**
   * Get all VPN connection logs
   */
  static async getAllConnectionLogs(limit?: number, userId?: string): Promise<AdminVPNConnectionLog[]> {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (userId) params.append('userId', userId);
    
    const response = await api.get(`/admin/vpn/logs?${params.toString()}`);
    return (response.data as any).data;
  }

  /**
   * Get network statistics and overview
   */
  static async getNetworkOverview(): Promise<AdminNetworkOverview> {
    const response = await api.get('/admin/vpn/networks');
    return (response.data as any).data;
  }

  /**
   * Force cleanup of orphaned networks
   */
  static async cleanupOrphanedNetworks(): Promise<{
    success: boolean;
    message: string;
    data: {
      cleanedNetworks: number;
      cleanedConfigs: number;
      timestamp: string;
    };
  }> {
    const response = await api.post('/admin/vpn/cleanup');
    return response.data as any;
  }

  /**
   * Generate complete WireGuard server configuration
   */
  static async generateServerConfig(): Promise<AdminServerConfig> {
    const response = await api.get('/admin/vpn/server-config');
    return (response.data as any).data;
  }

  /**
   * Helper method to format bytes
   */
  static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Helper method to format duration
   */
  static formatDuration(seconds: number): string {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    return `${hours}h ${minutes}m ${secs}s`;
  }

  /**
   * Helper method to get connection status color
   */
  static getConnectionStatusColor(action: string): string {
    switch (action) {
      case 'connect': return 'text-green-400';
      case 'disconnect': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      case 'handshake': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  }

  /**
   * Helper method to get network status color
   */
  static getNetworkStatusColor(status: string): string {
    switch (status) {
      case 'running': return 'text-green-400';
      case 'stopped': return 'text-red-400';
      case 'paused': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  }
}
