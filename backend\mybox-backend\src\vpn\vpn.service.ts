import { Injectable, HttpException, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class VpnService {
  private readonly logger = new Logger(VpnService.name);
  private readonly wgEasyUrl = process.env.WG_EASY_URL || 'http://localhost:51821';
  private readonly wgPassword = process.env.WG_PASSWORD || 'yourpassword';
  private readonly wgUsername = process.env.WG_USERNAME || 'admin'; // Default username

  constructor(private readonly httpService: HttpService) {
    this.logger.log('VpnService initialized');
  }

  async healthCheck(): Promise<{ status: string; message: string }> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(this.wgEasyUrl, { timeout: 5000 })
      );
      return {
        status: 'healthy',
        message: 'WireGuard Easy is accessible'
      };
    } catch (err) {
      this.logger.warn(`WireGuard Easy health check failed: ${err.message}`);
      return {
        status: 'unhealthy',
        message: `WireGuard Easy is not accessible: ${err.message}`
      };
    }
  }

  private async getSessionCookie(): Promise<string> {
    try {
      this.logger.debug(`Attempting to authenticate with WireGuard Easy at ${this.wgEasyUrl}`);
      
      const loginRes: AxiosResponse = await firstValueFrom(
        this.httpService.post(
          `${this.wgEasyUrl}/api/session`,
          { password: this.wgPassword },
          { 
            headers: { 'Content-Type': 'application/json' },
            withCredentials: true,
            timeout: 10000 // 10 second timeout
          }
        )
      );
      
      this.logger.debug(`Authentication response status: ${loginRes.status}`);
      
      const cookies = loginRes.headers['set-cookie'];
      if (cookies && cookies.length > 0) {
        const sessionCookie = cookies[0].split(';')[0];
        this.logger.debug('Successfully obtained session cookie');
        return sessionCookie;
      }
      throw new Error('No session cookie received');
    } catch (err) {
      this.logger.error(`Failed to authenticate with WireGuard Easy: ${err.message}`);
      if (err.response) {
        this.logger.error(`Response status: ${err.response.status}`);
        this.logger.error(`Response data: ${JSON.stringify(err.response.data)}`);
      }
      if (err.code === 'ECONNREFUSED') {
        throw new HttpException('WireGuard Easy service is not accessible. Please ensure it is running.', 503);
      }
      throw new HttpException('Failed to authenticate with WireGuard Easy', 500);
    }
  }

  private async getAuthHeaders(): Promise<{ [key: string]: string }> {
    const sessionCookie = await this.getSessionCookie();
    return {
      'Content-Type': 'application/json',
      'Cookie': sessionCookie,
    };
  }

  async createUser(username: string): Promise<string> {
    try {
      const headers = await this.getAuthHeaders();
      const res: AxiosResponse = await firstValueFrom(
        this.httpService.post(
          `${this.wgEasyUrl}/api/wireguard/client`,
          { name: username },
          { headers }
        )
      );
      
      // Get the configuration for the newly created client
      return await this.getWireguardConfig(username);
    } catch (err) {
      this.logger.error(`Failed to create WireGuard client: ${err.message}`);
      throw new HttpException('Failed to create WireGuard client', 500);
    }
  }

  async getWireguardConfig(username: string): Promise<string> {
    try {
      const headers = await this.getAuthHeaders();
      
      // First get the list of clients to find the client ID
      const clientsRes: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.wgEasyUrl}/api/wireguard/client`, { headers })
      );
      
      const client = clientsRes.data.find((c: any) => c.name === username);
      if (!client) {
        throw new Error(`Client ${username} not found`);
      }
      
      // Get the configuration for the specific client
      const configRes: AxiosResponse = await firstValueFrom(
        this.httpService.get(
          `${this.wgEasyUrl}/api/wireguard/client/${client.id}/configuration`,
          { headers }
        )
      );
      
      return configRes.data;
    } catch (err) {
      this.logger.error(`Failed to fetch WireGuard config: ${err.message}`);
      throw new HttpException('Failed to fetch WireGuard config', 500);
    }
  }

  async revokeUser(username: string): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      
      // First get the list of clients to find the client ID
      const clientsRes: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.wgEasyUrl}/api/wireguard/client`, { headers })
      );
      
      const client = clientsRes.data.find((c: any) => c.name === username);
      if (!client) {
        throw new Error(`Client ${username} not found`);
      }
      
      // Delete the client
      const res: AxiosResponse = await firstValueFrom(
        this.httpService.delete(
          `${this.wgEasyUrl}/api/wireguard/client/${client.id}`,
          { headers }
        )
      );
      
      return { success: true, message: `Client ${username} revoked successfully` };
    } catch (err) {
      this.logger.error(`Failed to revoke WireGuard client: ${err.message}`);
      throw new HttpException('Failed to revoke WireGuard client', 500);
    }
  }

  async getServerInfo(): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      // Return basic server information instead of status
      const clientsRes: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.wgEasyUrl}/api/wireguard/client`, { headers })
      );
      
      return {
        totalClients: clientsRes.data.length,
        clients: clientsRes.data.map((client: any) => ({
          id: client.id,
          name: client.name,
          enabled: client.enabled,
          address: client.address,
          latestHandshakeAt: client.latestHandshakeAt,
          transferRx: client.transferRx,
          transferTx: client.transferTx
        }))
      };
    } catch (err) {
      this.logger.error(`Failed to get server info: ${err.message}`);
      throw new HttpException('Failed to get server info', 500);
    }
  }

  async listUsers(): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      const res: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.wgEasyUrl}/api/wireguard/client`, { headers })
      );
      return res.data;
    } catch (err) {
      this.logger.error(`Failed to list WireGuard clients: ${err.message}`);
      throw new HttpException('Failed to list WireGuard clients', 500);
    }
  }

  async enableClient(username: string): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      
      // First get the list of clients to find the client ID
      const clientsRes: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.wgEasyUrl}/api/wireguard/client`, { headers })
      );
      
      const client = clientsRes.data.find((c: any) => c.name === username);
      if (!client) {
        throw new Error(`Client ${username} not found`);
      }
      
      // Enable the client
      const res: AxiosResponse = await firstValueFrom(
        this.httpService.post(
          `${this.wgEasyUrl}/api/wireguard/client/${client.id}/enable`,
          {},
          { headers }
        )
      );
      
      return { success: true, message: `Client ${username} enabled successfully` };
    } catch (err) {
      this.logger.error(`Failed to enable WireGuard client: ${err.message}`);
      throw new HttpException('Failed to enable WireGuard client', 500);
    }
  }

  async disableClient(username: string): Promise<any> {
    try {
      const headers = await this.getAuthHeaders();
      
      // First get the list of clients to find the client ID
      const clientsRes: AxiosResponse = await firstValueFrom(
        this.httpService.get(`${this.wgEasyUrl}/api/wireguard/client`, { headers })
      );
      
      const client = clientsRes.data.find((c: any) => c.name === username);
      if (!client) {
        throw new Error(`Client ${username} not found`);
      }
      
      // Disable the client
      const res: AxiosResponse = await firstValueFrom(
        this.httpService.post(
          `${this.wgEasyUrl}/api/wireguard/client/${client.id}/disable`,
          {},
          { headers }
        )
      );
      
      return { success: true, message: `Client ${username} disabled successfully` };
    } catch (err) {
      this.logger.error(`Failed to disable WireGuard client: ${err.message}`);
      throw new HttpException('Failed to disable WireGuard client', 500);
    }
  }
}
