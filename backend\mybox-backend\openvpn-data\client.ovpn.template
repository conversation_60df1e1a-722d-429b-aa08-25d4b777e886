client
dev tun
proto udp
remote ${SERVER_IP} 1194
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
compress lz4-v2
verb 3
cipher AES-256-CBC
auth SHA256
key-direction 1
remote-cert-tls server
reneg-sec 0
mute-replay-warnings

# Security
tls-version-min 1.2
tls-cipher DEFAULT:@SECLEVEL=0

# Performance
sndbuf 0
rcvbuf 0

<ca>
${CA_CERT}
</ca>

<cert>
${CLIENT_CERT}
</cert>

<key>
${CLIENT_KEY}
</key>

<tls-crypt>
${TLS_CRYPT}
</tls-crypt>
