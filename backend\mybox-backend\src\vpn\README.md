# HTB-style WireGuard VPN Management API Integration Plan

This document describes how to add HackTheBox-style WireGuard VPN management to your platform using [WireGuard Easy](https://github.com/wg-easy/wg-easy). This enables secure, dynamic VPN access for users to reach their Dockerized pentest machines.

---

## 1. Deploy WireGuard Easy with <PERSON><PERSON>

Run the initialization script to start WireGuard Easy:

**Linux/macOS:**
```bash
cd backend/mybox-backend
./scripts/init-vpn.sh
```

**Windows:**
```cmd
cd backend\mybox-backend
scripts\init-vpn.bat
```

Or manually run the Docker command:

```bash
docker run -d --name=wg-easy \
  -e WG_HOST=127.0.0.1 \
  -e PASSWORD=yourpassword \
  -e WG_DEFAULT_ADDRESS=******** \
  -e WG_ALLOWED_IPS=0.0.0.0/0 \
  -v ~/wg-easy:/etc/wireguard \
  -p 51820:51820/udp \
  -p 51821:51821/tcp \
  --cap-add=NET_ADMIN \
  --cap-add=SYS_MODULE \
  --sysctl="net.ipv4.conf.all.src_valid_mark=1" \
  --restart unless-stopped \
  weejewel/wg-easy
```

---

## 2. Test the WireGuard Easy API

- Web UI: [http://localhost:51821](http://localhost:51821) (password: yourpassword)
- API Base URL: [http://localhost:51821/api](http://localhost:51821/api)

**Environment Variables:**
- `WG_EASY_URL`: WireGuard Easy API URL (default: http://localhost:51821)
- `WG_PASSWORD`: WireGuard Easy web interface password (default: yourpassword)

---

## 3. Platform Integration (Backend)

**a. VPN Service (`src/vpn/vpn.service.ts`):**
- Handles:
  - Creating WireGuard clients via WireGuard Easy API
  - Downloading .conf files
  - Enabling/disabling clients
  - Revoking clients
  - Checking connection status

**b. VPN Controller (`src/vpn/vpn.controller.ts`):**
- Endpoints:
  - `POST /vpn/create` (creates WireGuard client, returns .conf)
  - `GET /vpn/config` (downloads .conf for current user)
  - `POST /vpn/enable` (enables client)
  - `POST /vpn/disable` (disables client)
  - `DELETE /vpn/user` (revokes client)
  - `GET /vpn/status` (checks WireGuard status)
  - `GET /vpn/users` (lists all clients)

**c. Security:**
- All endpoints are protected with JWT authentication
- Users can only manage their own VPN configurations

---

## 4. Frontend Integration

- Add a "Download VPN Config" button for users
- When clicked, call your backend `/vpn/create` endpoint
- Save the returned `.conf` file for the user to import into their WireGuard client
- Provide enable/disable functionality for VPN access

---

## 5. Container Network Integration

- Your Docker containers should use the `10.6.x.x` subnet
- When a user connects with their WireGuard config, they'll be on the same network and can access their containers
- WireGuard provides better performance and security compared to OpenVPN

---

## 6. Security Notes

- Change the default password (`WG_PASSWORD`) in production
- Use a proper hostname/IP for `WG_HOST` in production (not 127.0.0.1)
- Restrict access to the WireGuard Easy web interface
- Use HTTPS for the web interface in production
- Consider implementing automatic client cleanup when users are removed

---

## 7. WireGuard Client Setup

Users will need to:
1. Install WireGuard client on their device
2. Download their `.conf` file from your platform
3. Import the configuration into WireGuard
4. Connect to access their pentest machines

**Supported Platforms:**
- Windows, macOS, Linux (desktop clients)
- iOS, Android (mobile apps)
- Command line tools

---

## 8. API Authentication

The service handles WireGuard Easy authentication automatically:
1. Authenticates with the web interface using the configured password
2. Maintains session cookies for API requests
3. Handles session renewal as needed

---

**References:**
- [WireGuard Easy GitHub](https://github.com/wg-easy/wg-easy)
- [WireGuard Documentation](https://www.wireguard.com/)
- [WireGuard Installation Guide](https://www.wireguard.com/install/)