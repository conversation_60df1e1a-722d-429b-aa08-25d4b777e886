# Step-by-Step Implementation Guide
## Machine Deployment & VPN Access System

This guide provides detailed implementation steps for the machine deployment and VPN access system.

## 🚀 PHASE 1: Enhanced File Upload System

### Step 1.1: Install Dependencies

```bash
cd backend/mybox-backend
npm install node-7z yauzl fs-extra
```

### Step 1.2: Create Archive Extractor Service

**File**: `backend/mybox-backend/src/virtual-machines/services/archive-extractor.service.ts`

```typescript
import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import * as yauzl from 'yauzl';
import * as Seven from 'node-7z';
import * as fs from 'fs-extra';
import * as path from 'path';

@Injectable()
export class ArchiveExtractorService {
  private readonly logger = new Logger(ArchiveExtractorService.name);

  async extractArchive(
    filePath: string,
    extractPath: string,
    archiveType: 'zip' | '7z'
  ): Promise<{ files: string[]; structure: any }> {
    this.logger.log(`Extracting ${archiveType} archive: ${filePath}`);
    
    // Ensure extraction directory exists
    await fs.ensureDir(extractPath);
    
    if (archiveType === 'zip') {
      return this.extractZip(filePath, extractPath);
    } else if (archiveType === '7z') {
      return this.extract7z(filePath, extractPath);
    }
    
    throw new BadRequestException('Unsupported archive format');
  }

  private async extractZip(filePath: string, extractPath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const files: string[] = [];
      
      yauzl.open(filePath, { lazyEntries: true }, (err, zipfile) => {
        if (err) return reject(err);
        
        zipfile.readEntry();
        zipfile.on('entry', (entry) => {
          if (/\/$/.test(entry.fileName)) {
            // Directory entry
            zipfile.readEntry();
          } else {
            // File entry
            const fullPath = path.join(extractPath, entry.fileName);
            files.push(entry.fileName);
            
            // Ensure directory exists
            fs.ensureDirSync(path.dirname(fullPath));
            
            zipfile.openReadStream(entry, (err, readStream) => {
              if (err) return reject(err);
              
              const writeStream = fs.createWriteStream(fullPath);
              readStream.pipe(writeStream);
              
              writeStream.on('close', () => {
                zipfile.readEntry();
              });
            });
          }
        });
        
        zipfile.on('end', () => {
          resolve({ files, structure: this.analyzeStructure(extractPath) });
        });
      });
    });
  }

  private async extract7z(filePath: string, extractPath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const files: string[] = [];
      
      const stream = Seven.extractFull(filePath, extractPath, {
        $progress: true,
      });
      
      stream.on('data', (data) => {
        if (data.file) {
          files.push(data.file);
        }
      });
      
      stream.on('end', () => {
        resolve({ files, structure: this.analyzeStructure(extractPath) });
      });
      
      stream.on('error', reject);
    });
  }

  private analyzeStructure(extractPath: string): any {
    const structure = {
      hasDockerfile: false,
      hasDockerCompose: false,
      hasMachineDir: false,
      files: []
    };
    
    const files = fs.readdirSync(extractPath);
    structure.files = files;
    
    structure.hasDockerfile = files.includes('Dockerfile');
    structure.hasDockerCompose = files.some(f => 
      f === 'docker-compose.yml' || f === 'docker-compose.yaml'
    );
    structure.hasMachineDir = files.includes('machine');
    
    return structure;
  }

  validateMachineTemplate(structure: any): boolean {
    return structure.hasDockerfile && structure.hasDockerCompose;
  }
}
```

### Step 1.3: Enhance Machine Template Service

**File**: `backend/mybox-backend/src/virtual-machines/services/machine-template.service.ts`

Add the new method:

```typescript
import { ArchiveExtractorService } from './archive-extractor.service';

// Add to constructor
constructor(
  // ... existing dependencies
  private readonly archiveExtractor: ArchiveExtractorService,
) {}

async uploadMachineArchive(
  templateId: string,
  file: Express.Multer.File
): Promise<{ extractPath: string; files: string[]; isValid: boolean }> {
  const template = await this.machineTemplateModel.findById(templateId);
  if (!template) {
    throw new NotFoundException('Machine template not found');
  }

  // Determine archive type
  const fileExt = path.extname(file.originalname).toLowerCase();
  let archiveType: 'zip' | '7z';
  
  if (fileExt === '.zip') {
    archiveType = 'zip';
  } else if (fileExt === '.7z') {
    archiveType = '7z';
  } else {
    throw new BadRequestException('Only ZIP and 7z archives are supported');
  }

  // Create extraction path
  const extractPath = path.join(this.uploadsPath, 'dockers', template.slug);
  
  // Save uploaded file temporarily
  const tempFilePath = path.join(this.uploadsPath, 'temp', file.originalname);
  await fs.ensureDir(path.dirname(tempFilePath));
  await fs.writeFile(tempFilePath, file.buffer);

  try {
    // Extract archive
    const result = await this.archiveExtractor.extractArchive(
      tempFilePath,
      extractPath,
      archiveType
    );

    // Validate structure
    const isValid = this.archiveExtractor.validateMachineTemplate(result.structure);
    
    if (!isValid) {
      throw new BadRequestException(
        'Invalid machine template structure. Must contain Dockerfile and docker-compose.yml'
      );
    }

    // Clean up temp file
    await fs.remove(tempFilePath);

    this.logger.log(`Successfully extracted machine template: ${template.slug}`);
    
    return {
      extractPath,
      files: result.files,
      isValid
    };
  } catch (error) {
    // Clean up on error
    await fs.remove(tempFilePath);
    await fs.remove(extractPath);
    throw error;
  }
}
```

### Step 1.4: Add Archive Upload Endpoint

**File**: `backend/mybox-backend/src/virtual-machines/controllers/machine-admin.controller.ts`

```typescript
@Post(':id/upload-archive')
@ApiOperation({ summary: 'Upload machine template archive (ZIP/7z)' })
@ApiConsumes('multipart/form-data')
@ApiResponse({ status: 200, description: 'Archive uploaded and extracted successfully' })
@UseInterceptors(FileInterceptor('archive', {
  limits: { fileSize: 500 * 1024 * 1024 }, // 500MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.zip', '.7z'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new BadRequestException('Only ZIP and 7z files are allowed'), false);
    }
  }
}))
async uploadMachineArchive(
  @Param('id') templateId: string,
  @UploadedFile() file: Express.Multer.File
) {
  this.logger.log(`Uploading archive for template ${templateId}: ${file.originalname}`);
  return this.machineTemplateService.uploadMachineArchive(templateId, file);
}
```

## 🚀 PHASE 2: Docker Build Integration

### Step 2.1: Enhance Docker Machine Service

**File**: `backend/mybox-backend/src/virtual-machines/services/docker-machine.service.ts`

Add new methods:

```typescript
async buildMachineImage(
  templateSlug: string,
  templatePath: string
): Promise<{ imageId: string; buildLogs: string[] }> {
  const imageName = `rakcha/${templateSlug}:latest`;
  const buildLogs: string[] = [];
  
  this.logger.log(`Building Docker image: ${imageName}`);
  
  try {
    const stream = await this.docker.buildImage({
      context: templatePath,
      src: ['.']
    }, {
      t: imageName,
      dockerfile: 'Dockerfile'
    });

    // Collect build logs
    await new Promise((resolve, reject) => {
      this.docker.modem.followProgress(stream, (err, res) => {
        if (err) reject(err);
        else resolve(res);
      }, (event) => {
        if (event.stream) {
          buildLogs.push(event.stream);
          this.logger.log(`Build: ${event.stream.trim()}`);
        }
      });
    });

    // Get image info
    const image = this.docker.getImage(imageName);
    const imageInfo = await image.inspect();
    
    this.logger.log(`Successfully built image: ${imageName}`);
    
    return {
      imageId: imageInfo.Id,
      buildLogs
    };
  } catch (error) {
    this.logger.error(`Failed to build image ${imageName}: ${error.message}`);
    throw new BadRequestException(`Docker build failed: ${error.message}`);
  }
}

async getImageBuildLogs(templateSlug: string): Promise<string[]> {
  // In a real implementation, you'd store build logs in database
  // For now, return empty array
  return [];
}

async removeImage(templateSlug: string): Promise<void> {
  const imageName = `rakcha/${templateSlug}:latest`;
  
  try {
    const image = this.docker.getImage(imageName);
    await image.remove({ force: true });
    this.logger.log(`Removed image: ${imageName}`);
  } catch (error) {
    this.logger.warn(`Failed to remove image ${imageName}: ${error.message}`);
  }
}
```

### Step 2.2: Add Build Management Endpoints

**File**: `backend/mybox-backend/src/virtual-machines/controllers/machine-admin.controller.ts`

```typescript
@Post(':id/build-image')
@ApiOperation({ summary: 'Build Docker image from uploaded template' })
@ApiResponse({ status: 200, description: 'Image built successfully' })
async buildMachineImage(@Param('id') templateId: string) {
  const template = await this.machineTemplateService.findById(templateId);
  if (!template) {
    throw new NotFoundException('Machine template not found');
  }
  
  const templatePath = path.join(
    this.configService.get('UPLOADS_PATH', './uploads/machines'),
    'dockers',
    template.slug
  );
  
  if (!fs.existsSync(templatePath)) {
    throw new BadRequestException('Template files not found. Please upload archive first.');
  }
  
  return this.dockerMachineService.buildMachineImage(template.slug, templatePath);
}

@Get(':id/build-logs')
@ApiOperation({ summary: 'Get build logs for machine template' })
@ApiResponse({ status: 200, description: 'Build logs retrieved' })
async getBuildLogs(@Param('id') templateId: string) {
  const template = await this.machineTemplateService.findById(templateId);
  if (!template) {
    throw new NotFoundException('Machine template not found');
  }
  
  return this.dockerMachineService.getImageBuildLogs(template.slug);
}

@Delete(':id/image')
@ApiOperation({ summary: 'Remove built Docker image' })
@ApiResponse({ status: 200, description: 'Image removed successfully' })
async removeImage(@Param('id') templateId: string) {
  const template = await this.machineTemplateService.findById(templateId);
  if (!template) {
    throw new NotFoundException('Machine template not found');
  }
  
  await this.dockerMachineService.removeImage(template.slug);
  return { message: 'Image removed successfully' };
}
```

## 🚀 PHASE 3: VPN Integration

### Step 3.1: Create WireGuard Service

**File**: `backend/mybox-backend/src/vpn/wireguard.service.ts`

```typescript
import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

export interface WireGuardClient {
  id: string;
  name: string;
  address: string;
  publicKey: string;
  privateKey: string;
  enabled: boolean;
  createdAt: string;
}

@Injectable()
export class WireGuardService {
  private readonly logger = new Logger(WireGuardService.name);
  private readonly apiUrl: string;
  private readonly password: string;

  constructor(private configService: ConfigService) {
    this.apiUrl = this.configService.get('WG_EASY_URL', 'http://localhost:51821');
    this.password = this.configService.get('WG_PASSWORD', 'yourpassword');
  }

  async createClient(userId: string): Promise<WireGuardClient> {
    const clientName = `user-${userId}`;
    const clientAddress = `10.6.0.${parseInt(userId) + 1}`;
    
    try {
      const response = await axios.post(
        `${this.apiUrl}/api/clients`,
        {
          name: clientName,
          address: clientAddress
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.password}`
          }
        }
      );
      
      this.logger.log(`Created WireGuard client for user ${userId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to create WireGuard client: ${error.message}`);
      throw new BadRequestException('Failed to create VPN configuration');
    }
  }

  async getClientConfig(userId: string): Promise<string> {
    const clientName = `user-${userId}`;
    
    try {
      const response = await axios.get(
        `${this.apiUrl}/api/clients/${clientName}/configuration`,
        {
          headers: {
            'Authorization': `Bearer ${this.password}`
          }
        }
      );
      
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to get client config: ${error.message}`);
      throw new BadRequestException('Failed to retrieve VPN configuration');
    }
  }

  async enableClient(userId: string): Promise<void> {
    const clientName = `user-${userId}`;
    
    try {
      await axios.post(
        `${this.apiUrl}/api/clients/${clientName}/enable`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${this.password}`
          }
        }
      );
      
      this.logger.log(`Enabled WireGuard client for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to enable client: ${error.message}`);
      throw new BadRequestException('Failed to enable VPN client');
    }
  }

  async disableClient(userId: string): Promise<void> {
    const clientName = `user-${userId}`;
    
    try {
      await axios.post(
        `${this.apiUrl}/api/clients/${clientName}/disable`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${this.password}`
          }
        }
      );
      
      this.logger.log(`Disabled WireGuard client for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to disable client: ${error.message}`);
      throw new BadRequestException('Failed to disable VPN client');
    }
  }

  async revokeClient(userId: string): Promise<void> {
    const clientName = `user-${userId}`;
    
    try {
      await axios.delete(
        `${this.apiUrl}/api/clients/${clientName}`,
        {
          headers: {
            'Authorization': `Bearer ${this.password}`
          }
        }
      );
      
      this.logger.log(`Revoked WireGuard client for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to revoke client: ${error.message}`);
      throw new BadRequestException('Failed to revoke VPN client');
    }
  }
}
```

This implementation guide provides the foundation for the machine deployment and VPN system. Continue with the remaining phases following the same detailed approach.
