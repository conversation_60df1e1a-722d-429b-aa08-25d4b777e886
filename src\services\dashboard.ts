import { api, API_BASE_URL } from './api';

export interface SocialLinks {
  github: string;
  facebook: string;
  instagram: string;
  linkedin: string;
  twitter: string;
  discord: string;
  website: string;
}

export interface Sponsor {
  id: string;
  name: string;
  logo: string;
  website: string;
  description: string;
  tier: 'platinum' | 'gold' | 'silver' | 'bronze';
  isActive: boolean;
  order: number;
}

export interface Theme {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
}

export interface Features {
  showStats: boolean;
  showRecentActivity: boolean;
  showQuickActions: boolean;
  showLeaderboard: boolean;
  showAnnouncements: boolean;
  showSponsors: boolean;
}

export interface DashboardConfig {
  _id: string;
  configId: string;
  eventName: string;
  eventDescription: string;
  eventCoverImage: string;
  eventMessage: string;
  eventStartDate?: string;
  eventEndDate?: string;
  socialLinks: SocialLinks;
  sponsors: Sponsor[];
  theme: Theme;
  features: Features;
  updatedBy: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSponsorDto {
  name: string;
  logo: string;
  website?: string;
  description?: string;
  tier: 'platinum' | 'gold' | 'silver' | 'bronze';
  isActive?: boolean;
  order?: number;
}

export interface UpdateSponsorDto extends CreateSponsorDto {
  id: string;
}

export interface UpdateDashboardConfigDto {
  eventName?: string;
  eventDescription?: string;
  eventCoverImage?: string;
  eventMessage?: string;
  eventStartDate?: string;
  eventEndDate?: string;
  socialLinks?: Partial<SocialLinks>;
  sponsors?: Sponsor[];
  theme?: Partial<Theme>;
  features?: Partial<Features>;
}

export interface DashboardStats {
  totalSponsors: number;
  activeSponsors: number;
  sponsorsByTier: {
    platinum: number;
    gold: number;
    silver: number;
    bronze: number;
  };
  lastUpdated: string;
  isActive: boolean;
}

class DashboardService {
  // Utility function to clean MongoDB objects
  private cleanMongoObject(obj: any): any {
    if (obj === null || obj === undefined) return obj;
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.cleanMongoObject(item));
    }
    
    if (typeof obj === 'object') {
      const cleaned: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (key !== '_id' && key !== '__v') {
          cleaned[key] = this.cleanMongoObject(value);
        }
      }
      return cleaned;
    }
    
    return obj;
  }

  // Dashboard Configuration
  async getDashboardConfig(): Promise<DashboardConfig> {
    const response = await api.get('/dashboard/config');
    return response.data;
  }

  async updateDashboardConfig(data: UpdateDashboardConfigDto): Promise<{ message: string; config: DashboardConfig }> {
    // Clean the data before sending
    const cleanData = this.cleanMongoObject(data);
    const response = await api.put('/dashboard/config', cleanData);
    return response.data;
  }

  async uploadCoverImage(file: File): Promise<{ message: string; imageUrl: string }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/dashboard/config/cover-image', formData);
    return response.data;
  }

  // Sponsor Management
  async addSponsor(data: CreateSponsorDto): Promise<{ message: string; config: DashboardConfig }> {
    const cleanData = this.cleanMongoObject(data);
    const response = await api.post('/dashboard/sponsors', cleanData);
    return response.data;
  }

  async updateSponsor(id: string, data: Omit<UpdateSponsorDto, 'id'>): Promise<{ message: string; config: DashboardConfig }> {
    const cleanData = this.cleanMongoObject(data);
    const response = await api.put(`/dashboard/sponsors/${id}`, cleanData);
    return response.data;
  }

  async deleteSponsor(id: string): Promise<{ message: string; config: DashboardConfig }> {
    const response = await api.delete(`/dashboard/sponsors/${id}`);
    return response.data;
  }

  async reorderSponsors(sponsorIds: string[]): Promise<{ message: string; config: DashboardConfig }> {
    const response = await api.put('/dashboard/sponsors/reorder', { sponsorIds });
    return response.data;
  }

  async uploadSponsorLogo(sponsorId: string, file: File): Promise<{ message: string; logoUrl: string }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post(`/dashboard/sponsors/${sponsorId}/logo`, formData);
    return response.data;
  }

  // Statistics
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await api.get('/dashboard/stats');
    return response.data;
  }

  // Utility methods
  getSponsorsByTier(sponsors: Sponsor[], tier: Sponsor['tier']): Sponsor[] {
    return sponsors
      .filter(sponsor => sponsor.tier === tier && sponsor.isActive)
      .sort((a, b) => a.order - b.order);
  }

  getTierColor(tier: Sponsor['tier']): string {
    switch (tier) {
      case 'platinum':
        return 'from-gray-300 to-gray-100';
      case 'gold':
        return 'from-yellow-400 to-yellow-200';
      case 'silver':
        return 'from-gray-400 to-gray-300';
      case 'bronze':
        return 'from-amber-600 to-amber-400';
      default:
        return 'from-gray-500 to-gray-400';
    }
  }

  getTierBorder(tier: Sponsor['tier']): string {
    switch (tier) {
      case 'platinum':
        return 'border-gray-300';
      case 'gold':
        return 'border-yellow-400';
      case 'silver':
        return 'border-gray-400';
      case 'bronze':
        return 'border-amber-600';
      default:
        return 'border-gray-500';
    }
  }

  getSocialIcon(platform: keyof SocialLinks): string {
    const icons = {
      github: '🐙',
      facebook: '📘',
      instagram: '📷',
      linkedin: '💼',
      twitter: '🐦',
      discord: '🎮',
      website: '🌐',
    };
    return icons[platform] || '🔗';
  }

  formatEventDate(date: string): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  isEventActive(startDate?: string, endDate?: string): boolean {
    if (!startDate || !endDate) return true;
    
    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return now >= start && now <= end;
  }

  getEventStatus(startDate?: string, endDate?: string): 'upcoming' | 'active' | 'ended' {
    if (!startDate || !endDate) return 'active';
    
    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (now < start) return 'upcoming';
    if (now > end) return 'ended';
    return 'active';
  }
}

export const dashboardService = new DashboardService();