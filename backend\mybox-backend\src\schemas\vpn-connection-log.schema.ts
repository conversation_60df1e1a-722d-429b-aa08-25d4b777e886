import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type VPNConnectionLogDocument = VPNConnectionLog & Document;

export interface ConnectionStats {
  bytesReceived: number;
  bytesSent: number;
  packetsReceived: number;
  packetsSent: number;
  duration: number; // Connection duration in seconds
}

@Schema({ timestamps: true })
export class VPNConnectionLog {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'VPNConfig', required: true })
  vpnConfigId: Types.ObjectId;

  @Prop({ required: true })
  clientIP: string; // Client's real IP address

  @Prop({ required: true })
  vpnIP: string; // Assigned VPN IP address

  @Prop({ required: true })
  action: 'connect' | 'disconnect' | 'handshake' | 'error';

  @Prop({ default: Date.now })
  timestamp: Date;

  @Prop()
  disconnectedAt?: Date;

  @Prop({ type: Object })
  connectionStats?: ConnectionStats;

  @Prop()
  userAgent?: string; // WireGuard client information

  @Prop()
  errorMessage?: string; // Error details if action is 'error'

  @Prop()
  serverEndpoint?: string; // Which server endpoint was used

  @Prop({ type: [String], default: [] })
  accessedMachines: string[]; // List of machine IPs accessed during this session

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>; // Additional logging data
}

export const VPNConnectionLogSchema = SchemaFactory.createForClass(VPNConnectionLog);

// Indexes for performance and analytics
VPNConnectionLogSchema.index({ userId: 1, timestamp: -1 });
VPNConnectionLogSchema.index({ vpnConfigId: 1, timestamp: -1 });
VPNConnectionLogSchema.index({ action: 1, timestamp: -1 });
VPNConnectionLogSchema.index({ clientIP: 1 });
VPNConnectionLogSchema.index({ vpnIP: 1 });
VPNConnectionLogSchema.index({ timestamp: -1 });

// TTL index to automatically delete old logs after 90 days
VPNConnectionLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 90 * 24 * 60 * 60 });
