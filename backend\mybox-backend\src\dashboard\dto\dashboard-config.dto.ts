import { IsString, IsOptional, IsBoolean, IsDateString, IsArray, ValidateNested, IsEnum, IsNumber, IsObject } from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class SocialLinksDto {
  @IsOptional()
  @IsString()
  github?: string;

  @IsOptional()
  @IsString()
  facebook?: string;

  @IsOptional()
  @IsString()
  instagram?: string;

  @IsOptional()
  @IsString()
  linkedin?: string;

  @IsOptional()
  @IsString()
  twitter?: string;

  @IsOptional()
  @IsString()
  discord?: string;

  @IsOptional()
  @IsString()
  website?: string;
}

export class SponsorDto {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsString()
  logo: string;

  @IsOptional()
  @IsString()
  website?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(['platinum', 'gold', 'silver', 'bronze'])
  tier: 'platinum' | 'gold' | 'silver' | 'bronze';

  @IsBoolean()
  isActive: boolean;

  @IsNumber()
  order: number;
}

export class ThemeDto {
  @IsOptional()
  @IsString()
  primaryColor?: string;

  @IsOptional()
  @IsString()
  secondaryColor?: string;

  @IsOptional()
  @IsString()
  accentColor?: string;

  @IsOptional()
  @IsString()
  backgroundColor?: string;
}

export class FeaturesDto {
  @IsOptional()
  @IsBoolean()
  showStats?: boolean;

  @IsOptional()
  @IsBoolean()
  showRecentActivity?: boolean;

  @IsOptional()
  @IsBoolean()
  showQuickActions?: boolean;

  @IsOptional()
  @IsBoolean()
  showLeaderboard?: boolean;

  @IsOptional()
  @IsBoolean()
  showAnnouncements?: boolean;

  @IsOptional()
  @IsBoolean()
  showSponsors?: boolean;
}

export class CreateDashboardConfigDto {
  @IsOptional()
  @IsString()
  eventName?: string;

  @IsOptional()
  @IsString()
  eventDescription?: string;

  @IsOptional()
  @IsString()
  eventCoverImage?: string;

  @IsOptional()
  @IsString()
  eventMessage?: string;

  @IsOptional()
  @IsDateString()
  eventStartDate?: string;

  @IsOptional()
  @IsDateString()
  eventEndDate?: string;

  @IsOptional()
  @IsObject()
  socialLinks?: any;

  @IsOptional()
  @IsArray()
  sponsors?: any[];

  @IsOptional()
  @IsObject()
  theme?: any;

  @IsOptional()
  @IsObject()
  features?: any;
}

export class UpdateDashboardConfigDto extends CreateDashboardConfigDto {}

export class CreateSponsorDto {
  @IsString()
  name: string;

  @IsString()
  logo: string;

  @IsOptional()
  @IsString()
  website?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(['platinum', 'gold', 'silver', 'bronze'])
  tier: 'platinum' | 'gold' | 'silver' | 'bronze';

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsNumber()
  order?: number;
}

export class UpdateSponsorDto extends CreateSponsorDto {
  @IsString()
  id: string;
}