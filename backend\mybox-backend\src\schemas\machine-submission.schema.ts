import { Prop, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type MachineSubmissionDocument = MachineSubmission & Document;

@Schema({ timestamps: true })
export class MachineSubmission {
  @Prop({ type: Types.ObjectId, ref: 'MachineInstance', required: true })
  instanceId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'MachineTemplate', required: true })
  templateId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Team', required: false })
  teamId?: Types.ObjectId;

  @Prop({ required: true })
  flagName: string;

  @Prop({ required: true })
  submittedFlag: string;

  @Prop({ required: true })
  isCorrect: boolean;

  @Prop({ default: 0 })
  pointsAwarded: number;

  @Prop({ default: 0 })
  teamPointsAwarded: number;

  @Prop({ default: false })
  isFirstBlood: boolean;

  @Prop({ default: false })
  isFirstTeamSolve: boolean;

  @Prop({ required: true })
  submissionIP: string;

  @Prop({ 
    required: true, 
    enum: ['web', 'api', 'ssh', 'direct'],
    default: 'web'
  })
  submissionMethod: string;

  @Prop({ type: Object, default: {} })
  verificationData: Record<string, any>;

  @Prop({ required: true })
  submittedAt: Date;

  @Prop({ required: false })
  verifiedAt?: Date;

  @Prop({ required: false })
  verificationError?: string;
}

export const MachineSubmissionSchema = SchemaFactory.createForClass(MachineSubmission);
