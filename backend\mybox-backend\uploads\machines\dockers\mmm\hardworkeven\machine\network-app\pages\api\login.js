import { serialize } from 'cookie';

export default function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method Not Allowed' });
    }

    const { username, password } = req.body;

    if (username === 'KYBSAdmin' && password === '6rU73f0rCiL5b33hy33ta7f0uun') {

        const cookie = serialize('auth_token', 'user_is_authenticated', {
            httpOnly: true,
            secure: process.env.NODE_ENV !== 'development',
            sameSite: 'strict',
            maxAge: 60 * 60, // 1 hour
            path: '/',
        });

        res.setHeader('Set-Cookie', cookie);
        res.status(200).json({ success: true });
    } else {
        res.status(401).json({ message: 'Invalid credentials' });
    }
}