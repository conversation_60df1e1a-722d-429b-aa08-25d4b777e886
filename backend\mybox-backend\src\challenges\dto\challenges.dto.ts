import { 
  IsString, 
  IsEnum, 
  IsNumber, 
  IsOptional, 
  IsArray, 
  IsBoolean, 
  IsDate, 
  Min, 
  ArrayUnique, 
  IsMongoId,
  ValidateNested
} from 'class-validator';
import { ChallengeDifficulty, Flag } from '../../schemas/challenge.schema';
import { Type } from 'class-transformer';

export class PublicFlagDto implements Partial<Flag> {
  @IsString()
  value: string;

  @IsBoolean()
  @IsOptional()
  isCaseSensitive?: boolean = false;

  @IsNumber()
  @IsOptional()
  points?: number;

  @IsString()
  @IsOptional()
  description?: string;
}

// Renamed to avoid conflict with admin DTOs
export class PublicCreateChallengeDto {
  @IsString()
  title: string;

  @IsString()
  description: string;

  @IsString()
  category: string;

  @IsEnum(['easy', 'medium', 'hard', 'insane'])
  difficulty: ChallengeDifficulty;

  @IsNumber()
  @Min(1)
  points: number;

  @IsString()
  @IsOptional()
  flag?: string;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => PublicFlagDto)
  flags?: PublicFlagDto[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  hints?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @ArrayUnique()
  tags?: string[];

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  releaseDate?: Date;
}

// Renamed to avoid conflict with admin DTOs
export class PublicUpdateChallengeDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  category?: string;

  @IsEnum(['easy', 'medium', 'hard', 'insane'])
  @IsOptional()
  difficulty?: ChallengeDifficulty;

  @IsNumber()
  @Min(1)
  @IsOptional()
  points?: number;

  @IsString()
  @IsOptional()
  flag?: string;
  
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => PublicFlagDto)
  flags?: PublicFlagDto[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  hints?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @ArrayUnique()
  tags?: string[];

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  releaseDate?: Date;
}

export class FlagSubmissionDto {
  @IsMongoId()
  challengeId: string;

  @IsString()
  flag: string;
}

export class ChallengePaginationDto {
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  limit?: number = 20;

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsEnum(['easy', 'medium', 'hard', 'insane'])
  difficulty?: ChallengeDifficulty;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  onlySolved?: boolean;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  onlyUnsolved?: boolean;
}
