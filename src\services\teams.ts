// Teams API Service
import { API_BASE_URL } from './api';

export interface TeamMember {
  userId: string;
  username: string;
  email: string;
  role: 'captain' | 'member';
  status: string;
  joinedAt: string;
}

export interface Team {
  id: string;
  name: string;
  description: string;
  isPublic: boolean;
  captainId: string;
  captain: {
    id: string;
    username: string;
    email: string;
  };
  members: TeamMember[];
  maxMembers: number;
  teamScore: number;
  rank: number;
  inviteCode: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTeamRequest {
  name: string;
  description: string;
  isPublic: boolean;
  maxMembers: number;
}

export interface UpdateTeamRequest {
  name?: string;
  description?: string;
  isPublic?: boolean;
  maxMembers?: number;
}

export interface JoinTeamRequest {
  teamIdentifier: string;
}

export interface TeamsResponse {
  teams: Team[];
  total: number;
  page: number;
  limit: number;
}

export interface TeamStats {
  totalMembers: number;
  activeChallenges: number;
  solvedChallenges: number;
  totalScore: number;
  rank: number;
  recentActivity: any[];
}

// New interfaces for team APIs
export interface TeamInvitation {
  id: string;
  teamId: string;
  teamName: string;
  invitedUserId: string;
  invitedUserEmail: string;
  invitedByUserId: string;
  invitedByUsername: string;
  status: 'pending' | 'accepted' | 'declined' | 'cancelled';
  createdAt: string;
  respondedAt?: string;
  expiresAt: string;
}

export interface InviteMemberRequest {
  username: string; // Can be username or email
}

export interface TransferCaptainshipRequest {
  newCaptainId: string;
}

export interface TeamSolve {
  challengeId: string;
  challengeTitle: string;
  challengeCategory: string;
  challengeDifficulty: string;
  solvedBy: string;
  solvedByUsername: string;
  pointsAwarded: number;
  solvedAt: string;
  isFirstBlood: boolean;
  flagIndex: number;
}

export interface TeamMemberDetailed {
  userId: string;
  username: string;
  email: string;
  role: 'captain' | 'member';
  status: string;
  joinedAt: string;
  score: number;
  solvedChallenges: number;
}

export interface TeamLeaderboardEntry {
  teamId: string;
  teamName: string;
  teamScore: number;
  rank: number;
  memberCount: number;
  solvedChallenges: number;
  lastSolved?: string;
  captain: {
    id: string;
    username: string;
  };
}

export interface TeamLeaderboardResponse {
  teams: TeamLeaderboardEntry[];
  total: number;
  page: number;
  limit: number;
}

export class TeamsService {
  private static getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('mybox_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  private static async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Request failed' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  static async createTeam(data: CreateTeamRequest): Promise<Team> {
    const response = await fetch(`${API_BASE_URL}/teams`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse<Team>(response);
  }

  static async getAllTeams(page: number = 1, limit: number = 20, isPublic?: boolean): Promise<TeamsResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });
    
    if (isPublic !== undefined) {
      params.append('isPublic', isPublic.toString());
    }

    const response = await fetch(`${API_BASE_URL}/teams?${params}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<TeamsResponse>(response);
  }

  static async getMyTeam(): Promise<Team | null> {
    const response = await fetch(`${API_BASE_URL}/teams/my-team`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (response.status === 404) {
      return null;
    }

    return this.handleResponse<Team>(response);
  }

  static async getTeamById(teamId: string): Promise<Team> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<Team>(response);
  }

  static async updateTeam(teamId: string, data: UpdateTeamRequest): Promise<Team> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse<Team>(response);
  }

  static async deleteTeam(teamId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Request failed' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
  }

  static async joinTeam(data: JoinTeamRequest): Promise<Team> {
    const response = await fetch(`${API_BASE_URL}/teams/join`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse<Team>(response);
  }

  static async leaveTeam(): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/teams/leave`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Request failed' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
  }

  static async removeMember(teamId: string, memberId: string): Promise<Team> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/members/${memberId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<Team>(response);
  }

  static async getTeamStats(teamId: string): Promise<TeamStats> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/stats`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<TeamStats>(response);
  }

  static async regenerateInviteCode(teamId: string): Promise<{ inviteCode: string }> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/regenerate-invite`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<{ inviteCode: string }>(response);
  }

  // Transfer captainship
  static async transferCaptainship(teamId: string, data: TransferCaptainshipRequest): Promise<Team> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/transfer-captainship`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse<Team>(response);
  }

  // Team invitation methods
  static async inviteMember(teamId: string, data: InviteMemberRequest): Promise<TeamInvitation> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/invitations`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse<TeamInvitation>(response);
  }

  static async getTeamInvitations(teamId: string): Promise<TeamInvitation[]> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/invitations`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<TeamInvitation[]>(response);
  }

  static async acceptInvitation(invitationId: string): Promise<Team> {
    const response = await fetch(`${API_BASE_URL}/teams/invitations/${invitationId}/accept`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<Team>(response);
  }

  static async declineInvitation(invitationId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/teams/invitations/${invitationId}/decline`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Request failed' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
  }

  static async cancelInvitation(invitationId: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/teams/invitations/${invitationId}/cancel`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Request failed' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
  }

  // Team data methods
  static async getTeamSolves(teamId: string): Promise<TeamSolve[]> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/solves`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<TeamSolve[]>(response);
  }

  static async getTeamMembers(teamId: string): Promise<TeamMemberDetailed[]> {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/members`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<TeamMemberDetailed[]>(response);
  }

  static async getTeamLeaderboard(page: number = 1, limit: number = 20): Promise<TeamLeaderboardResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await fetch(`${API_BASE_URL}/teams/leaderboard?${params}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<TeamLeaderboardResponse>(response);
  }
}
