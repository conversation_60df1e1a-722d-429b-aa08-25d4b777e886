import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Challenge } from '../schemas/challenge.schema';
import { ChallengeSubmission } from '../schemas/challenge-submission.schema';
import { TeamChallengeSolve } from '../schemas/team-challenge-solve.schema';
import { User } from '../schemas/user.schema';
import { Team } from '../schemas/team.schema';

@Injectable()
export class StatisticsService {
  constructor(
    @InjectModel(Challenge.name) private challengeModel: Model<Challenge>,
    @InjectModel(ChallengeSubmission.name) private challengeSubmissionModel: Model<ChallengeSubmission>,
    @InjectModel(TeamChallengeSolve.name) private teamChallengeSolveModel: Model<TeamChallengeSolve>,
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Team.name) private teamModel: Model<Team>,
  ) {}

  async getGlobalStatistics() {
    const [
      totalChallenges,
      activeChallenges,
      totalUsers,
      activeUsers,
      totalTeams,
      activeTeams,
      totalSubmissions,
      correctSubmissions,
      totalSolves,
      totalPoints
    ] = await Promise.all([
      this.challengeModel.countDocuments(),
      this.challengeModel.countDocuments({ isActive: true }),
      this.userModel.countDocuments(),
      this.userModel.countDocuments({ lastLoginAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } }),
      this.teamModel.countDocuments(),
      this.teamModel.countDocuments({ 'members.status': 'active' }),
      this.challengeSubmissionModel.countDocuments(),
      this.challengeSubmissionModel.countDocuments({ isCorrect: true }),
      this.teamChallengeSolveModel.countDocuments(),
      this.challengeModel.aggregate([
        { $group: { _id: null, total: { $sum: '$points' } } }
      ]).then(result => result[0]?.total || 0)
    ]);

    // Get category distribution
    const categoryStats = await this.challengeModel.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalPoints: { $sum: '$points' },
          avgPoints: { $avg: '$points' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get difficulty distribution
    const difficultyStats = await this.challengeModel.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$difficulty',
          count: { $sum: 1 },
          totalPoints: { $sum: '$points' },
          avgPoints: { $avg: '$points' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get recent activity (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentActivity = await this.challengeSubmissionModel.aggregate([
      { $match: { submittedAt: { $gte: sevenDaysAgo } } },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$submittedAt' }
          },
          submissions: { $sum: 1 },
          correctSubmissions: {
            $sum: { $cond: ['$isCorrect', 1, 0] }
          }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    return {
      overview: {
        totalChallenges,
        activeChallenges,
        totalUsers,
        activeUsers,
        totalTeams,
        activeTeams,
        totalSubmissions,
        correctSubmissions,
        totalSolves,
        totalPoints,
        successRate: totalSubmissions > 0 ? Math.round((correctSubmissions / totalSubmissions) * 100) : 0
      },
      categoryStats,
      difficultyStats,
      recentActivity
    };
  }

  async getChallengeStatistics() {
    // Get challenge statistics with solve counts and first blood info
    const challengeStats = await this.challengeModel.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: 'challengesubmissions',
          localField: '_id',
          foreignField: 'challengeId',
          as: 'submissions'
        }
      },
      {
        $addFields: {
          totalSubmissions: { $size: '$submissions' },
          correctSubmissions: {
            $size: {
              $filter: {
                input: '$submissions',
                cond: { $eq: ['$$this.isCorrect', true] }
              }
            }
          },
          uniqueSolvers: {
            $size: {
              $setUnion: {
                $map: {
                  input: {
                    $filter: {
                      input: '$submissions',
                      cond: { $eq: ['$$this.isCorrect', true] }
                    }
                  },
                  as: 'submission',
                  in: '$$submission.userId'
                }
              }
            }
          },
          successRate: {
            $cond: [
              { $gt: [{ $size: '$submissions' }, 0] },
              {
                $multiply: [
                  {
                    $divide: [
                      {
                        $size: {
                          $filter: {
                            input: '$submissions',
                            cond: { $eq: ['$$this.isCorrect', true] }
                          }
                        }
                      },
                      { $size: '$submissions' }
                    ]
                  },
                  100
                ]
              },
              0
            ]
          }
        }
      },
      {
        $project: {
          _id: 1,
          title: 1,
          category: 1,
          difficulty: 1,
          points: 1,
          totalSubmissions: 1,
          correctSubmissions: 1,
          uniqueSolvers: 1,
          successRate: 1,
          firstBlood: 1,
          flagsFirstBlood: 1,
          solveCount: 1,
          createdAt: 1
        }
      },
      { $sort: { solveCount: -1, createdAt: -1 } }
    ]);

    // Get most popular challenges (by submission count)
    const popularChallenges = challengeStats
      .sort((a, b) => b.totalSubmissions - a.totalSubmissions)
      .slice(0, 10);

    // Get most difficult challenges (lowest success rate with min 10 submissions)
    const difficultChallenges = challengeStats
      .filter(c => c.totalSubmissions >= 10)
      .sort((a, b) => a.successRate - b.successRate)
      .slice(0, 10);

    // Get unsolved challenges
    const unsolvedChallenges = challengeStats
      .filter(c => c.correctSubmissions === 0)
      .sort((a, b) => b.totalSubmissions - a.totalSubmissions);

    return {
      allChallenges: challengeStats,
      popularChallenges,
      difficultChallenges,
      unsolvedChallenges
    };
  }

  async getFirstBloodStatistics() {
    // Get all challenges with first blood information
    const challengesWithFirstBlood = await this.challengeModel.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: 'users',
          localField: 'firstBlood.userId',
          foreignField: '_id',
          as: 'firstBloodUser'
        }
      },
      {
        $lookup: {
          from: 'teams',
          localField: 'firstBlood.teamId',
          foreignField: '_id',
          as: 'firstBloodTeam'
        }
      },
      {
        $addFields: {
          firstBloodUserInfo: { $arrayElemAt: ['$firstBloodUser', 0] },
          firstBloodTeamInfo: { $arrayElemAt: ['$firstBloodTeam', 0] }
        }
      },
      {
        $project: {
          _id: 1,
          title: 1,
          category: 1,
          difficulty: 1,
          points: 1,
          firstBlood: 1,
          flagsFirstBlood: 1,
          firstBloodUserInfo: {
            _id: 1,
            username: 1,
            avatar: 1
          },
          firstBloodTeamInfo: {
            _id: 1,
            name: 1
          },
          solveCount: 1,
          createdAt: 1
        }
      },
      { $sort: { 'firstBlood.timestamp': -1 } }
    ]);

    // Get first blood leaderboard (users with most first bloods)
    const firstBloodLeaderboard = await this.challengeModel.aggregate([
      { $match: { 'firstBlood.userId': { $exists: true } } },
      {
        $group: {
          _id: '$firstBlood.userId',
          username: { $first: '$firstBlood.username' },
          teamId: { $first: '$firstBlood.teamId' },
          teamName: { $first: '$firstBlood.teamName' },
          firstBloodCount: { $sum: 1 },
          totalPoints: { $sum: '$points' },
          challenges: {
            $push: {
              challengeId: '$_id',
              title: '$title',
              category: '$category',
              difficulty: '$difficulty',
              points: '$points',
              timestamp: '$firstBlood.timestamp'
            }
          }
        }
      },
      { $sort: { firstBloodCount: -1, totalPoints: -1 } },
      { $limit: 20 }
    ]);

    // Get team first blood leaderboard
    const teamFirstBloodLeaderboard = await this.challengeModel.aggregate([
      { $match: { 'firstBlood.teamId': { $exists: true } } },
      {
        $group: {
          _id: '$firstBlood.teamId',
          teamName: { $first: '$firstBlood.teamName' },
          firstBloodCount: { $sum: 1 },
          totalPoints: { $sum: '$points' },
          challenges: {
            $push: {
              challengeId: '$_id',
              title: '$title',
              category: '$category',
              difficulty: '$difficulty',
              points: '$points',
              timestamp: '$firstBlood.timestamp',
              solvedBy: '$firstBlood.username'
            }
          }
        }
      },
      { $sort: { firstBloodCount: -1, totalPoints: -1 } },
      { $limit: 20 }
    ]);

    // Get recent first bloods (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentFirstBloods = challengesWithFirstBlood
      .filter(c => c.firstBlood && new Date(c.firstBlood.timestamp) >= thirtyDaysAgo)
      .sort((a, b) => new Date(b.firstBlood.timestamp).getTime() - new Date(a.firstBlood.timestamp).getTime())
      .slice(0, 20);

    return {
      challengesWithFirstBlood,
      firstBloodLeaderboard,
      teamFirstBloodLeaderboard,
      recentFirstBloods
    };
  }

  async getUserActivityStatistics() {
    // Get user activity statistics
    const userStats = await this.userModel.aggregate([
      {
        $lookup: {
          from: 'challengesubmissions',
          localField: '_id',
          foreignField: 'userId',
          as: 'submissions'
        }
      },
      {
        $addFields: {
          totalSubmissions: { $size: '$submissions' },
          correctSubmissions: {
            $size: {
              $filter: {
                input: '$submissions',
                cond: { $eq: ['$$this.isCorrect', true] }
              }
            }
          },
          firstBloods: {
            $size: {
              $filter: {
                input: '$submissions',
                cond: { $eq: ['$$this.isFirstBlood', true] }
              }
            }
          },
          lastSubmission: { $max: '$submissions.submittedAt' }
        }
      },
      {
        $project: {
          _id: 1,
          username: 1,
          email: 1,
          avatar: 1,
          score: 1,
          totalSubmissions: 1,
          correctSubmissions: 1,
          firstBloods: 1,
          lastSubmission: 1,
          createdAt: 1,
          lastLoginAt: 1
        }
      },
      { $sort: { score: -1 } }
    ]);

    // Get most active users (by submission count)
    const mostActiveUsers = userStats
      .sort((a, b) => b.totalSubmissions - a.totalSubmissions)
      .slice(0, 10);

    // Get users with highest success rate (min 10 submissions)
    const highestSuccessRateUsers = userStats
      .filter(u => u.totalSubmissions >= 10)
      .map(u => ({
        ...u,
        successRate: u.totalSubmissions > 0 ? (u.correctSubmissions / u.totalSubmissions) * 100 : 0
      }))
      .sort((a, b) => b.successRate - a.successRate)
      .slice(0, 10);

    // Get registration trends (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const registrationTrends = await this.userModel.aggregate([
      { $match: { createdAt: { $gte: thirtyDaysAgo } } },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          registrations: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    return {
      userStats,
      mostActiveUsers,
      highestSuccessRateUsers,
      registrationTrends
    };
  }

  async getTeamActivityStatistics() {
    // Get team activity statistics
    const teamStats = await this.teamModel.aggregate([
      {
        $match: { 'members.status': 'active' }
      },
      {
        $lookup: {
          from: 'teamchallengesolves',
          localField: '_id',
          foreignField: 'teamId',
          as: 'solves'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'captainId',
          foreignField: '_id',
          as: 'captain'
        }
      },
      {
        $addFields: {
          memberCount: {
            $size: {
              $filter: {
                input: '$members',
                cond: { $eq: ['$$this.status', 'active'] }
              }
            }
          },
          solveCount: { $size: '$solves' },
          firstBloods: {
            $size: {
              $filter: {
                input: '$solves',
                cond: { $eq: ['$$this.isFirstBlood', true] }
              }
            }
          },
          lastSolve: { $max: '$solves.solvedAt' },
          captainInfo: { $arrayElemAt: ['$captain', 0] }
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          teamScore: 1,
          memberCount: 1,
          solveCount: 1,
          firstBloods: 1,
          lastSolve: 1,
          captainInfo: {
            _id: 1,
            username: 1
          },
          createdAt: 1
        }
      },
      { $sort: { teamScore: -1 } }
    ]);

    // Get most active teams (by solve count)
    const mostActiveTeams = teamStats
      .sort((a, b) => b.solveCount - a.solveCount)
      .slice(0, 10);

    // Get team formation trends (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const teamFormationTrends = await this.teamModel.aggregate([
      { $match: { createdAt: { $gte: thirtyDaysAgo } } },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          teamsFormed: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Get team size distribution
    const teamSizeDistribution = await this.teamModel.aggregate([
      {
        $match: { 'members.status': 'active' }
      },
      {
        $addFields: {
          memberCount: {
            $size: {
              $filter: {
                input: '$members',
                cond: { $eq: ['$$this.status', 'active'] }
              }
            }
          }
        }
      },
      {
        $group: {
          _id: '$memberCount',
          teamCount: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    return {
      teamStats,
      mostActiveTeams,
      teamFormationTrends,
      teamSizeDistribution
    };
  }
}