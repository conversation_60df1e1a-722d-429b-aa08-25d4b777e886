import { useState } from 'react';
import Layout from '../components/Layout';
import { Save } from 'lucide-react';

 const SettingsSection = ({ title, description, children }) => (
    <div className="bg-black border border-gray-800 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-bold text-white">{title}</h2>
        <p className="text-gray-400 mt-1 mb-6">{description}</p>
        <div className="space-y-4">
            {children}
        </div>
    </div>
);

 const Toggle = ({ label, enabled, setEnabled }) => (
    <div className="flex items-center justify-between">
        <span className="text-gray-300">{label}</span>
        <button 
            onClick={() => setEnabled(!enabled)} 
            className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black focus:ring-white ${enabled ? 'bg-white' : 'bg-gray-600'}`}
        >
            <span className={`inline-block w-4 h-4 transform bg-black rounded-full transition-transform ${enabled ? 'translate-x-6' : 'translate-x-1'}`} />
        </button>
    </div>
);

 export default function SettingsPage() {
     const [emailNotifications, setEmailNotifications] = useState(true);
    const [pushNotifications, setPushNotifications] = useState(false);
    const [twoFactor, setTwoFactor] = useState(true);

    return (
        <Layout>
            <h1 className="text-4xl font-bold text-white mb-2">Settings</h1>
            <p className="text-gray-400 mb-8">Manage your account and notification preferences.</p>

            <SettingsSection title="Profile" description="Update your personal information.">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-400">Username</label>
                        <input type="text" disabled value="KYBSAdmin" className="mt-2 w-full bg-gray-900 border border-gray-700 rounded-md px-3 py-2 text-gray-500 cursor-not-allowed"/>
                    </div>
                     <div>
                        <label className="block text-sm font-medium text-gray-400">Email Address</label>
                        <input type="email" defaultValue="<EMAIL>" className="mt-2 w-full bg-black border border-gray-700 rounded-md px-3 py-2 text-white focus:ring-1 focus:ring-white outline-none"/>
                    </div>
                </div>
            </SettingsSection>
            
            <SettingsSection title="Security" description="Manage your account security settings.">
                <Toggle label="Two-Factor Authentication (2FA)" enabled={twoFactor} setEnabled={setTwoFactor} />
                <div className="border-t border-gray-800 my-4"></div>
                <button className="text-white hover:underline">Change Password...</button>
            </SettingsSection>

            <SettingsSection title="Notifications" description="Select how you want to receive alerts.">
                <Toggle label="Email Notifications" enabled={emailNotifications} setEnabled={setEmailNotifications} />
                <Toggle label="Push Notifications" enabled={pushNotifications} setEnabled={setPushNotifications} />
            </SettingsSection>

            <div className="flex justify-end mt-4">
                <button className="group flex items-center justify-center py-3 px-6 font-semibold text-black bg-white rounded-lg hover:bg-gray-200 transition-all duration-200">
                    <Save className="mr-2 h-5 w-5" />
                    Save Changes
                </button>
            </div>
        </Layout>
    );
}
