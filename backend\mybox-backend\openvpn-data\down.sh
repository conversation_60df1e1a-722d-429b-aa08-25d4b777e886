#!/bin/bash

# Create log directory if it doesn't exist
mkdir -p /var/log/openvpn

# Log script execution
{
    echo "=== OpenVPN Down Script ==="
    echo "Date: $(date)"
    echo "Interface: $1"
    echo "MTU: $2"
    echo "Link MTU: $3"
    echo "Local IP: $4"
    echo "Remote IP: $5"
    echo "Init: $6"
    
    # Remove NAT rules
    iptables -t nat -D POSTROUTING -s *************/24 -o eth0 -j MASQUERADE 2>/dev/null

    # Remove forwarding rules
    iptables -D FORWARD -i tun+ -o eth0 -j ACCEPT 2>/dev/null
    iptables -D FORWARD -i eth0 -o tun+ -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null

    # Remove logging chain
    if iptables -L LOGGING >/dev/null 2>&1; then
        iptables -D INPUT -j LOGGING 2>/dev/null
        iptables -D OUTPUT -j LOGGING 2>/dev/null
        iptables -F LOGGING 2>/dev/null
        iptables -X LOGGING 2>/dev/null
    fi
    
    # Flush all rules
    iptables -F
    iptables -t nat -F
    iptables -t mangle -F
    
    # Set default policies
    iptables -P INPUT ACCEPT
    iptables -P FORWARD ACCEPT
    iptables -P OUTPUT ACCEPT
    
    # Log current iptables rules
    echo "=== Final iptables rules ==="
    iptables -t nat -L -v -n
    iptables -L -v -n
    
    echo "=== Network interfaces ==="
    ip a
    echo "=== Routes ==="
    ip route
} >> /var/log/openvpn/down-script.log 2>&1

exit 0
