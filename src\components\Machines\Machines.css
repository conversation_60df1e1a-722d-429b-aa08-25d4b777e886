/* Machines Page - Statistics Style Design */

/* Custom scrollbar for machines page */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(139, 92, 246, 0.1);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(139, 92, 246, 0.1);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #8B5CF6, #EC4899);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #A78BFA, #F472B6);
}

/* Glass card effects - Always visible */
.glass-card {
  backdrop-filter: blur(20px) !important;
  background: rgba(139, 92, 246, 0.1) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(139, 92, 246, 0.1),
    inset 0 0 20px rgba(139, 92, 246, 0.05) !important;
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(139, 92, 246, 0.15) !important;
  border: 1px solid rgba(139, 92, 246, 0.4) !important;
  transform: translateY(-2px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(139, 92, 246, 0.2),
    inset 0 0 30px rgba(139, 92, 246, 0.1) !important;
}

.glass-card-dark {
  backdrop-filter: blur(20px) !important;
  background: rgba(139, 92, 246, 0.08) !important;
  border: 1px solid rgba(139, 92, 246, 0.25) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(139, 92, 246, 0.1),
    inset 0 0 20px rgba(139, 92, 246, 0.05) !important;
  transition: all 0.3s ease;
}

.glass-card-dark:hover {
  background: rgba(139, 92, 246, 0.12) !important;
  border: 1px solid rgba(139, 92, 246, 0.35) !important;
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(139, 92, 246, 0.15),
    inset 0 0 30px rgba(139, 92, 246, 0.08) !important;
}

/* Override MachineCard styles with higher specificity */
.machine-card-wrapper .group {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(236, 72, 153, 0.05)) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(139, 92, 246, 0.1),
    inset 0 0 20px rgba(139, 92, 246, 0.05) !important;
  transition: all 0.3s ease !important;
}

.machine-card-wrapper .group:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(236, 72, 153, 0.1)) !important;
  border: 1px solid rgba(139, 92, 246, 0.4) !important;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(139, 92, 246, 0.2),
    inset 0 0 30px rgba(139, 92, 246, 0.1) !important;
  transform: translateY(-8px) !important;
}

/* Machine card wrapper enhancements */
.machine-card-wrapper {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.machine-card-wrapper:hover {
  z-index: 10;
}

/* Enhanced machine card styles - Always visible effects */
.machine-card-wrapper .group::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(236, 72, 153, 0.05));
  opacity: 1;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.machine-card-wrapper:hover .group::before {
  opacity: 0.3;
}

/* Floating particles on hover */
.machine-card-wrapper:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.3) 2px, transparent 2px),
    radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.3) 2px, transparent 2px),
    radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.3) 2px, transparent 2px);
  background-size: 50px 50px, 60px 60px, 40px 40px;
  animation: float-particles 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 2;
}

@keyframes float-particles {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px);
    opacity: 0.7;
  }
}

/* Neon glow effects - Always visible */
.neon-glow {
  box-shadow: 
    0 0 5px rgba(139, 92, 246, 0.5),
    0 0 10px rgba(139, 92, 246, 0.3),
    0 0 15px rgba(139, 92, 246, 0.2),
    0 0 20px rgba(139, 92, 246, 0.1);
}

.neon-glow-pink {
  box-shadow: 
    0 0 5px rgba(236, 72, 153, 0.5),
    0 0 10px rgba(236, 72, 153, 0.3),
    0 0 15px rgba(236, 72, 153, 0.2),
    0 0 20px rgba(236, 72, 153, 0.1);
}

.neon-glow-blue {
  box-shadow: 
    0 0 5px rgba(6, 182, 212, 0.5),
    0 0 10px rgba(6, 182, 212, 0.3),
    0 0 15px rgba(6, 182, 212, 0.2),
    0 0 20px rgba(6, 182, 212, 0.1);
}

.neon-glow-green {
  box-shadow: 
    0 0 5px rgba(16, 185, 129, 0.5),
    0 0 10px rgba(16, 185, 129, 0.3),
    0 0 15px rgba(16, 185, 129, 0.2),
    0 0 20px rgba(16, 185, 129, 0.1);
}

.neon-glow-yellow {
  box-shadow: 
    0 0 5px rgba(245, 158, 11, 0.5),
    0 0 10px rgba(245, 158, 11, 0.3),
    0 0 15px rgba(245, 158, 11, 0.2),
    0 0 20px rgba(245, 158, 11, 0.1);
}

/* Floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Morph shapes */
.morph-shape {
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: translate3d(0, 0, 0) rotateZ(0.01deg);
  }
  34% {
    border-radius: 70% 60% 70% 30% / 50% 60% 30% 60%;
    transform: translate3d(5px, -10px, 0) rotateZ(0.01deg);
  }
  67% {
    border-radius: 100% 60% 60% 100% / 100% 100% 60% 60%;
    transform: translate3d(-5px, 10px, 0) rotateZ(0.01deg);
  }
}

/* Pulse animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text {
  background: linear-gradient(-45deg, #8B5CF6, #EC4899, #06B6D4, #10B981);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced button styles */
.machine-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.machine-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.machine-button:hover::before {
  left: 100%;
}

.machine-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Search input enhancements */
.search-input {
  position: relative;
  transition: all 0.3s ease;
}

.search-input:focus-within {
  transform: scale(1.02);
}

.search-input input {
  transition: all 0.3s ease;
}

.search-input input:focus {
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
}

/* Stagger animation delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }
.stagger-7 { animation-delay: 0.7s; }
.stagger-8 { animation-delay: 0.8s; }

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, rgba(139, 92, 246, 0.1) 25%, rgba(139, 92, 246, 0.2) 50%, rgba(139, 92, 246, 0.1) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Progress bars */
.progress-bar {
  position: relative;
  overflow: hidden;
  border-radius: 9999px;
  background: rgba(139, 92, 246, 0.1);
}

.progress-fill {
  height: 100%;
  border-radius: 9999px;
  transition: width 1s ease-in-out;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Hover lift effect */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Mobile scrolling improvements */
@media (max-width: 768px) {
  .machine-card-wrapper {
    margin-bottom: 1rem;
  }
  
  .glass-card-dark {
    padding: 1rem;
  }
  
  .search-input {
    margin-bottom: 1rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .glass-card,
  .glass-card-dark {
    backdrop-filter: blur(25px);
  }
}

/* Text clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}