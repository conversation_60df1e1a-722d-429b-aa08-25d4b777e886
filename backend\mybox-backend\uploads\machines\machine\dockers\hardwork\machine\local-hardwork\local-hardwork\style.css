/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    background-color: #f5f7fa;
    color: #2d3748;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background-color: #ffffff;
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: 600;
    color: #4a5568;
}

.nav-links a {
    text-decoration: none;
    color: #4a5568;
    margin-left: 2rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover,
.nav-links a.active {
    color: #4299e1;
}

/* Hero section */
.hero {
    text-align: center;
    padding: 4rem 0;
}

.hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #2d3748;
}

.hero-content p {
    font-size: 1.2rem;
    color: #718096;
    margin-bottom: 2rem;
}

.cta-button {
    display: inline-block;
    padding: 0.8rem 2rem;
    background-color: #4299e1;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.cta-button:hover {
    background-color: #3182ce;
}

/* Dashboard styles */
.dashboard {
    padding: 2rem 0;
}

.dashboard-content {
    background-color: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard h1 {
    margin-bottom: 2rem;
    color: #2d3748;
}

/* Profile settings */
.profile-settings {
    max-width: 600px;
    margin: 0 auto;
}

.profile-settings h2 {
    margin-bottom: 1.5rem;
    color: #2d3748;
}

.profile-photo {
    text-align: center;
    margin-bottom: 2rem;
}

.profile-photo img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
    border: 3px solid #e2e8f0;
}

.upload-btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: #4299e1;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.upload-btn:hover {
    background-color: #3182ce;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #4a5568;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #4299e1;
}

.save-btn {
    width: 100%;
    padding: 0.8rem;
    background-color: #4299e1;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.save-btn:hover {
    background-color: #3182ce;
}

/* Responsive design */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        text-align: center;
    }
    
    .nav-links {
        margin-top: 1rem;
    }
    
    .nav-links a {
        margin: 0 1rem;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
} 