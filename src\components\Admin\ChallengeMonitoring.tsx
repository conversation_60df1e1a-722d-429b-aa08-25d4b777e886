import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { AdminService, AdminChallengeListItem, AdminPaginationParams, CreateChallengeRequest, UpdateChallengeRequest } from '../../services/admin';
import { CategoriesService, Category } from '../../services/categories';
import { Edit, Trash2, Search, ChevronLeft, ChevronRight, Flag, Eye, EyeOff, Plus, X, Info } from 'lucide-react';
import { ChallengeDetails } from './ChallengeDetails';

interface ChallengeFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (challenge: CreateChallengeRequest | UpdateChallengeRequest) => Promise<void>;
  challenge?: AdminChallengeListItem;
  title: string;
}

function ChallengeFormModal({ isOpen, onClose, onSubmit, challenge, title }: ChallengeFormModalProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [formData, setFormData] = useState<CreateChallengeRequest | UpdateChallengeRequest>({
    title: '',
    description: '',
    category: '',
    difficulty: 'easy',
    points: 100,
    flag: '',
    isActive: true,
    hints: [],
    tags: [],
    flags: [],
    files: [],
    releaseDate: new Date().toISOString().split('T')[0],
    requiresServer: false
  });
  
  const [currentHint, setCurrentHint] = useState<string>('');
  const [currentTag, setCurrentTag] = useState<string>('');
  const [currentFlag, setCurrentFlag] = useState({
    value: '',
    description: 'Primary Flag',
    points: 100,
    isCaseSensitive: false
  });
  const [currentFile, setCurrentFile] = useState({
    name: '',
    path: '',
    description: '',
    type: '',
    size: 0
  });
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [usesMultipleFlags, setUsesMultipleFlags] = useState<boolean>(false);

  // Fetch categories when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchCategories();
    }
  }, [isOpen]);

  const fetchCategories = async () => {
    try {
      setLoadingCategories(true);
      const categoriesData = await CategoriesService.getCategories();
      setCategories(categoriesData);

      // Set default category if form is empty
      if (!formData.category && categoriesData.length > 0) {
        setFormData(prev => ({ ...prev, category: categoriesData[0].name }));
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    } finally {
      setLoadingCategories(false);
    }
  };

  useEffect(() => {
    if (challenge) {
      setFormData({
        title: challenge.title,
        description: challenge.description || '',
        category: challenge.category,
        difficulty: challenge.difficulty,
        points: challenge.points,
        flag: challenge.flag || '',
        flags: challenge.flags || [],
        isActive: challenge.isActive,
        hints: challenge.hints || [],
        tags: challenge.tags || [],
        files: challenge.files || [],
        authorId: challenge.authorId,
        authorName: challenge.authorName,
        releaseDate: challenge.releaseDate ? new Date(challenge.releaseDate).toISOString().split('T')[0] : undefined,
        expiryDate: challenge.expiryDate ? new Date(challenge.expiryDate).toISOString().split('T')[0] : undefined,
        dockerImage: challenge.dockerImage,
        requiresServer: challenge.requiresServer || false,
        serverConfig: challenge.serverConfig
      });
      setUsesMultipleFlags(!!challenge.flags && challenge.flags.length > 0);
    } else {
      // Reset form for new challenge
      const defaultCategory = categories.length > 0 ? categories[0].name : '';
      setFormData({
        title: '',
        description: '',
        category: defaultCategory,
        difficulty: 'easy',
        points: 100,
        flag: '',
        isActive: true,
        hints: [],
        tags: [],
        flags: [],
        files: [],
        releaseDate: new Date().toISOString().split('T')[0],
        requiresServer: false
      });
      setUsesMultipleFlags(false);
    }
  }, [challenge, isOpen]);
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'points') {
      setFormData({ ...formData, [name]: parseInt(value) });
    } else if (name === 'isActive' || name === 'requiresServer') {
      setFormData({ ...formData, [name]: (e.target as HTMLInputElement).checked });
    } else if (name === 'flag' && usesMultipleFlags) {
      // If using multiple flags, we don't update the legacy flag field
      return;
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };
  
  const toggleMultiFlag = () => {
    const newState = !usesMultipleFlags;
    setUsesMultipleFlags(newState);
    
    if (newState && formData.flag) {
      // Convert single flag to multiple flags
      setFormData({
        ...formData,        flags: [
          {
            value: formData.flag,
            description: 'Primary Flag',
            points: formData.points || 100,
            isCaseSensitive: false
          }
        ]
      });
    } else if (!newState && formData.flags && formData.flags.length > 0) {
      // Convert first flag to single flag
      setFormData({
        ...formData,
        flag: formData.flags[0].value
      });
    }
  };
  
  const addHint = () => {
    if (currentHint.trim()) {
      setFormData({
        ...formData,
        hints: [...(formData.hints || []), currentHint.trim()]
      });
      setCurrentHint('');
    }
  };
  
  const removeHint = (index: number) => {
    const hints = [...(formData.hints || [])];
    hints.splice(index, 1);
    setFormData({ ...formData, hints });
  };
  
  const addTag = () => {
    if (currentTag.trim()) {
      setFormData({
        ...formData,
        tags: [...(formData.tags || []), currentTag.trim()]
      });
      setCurrentTag('');
    }
  };
  
  const removeTag = (index: number) => {
    const tags = [...(formData.tags || [])];
    tags.splice(index, 1);
    setFormData({ ...formData, tags });
  };
  
  const handleFlagChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name === 'flagPoints') {
      setCurrentFlag({ ...currentFlag, points: parseInt(value) });
    } else if (name === 'flagCaseSensitive') {
      setCurrentFlag({ ...currentFlag, isCaseSensitive: (e.target as HTMLInputElement).checked });
    } else {
      setCurrentFlag({ ...currentFlag, [name.replace('flag', '').toLowerCase()]: value });
    }
  };
  
  const addFlag = () => {
    if (currentFlag.value.trim()) {
      setFormData({
        ...formData,
        flags: [...(formData.flags || []), { ...currentFlag, value: currentFlag.value.trim() }]
      });
      setCurrentFlag({
        value: '',
        description: 'Flag',
        points: 100,
        isCaseSensitive: false
      });
    }
  };
  
  const removeFlag = (index: number) => {
    const flags = [...(formData.flags || [])];
    flags.splice(index, 1);
    setFormData({ ...formData, flags });
  };
    const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name === 'fileSize') {
      setCurrentFile({ ...currentFile, size: parseInt(value) });
    } else {
      setCurrentFile({ ...currentFile, [name.replace('file', '').toLowerCase()]: value });
    }
  };
  
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setSelectedFile(file);
      
      // Auto-fill file details
      setCurrentFile({
        name: file.name,
        path: '', // Will be set after upload
        description: currentFile.description,
        type: file.type,
        size: file.size
      });
    }
  };  const uploadFile = async () => {
    if (!selectedFile) return;
    
    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      // Create form data for file upload
      const formDataPayload = new FormData();
      formDataPayload.append('file', selectedFile);
      
      // Add challenge name for organized folder structure
      if (formData.title) {
        formDataPayload.append('challengeName', formData.title);
      }
        // Get token from localStorage (consistent with other services)
      const token = localStorage.getItem('mybox_token');
        // Call API to upload file
      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/upload`, {
        method: 'POST',
        headers: {
          ...(token && { Authorization: `Bearer ${token}` }),
          // Note: Don't set Content-Type for FormData, let the browser set it
        },
        body: formDataPayload
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || 'Failed to upload file');
      }
      
      // Automatically add the uploaded file to the challenge files
      const newFile = {
        name: result.originalName,
        path: result.filePath,
        size: result.size,
        type: result.mimeType,
        description: currentFile.description || ''
      };
      
      setFormData({
        ...formData,
        files: [...(formData.files || []), newFile]
      });
      
      // Reset file selection and current file
      setSelectedFile(null);
      setCurrentFile({
        name: '',
        path: '',
        description: '',
        type: '',
        size: 0
      });
      setError(null);
    } catch (err: any) {
      setError((err as Error)?.message || 'Failed to upload file');
    } finally {      setIsUploading(false);
    }
  };

  const removeFile = (index: number) => {
    const files = [...(formData.files || [])];
    files.splice(index, 1);
    setFormData({ ...formData, files });
  };
    const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);
      // Validate the form data
    if (!formData.title?.trim()) {
      setError('Challenge title is required');
      setIsSubmitting(false);
      return;
    }
    
    // Validate flag(s)
    if (!usesMultipleFlags && !formData.flag?.trim()) {
      setError('Flag is required');
      setIsSubmitting(false);
      return;
    }
    
    if (usesMultipleFlags && (!formData.flags || formData.flags.length === 0)) {
      setError('At least one flag is required');
      setIsSubmitting(false);
      return;
    }
    
    // Handle server configuration
    if (formData.requiresServer && formData.serverConfig) {
      try {
        if (typeof formData.serverConfig === 'string') {
          formData.serverConfig = JSON.parse(formData.serverConfig as string);
        }
      } catch (error) {
        setError('Server configuration must be valid JSON');
        setIsSubmitting(false);
        return;
      }
    }
    
    // Prepare final data
    const finalData = { ...formData };
    
    // If using multiple flags, remove the single flag field
    if (usesMultipleFlags) {
      delete finalData.flag;
    } else {
      // If using single flag, remove the flags array
      delete finalData.flags;
    }
    
    try {
      await onSubmit(finalData);
      onClose();    } catch (err: any) {
      setError((err as Error)?.message || 'Failed to save challenge');
      console.error('Error saving challenge:', err);
    } finally {
      setIsSubmitting(false);
    }
  };  if (!isOpen) return null;
  
  const modalContent = (
    <>
      <style>
        {`
          .challenge-modal-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            z-index: 2147483647 !important;
            width: 100vw !important;
            height: 100vh !important;
            overflow-y: auto !important;
            background-color: rgba(0, 0, 0, 0.75) !important;
            backdrop-filter: blur(4px) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
          }
          .challenge-modal-content {
            position: relative !important;
            width: 100% !important;
            max-width: 64rem !important;
            margin: 0 auto !important;
            background-color: rgb(15 23 42) !important;
            border-radius: 0.75rem !important;
            border: 1px solid rgb(51 65 85) !important;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
            padding: 1.5rem !important;
            max-height: 90vh !important;
            overflow-y: auto !important;
            z-index: 2147483647 !important;
          }
        `}
      </style>
      <div className="challenge-modal-overlay" onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}>
        <div className="challenge-modal-content" onClick={(e) => e.stopPropagation()}>
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-slate-400 hover:text-white"
        >
          <X className="w-5 h-5" />
        </button>
        
        <h2 className="text-xl font-bold text-white mb-4">{title}</h2>
        
        {error && (
          <div className="mb-4 p-3 bg-red-900/30 border border-red-800 rounded-lg text-red-400">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-1">
                Title
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                placeholder="Challenge Title"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-1">
                  Category
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  required
                  disabled={loadingCategories}
                  className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400 disabled:opacity-50"
                >
                  {loadingCategories ? (
                    <option value="">Loading categories...</option>
                  ) : categories.length === 0 ? (
                    <option value="">No categories available</option>
                  ) : (
                    categories.map(category => (
                      <option key={category.id} value={category.name}>
                        {category.displayName}
                      </option>
                    ))
                  )}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-1">
                  Difficulty
                </label>
                <select
                  name="difficulty"
                  value={formData.difficulty}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                >
                  <option value="easy">Easy</option>
                  <option value="medium">Medium</option>
                  <option value="hard">Hard</option>
                  <option value="insane">Insane</option>
                </select>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-1">
                Points
              </label>
              <input
                type="number"
                name="points"
                value={formData.points}
                onChange={handleChange}
                required
                min={10}
                step={10}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
              />
            </div>
              <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-slate-300">
                  Flag Management
                </label>
                <div className="flex items-center">
                  <label className="mr-2 text-xs text-slate-400">Single Flag</label>
                  <div 
                    onClick={toggleMultiFlag}
                    className={`relative inline-block w-10 h-5 rounded-full cursor-pointer transition-colors duration-200 ease-in-out ${usesMultipleFlags ? 'bg-emerald-600' : 'bg-slate-700'}`}
                  >
                    <span 
                      className={`absolute left-1 top-1 bg-white w-3 h-3 rounded-full transition-transform duration-200 ease-in-out ${usesMultipleFlags ? 'transform translate-x-5' : ''}`} 
                    />
                  </div>
                  <label className="ml-2 text-xs text-slate-400">Multiple Flags</label>
                </div>
              </div>
              
              {!usesMultipleFlags ? (
                <input
                  type="text"
                  name="flag"
                  value={formData.flag || ''}
                  onChange={handleChange}
                  required={!challenge && !usesMultipleFlags}
                  className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                  placeholder="HTB{flag_format_here}"
                />
              ) : (
                <div className="space-y-4">
                  <div className="p-3 bg-slate-800/50 border border-slate-700 rounded-lg">
                    <div className="grid grid-cols-1 gap-3">
                      <input
                        type="text"
                        name="flagValue"
                        value={currentFlag.value}
                        onChange={handleFlagChange}
                        placeholder="Flag value (e.g. HTB{flag_here})"
                        className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                      />
                      <div className="grid grid-cols-2 gap-3">
                        <input
                          type="text"
                          name="flagDescription"
                          value={currentFlag.description}
                          onChange={handleFlagChange}
                          placeholder="Flag description"
                          className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                        />
                        <input
                          type="number"
                          name="flagPoints"
                          value={currentFlag.points}
                          onChange={handleFlagChange}
                          placeholder="Points"
                          min={10}
                          step={10}
                          className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                        />
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="flagCaseSensitive"
                          name="flagCaseSensitive"
                          checked={currentFlag.isCaseSensitive}
                          onChange={handleFlagChange}
                          className="w-4 h-4 text-emerald-500 bg-slate-800 border-slate-700 rounded focus:ring-emerald-400"
                        />
                        <label htmlFor="flagCaseSensitive" className="ml-2 text-sm text-slate-300">
                          Case sensitive
                        </label>
                      </div>
                      <div className="flex justify-end">
                        <button
                          type="button"
                          onClick={addFlag}
                          disabled={!currentFlag.value.trim()}
                          className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg flex items-center gap-2"
                        >
                          <Plus className="w-4 h-4" />
                          Add Flag
                        </button>
                      </div>
                    </div>
                  </div>

                  {formData.flags && formData.flags.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-slate-300">Added Flags:</h4>
                      {formData.flags.map((flag, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-slate-800 rounded-lg">
                          <div>
                            <div className="text-white text-sm font-mono">{flag.value}</div>
                            <div className="flex items-center gap-4 text-xs text-slate-400 mt-1">
                              <span>{flag.description}</span>
                              <span className="text-emerald-400">{flag.points} pts</span>
                              {flag.isCaseSensitive && <span className="text-amber-400">Case sensitive</span>}
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFlag(index)}
                            className="text-red-400 hover:text-red-300"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-slate-300 mb-1">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
                rows={5}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                placeholder="Challenge description with detailed instructions..."
              ></textarea>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-1">
                Hints
              </label>
              <div className="flex">
                <input
                  type="text"
                  value={currentHint}
                  onChange={(e) => setCurrentHint(e.target.value)}
                  className="flex-1 px-3 py-2 bg-slate-800 border border-slate-700 rounded-l-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                  placeholder="Add a hint"
                />
                <button
                  type="button"
                  onClick={addHint}
                  className="px-3 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-r-lg"
                >
                  Add
                </button>
              </div>
              <div className="mt-2 space-y-2">
                {formData.hints && formData.hints.map((hint, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-slate-800 rounded-lg">
                    <span className="text-slate-300 text-sm">{hint}</span>
                    <button
                      type="button"
                      onClick={() => removeHint(index)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
              <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-1">
                  Tags
                </label>
                <div className="flex">
                  <input
                    type="text"
                    value={currentTag}
                    onChange={(e) => setCurrentTag(e.target.value)}
                    className="flex-1 px-3 py-2 bg-slate-800 border border-slate-700 rounded-l-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                    placeholder="Add a tag"
                  />
                  <button
                    type="button"
                    onClick={addTag}
                    className="px-3 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-r-lg"
                  >
                    Add
                  </button>
                </div>
                <div className="mt-2 flex flex-wrap gap-2">
                  {formData.tags && formData.tags.map((tag, index) => (
                    <div key={index} className="flex items-center gap-1 px-2 py-1 bg-slate-800 rounded-lg">
                      <span className="text-slate-300 text-sm">{tag}</span>
                      <button
                        type="button"
                        onClick={() => removeTag(index)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
                <div>
                <label className="block text-sm font-medium text-slate-300 mb-1">
                  Challenge Files
                </label>
                <div className="p-4 bg-slate-800/50 border border-slate-700 rounded-lg">
                  <div className="grid grid-cols-1 gap-4">
                    {/* File Upload Section */}
                    <div className="border border-dashed border-slate-600 rounded-lg p-4 bg-slate-800/30">
                      <div className="text-center mb-4">
                        <label htmlFor="fileUpload" className="cursor-pointer">
                          <div className="text-sm text-slate-300 mb-2">
                            {selectedFile ? `Selected: ${selectedFile.name}` : 'Upload a file'}
                          </div>
                          <div className="w-full h-24 flex items-center justify-center bg-slate-800/50 rounded-lg hover:bg-slate-800 transition-colors">
                            <div className="flex flex-col items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-emerald-500 mb-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                              </svg>
                              <span className="text-sm text-slate-400">Click or drop file</span>
                            </div>
                          </div>
                        </label>
                        <input 
                          id="fileUpload" 
                          type="file" 
                          onChange={handleFileSelect}
                          className="hidden" 
                        />
                      </div>
                      
                      {/* Upload Button & Progress */}
                      {selectedFile && (
                        <div className="space-y-3">
                          {isUploading ? (
                            <div className="w-full bg-slate-700 rounded-full h-2.5 mt-2">
                              <div 
                                className="bg-emerald-600 h-2.5 rounded-full" 
                                style={{ width: `${uploadProgress}%` }}
                              ></div>
                              <div className="text-xs text-slate-400 text-center mt-1">
                                Uploading... {uploadProgress}%
                              </div>
                            </div>
                          ) : (
                            <button
                              type="button"
                              onClick={uploadFile}
                              className="w-full px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg flex items-center justify-center gap-2"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                              </svg>
                              Upload File
                            </button>
                          )}
                        </div>
                      )}                    </div>
                    
                    {/* File Description (Optional) */}
                    {selectedFile && (
                      <div className="mt-4">
                        <input
                          type="text"
                          name="fileDescription"
                          value={currentFile.description}
                          onChange={handleFileChange}
                          placeholder="File description (optional)"
                          className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                        />
                        <p className="text-xs text-slate-500 mt-1">
                          File will be automatically added to the challenge after upload
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                
                {formData.files && formData.files.length > 0 && (
                  <div className="mt-3 space-y-2">
                    <h4 className="text-sm font-medium text-slate-300">Added Files:</h4>
                    {formData.files.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-slate-800 rounded-lg">
                        <div>
                          <div className="text-white text-sm">{file.name}</div>
                          <div className="text-xs text-slate-400 mt-1">
                            {file.path}
                            {file.description && <span className="ml-3">({file.description})</span>}
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => removeFile(index)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-1">
                Release Date
              </label>
              <input
                type="date"
                name="releaseDate"
                value={formData.releaseDate || ''}
                onChange={handleChange}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-1">
                Expiry Date (Optional)
              </label>
              <input
                type="date"
                name="expiryDate"
                value={formData.expiryDate || ''}
                onChange={handleChange}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
              />
            </div>
          </div>

          <div className="space-y-3 border-t border-slate-800 pt-4">
            <h3 className="text-md font-medium text-white">Server Configuration</h3>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="requiresServer"
                name="requiresServer"
                checked={formData.requiresServer || false}
                onChange={(e) => setFormData({ ...formData, requiresServer: e.target.checked })}
                className="w-4 h-4 text-emerald-500 bg-slate-800 border-slate-700 rounded focus:ring-emerald-400"
              />
              <label htmlFor="requiresServer" className="ml-2 text-sm font-medium text-slate-300">
                This challenge requires a server
              </label>
            </div>
            
            {formData.requiresServer && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-1">
                    Docker Image
                  </label>
                  <input
                    type="text"
                    name="dockerImage"
                    value={formData.dockerImage || ''}
                    onChange={handleChange}
                    placeholder="e.g. mybox/web-challenge:latest"
                    className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-1">
                    Server Configuration (JSON)
                  </label>                  <textarea
                    name="serverConfig"
                    value={formData.serverConfig ? JSON.stringify(formData.serverConfig, null, 2) : ''}
                    onChange={(e) => {
                      try {
                        // Allow empty string
                        if (!e.target.value.trim()) {
                          setFormData({ ...formData, serverConfig: undefined });
                          return;
                        }
                        
                        // Try to parse as JSON
                        const jsonValue = JSON.parse(e.target.value);
                        setFormData({ ...formData, serverConfig: jsonValue });
                        setError(null);
                      } catch (err) {
                        // Store as string if invalid JSON, will be validated on submit
                        setFormData({ ...formData, serverConfig: e.target.value });
                      }
                    }}
                    placeholder='{ "port": 8080, "env": { "CTF_FLAG": "..."} }'
                    className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                    rows={3}
                  />
                </div>
              </div>
            )}
          </div>

          <div className="space-y-3 border-t border-slate-800 pt-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                className="w-4 h-4 text-emerald-500 bg-slate-800 border-slate-700 rounded focus:ring-emerald-400"
              />
              <label htmlFor="isActive" className="ml-2 text-sm font-medium text-slate-300">
                Active (visible to users)
              </label>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4 border-t border-slate-800">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-slate-700 text-slate-300 rounded-lg hover:bg-slate-800"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-4 py-2 bg-emerald-600 text-white rounded-lg ${
                isSubmitting 
                  ? 'opacity-70 cursor-not-allowed' 
                  : 'hover:bg-emerald-700'
              }`}
            >
              {isSubmitting ? 'Saving...' : 'Save Challenge'}
            </button>
          </div>        </form>
      </div>
    </div>
    </>
  );

  // Use createPortal to render the modal at the document body level
  return createPortal(modalContent, document.body);
}

export function ChallengeMonitoring() {
  const [challenges, setChallenges] = useState<AdminChallengeListItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalChallenges, setTotalChallenges] = useState<number>(0);
  const [limit] = useState<number>(10);
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [showDetailsView, setShowDetailsView] = useState<boolean>(false);
  const [currentChallenge, setCurrentChallenge] = useState<AdminChallengeListItem | undefined>(undefined);

  const fetchChallenges = async (params: AdminPaginationParams = {}) => {
    setLoading(true);
    try {
      const response = await AdminService.getChallenges({
        page: params.page || currentPage,
        limit,
        search: params.search || searchTerm,
        sortBy: 'createdAt',
        sortDirection: 'desc'
      });
      setChallenges(response.challenges);
      setTotalPages(response.pages);      setTotalChallenges(response.total);
      setCurrentPage(response.page);
    } catch (err) {
      setError((err as Error)?.message || 'Failed to fetch challenges');
      console.error('Error fetching challenges:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchChallenges();
    fetchCategories();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchCategories = async () => {
    try {
      const categoriesData = await CategoriesService.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchChallenges({ page: 1, search: searchTerm });
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      fetchChallenges({ page: newPage });
    }
  };

  const handleToggleChallengeStatus = async (challengeId: string, currentStatus: boolean) => {
    try {
      await AdminService.updateChallengeStatus(challengeId, !currentStatus);
      fetchChallenges();
    } catch (err: any) {      console.error('Error toggling challenge status:', err);
      setError((err as Error)?.message || 'Failed to update challenge status');
    }
  };

  const handleDeleteChallenge = async (challengeId: string) => {
    if (confirm('Are you sure you want to delete this challenge? This action cannot be undone.')) {
      try {
        await AdminService.deleteChallenge(challengeId);
        fetchChallenges();
      } catch (err: any) {        console.error('Error deleting challenge:', err);
        setError((err as Error)?.message || 'Failed to delete challenge');
      }
    }
  };
  const handleCreateChallenge = async (challenge: CreateChallengeRequest | UpdateChallengeRequest) => {
    try {
      await AdminService.createChallenge(challenge as CreateChallengeRequest);
      fetchChallenges();
    } catch (err: any) {
      throw new Error((err as Error)?.message || 'Failed to create challenge');
    }
  };

  const handleEditChallenge = async (challenge: CreateChallengeRequest | UpdateChallengeRequest) => {
    if (!currentChallenge) return;
    
    try {
      await AdminService.updateChallenge(currentChallenge.id, challenge as UpdateChallengeRequest);
      fetchChallenges();
    } catch (err: any) {
      throw new Error((err as Error)?.message || 'Failed to update challenge');
    }
  };
  const openEditModal = (challenge: AdminChallengeListItem) => {
    setCurrentChallenge(challenge);
    setShowEditModal(true);
  };

  const openDetailsView = (challenge: AdminChallengeListItem) => {
    setCurrentChallenge(challenge);
    setShowDetailsView(true);
  };

  const closeDetailsView = () => {
    setShowDetailsView(false);
    setCurrentChallenge(undefined);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const getCategoryColor = (categoryName: string) => {
    const category = categories.find(cat => cat.name === categoryName);
    if (category) {
      // Convert hex color to Tailwind-like classes
      const color = category.color;
      return `text-white`;
    }
    // Fallback for unknown categories
    return 'bg-gray-500/20 text-gray-400';
  };

  const getCategoryStyle = (categoryName: string) => {
    const category = categories.find(cat => cat.name === categoryName);
    if (category) {
      return {
        backgroundColor: `${category.color}20`, // Add 20% opacity
        color: category.color,
        borderColor: `${category.color}40`
      };
    }
    return {
      backgroundColor: '#6B728020',
      color: '#9CA3AF',
      borderColor: '#6B728040'
    };
  };
  
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-emerald-500/20 text-emerald-400';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'hard':
        return 'bg-orange-500/20 text-orange-400';
      case 'insane':
        return 'bg-red-500/20 text-red-400';
      default:
        return 'bg-slate-500/20 text-slate-400';
    }  };

  // Show details view if a challenge is selected for viewing
  if (showDetailsView && currentChallenge) {
    return (
      <ChallengeDetails 
        challengeId={currentChallenge.id} 
        onBack={closeDetailsView} 
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold text-white">Challenge Management</h3>
        <button 
          className="flex items-center space-x-2 px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg transition-colors"
          onClick={() => setShowCreateModal(true)}
        >
          <Plus className="w-4 h-4" />
          <span>Add Challenge</span>
        </button>
      </div>

      <ChallengeFormModal 
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateChallenge}
        title="Create New Challenge"
      />
      
      <ChallengeFormModal 
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSubmit={handleEditChallenge}
        challenge={currentChallenge}
        title="Edit Challenge"
      />
      
      <form onSubmit={handleSearchSubmit} className="relative">
        <Search className="absolute left-3 top-3 w-5 h-5 text-slate-400" />
        <input
          type="text"
          placeholder="Search challenges by title, category, or difficulty..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-3 bg-slate-900/50 border border-slate-800 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400 backdrop-blur-sm"
        />
      </form>
      
      {error && (
        <div className="p-4 bg-red-900/30 border border-red-800 rounded-xl text-red-400">
          {error}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading && Array(6).fill(0).map((_, i) => (
          <div 
            key={`skeleton-${i}`} 
            className="bg-slate-800/50 rounded-xl border border-slate-700 p-6 animate-pulse"
          >
            <div className="flex justify-between items-center mb-4">
              <div className="h-4 bg-slate-700 rounded w-2/3"></div>
              <div className="flex space-x-1">
                <div className="w-6 h-6 rounded-full bg-slate-700"></div>
                <div className="w-6 h-6 rounded-full bg-slate-700"></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <div className="h-3 bg-slate-700 rounded w-1/4"></div>
                <div className="h-3 bg-slate-700 rounded w-1/4"></div>
              </div>
              <div className="flex justify-between">
                <div className="h-3 bg-slate-700 rounded w-1/4"></div>
                <div className="h-3 bg-slate-700 rounded w-1/4"></div>
              </div>
              <div className="flex justify-between">
                <div className="h-3 bg-slate-700 rounded w-1/4"></div>
                <div className="h-3 bg-slate-700 rounded w-1/4"></div>
              </div>
            </div>
          </div>
        ))}
        
        {!loading && challenges.length === 0 && (
          <div className="col-span-3 text-center py-12">
            <p className="text-slate-400 text-lg">No challenges found.</p>
            <p className="text-slate-500 mt-2">Try adjusting your search or create a new challenge.</p>
          </div>
        )}
        
        {!loading && challenges.map((challenge) => (
          <div 
            key={challenge.id} 
            className="bg-slate-800/50 rounded-xl border border-slate-700 p-6 hover:border-slate-600 transition-colors"
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-white truncate">{challenge.title}</h4>
              <div className="flex items-center space-x-1">                <button 
                  onClick={() => handleToggleChallengeStatus(challenge.id, challenge.isActive)}
                  className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-white transition-colors"
                  title={challenge.isActive ? "Deactivate Challenge" : "Activate Challenge"}
                >
                  {challenge.isActive ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
                <button 
                  onClick={() => openDetailsView(challenge)}
                  className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-emerald-400 transition-colors"
                  title="View Challenge Details"
                >
                  <Info className="w-4 h-4" />
                </button>
                <button 
                  onClick={() => openEditModal(challenge)}
                  className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-blue-400 transition-colors"
                  title="Edit Challenge"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button 
                  onClick={() => handleDeleteChallenge(challenge.id)}
                  className="p-1 rounded-full hover:bg-slate-700 text-slate-400 hover:text-red-400 transition-colors"
                  title="Delete Challenge"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Category</span>
                <span
                  className="px-2 py-1 rounded-full border"
                  style={getCategoryStyle(challenge.category)}
                >
                  {categories.find(cat => cat.name === challenge.category)?.displayName || challenge.category}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Difficulty</span>
                <span className={`px-2 py-1 rounded-full ${getDifficultyColor(challenge.difficulty)}`}>
                  {challenge.difficulty}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Points</span>
                <span className="text-emerald-400 font-medium">{challenge.points}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Solves</span>
                <div className="flex items-center space-x-1 text-white">
                  <Flag className="w-3 h-3 text-blue-400" />
                  <span>{challenge.solveCount}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Status</span>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  challenge.isActive 
                    ? 'bg-emerald-500/20 text-emerald-400' 
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {challenge.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Created</span>
                <span className="text-slate-300">{formatDate(challenge.createdAt)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="flex-1 flex justify-between items-center">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`flex items-center px-4 py-2 border rounded-md ${
                currentPage === 1
                  ? 'border-slate-700 bg-slate-800/30 text-slate-500 cursor-not-allowed'
                  : 'border-slate-700 bg-slate-800 text-slate-300 hover:bg-slate-700'
              }`}
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous
            </button>            <div className="text-sm text-slate-400">
              Page {currentPage} of {totalPages} ({totalChallenges} total challenges)
            </div>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`flex items-center px-4 py-2 border rounded-md ${
                currentPage === totalPages
                  ? 'border-slate-700 bg-slate-800/30 text-slate-500 cursor-not-allowed'
                  : 'border-slate-700 bg-slate-800 text-slate-300 hover:bg-slate-700'
              }`}
            >
              Next
              <ChevronRight className="w-4 h-4 ml-2" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
