import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { publicSettingsService, PublicSettings } from '../services/publicSettings';

interface PlatformSettingsContextType {
  settings: PublicSettings | null;
  loading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
  isFeatureEnabled: (feature: keyof PublicSettings) => boolean;
  getSetting: <K extends keyof PublicSettings>(setting: K) => PublicSettings[K] | undefined;
}

const PlatformSettingsContext = createContext<PlatformSettingsContextType | undefined>(undefined);

interface PlatformSettingsProviderProps {
  children: ReactNode;
}

export function PlatformSettingsProvider({ children }: PlatformSettingsProviderProps) {
  const [settings, setSettings] = useState<PublicSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      const platformSettings = await publicSettingsService.getPublicSettings();
      setSettings(platformSettings);
    } catch (err) {
      console.error('Failed to load platform settings:', err);
      setError('Failed to load platform settings');
    } finally {
      setLoading(false);
    }
  };

  const refreshSettings = async () => {
    publicSettingsService.clearCache();
    await loadSettings();
  };

  const isFeatureEnabled = (feature: keyof PublicSettings): boolean => {
    if (!settings) return false;
    return Boolean(settings[feature]);
  };

  const getSetting = <K extends keyof PublicSettings>(setting: K): PublicSettings[K] | undefined => {
    return settings?.[setting];
  };

  useEffect(() => {
    loadSettings();
  }, []);

  const value: PlatformSettingsContextType = {
    settings,
    loading,
    error,
    refreshSettings,
    isFeatureEnabled,
    getSetting,
  };

  return (
    <PlatformSettingsContext.Provider value={value}>
      {children}
    </PlatformSettingsContext.Provider>
  );
}

export function usePlatformSettings() {
  const context = useContext(PlatformSettingsContext);
  if (context === undefined) {
    throw new Error('usePlatformSettings must be used within a PlatformSettingsProvider');
  }
  return context;
}