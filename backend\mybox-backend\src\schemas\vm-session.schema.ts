import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { VMStatus } from './virtual-machine.schema';

@Schema({ timestamps: true })
export class VMSession extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'VirtualMachine', required: true })
  vmId: Types.ObjectId;

  @Prop()
  containerId: string;

  @Prop()
  ipAddress: string;

  @Prop({
    type: String,
    enum: ['running', 'stopped', 'starting', 'stopping'],
    default: 'starting',
  })
  status: VMStatus;

  @Prop({ default: Date.now })
  startedAt: Date;

  @Prop({ required: true })
  expiresAt: Date;

  @Prop({ type: Types.ObjectId, ref: 'Team' })
  teamId?: Types.ObjectId;

  @Prop({ type: Date })
  stoppedAt?: Date;

  @Prop({ type: Object, default: {} })
  portMappings?: Record<string, number>;

  createdAt: Date;
}

export const VMSessionSchema = SchemaFactory.createForClass(VMSession);

// Create indexes
VMSessionSchema.index({ userId: 1, status: 1 });
VMSessionSchema.index({ vmId: 1, status: 1 });
VMSessionSchema.index({ expiresAt: 1 });
