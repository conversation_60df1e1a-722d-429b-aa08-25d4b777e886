# Dashboard Integration Guide

## ✅ **Integration Status**

### **Backend Integration**
- ✅ Dashboard schema created (`dashboard-config.schema.ts`)
- ✅ Dashboard service implemented (`dashboard.service.ts`)
- ✅ Dashboard controller created (`dashboard.controller.ts`)
- ✅ Dashboard module added (`dashboard.module.ts`)
- ✅ Module integrated into main app (`app.module.ts`)

### **Frontend Integration**
- ✅ Dashboard service created (`dashboard.ts`)
- ✅ Enhanced Dashboard component (`Dashboard.tsx`)
- ✅ Event Header component (`EventHeader.tsx`)
- ✅ Social Links component (`SocialLinks.tsx`)
- ✅ Sponsors Section component (`SponsorsSection.tsx`)
- ✅ Admin Dashboard Config (`DashboardConfig.tsx`)
- ✅ Admin panel updated with Dashboard tab

## 🚀 **How to Use**

### **For Admins**
1. **Access Admin Panel**: Navigate to `/admin` (requires admin role)
2. **Dashboard Tab**: Click on the "Dashboard" tab in the admin interface
3. **Configure Settings**:
   - **General**: Set event name, description, dates, cover image
   - **Social Links**: Add social media URLs
   - **Sponsors**: Upload sponsor logos and set tiers
   - **Theme**: Customize colors
   - **Features**: Toggle dashboard sections

### **For Users**
- The main dashboard (`/dashboard`) will automatically reflect admin configurations
- Event branding appears at the top
- Social links appear in the footer
- Sponsors are showcased professionally
- All content is responsive and animated

## 🔧 **API Endpoints**

### **Dashboard Configuration**
- `GET /api/dashboard/config` - Get dashboard configuration
- `PUT /api/dashboard/config` - Update dashboard configuration (admin only)
- `POST /api/dashboard/config/cover-image` - Upload cover image (admin only)

### **Sponsor Management**
- `POST /api/dashboard/sponsors` - Add sponsor (admin only)
- `PUT /api/dashboard/sponsors/:id` - Update sponsor (admin only)
- `DELETE /api/dashboard/sponsors/:id` - Delete sponsor (admin only)
- `PUT /api/dashboard/sponsors/reorder` - Reorder sponsors (admin only)
- `POST /api/dashboard/sponsors/:id/logo` - Upload sponsor logo (admin only)

### **Statistics**
- `GET /api/dashboard/stats` - Get dashboard statistics (admin only)

## 📁 **File Structure**

```
backend/
├── src/
│   ├── dashboard/
│   │   ├── dashboard.controller.ts
│   │   ├── dashboard.service.ts
│   │   ├── dashboard.module.ts
│   │   ��── dto/
│   │       └── dashboard-config.dto.ts
│   └── schemas/
│       └── dashboard-config.schema.ts

frontend/
├── src/
│   ├── components/
│   │   ├── Dashboard/
│   │   │   ├── Dashboard.tsx (updated)
│   │   │   ├── EventHeader.tsx (new)
│   │   │   ├── SocialLinks.tsx (new)
│   │   │   └── SponsorsSection.tsx (new)
│   │   └── Admin/
│   │       ├── Admin.tsx (updated)
│   │       └── DashboardConfig.tsx (new)
│   └── services/
│       └── dashboard.ts (new)
```

## 🎨 **Features Available**

### **Event Branding**
- Custom event name and description
- Cover image upload and display
- Event start/end dates with status
- Personalized welcome messages

### **Social Media Integration**
- GitHub, Facebook, Instagram links
- LinkedIn, Twitter, Discord links
- Website and custom links
- Platform-specific icons and styling

### **Sponsor Management**
- Four-tier system (Platinum, Gold, Silver, Bronze)
- Logo upload and management
- Website links and descriptions
- Custom ordering within tiers
- Responsive display with hover effects

### **Theme Customization**
- Primary, secondary, accent colors
- Background color customization
- Automatic color application

### **Feature Toggles**
- Show/hide statistics cards
- Show/hide recent activity
- Show/hide quick actions
- Show/hide sponsors section
- Show/hide leaderboard
- Show/hide announcements

## 🔍 **Testing the Integration**

### **1. Start the Backend**
```bash
cd backend/mybox-backend
npm run start:dev
```

### **2. Start the Frontend**
```bash
cd frontend
npm run dev
```

### **3. Test Admin Access**
1. Login as admin user
2. Navigate to `/admin`
3. Click on "Dashboard" tab
4. Try configuring different settings

### **4. Test User Experience**
1. Login as regular user
2. Navigate to `/dashboard`
3. Verify that admin configurations are reflected

## 🐛 **Troubleshooting**

### **Common Issues**

1. **Dashboard tab not showing in admin**
   - Verify `DashboardConfig` component is imported in `Admin.tsx`
   - Check that the "Dashboard" tab is added to the tabs array

2. **Dashboard not loading configuration**
   - Verify backend is running and accessible
   - Check browser console for API errors
   - Ensure MongoDB is connected

3. **File uploads not working**
   - Check file size limits (10MB for cover, 5MB for logos)
   - Verify file types (images only)
   - Ensure proper permissions

4. **Styling issues**
   - Verify all CSS animations are loaded
   - Check that Tailwind CSS is properly configured
   - Ensure Framer Motion is installed

### **Required Dependencies**

**Backend:**
- `@nestjs/mongoose`
- `multer`
- `uuid`

**Frontend:**
- `framer-motion`
- `lucide-react`

## 🎯 **Next Steps**

1. **Test all functionality** in both admin and user interfaces
2. **Upload sample content** (cover image, sponsor logos)
3. **Configure social links** for your organization
4. **Add sponsors** with proper tier assignments
5. **Customize theme colors** to match your branding

## 📞 **Support**

If you encounter any issues:
1. Check the browser console for errors
2. Verify backend logs for API issues
3. Ensure all dependencies are installed
4. Check file permissions for uploads

The dashboard personalization system is now fully integrated and ready for use!