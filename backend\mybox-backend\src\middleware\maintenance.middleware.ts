import { Injectable, NestMiddleware, ServiceUnavailableException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { AdminSettings } from '../schemas/admin-settings.schema';
import { User } from '../schemas/user.schema';

@Injectable()
export class MaintenanceMiddleware implements NestMiddleware {
  constructor(
    @InjectModel(AdminSettings.name) private adminSettingsModel: Model<AdminSettings>,
    @InjectModel(User.name) private userModel: Model<User>,
    private jwtService: JwtService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      // Debug: Log the request path to understand the routing
      console.log('Maintenance middleware - Request path:', req.path, 'URL:', req.url, 'Method:', req.method);

      // Check if maintenance mode is enabled
      const settings = await this.adminSettingsModel.findOne({ configId: 'default' });

      if (settings && settings.maintenanceMode) {
        // For authenticated requests, check if user is admin and allow them to bypass maintenance mode
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
          const isAdmin = await this.isUserAdmin(req);

          if (isAdmin) {
            console.log('Admin user bypassing maintenance mode');
            return next();
          }
        }

        // Return maintenance mode response for non-admin users or unauthenticated requests
        console.log('Blocking request due to maintenance mode');
        return res.status(503).json({
          error: 'Service Unavailable',
          message: 'The platform is currently under maintenance. Please try again later.',
          maintenanceMode: true,
          retryAfter: '1800' // 30 minutes
        });
      }

      next();
    } catch (error) {
      console.error('Maintenance middleware error:', error);
      // If we can't check maintenance mode, allow the request to proceed
      next();
    }
  }

  private async isUserAdmin(req: Request): Promise<boolean> {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return false;
      }

      const token = authHeader.substring(7);
      const decoded = this.jwtService.verify(token);

      if (!decoded || !decoded.sub) {
        return false;
      }

      const user = await this.userModel.findById(decoded.sub);
      return user ? user.role === 'admin' : false;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }
}