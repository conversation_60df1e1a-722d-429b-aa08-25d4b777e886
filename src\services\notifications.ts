import { api } from './api';

export interface Notification {
  _id: string;
  title: string;
  message: string;
  type: 'admin_message' | 'first_blood' | 'challenge_solved' | 'new_challenge' | 'new_machine' | 'machine_first_blood' | 'system_announcement' | 'team_invitation' | 'competition_update';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  sender?: {
    _id: string;
    username: string;
    email: string;
  };
  recipients: string[];
  readBy: string[];
  isGlobal: boolean;
  metadata: {
    challengeId?: string;
    challengeName?: string;
    challengeCategory?: string;
    challengeDifficulty?: string;
    challengePoints?: number;
    machineId?: string;
    machineName?: string;
    machineOs?: string;
    machineDifficulty?: string;
    machinePoints?: number;
    username?: string;
    teamName?: string;
    points?: number;
    icon?: string;
    color?: string;
    actionUrl?: string;
    [key: string]: any;
  };
  expiresAt: string;
  isActive: boolean;
  isRead?: boolean;
  createdAt: string;
  updatedAt: string;
}

// Keep the old interface name for backward compatibility
export interface Notifications extends Notification {}

export interface NotificationQuery {
  type?: string;
  priority?: string;
  unreadOnly?: boolean;
  page?: number;
  limit?: number;
}

export interface NotificationsResponse {
  notifications: Notification[];
  total: number;
  unreadCount: number;
}

export interface CreateNotificationDto {
  title: string;
  message: string;
  type: string;
  priority?: string;
  recipients?: string[];
  isGlobal?: boolean;
  metadata?: any;
  expiresAt?: string;
}

class NotificationsService {
  async getUserNotifications(query: NotificationQuery = {}): Promise<NotificationsResponse> {
    const params = new URLSearchParams();
    
    if (query.type) params.append('type', query.type);
    if (query.priority) params.append('priority', query.priority);
    if (query.unreadOnly) params.append('unreadOnly', 'true');
    if (query.page) params.append('page', query.page.toString());
    if (query.limit) params.append('limit', query.limit.toString());

    const response = await api.get(`/notifications?${params.toString()}`);
    return response.data;
  }

  async getUnreadCount(): Promise<{ count: number }> {
    const response = await api.get('/notifications/unread-count');
    return response.data;
  }

  async getNotification(id: string): Promise<Notification> {
    const response = await api.get(`/notifications/${id}`);
    return response.data;
  }

  async markAsRead(notificationIds: string[]): Promise<{ message: string; unreadCount: number }> {
    const response = await api.post('/notifications/mark-as-read', { notificationIds });
    return response.data;
  }

  async markAllAsRead(): Promise<{ message: string; unreadCount: number }> {
    const response = await api.post('/notifications/mark-all-as-read');
    return response.data;
  }

  // Admin endpoints
  async sendAdminNotification(notification: CreateNotificationDto): Promise<{ message: string; notification: Notification }> {
    const response = await api.post('/notifications/admin/send', notification);
    return response.data;
  }

  async createSystemAnnouncement(notification: CreateNotificationDto): Promise<{ message: string; notification: Notification }> {
    const response = await api.post('/notifications/admin/announcement', notification);
    return response.data;
  }

  async getAdminNotifications(query: NotificationQuery = {}): Promise<{ notifications: Notification[]; total: number }> {
    const params = new URLSearchParams();
    
    if (query.type) params.append('type', query.type);
    if (query.priority) params.append('priority', query.priority);
    if (query.page) params.append('page', query.page.toString());
    if (query.limit) params.append('limit', query.limit.toString());

    const response = await api.get(`/notifications/admin/all?${params.toString()}`);
    return response.data;
  }

  async updateNotification(id: string, updates: Partial<CreateNotificationDto>): Promise<{ message: string; notification: Notification }> {
    const response = await api.put(`/notifications/admin/${id}`, updates);
    return response.data;
  }

  async deleteNotification(id: string): Promise<{ message: string }> {
    const response = await api.delete(`/notifications/admin/${id}`);
    return response.data;
  }

  async cleanupExpiredNotifications(): Promise<{ message: string }> {
    const response = await api.post('/notifications/admin/cleanup');
    return response.data;
  }

  async getNotificationStats(): Promise<{ connectedUsers: number; connectedUsersList: string[] }> {
    const response = await api.get('/notifications/admin/stats');
    return response.data;
  }
}

export const notificationsService = new NotificationsService();