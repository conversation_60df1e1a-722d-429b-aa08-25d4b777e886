import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronDown,
  ChevronRight,
  Flag,
  Crown,
  User,
  Globe,
  CheckCircle,
  Clock,
  Award,
  BookOpen,
  Image as ImageIcon
} from 'lucide-react';
import { MachineTemplate, MachineFlag } from '../../services/machines';
import { getImageUrl } from '../../utils/imageUtils';

interface MachineTopicsDisplayProps {
  machine: MachineTemplate;
  userFlags?: MachineFlag[];
  onFlagSubmit?: (flagName: string, flag: string) => void;
}

export function MachineTopicsDisplay({ machine, userFlags = [], onFlagSubmit }: MachineTopicsDisplayProps) {
  const [expandedTopics, setExpandedTopics] = useState<Set<number>>(new Set());
  const [flagSubmissions, setFlagSubmissions] = useState<Record<string, string>>({});

  const flagTypeIcons = {
    root: Crown,
    user: User,
    'www-data': Globe,
    custom: Flag
  };

  const flagTypeColors = {
    root: 'text-red-400 bg-red-500/20 border-red-500/30',
    user: 'text-blue-400 bg-blue-500/20 border-blue-500/30',
    'www-data': 'text-green-400 bg-green-500/20 border-green-500/30',
    custom: 'text-purple-400 bg-purple-500/20 border-purple-500/30'
  };

  const toggleTopic = (index: number) => {
    const newExpanded = new Set(expandedTopics);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedTopics(newExpanded);
  };

  const handleFlagSubmit = (flagName: string) => {
    const flag = flagSubmissions[flagName];
    if (flag && flag.trim() && onFlagSubmit) {
      onFlagSubmit(flagName, flag.trim());
      setFlagSubmissions({ ...flagSubmissions, [flagName]: '' });
    }
  };

  const getFlagStatus = (flagName: string) => {
    return userFlags.find(f => f.name === flagName);
  };

  const sortedTopics = [...(machine.topics || [])].sort((a, b) => a.order - b.order);
  const sortedFlags = [...(machine.flags || [])].sort((a, b) => {
    // Sort by type priority (root > user > www-data > custom) then by points
    const typePriority = { root: 4, user: 3, 'www-data': 2, custom: 1 };
    const aPriority = typePriority[a.type] || 0;
    const bPriority = typePriority[b.type] || 0;
    
    if (aPriority !== bPriority) {
      return bPriority - aPriority;
    }
    return b.points - a.points;
  });

  return (
    <div className="space-y-6">
      {/* Topics Section */}
      {sortedTopics.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-2 mb-4">
            <BookOpen className="w-5 h-5 text-purple-400" />
            <h3 className="text-xl font-semibold text-white">Topics & Hints</h3>
          </div>
          
          <div className="space-y-3">
            {sortedTopics.map((topic, index) => (
              <motion.div
                key={index}
                className="border border-gray-700 rounded-lg overflow-hidden"
                layout
              >
                <button
                  onClick={() => toggleTopic(index)}
                  className="w-full p-4 text-left hover:bg-gray-700/50 transition-colors flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    {topic.imageUrl && (
                      <img
                        src={getImageUrl(topic.imageUrl)}
                        alt={topic.title}
                        className="w-10 h-10 object-cover rounded-lg"
                      />
                    )}
                    <div>
                      <h4 className="font-medium text-white">{topic.title}</h4>
                      <p className="text-sm text-gray-400 mt-1 line-clamp-1">
                        {topic.description}
                      </p>
                    </div>
                  </div>
                  {expandedTopics.has(index) ? (
                    <ChevronDown className="w-5 h-5 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  )}
                </button>
                
                <AnimatePresence>
                  {expandedTopics.has(index) && (
                    <motion.div
                      className="border-t border-gray-700 p-4 bg-gray-700/30"
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      {topic.imageUrl && (
                        <div className="mb-4">
                          <img
                            src={getImageUrl(topic.imageUrl)}
                            alt={topic.title}
                            className="max-w-full h-auto rounded-lg border border-gray-600"
                          />
                        </div>
                      )}
                      <p className="text-gray-300 whitespace-pre-wrap">
                        {topic.description}
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Flags Section */}
      {sortedFlags.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Flag className="w-5 h-5 text-purple-400" />
            <h3 className="text-xl font-semibold text-white">Flags</h3>
          </div>
          
          <div className="grid gap-4">
            {sortedFlags.map((flag, index) => {
              const IconComponent = flagTypeIcons[flag.type];
              const flagStatus = getFlagStatus(flag.name);
              const isSolved = !!flagStatus?.isSolved;
              
              return (
                <motion.div
                  key={index}
                  className={`border rounded-lg p-4 ${
                    isSolved 
                      ? 'border-green-500/30 bg-green-500/10' 
                      : 'border-gray-700 bg-gray-700/30'
                  }`}
                  layout
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg border ${flagTypeColors[flag.type]}`}>
                        <IconComponent className="w-5 h-5" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-white">{flag.name}</h4>
                          {flag.isRequired && (
                            <span className="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full">
                              Required
                            </span>
                          )}
                          {isSolved && (
                            <CheckCircle className="w-4 h-4 text-green-400" />
                          )}
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <div className="flex items-center space-x-1">
                            <Award className="w-4 h-4 text-purple-400" />
                            <span className="text-purple-400 font-medium">{flag.points} points</span>
                          </div>
                          <span className="text-gray-400 text-sm capitalize">{flag.type} flag</span>
                          {isSolved && flagStatus?.solvedAt && (
                            <div className="flex items-center space-x-1">
                              <Clock className="w-4 h-4 text-green-400" />
                              <span className="text-green-400 text-sm">
                                Solved {new Date(flagStatus.solvedAt).toLocaleDateString()}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {flag.description && (
                    <p className="text-gray-300 text-sm mb-3">{flag.description}</p>
                  )}
                  
                  {!isSolved && onFlagSubmit && (
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        placeholder="Enter flag..."
                        value={flagSubmissions[flag.name] || ''}
                        onChange={(e) => setFlagSubmissions({
                          ...flagSubmissions,
                          [flag.name]: e.target.value
                        })}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            handleFlagSubmit(flag.name);
                          }
                        }}
                        className="flex-1 p-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
                      />
                      <button
                        onClick={() => handleFlagSubmit(flag.name)}
                        disabled={!flagSubmissions[flag.name]?.trim()}
                        className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                      >
                        Submit
                      </button>
                    </div>
                  )}
                  
                  {isSolved && (
                    <div className="flex items-center space-x-2 text-green-400">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">Flag captured!</span>
                      <span className="text-sm">+{flag.points} points</span>
                    </div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </div>
      )}

      {/* Empty State */}
      {sortedTopics.length === 0 && sortedFlags.length === 0 && (
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <BookOpen className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">No Topics or Flags</h3>
          <p className="text-gray-500">
            This machine doesn't have any topics or flags configured yet.
          </p>
        </div>
      )}
    </div>
  );
}
