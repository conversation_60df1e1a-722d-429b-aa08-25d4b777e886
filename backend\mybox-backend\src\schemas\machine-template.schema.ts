import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type MachineTemplateDocument = MachineTemplate & Document;

@Schema({ timestamps: true })
export class MachineTemplate {
  @Prop({ required: true, unique: true })
  name: string;

  @Prop({ required: true, unique: true })
  slug: string;

  @Prop({ required: true })
  description: string;

  @Prop({ 
    required: true, 
    enum: ['web', 'crypto', 'pwn', 'reverse', 'forensics', 'misc', 'osint'] 
  })
  category: string;

  @Prop({ 
    required: true, 
    enum: ['easy', 'medium', 'hard', 'insane'] 
  })
  difficulty: string;

  @Prop({ 
    required: true, 
    enum: ['linux', 'windows', 'other'] 
  })
  os: string;

  @Prop({ 
    required: true, 
    enum: ['docker', 'ova', 'qcow2'],
    default: 'docker'
  })
  machineType: string;

  @Prop({ required: false })
  dockerImage?: string;

  @Prop({ required: false })
  dockerFile?: string;

  @Prop({ required: false })
  ovaFile?: string;

  @Prop({ type: [Number], default: [] })
  exposedPorts: number[];

  @Prop({ default: 1024 })
  requiredRAM: number; // MB

  @Prop({ default: 1 })
  requiredCPU: number; // vCPU cores

  @Prop({ default: 10 })
  maxInstances: number; // Max concurrent instances

  @Prop({ type: [{
    name: String,
    value: String,
    type: { type: String, enum: ['root', 'user', 'www-data', 'custom'], default: 'custom' },
    points: Number,
    description: String,
    isRequired: { type: Boolean, default: false }
  }], default: [] })
  flags: {
    name: string;
    value: string;
    type: 'root' | 'user' | 'www-data' | 'custom';
    points: number;
    description?: string;
    isRequired: boolean;
  }[];

  @Prop({ type: [{
    title: String,
    description: String,
    imageUrl: String,
    order: { type: Number, default: 0 }
  }], default: [] })
  topics: {
    title: string;
    description: string;
    imageUrl?: string;
    order: number;
  }[];

  @Prop({ type: [String], default: [] })
  hints: string[];

  @Prop({ required: false })
  walkthrough?: string;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: false })
  requiresVPN: boolean;

  @Prop({ required: true })
  releaseDate: Date;

  @Prop({ required: false })
  retireDate?: Date;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  authorId: Types.ObjectId;

  @Prop({ required: true })
  authorName: string;

  @Prop({ required: false })
  machineIP?: string;

  @Prop({ default: 0 })
  downloadCount: number;

  @Prop({ default: 0 })
  solveCount: number;

  @Prop({ default: 0 })
  rating: number;

  @Prop({ default: 60 })
  maxUptimeMinutes: number; // Auto-shutdown after X minutes

  @Prop({ type: [String], default: [] })
  tags: string[];
}

export const MachineTemplateSchema = SchemaFactory.createForClass(MachineTemplate);
