import { api } from './api';

export interface PublicSettings {
  allowRegistration: boolean;
  emailVerification: boolean;
  maintenanceMode: boolean;
  enableTeams: boolean;
  maxTeamSize: number;
  maxConcurrentVMs: number;
  defaultSessionTime: number;
  minPasswordLength: number;
  requirePasswordComplexity: boolean;
}

export interface MaintenanceStatus {
  maintenanceMode: boolean;
}

export interface TeamsStatus {
  teamsEnabled: boolean;
  maxTeamSize: number;
}

class PublicSettingsService {
  private cachedSettings: PublicSettings | null = null;
  private lastFetch: number = 0;
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  async getPublicSettings(): Promise<PublicSettings> {
    const now = Date.now();
    
    // Return cached settings if they're still fresh
    if (this.cachedSettings && (now - this.lastFetch) < this.cacheTimeout) {
      return this.cachedSettings;
    }

    try {
      const response = await api.get<PublicSettings>('/public-settings');
      this.cachedSettings = response.data;
      this.lastFetch = now;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch public settings:', error);
      
      // Return cached settings if available, otherwise defaults
      if (this.cachedSettings) {
        return this.cachedSettings;
      }
      
      // Return default settings as fallback
      return {
        allowRegistration: true,
        emailVerification: false,
        maintenanceMode: false,
        enableTeams: true,
        maxTeamSize: 5,
        maxConcurrentVMs: 1,
        defaultSessionTime: 2,
        minPasswordLength: 8,
        requirePasswordComplexity: true,
      };
    }
  }

  async getMaintenanceStatus(): Promise<MaintenanceStatus> {
    try {
      const response = await api.get<MaintenanceStatus>('/public-settings/maintenance');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch maintenance status:', error);
      return { maintenanceMode: false };
    }
  }

  async getTeamsStatus(): Promise<TeamsStatus> {
    try {
      const response = await api.get<TeamsStatus>('/public-settings/teams');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch teams status:', error);
      return { teamsEnabled: true, maxTeamSize: 5 };
    }
  }

  // Clear cache to force refresh
  clearCache(): void {
    this.cachedSettings = null;
    this.lastFetch = 0;
  }

  // Check if a specific feature is enabled
  async isFeatureEnabled(feature: keyof PublicSettings): Promise<boolean> {
    const settings = await this.getPublicSettings();
    return Boolean(settings[feature]);
  }

  // Get setting value
  async getSetting<K extends keyof PublicSettings>(setting: K): Promise<PublicSettings[K]> {
    const settings = await this.getPublicSettings();
    return settings[setting];
  }
}

export const publicSettingsService = new PublicSettingsService();