import { 
  Controller, 
  Post, 
  Body, 
  UseGuards, 
  Request,
  ValidationPipe,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LocalAuthGuard } from './local-auth.guard';
import { JwtAuthGuard } from './jwt-auth.guard';
import { LoginDto, RegisterDto, AuthResponseDto, VerifyEmailDto, ResendVerificationDto } from './dto/auth.dto';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('register')
  async register(
    @Body(ValidationPipe) registerDto: RegisterDto
  ): Promise<AuthResponseDto> {
    return this.authService.register(registerDto);
  }
  @UseGuards(LocalAuthGuard)
  @Post('login')
  async login(@Request() req, @Body() loginDto: LoginDto): Promise<AuthResponseDto> {
    return this.authService.login(loginDto);
  }

  @Post('verify-email')
  async verifyEmail(
    @Body(ValidationPipe) verifyEmailDto: VerifyEmailDto
  ): Promise<AuthResponseDto> {
    return this.authService.verifyEmail(verifyEmailDto);
  }

  @Post('resend-verification')
  async resendVerification(
    @Body(ValidationPipe) resendDto: ResendVerificationDto
  ): Promise<{ message: string }> {
    return this.authService.resendVerificationCode(resendDto);
  }

  @UseGuards(JwtAuthGuard)
  @Post('refresh')
  async refresh(@Request() req): Promise<AuthResponseDto> {
    return this.authService.refreshToken(req.user);
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  async logout(): Promise<{ message: string }> {
    // In a stateless JWT system, logout is typically handled client-side
    // by removing the token. For server-side logout, you'd need to implement
    // a blacklist or token invalidation mechanism.
    return { message: 'Logged out successfully' };
  }

  // Debug endpoint to test password validation
  @Post('debug-login')
  async debugLogin(@Body() body: { email: string; password: string }) {
    console.log('🔧 Debug login endpoint called for email:', body.email);
    try {
      const user = await this.authService.validateUser(body.email, body.password);
      return {
        success: !!user,
        message: user ? 'User validation successful' : 'User validation failed',
        userFound: !!user,
      };
    } catch (error) {
      console.error('Debug login error:', error);
      return {
        success: false,
        message: 'Error during validation',
        error: error.message,
      };
    }
  }

  // Debug endpoint to check database connection and users
  @Post('debug-users')
  async debugUsers() {
    console.log('🔧 Debug users endpoint called');
    try {
      const users = await this.authService.debugGetUsers();
      return {
        success: true,
        userCount: users.length,
        users: users.map(u => ({
          id: u._id,
          email: u.email,
          username: u.username,
          isActive: u.isActive,
          isEmailVerified: u.isEmailVerified
        })),
      };
    } catch (error) {
      console.error('Debug users error:', error);
      return {
        success: false,
        message: 'Error fetching users',
        error: error.message,
      };
    }
  }
}
