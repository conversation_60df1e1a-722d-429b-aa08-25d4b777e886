import * as mongoose from 'mongoose';
import * as bcrypt from 'bcryptjs';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function createAdmin() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/mybox');
    console.log('Connected to MongoDB successfully');    // Define user schema
    const userSchema = new mongoose.Schema({
      username: { type: String, required: true, unique: true },
      email: { type: String, required: true, unique: true },
      passwordHash: { type: String, required: true },
      role: { type: String, enum: ['user', 'admin', 'moderator'], default: 'user' },
      score: { type: Number, default: 0 },
      rank: { type: Number, default: 0 },
      teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'Team', default: null },
      avatarUrl: { type: String },
      apiToken: { type: String },
      isActive: { type: Boolean, default: true },
      isEmailVerified: { type: Boolean, default: false },
      emailVerificationCode: { type: String },
      emailVerificationExpires: { type: Date },
      lastActive: { type: Date, default: Date.now },
      country: { type: String },
      bio: { type: String },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });
    
    // Create model
    const UserModel = mongoose.model('User', userSchema);
    
    // Check if admin exists
    const adminExists = await UserModel.findOne({ role: 'admin' });
    
    if (adminExists) {
      console.log('Admin user already exists. Updating password...');
      
      // Hash the password
      const saltRounds = 12;
      const adminPassword = process.env.ADMIN_PASSWORD || 'Admin@123';
      const passwordHash = await bcrypt.hash(adminPassword, saltRounds);
      
      // Update admin password
      await UserModel.updateOne(
        { role: 'admin' }, 
        { $set: { passwordHash } }
      );
      
      console.log('Admin password updated successfully');
      console.log('Email:', adminExists.email);
      console.log('Password:', process.env.ADMIN_PASSWORD || 'Admin@123');
    } else {
      console.log('Creating admin user...');
      
      // Hash the password
      const saltRounds = 12;
      const adminUsername = process.env.ADMIN_USERNAME || 'admin';
      const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
      const adminPassword = process.env.ADMIN_PASSWORD || 'Admin@123';
      const passwordHash = await bcrypt.hash(adminPassword, saltRounds);
      
      // Create admin user
      const admin = new UserModel({
        username: adminUsername,
        email: adminEmail,
        passwordHash,
        role: 'admin',
        score: 0,
        rank: 0,
        isActive: true,
        isEmailVerified: true, // Admin email is verified by default
        lastActive: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      await admin.save();
      console.log('Admin user created successfully');
      console.log('Username:', adminUsername);
      console.log('Email:', adminEmail, '(auto-verified)');
      console.log('Password:', adminPassword);
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

createAdmin();
