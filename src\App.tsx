import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AppProvider, useApp } from './contexts/AppContext';
import { PlatformSettingsProvider } from './contexts/PlatformSettingsContext';
import { NotificationsProvider } from './contexts/NotificationsContext';
import { Layout } from './components/Layout/Layout';
import { AuthForm } from './components/Auth/AuthForm';
import { Dashboard } from './components/Dashboard/Dashboard';
import { Challenges } from './components/Challenges/Challenges';
import { Machines } from './components/Machines/Machines';
import { Teams } from './components/Teams/Teams';
import { Leaderboard } from './components/Leaderboard/Leaderboard';
import { Statistics } from './components/Statistics/Statistics';
import { VPN } from './components/VPN/VPN';
import { Profile } from './components/Profile/Profile';
import { Admin } from './components/Admin/Admin';
import { Notifications } from './components/Notifications/Notifications';
import { FirstBloodAnimation } from './components/Notifications/FirstBloodAnimation';
import { NotificationToastContainer } from './components/Notifications/NotificationToastContainer';
import { LoadingScreen } from './components/Loading/LoadingScreen';
import { useLoadingScreen } from './hooks/useLoadingScreen';
import { ProtectedRoute } from './components/ProtectedRoute';
import { MaintenanceGuard, TeamsGuard, RegistrationGuard } from './components/Guards/FeatureGuard';

function AppContent() {
  const { state } = useApp();
  const { showLoading, onLoadingComplete } = useLoadingScreen(state.auth.isLoading, {
    minLoadingTime: 8000, // Show loading for at least 8 seconds for immersive experience
    initialDelay: 0
  });

  if (showLoading) {
    return <LoadingScreen isLoading={true} onComplete={onLoadingComplete} />;
  }

  if (!state.auth.isAuthenticated) {
    return (
      <RegistrationGuard>
        <AuthForm />
      </RegistrationGuard>
    );
  }

  return (
    <MaintenanceGuard>
      <NotificationsProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/challenges" element={<Challenges />} />
              <Route path="/machines" element={<Machines />} />
              <Route 
                path="/teams" 
                element={
                  <TeamsGuard>
                    <Teams />
                  </TeamsGuard>
                } 
              />
              <Route path="/leaderboard" element={<Leaderboard />} />
              <Route path="/statistics" element={<Statistics />} />
              <Route path="/notifications" element={<Notifications />} />
              <Route path="/vpn" element={<VPN />} />
              <Route path="/profile" element={<Profile />} />
              <Route 
                path="/admin" 
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Admin />
                  </ProtectedRoute>
                } 
              />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
            
            {/* First Blood Animation Overlay */}
            <FirstBloodAnimation />
            
            {/* Toast Notifications - appears on all pages */}
            <NotificationToastContainer />
          </Layout>
        </Router>
      </NotificationsProvider>
    </MaintenanceGuard>
  );
}

function App() {
  return (
    <AppProvider>
      <PlatformSettingsProvider>
        <AppContent />
      </PlatformSettingsProvider>
    </AppProvider>
  );
}

export default App;