import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { NotificationsModule } from '../notifications/notifications.module';

// Controllers
import { VirtualMachinesController } from './controllers/virtual-machines.controller';
import { MachineAdminController } from './controllers/machine-admin.controller';

// Services
import { DockerMachineService } from './services/docker-machine.service';
import { MachineInstanceService } from './services/machine-instance.service';
import { FlagSubmissionService } from './services/flag-submission.service';
import { MachineMaintenanceService } from './services/machine-maintenance.service';
import { MachineTemplateService } from './services/machine-template.service';
import { ArchiveExtractorService } from './services/archive-extractor.service';

// Schemas
import { MachineTemplate, MachineTemplateSchema } from '../schemas/machine-template.schema';
import { MachineInstance, MachineInstanceSchema } from '../schemas/machine-instance.schema';
import { MachineOperation, MachineOperationSchema } from '../schemas/machine-operation.schema';
import { MachineSubmission, MachineSubmissionSchema } from '../schemas/machine-submission.schema';
import { MachineFile, MachineFileSchema } from '../schemas/machine-file.schema';
import { User, UserSchema } from '../schemas/user.schema';
import { Team, TeamSchema } from '../schemas/team.schema';

@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(),
    NotificationsModule,
    MongooseModule.forFeature([
      { name: MachineTemplate.name, schema: MachineTemplateSchema },
      { name: MachineInstance.name, schema: MachineInstanceSchema },
      { name: MachineOperation.name, schema: MachineOperationSchema },
      { name: MachineSubmission.name, schema: MachineSubmissionSchema },
      { name: MachineFile.name, schema: MachineFileSchema },
      { name: User.name, schema: UserSchema },
      { name: Team.name, schema: TeamSchema },
    ]),
  ],
  controllers: [VirtualMachinesController, MachineAdminController],
  providers: [
    DockerMachineService,
    MachineInstanceService,
    FlagSubmissionService,
    MachineMaintenanceService,
    MachineTemplateService,
    ArchiveExtractorService,
  ],
  exports: [
    DockerMachineService,
    MachineInstanceService,
    FlagSubmissionService,
    MachineMaintenanceService,
    MachineTemplateService,
  ],
})
export class VirtualMachinesModule {}