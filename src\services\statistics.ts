import { api } from './api';

export interface GlobalStatistics {
  overview: {
    totalChallenges: number;
    activeChallenges: number;
    totalUsers: number;
    activeUsers: number;
    totalTeams: number;
    activeTeams: number;
    totalSubmissions: number;
    correctSubmissions: number;
    totalSolves: number;
    totalPoints: number;
    successRate: number;
  };
  categoryStats: Array<{
    _id: string;
    count: number;
    totalPoints: number;
    avgPoints: number;
  }>;
  difficultyStats: Array<{
    _id: string;
    count: number;
    totalPoints: number;
    avgPoints: number;
  }>;
  recentActivity: Array<{
    _id: string;
    submissions: number;
    correctSubmissions: number;
  }>;
}

export interface ChallengeStatistics {
  allChallenges: Array<{
    _id: string;
    title: string;
    category: string;
    difficulty: string;
    points: number;
    totalSubmissions: number;
    correctSubmissions: number;
    uniqueSolvers: number;
    successRate: number;
    firstBlood?: any;
    flagsFirstBlood?: any;
    solveCount: number;
    createdAt: string;
  }>;
  popularChallenges: Array<any>;
  difficultChallenges: Array<any>;
  unsolvedChallenges: Array<any>;
}

export interface FirstBloodStatistics {
  challengesWithFirstBlood: Array<{
    _id: string;
    title: string;
    category: string;
    difficulty: string;
    points: number;
    firstBlood?: {
      userId: string;
      username: string;
      timestamp: string;
      teamId?: string;
      teamName?: string;
    };
    flagsFirstBlood?: any;
    firstBloodUserInfo?: {
      _id: string;
      username: string;
      avatar?: string;
    };
    firstBloodTeamInfo?: {
      _id: string;
      name: string;
    };
    solveCount: number;
    createdAt: string;
  }>;
  firstBloodLeaderboard: Array<{
    _id: string;
    username: string;
    teamId?: string;
    teamName?: string;
    firstBloodCount: number;
    totalPoints: number;
    challenges: Array<{
      challengeId: string;
      title: string;
      category: string;
      difficulty: string;
      points: number;
      timestamp: string;
    }>;
  }>;
  teamFirstBloodLeaderboard: Array<{
    _id: string;
    teamName: string;
    firstBloodCount: number;
    totalPoints: number;
    challenges: Array<{
      challengeId: string;
      title: string;
      category: string;
      difficulty: string;
      points: number;
      timestamp: string;
      solvedBy: string;
    }>;
  }>;
  recentFirstBloods: Array<any>;
}

export interface UserActivityStatistics {
  userStats: Array<{
    _id: string;
    username: string;
    email: string;
    avatar?: string;
    score: number;
    totalSubmissions: number;
    correctSubmissions: number;
    firstBloods: number;
    lastSubmission?: string;
    createdAt: string;
    lastLoginAt?: string;
  }>;
  mostActiveUsers: Array<any>;
  highestSuccessRateUsers: Array<any>;
  registrationTrends: Array<{
    _id: string;
    registrations: number;
  }>;
}

export interface TeamActivityStatistics {
  teamStats: Array<{
    _id: string;
    name: string;
    teamScore: number;
    memberCount: number;
    solveCount: number;
    firstBloods: number;
    lastSolve?: string;
    captainInfo: {
      _id: string;
      username: string;
    };
    createdAt: string;
  }>;
  mostActiveTeams: Array<any>;
  teamFormationTrends: Array<{
    _id: string;
    teamsFormed: number;
  }>;
  teamSizeDistribution: Array<{
    _id: number;
    teamCount: number;
  }>;
}

export const statisticsService = {
  async getGlobalStatistics(): Promise<GlobalStatistics> {
    const response = await api.get('/statistics');
    return response.data;
  },

  async getChallengeStatistics(): Promise<ChallengeStatistics> {
    const response = await api.get('/statistics/challenges');
    return response.data;
  },

  async getFirstBloodStatistics(): Promise<FirstBloodStatistics> {
    const response = await api.get('/statistics/first-bloods');
    return response.data;
  },

  async getUserActivityStatistics(): Promise<UserActivityStatistics> {
    const response = await api.get('/statistics/user-activity');
    return response.data;
  },

  async getTeamActivityStatistics(): Promise<TeamActivityStatistics> {
    const response = await api.get('/statistics/team-activity');
    return response.data;
  },
};