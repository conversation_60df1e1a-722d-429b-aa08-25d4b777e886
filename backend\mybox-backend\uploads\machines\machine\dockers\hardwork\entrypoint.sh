#!/bin/bash

# Ensure the FTP directory has correct permissions
chmod 755 /var/public/ftp

# Start cron
cron

# SMB config
useradd -M -s /usr/sbin/nologin developer
(echo "hardwork112"; echo "hardwork112") | smbpasswd -s -a developer

useradd -M -s /usr/sbin/nologin admin
echo -e "admin\nadmin" | smbpasswd -s -a admin

# Create shared folders
mkdir -p /srv/samba/guest /srv/samba/dev /srv/samba/admin
chmod -R 777 /srv/samba/guest
chown -R developer /srv/samba/dev
chown -R admin /srv/samba/admin

# Apache config
a2enmod proxy proxy_http rewrite headers
a2ensite hardwork.kybs.conf
a2ensite networkManagementlocalAdministrationP4nel.hardwork.kybs.conf
a2ensite uploadsSubdomain.hardwork.kybs
echo "ServerName hardwork.kybs" >> /etc/apache2/apache2.conf
apachectl -DFOREGROUND &

# FTP
echo "[*] Starting FTP server..."
/usr/sbin/vsftpd /etc/vsftpd.conf &

# Samba
echo "[*] Starting Samba (SMB) server..."
smbd -F --no-process-group &

# Node.js app (network-app)
echo "[*] Starting network-app (Node.js)..."
cd /opt/network-app
npm run dev &

# Next.js app
echo "[*] Starting nextjs-app..."
cd /opt/nextjs-app
npm run dev & 

# Flask app
echo "[*] Starting Flask app as developer..."
su - developer -c 'cd /home/<USER>/app && FLASK_APP=app.py nohup flask run --host=127.0.0.1 --port=5000 > /home/<USER>/flask.log 2>&1 &'

# Keep container alive
wait
