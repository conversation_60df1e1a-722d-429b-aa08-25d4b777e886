# Phase 3 VPN Integration Testing Guide
## WireGuard VPN System for Secure Machine Access

This guide will help you test the new VPN integration system that provides secure, isolated access to deployed machines.

## 🎯 **What's New in Phase 3**

### ✅ **Backend VPN Infrastructure**
- **VPN Configuration Service**: WireGuard key generation and management
- **Network Isolation Service**: Per-user Docker networks with subnet isolation
- **Machine-VPN Integration**: Automatic VPN setup when deploying machines
- **Connection Logging**: Comprehensive VPN connection tracking
- **Admin Management**: Full VPN administration interface

### ✅ **Database Schemas**
- **VPNConfig**: User VPN configurations with encrypted keys
- **MachineNetwork**: Machine network mappings and isolation
- **VPNConnectionLog**: Connection history and statistics

### ✅ **API Endpoints**
- **User VPN APIs**: `/api/vpn/*` - Generate, download, manage VPN configs
- **Admin VPN APIs**: `/api/admin/vpn/*` - Monitor and manage all VPN users

## 🧪 **Testing Steps**

### **Phase 3.1: VPN Configuration Testing**

#### **Step 1: Generate User VPN Configuration**
1. **Login as a regular user**
2. **Make API call to generate VPN**:
   ```bash
   POST /api/vpn/generate
   Authorization: Bearer <user_token>
   ```
3. **Expected Response**:
   ```json
   {
     "success": true,
     "message": "VPN configuration created successfully",
     "data": {
       "ipAddress": "********",
       "subnet": "********/24",
       "hasConfig": true
     }
   }
   ```

#### **Step 2: Download VPN Configuration File**
1. **Download config file**:
   ```bash
   GET /api/vpn/config
   Authorization: Bearer <user_token>
   ```
2. **Expected**: WireGuard `.conf` file download
3. **File should contain**:
   ```ini
   [Interface]
   PrivateKey = [USER_PRIVATE_KEY]
   Address = ********/24
   DNS = *******,*******
   MTU = 1420

   [Peer]
   PublicKey = [SERVER_PUBLIC_KEY]
   Endpoint = your-server.com:51820
   AllowedIPs = ********/24
   PersistentKeepalive = 25
   ```

#### **Step 3: Check VPN Status**
1. **Get VPN status**:
   ```bash
   GET /api/vpn/status
   Authorization: Bearer <user_token>
   ```
2. **Expected Response**:
   ```json
   {
     "success": true,
     "data": {
       "hasVPNConfig": true,
       "vpnInfo": {
         "ipAddress": "********",
         "subnet": "********/24",
         "serverEndpoint": "your-server.com",
         "serverPort": 51820
       },
       "serverStatus": {
         "isRunning": false,
         "totalUsers": 1,
         "activeConnections": 0
       },
       "userNetworks": []
     }
   }
   ```

### **Phase 3.2: Network Isolation Testing**

#### **Step 4: Deploy Machine with VPN Integration**
1. **Create a machine template** (if not already done)
2. **Deploy machine** using existing machine deployment API
3. **System should automatically**:
   - Create user's isolated Docker network
   - Assign machine to user's subnet
   - Configure network isolation rules

#### **Step 5: Verify Network Isolation**
1. **Check machine network configuration**:
   ```bash
   GET /api/vpn/status
   Authorization: Bearer <user_token>
   ```
2. **Should show user networks**:
   ```json
   {
     "userNetworks": [
       {
         "machineId": "...",
         "internalIP": "*********",
         "exposedPorts": [
           {"internal": 80, "protocol": "tcp"}
         ],
         "status": "running"
       }
     ]
   }
   ```

### **Phase 3.3: Admin VPN Management Testing**

#### **Step 6: Admin VPN Overview**
1. **Login as admin**
2. **Get VPN server status**:
   ```bash
   GET /api/admin/vpn/status
   Authorization: Bearer <admin_token>
   ```
3. **Expected Response**:
   ```json
   {
     "success": true,
     "data": {
       "server": {
         "isRunning": false,
         "activeConnections": 0,
         "totalUsers": 1,
         "serverEndpoint": "your-server.com",
         "serverPort": 51820
       },
       "networks": {
         "totalNetworks": 1,
         "activeConnections": 0,
         "totalMachines": 1
       }
     }
   }
   ```

#### **Step 7: View All VPN Configurations**
1. **Get all VPN configs**:
   ```bash
   GET /api/admin/vpn/configs
   Authorization: Bearer <admin_token>
   ```
2. **Should list all user VPN configurations**

#### **Step 8: Generate Server Configuration**
1. **Get complete server config**:
   ```bash
   GET /api/admin/vpn/server-config
   Authorization: Bearer <admin_token>
   ```
2. **Should return complete WireGuard server configuration**

### **Phase 3.4: VPN Setup Instructions Testing**

#### **Step 9: Get Setup Instructions**
1. **Get OS-specific setup instructions**:
   ```bash
   GET /api/vpn/setup-instructions
   Authorization: Bearer <user_token>
   ```
2. **Should return instructions for**:
   - Windows
   - macOS
   - Linux
   - Android
   - iOS

## 🔍 **Verification Checklist**

### **Backend Functionality**
- [ ] VPN configurations are generated with unique subnets
- [ ] Private keys are encrypted in database
- [ ] Docker networks are created per user
- [ ] Network isolation rules are applied
- [ ] Connection logs are recorded
- [ ] Admin can view all VPN users
- [ ] Server configuration is generated correctly

### **Database Integrity**
- [ ] VPNConfig documents are created correctly
- [ ] MachineNetwork documents link machines to networks
- [ ] Connection logs are stored with proper TTL
- [ ] Indexes are working for performance

### **Security Features**
- [ ] Each user gets isolated subnet (10.x.0.0/24)
- [ ] Private keys are encrypted before storage
- [ ] Network traffic is isolated between users
- [ ] Admin can revoke user access
- [ ] Connection events are logged

### **API Endpoints**
- [ ] All user VPN endpoints work correctly
- [ ] All admin VPN endpoints work correctly
- [ ] Proper authentication and authorization
- [ ] Error handling works as expected
- [ ] File downloads work correctly

## 🚨 **Common Issues & Solutions**

### **Issue: "WireGuard not found"**
**Solution**: Install WireGuard tools:
- **Ubuntu**: `sudo apt install wireguard-tools`
- **Windows**: Download from https://www.wireguard.com/install/
- **macOS**: `brew install wireguard-tools`

### **Issue: "No available subnets"**
**Solution**: Check database for existing VPN configs, clean up unused ones

### **Issue: "Docker network creation failed"**
**Solution**: Ensure Docker daemon is running and user has permissions

### **Issue: "Private key encryption failed"**
**Solution**: Set `VPN_ENCRYPTION_KEY` environment variable

## 🔧 **Environment Variables**

Add these to your `.env` file:

```env
# WireGuard Server Configuration
WIREGUARD_SERVER_PRIVATE_KEY=your_server_private_key
WIREGUARD_SERVER_PUBLIC_KEY=your_server_public_key
WIREGUARD_SERVER_ENDPOINT=your-server.com
WIREGUARD_SERVER_PORT=51820

# VPN Security
VPN_ENCRYPTION_KEY=your-32-character-encryption-key

# Docker Configuration
DOCKER_SOCKET_PATH=/var/run/docker.sock
```

## 📊 **Expected Results**

### **Successful VPN Generation**
- User gets unique subnet (********/24, ********/24, etc.)
- WireGuard configuration file is downloadable
- Database contains encrypted VPN config
- Docker network is created for user

### **Successful Machine Deployment**
- Machine gets IP in user's subnet (e.g., *********)
- Network isolation is enforced
- Machine is accessible only via VPN
- Connection instructions are provided

### **Successful Admin Management**
- Admin can see all VPN users
- Server configuration is generated
- Connection logs are available
- User access can be revoked

## 🚀 **Next Steps After Testing**

Once Phase 3 testing is complete:

1. **✅ Phase 3 Complete**: VPN integration system
2. **🔄 Phase 4 Ready**: Frontend VPN interface
   - User VPN dashboard
   - Machine access interface
   - VPN setup wizard
   - Admin VPN management panel

## 📞 **Support**

If you encounter issues during testing:

1. Check backend logs for VPN service errors
2. Verify WireGuard tools are installed
3. Ensure Docker daemon is running
4. Check database connectivity
5. Verify environment variables are set

The VPN system provides enterprise-grade security and isolation for your cybersecurity training platform!
