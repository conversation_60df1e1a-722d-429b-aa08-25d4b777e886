import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { NotificationsService } from './notifications.service';
import { Notification } from '../schemas/notification.schema';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  username?: string;
}

@Injectable()
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    credentials: true,
  },
  namespace: '/notifications',
})
export class NotificationsGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private connectedUsers = new Map<string, AuthenticatedSocket>();

  constructor(
    private jwtService: JwtService,
    private notificationsService: NotificationsService,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      client.userId = payload.sub;
      client.username = payload.username;

      if (client.userId) {
        this.connectedUsers.set(client.userId, client);
        
        // Join user to their personal room
        client.join(`user:${client.userId}`);
        
        // Send initial notification count
        const { unreadCount } = await this.notificationsService.getUserNotifications(client.userId, {});
        client.emit('unread_count', { count: unreadCount });
      }
      
    } catch (error) {
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    if (client.userId) {
      this.connectedUsers.delete(client.userId);
    }
  }

  @SubscribeMessage('join_room')
  handleJoinRoom(@ConnectedSocket() client: AuthenticatedSocket, @MessageBody() data: { room: string }) {
    client.join(data.room);
  }

  @SubscribeMessage('leave_room')
  handleLeaveRoom(@ConnectedSocket() client: AuthenticatedSocket, @MessageBody() data: { room: string }) {
    client.leave(data.room);
  }

  @SubscribeMessage('mark_as_read')
  async handleMarkAsRead(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { notificationIds: string[] }
  ) {
    if (!client.userId) return;

    try {
      await this.notificationsService.markAsRead(client.userId, data.notificationIds);
      
      // Send updated unread count
      const { unreadCount } = await this.notificationsService.getUserNotifications(client.userId, {});
      client.emit('unread_count', { count: unreadCount });
      
      client.emit('notifications_marked_read', { notificationIds: data.notificationIds });
    } catch (error) {
      client.emit('error', { message: 'Failed to mark notifications as read' });
    }
  }

  @SubscribeMessage('mark_all_as_read')
  async handleMarkAllAsRead(@ConnectedSocket() client: AuthenticatedSocket) {
    if (!client.userId) return;

    try {
      await this.notificationsService.markAllAsRead(client.userId);
      
      // Send updated unread count
      const { unreadCount } = await this.notificationsService.getUserNotifications(client.userId, {});
      client.emit('unread_count', { count: unreadCount });
      
      client.emit('all_notifications_marked_read');
    } catch (error) {
      client.emit('error', { message: 'Failed to mark all notifications as read' });
    }
  }

  // Broadcast notification to all connected users
  async broadcastGlobalNotification(notification: Notification) {
    this.server.emit('new_notification', {
      notification,
      timestamp: new Date().toISOString(),
    });

    // Update unread counts for all connected users
    for (const [userId, socket] of this.connectedUsers) {
      try {
        const { unreadCount } = await this.notificationsService.getUserNotifications(userId, {});
        socket.emit('unread_count', { count: unreadCount });
      } catch (error) {
        // Silent error handling
      }
    }
  }

  // Send notification to specific users
  async sendNotificationToUsers(notification: Notification, userIds: string[]) {
    for (const userId of userIds) {
      const socket = this.connectedUsers.get(userId);
      if (socket) {
        socket.emit('new_notification', {
          notification,
          timestamp: new Date().toISOString(),
        });

        // Update unread count
        try {
          const { unreadCount } = await this.notificationsService.getUserNotifications(userId, {});
          socket.emit('unread_count', { count: unreadCount });
        } catch (error) {
          // Silent error handling
        }
      }
    }
  }

  // Send first blood notification with special animation
  async broadcastFirstBloodNotification(notification: Notification) {
    this.server.emit('first_blood_notification', {
      notification,
      timestamp: new Date().toISOString(),
      animation: 'gold_celebration',
    });

    // Update unread counts for all connected users
    for (const [userId, socket] of this.connectedUsers) {
      try {
        const { unreadCount } = await this.notificationsService.getUserNotifications(userId, {});
        socket.emit('unread_count', { count: unreadCount });
      } catch (error) {
        // Silent error handling
      }
    }
  }

  // Get connected users count
  getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  // Get connected users
  getConnectedUsers(): string[] {
    return Array.from(this.connectedUsers.keys());
  }
}