import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type VMCategory = 'web' | 'pwn' | 'crypto' | 'forensics' | 'misc';
export type VMDifficulty = 'easy' | 'medium' | 'hard' | 'insane';
export type VMStatus = 'running' | 'stopped' | 'starting' | 'stopping';

@Schema({ timestamps: true })
export class VirtualMachine extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  os: string;

  @Prop({ 
    type: String,
    enum: ['easy', 'medium', 'hard', 'insane'],
    required: true
  })
  difficulty: VMDifficulty;

  @Prop({ 
    type: String,
    enum: ['web', 'pwn', 'crypto', 'forensics', 'misc'],
    required: true
  })
  category: VMCategory;

  @Prop({ required: true })
  dockerImage: string;

  @Prop({ type: [Number], default: [] })
  ports: number[];

  @Prop({ required: true, default: 2 })
  maxRuntimeHours: number;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: false })
  needsVpn: boolean;

  @Prop({ default: 0 })
  accessCount: number;

  createdAt: Date;
  updatedAt: Date;
}

export const VirtualMachineSchema = SchemaFactory.createForClass(VirtualMachine);

// Create indexes
VirtualMachineSchema.index({ category: 1, difficulty: 1 });
VirtualMachineSchema.index({ isActive: 1 });
