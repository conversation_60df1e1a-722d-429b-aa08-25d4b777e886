import { Injectable, Logger, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import Docker = require('dockerode');
import { MachineTemplate } from '../../schemas/machine-template.schema';
import { MachineInstance } from '../../schemas/machine-instance.schema';
import { MachineFile } from '../../schemas/machine-file.schema';
import { DockerMachineService } from './docker-machine.service';
import { ArchiveExtractorService } from './archive-extractor.service';
import { NotificationsService } from '../../notifications/notifications.service';
import { NotificationsGateway } from '../../notifications/notifications.gateway';

export interface CreateMachineTemplateDto {
  name: string;
  description: string;
  category: 'web' | 'crypto' | 'pwn' | 'reverse' | 'forensics' | 'misc' | 'osint';
  difficulty: 'easy' | 'medium' | 'hard' | 'insane';
  os: 'linux' | 'windows' | 'other';
  machineType: 'docker' | 'ova' | 'qcow2';
  dockerImage?: string;
  exposedPorts: number[];
  requiredRAM: number;
  requiredCPU: number;
  maxInstances: number;
  flags: Array<{
    name: string;
    value: string;
    type: 'root' | 'user' | 'www-data' | 'custom';
    points: number;
    description?: string;
    isRequired: boolean;
  }>;
  topics: Array<{
    title: string;
    description: string;
    imageUrl?: string;
    order: number;
  }>;
  hints: string[];
  walkthrough?: string;
  releaseDate: Date;
  retireDate?: Date;
  tags: string[];
}

@Injectable()
export class MachineTemplateService {
  private readonly logger = new Logger(MachineTemplateService.name);
  private readonly docker: Docker;
  private readonly uploadsPath: string;

  constructor(
    @InjectModel(MachineTemplate.name) private machineTemplateModel: Model<MachineTemplate>,
    @InjectModel(MachineInstance.name) private machineInstanceModel: Model<MachineInstance>,
    @InjectModel(MachineFile.name) private machineFileModel: Model<MachineFile>,
    private configService: ConfigService,
    private dockerMachineService: DockerMachineService,
    private archiveExtractor: ArchiveExtractorService,
    private notificationsService: NotificationsService,
    private notificationsGateway: NotificationsGateway,
  ) {
    this.docker = new Docker({
      socketPath: this.configService.get('DOCKER_SOCKET_PATH', '/var/run/docker.sock'),
    });
    this.uploadsPath = this.configService.get('UPLOADS_PATH', './uploads/machines');
    this.ensureUploadsDirectory();
  }

  /**
   * Get all machine templates (admin view)
   */
  async getAllTemplates(options: {
    page?: number;
    limit?: number;
    includeInactive?: boolean;
  } = {}): Promise<{ templates: MachineTemplate[]; total: number; hasMore: boolean }> {
    const { page = 1, limit = 20, includeInactive = false } = options;
    
    const query: any = {};
    if (!includeInactive) {
      query.isActive = true;
    }

    const skip = (page - 1) * limit;
    
    const [templates, total] = await Promise.all([
      this.machineTemplateModel
        .find(query)
        .populate('authorId', 'username')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.machineTemplateModel.countDocuments(query),
    ]);

    // Add instance counts and transform templates
    const transformedTemplates: any[] = [];
    for (const template of templates) {
      const instanceCount = await this.machineInstanceModel.countDocuments({
        templateId: template._id,
        status: { $in: ['starting', 'running'] }
      });
      const transformed = this.transformTemplate(template);
      transformed.activeInstances = instanceCount;
      transformedTemplates.push(transformed);
    }

    return {
      templates: transformedTemplates,
      total,
      hasMore: skip + templates.length < total,
    };
  }

  /**
   * Create a new machine template
   */
  async createTemplate(authorId: string, createDto: CreateMachineTemplateDto): Promise<MachineTemplate> {
    this.logger.log(`Creating machine template: ${createDto.name}`);

    // Check if name already exists
    const existingTemplate = await this.machineTemplateModel.findOne({
      name: createDto.name,
    });

    if (existingTemplate) {
      throw new ConflictException('Machine template with this name already exists');
    }

    // Generate slug
    const slug = this.generateSlug(createDto.name);

    // Validate Docker image if provided
    if (createDto.dockerImage) {
      await this.validateDockerImage(createDto.dockerImage);
    }

    // Create template
    const template = new this.machineTemplateModel({
      ...createDto,
      slug,
      authorId: new Types.ObjectId(authorId),
      authorName: 'Admin', // TODO: Get from user service
      downloadCount: 0,
      solveCount: 0,
      rating: 0,
      defaultMaxUptime: 240, // 4 hours default
    });

    await template.save();

    // If template is created as active, send notification
    if (template.isActive) {
      try {
        console.log(`🎯 Creating new machine notification for "${template.name}" (Machine Template Service)`);
        const notification = await this.notificationsService.createNewMachineNotification(
          (template._id as any).toString(),
          template.name,
          template.os,
          template.difficulty,
          template.flags.reduce((sum, flag) => sum + flag.points, 0)
        );
        
        // Broadcast the new machine notification to all users
        await this.notificationsGateway.broadcastGlobalNotification(notification);
        console.log(`✅ New machine notification broadcasted successfully (Machine Template Service)`);
      } catch (error) {
        console.error('Failed to create new machine notification (Machine Template Service):', error);
      }
    }

    this.logger.log(`Successfully created machine template: ${template._id}`);
    return this.transformTemplate(template);
  }

  /**
   * Upload topic image
   */
  async uploadTopicImage(file: Express.Multer.File): Promise<{ imageUrl: string; filename: string }> {
    this.logger.log(`Uploading topic image: ${file.originalname}`);

    // Create directory for topic images using the configured uploads path
    const topicsDir = path.join(this.uploadsPath, 'machine', 'topics');
    if (!fs.existsSync(topicsDir)) {
      fs.mkdirSync(topicsDir, { recursive: true });
    }

    // Generate unique filename
    const fileExtension = path.extname(file.originalname);
    const fileName = `topic_${Date.now()}_${crypto.randomUUID()}${fileExtension}`;
    const filePath = path.join(topicsDir, fileName);

    // Save file to disk
    fs.writeFileSync(filePath, file.buffer);

    // Return the correct relative URL path (includes 'machines' since uploadsPath is './uploads/machines')
    const imageUrl = `/uploads/machines/machine/topics/${fileName}`;

    this.logger.log(`Topic image uploaded successfully: ${imageUrl}`);
    this.logger.log(`File saved to: ${filePath}`);
    this.logger.log(`File exists: ${fs.existsSync(filePath)}`);

    return {
      imageUrl,
      filename: fileName
    };
  }

  /**
   * Upload file for machine template
   */
  async uploadFile(
    templateId: string,
    file: Express.Multer.File,
    fileType: 'dockerfile' | 'ova' | 'vmdk' | 'qcow2' | 'resource'
  ): Promise<MachineFile> {
    const template = await this.machineTemplateModel.findById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    // Create directory for this template
    const templateDir = path.join(this.uploadsPath, template.slug);
    if (!fs.existsSync(templateDir)) {
      fs.mkdirSync(templateDir, { recursive: true });
    }

    // Generate unique filename
    const fileExtension = path.extname(file.originalname);
    const fileName = `${fileType}_${Date.now()}${fileExtension}`;
    const filePath = path.join(templateDir, fileName);

    // Save file to disk
    fs.writeFileSync(filePath, file.buffer);

    // Calculate file hash
    const fileHash = crypto.createHash('sha256').update(file.buffer).digest('hex');

    // Create file record
    const machineFile = new this.machineFileModel({
      templateId: template._id,
      fileName: file.originalname,
      filePath: path.relative(this.uploadsPath, filePath),
      fileType,
      fileSize: file.size,
      fileHash,
      isDownloadable: fileType === 'resource',
      requiresAuth: true,
      uploadedBy: template.authorId,
    });

    await machineFile.save();

    // Update template with file paths
    switch (fileType) {
      case 'dockerfile':
        template.dockerFile = machineFile.filePath;
        break;
      case 'ova':
        template.ovaFile = machineFile.filePath;
        break;
    }

    await template.save();

    this.logger.log(`Uploaded file ${fileName} for template ${templateId}`);
    return machineFile;
  }

  /**
   * Build Docker image from Dockerfile
   */
  async buildDockerImage(
    templateId: string,
    dockerfilePath?: string,
    buildArgs?: Record<string, string>
  ): Promise<{ imageId: string; imageName: string }> {
    const template = await this.machineTemplateModel.findById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    const buildPath = dockerfilePath || template.dockerFile;
    if (!buildPath) {
      throw new BadRequestException('No Dockerfile found for this template');
    }

    const fullDockerfilePath = path.join(this.uploadsPath, buildPath);
    if (!fs.existsSync(fullDockerfilePath)) {
      throw new BadRequestException('Dockerfile not found on disk');
    }

    // Generate image name
    const imageName = `mybox/${template.slug}:latest`;

    try {
      this.logger.log(`Building Docker image for template ${templateId}: ${imageName}`);

      // Create build context
      const buildContext = path.dirname(fullDockerfilePath);
      
      // Build image
      const stream = await this.docker.buildImage({
        context: buildContext,
        src: ['.'],
      }, {
        t: imageName,
        dockerfile: path.basename(fullDockerfilePath),
        buildargs: buildArgs,
        labels: {
          'mybox.template.id': template._id.toString(),
          'mybox.template.name': template.name,
          'mybox.built.at': new Date().toISOString(),
        },
      });

      // Wait for build to complete
      const buildResult = await new Promise<string>((resolve, reject) => {
        this.docker.modem.followProgress(stream, (err, res) => {
          if (err) return reject(err);
          
          // Extract image ID from final result
          const finalResult = res[res.length - 1];
          if (finalResult.aux?.ID) {
            resolve(finalResult.aux.ID);
          } else {
            resolve('unknown');
          }
        });
      });

      // Update template with new image
      template.dockerImage = imageName;
      await template.save();

      this.logger.log(`Successfully built Docker image: ${imageName}`);
      return {
        imageId: buildResult,
        imageName,
      };

    } catch (error) {
      this.logger.error(`Failed to build Docker image: ${error.message}`);
      throw new BadRequestException(`Docker build failed: ${error.message}`);
    }
  }

  /**
   * Update machine template
   */
  async updateTemplate(templateId: string, updateDto: Partial<CreateMachineTemplateDto>): Promise<MachineTemplate> {
    const template = await this.machineTemplateModel.findById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    // If name is being changed, check for conflicts
    if (updateDto.name && updateDto.name !== template.name) {
      const existingTemplate = await this.machineTemplateModel.findOne({
        name: updateDto.name,
        _id: { $ne: templateId },
      });

      if (existingTemplate) {
        throw new ConflictException('Machine template with this name already exists');
      }
      
      // Update slug if name changed
      (updateDto as any).slug = this.generateSlug(updateDto.name);
    }

    // Validate Docker image if provided
    if (updateDto.dockerImage) {
      await this.validateDockerImage(updateDto.dockerImage);
    }

    Object.assign(template, updateDto);
    await template.save();

    this.logger.log(`Updated machine template: ${templateId}`);
    return this.transformTemplate(template);
  }

  /**
   * Delete machine template
   */
  async deleteTemplate(templateId: string): Promise<void> {
    const template = await this.machineTemplateModel.findById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    // Check for active instances
    const activeInstances = await this.machineInstanceModel.countDocuments({
      templateId,
      status: { $in: ['starting', 'running'] }
    });

    if (activeInstances > 0) {
      throw new BadRequestException(`Cannot delete template with ${activeInstances} active instances`);
    }

    // Delete associated files
    const files = await this.machineFileModel.find({ templateId });
    for (const file of files) {
      const fullPath = path.join(this.uploadsPath, file.filePath);
      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
      }
    }

    // Delete file records
    await this.machineFileModel.deleteMany({ templateId });

    // Delete Docker image if exists
    if (template.dockerImage) {
      try {
        const image = this.docker.getImage(template.dockerImage);
        await image.remove({ force: true });
        this.logger.log(`Deleted Docker image: ${template.dockerImage}`);
      } catch (error) {
        this.logger.warn(`Failed to delete Docker image: ${error.message}`);
      }
    }

    // Delete template directory
    const templateDir = path.join(this.uploadsPath, template.slug);
    if (fs.existsSync(templateDir)) {
      fs.rmSync(templateDir, { recursive: true, force: true });
    }

    // Delete template
    await this.machineTemplateModel.findByIdAndDelete(templateId);

    this.logger.log(`Deleted machine template: ${templateId}`);
  }

  /**
   * Toggle template status
   */
  async toggleStatus(templateId: string, isActive: boolean): Promise<MachineTemplate> {
    // Get the original template to check if status changed
    const originalTemplate = await this.machineTemplateModel.findById(templateId);
    if (!originalTemplate) {
      throw new NotFoundException('Machine template not found');
    }

    const template = await this.machineTemplateModel.findByIdAndUpdate(
      templateId,
      { isActive },
      { new: true }
    );

    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    // If template status changed from inactive to active, send notification
    if (!originalTemplate.isActive && template.isActive) {
      try {
        console.log(`🎯 Creating new machine notification for activated machine "${template.name}" (Machine Template Service)`);
        const notification = await this.notificationsService.createNewMachineNotification(
          (template._id as any).toString(),
          template.name,
          template.os,
          template.difficulty,
          template.flags.reduce((sum, flag) => sum + flag.points, 0)
        );
        
        // Broadcast the new machine notification to all users
        await this.notificationsGateway.broadcastGlobalNotification(notification);
        console.log(`✅ New machine notification broadcasted successfully (Machine Template Service)`);
      } catch (error) {
        console.error('Failed to create new machine notification (Machine Template Service):', error);
      }
    }

    this.logger.log(`Template ${templateId} status changed to: ${isActive ? 'active' : 'inactive'}`);
    return this.transformTemplate(template);
  }

  /**
   * Test machine template
   */
  async testTemplate(templateId: string, userId: string): Promise<any> {
    const template = await this.machineTemplateModel.findById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    if (!template.dockerImage) {
      throw new BadRequestException('Template has no Docker image to test');
    }

    try {
      // Spawn a test instance
      const testInstance = await this.dockerMachineService.spawnMachine(
        templateId,
        userId,
        undefined,
        { extendedRuntime: 30 } // 30 minute test
      );

      this.logger.log(`Test instance created for template ${templateId}: ${(testInstance as any)._id}`);
      
      return {
        message: 'Test instance created successfully',
        instanceId: (testInstance as any)._id,
        connectionInfo: {
          instanceIP: testInstance.instanceIP,
          exposedPorts: testInstance.exposedPorts,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to test template ${templateId}: ${error.message}`);
      throw new BadRequestException(`Template test failed: ${error.message}`);
    }
  }

  /**
   * Get all instances (admin view)
   */
  async getAllInstances(options: {
    status?: string;
    templateId?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<{ instances: any[]; total: number; hasMore: boolean }> {
    const { status, templateId, page = 1, limit = 50 } = options;
    
    const query: any = {};
    if (status) {
      query.status = status;
    }
    if (templateId) {
      query.templateId = templateId;
    }

    const skip = (page - 1) * limit;
    
    const [instances, total] = await Promise.all([
      this.machineInstanceModel
        .find(query)
        .populate('templateId', 'name difficulty category')
        .populate('ownerId', 'username email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.machineInstanceModel.countDocuments(query),
    ]);

    return {
      instances,
      total,
      hasMore: skip + instances.length < total,
    };
  }

  /**
   * Force terminate instance (admin)
   */
  async forceTerminateInstance(instanceId: string): Promise<void> {
    const instance = await this.machineInstanceModel.findById(instanceId);
    if (!instance) {
      throw new NotFoundException('Machine instance not found');
    }

    await this.dockerMachineService.terminateMachine(instanceId, instance.ownerId.toString());
    this.logger.log(`Force terminated instance: ${instanceId}`);
  }

  /**
   * Get system resource information
   */
  async getSystemResources(): Promise<{
    docker: {
      version: string;
      containers: { running: number; total: number };
      images: number;
      networks: number;
    };
    system: {
      totalMemory: number;
      usedMemory: number;
      cpuCount: number;
      diskSpace: { total: number; used: number; available: number };
    };
  }> {
    try {
      const [dockerInfo, dockerVersion] = await Promise.all([
        this.docker.info(),
        this.docker.version(),
      ]);

      const containers = await this.docker.listContainers({ all: true });
      const images = await this.docker.listImages();
      const networks = await this.docker.listNetworks();

      const runningContainers = containers.filter(c => c.State === 'running').length;

      // Get disk space for uploads directory
      const stats = fs.statSync(this.uploadsPath);
      
      return {
        docker: {
          version: dockerVersion.Version,
          containers: {
            running: runningContainers,
            total: containers.length,
          },
          images: images.length,
          networks: networks.length,
        },
        system: {
          totalMemory: dockerInfo.MemTotal,
          usedMemory: dockerInfo.MemTotal - dockerInfo.MemoryLimit,
          cpuCount: dockerInfo.NCPU,
          diskSpace: {
            total: 0, // TODO: Implement disk space calculation
            used: 0,
            available: 0,
          },
        },
      };

    } catch (error) {
      this.logger.error(`Failed to get system resources: ${error.message}`);
      throw new BadRequestException('Failed to retrieve system resources');
    }
  }

  /**
   * Get usage analytics
   */
  async getUsageAnalytics(options: {
    startDate?: Date;
    endDate?: Date;
    templateId?: string;
  } = {}): Promise<any> {
    const { startDate, endDate, templateId } = options;
    
    const matchStage: any = {};
    
    if (startDate || endDate) {
      matchStage.createdAt = {};
      if (startDate) matchStage.createdAt.$gte = startDate;
      if (endDate) matchStage.createdAt.$lte = endDate;
    }
    
    if (templateId) {
      matchStage.templateId = new Types.ObjectId(templateId);
    }

    const [usageStats, popularTemplates, userActivity] = await Promise.all([
      // Overall usage statistics
      this.machineInstanceModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            totalInstances: { $sum: 1 },
            averageUptime: { $avg: '$maxUptime' },
            totalUptime: { $sum: '$maxUptime' },
          }
        }
      ]),

      // Most popular templates
      this.machineInstanceModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$templateId',
            instanceCount: { $sum: 1 },
            averageUptime: { $avg: '$maxUptime' },
          }
        },
        { $sort: { instanceCount: -1 } },
        { $limit: 10 },
        {
          $lookup: {
            from: 'machinetemplates',
            localField: '_id',
            foreignField: '_id',
            as: 'template'
          }
        },
        { $unwind: '$template' }
      ]),

      // User activity
      this.machineInstanceModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$ownerId',
            instanceCount: { $sum: 1 },
            totalUptime: { $sum: '$maxUptime' },
          }
        },
        { $sort: { instanceCount: -1 } },
        { $limit: 10 },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: '$user' }
      ]),
    ]);

    return {
      overview: usageStats[0] || { totalInstances: 0, averageUptime: 0, totalUptime: 0 },
      popularTemplates,
      topUsers: userActivity,
    };
  }

  /**
   * Export templates configuration
   */
  async exportTemplates(format: 'json' | 'yaml' = 'json'): Promise<any> {
    const templates = await this.machineTemplateModel
      .find({ isActive: true })
      .populate('authorId', 'username')
      .exec();

    const exportData = {
      exportedAt: new Date().toISOString(),
      version: '1.0',
      templates: templates.map(template => ({
        name: template.name,
        description: template.description,
        category: template.category,
        difficulty: template.difficulty,
        os: template.os,
        dockerImage: template.dockerImage,
        exposedPorts: template.exposedPorts,
        requiredRAM: template.requiredRAM,
        requiredCPU: template.requiredCPU,
        flags: template.flags,
        hints: template.hints,
        tags: template.tags,
      })),
    };

    if (format === 'yaml') {
      // TODO: Implement YAML export
      throw new BadRequestException('YAML export not yet implemented');
    }

    return exportData;
  }

  /**
   * Import templates configuration
   */
  async importTemplates(
    userId: string,
    file: Express.Multer.File,
    overwrite: boolean = false
  ): Promise<{ imported: number; skipped: number; errors: string[] }> {
    try {
      const importData = JSON.parse(file.buffer.toString());
      
      if (!importData.templates || !Array.isArray(importData.templates)) {
        throw new BadRequestException('Invalid import file format');
      }

      let imported = 0;
      let skipped = 0;
      const errors: string[] = [];

      for (const templateData of importData.templates) {
        try {
          const existingTemplate = await this.machineTemplateModel.findOne({
            name: templateData.name,
          });

          if (existingTemplate && !overwrite) {
            skipped++;
            continue;
          }

          if (existingTemplate && overwrite) {
            // Update existing template
            Object.assign(existingTemplate, templateData);
            await existingTemplate.save();
          } else {
            // Create new template
            await this.createTemplate(userId, {
              ...templateData,
              releaseDate: new Date(),
            });
          }

          imported++;

        } catch (error) {
          errors.push(`Template ${templateData.name}: ${error.message}`);
        }
      }

      this.logger.log(`Import completed: ${imported} imported, ${skipped} skipped, ${errors.length} errors`);
      
      return { imported, skipped, errors };

    } catch (error) {
      this.logger.error(`Import failed: ${error.message}`);
      throw new BadRequestException(`Import failed: ${error.message}`);
    }
  }

  /**
   * Generate URL-friendly slug from name
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }

  /**
   * Validate Docker image exists
   */
  private async validateDockerImage(imageName: string): Promise<void> {
    try {
      await this.docker.getImage(imageName).inspect();
    } catch (error) {
      this.logger.warn(`Docker image validation failed for ${imageName}: ${error.message}`);
      // Don't throw error - image might be pulled later
    }
  }

  /**
   * Ensure uploads directory exists
   */
  private ensureUploadsDirectory(): void {
    if (!fs.existsSync(this.uploadsPath)) {
      fs.mkdirSync(this.uploadsPath, { recursive: true });
      this.logger.log(`Created uploads directory: ${this.uploadsPath}`);
    }
  }

  /**
   * Upload and extract machine template archive
   */
  async uploadMachineArchive(
    templateId: string,
    file: Express.Multer.File
  ): Promise<{ extractPath: string; files: string[]; isValid: boolean; structure: any }> {
    const template = await this.machineTemplateModel.findById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    // Determine archive type
    const archiveType = this.archiveExtractor.getArchiveType(file.originalname);
    if (!archiveType) {
      throw new BadRequestException('Only ZIP and 7z archives are supported');
    }

    // Create extraction path in dockers directory
    const dockersPath = path.join(this.uploadsPath, 'dockers');
    const extractPath = path.join(dockersPath, template.slug);

    // Ensure dockers directory exists
    if (!fs.existsSync(dockersPath)) {
      fs.mkdirSync(dockersPath, { recursive: true });
    }

    // Save uploaded file temporarily
    const tempDir = path.join(this.uploadsPath, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFilePath = path.join(tempDir, `${Date.now()}-${file.originalname}`);
    fs.writeFileSync(tempFilePath, file.buffer);

    try {
      // Extract archive
      const result = await this.archiveExtractor.extractArchive(
        tempFilePath,
        extractPath,
        archiveType
      );

      // Validate structure
      const isValid = this.archiveExtractor.validateMachineTemplate(result.structure);

      if (!isValid) {
        // Clean up extraction on validation failure
        await this.archiveExtractor.cleanupExtraction(extractPath);
        throw new BadRequestException(
          'Invalid machine template structure. Must contain Dockerfile and docker-compose.yml'
        );
      }

      // Clean up temp file
      fs.unlinkSync(tempFilePath);

      this.logger.log(`Successfully extracted machine template: ${template.slug}`);

      // Update template with extraction info
      await this.machineTemplateModel.findByIdAndUpdate(templateId, {
        $set: {
          hasArchive: true,
          archiveExtractedAt: new Date(),
          archiveFileCount: result.files.length
        }
      });

      return {
        extractPath,
        files: result.files,
        isValid,
        structure: result.structure
      };
    } catch (error) {
      // Clean up on error
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
      if (fs.existsSync(extractPath)) {
        await this.archiveExtractor.cleanupExtraction(extractPath);
      }

      this.logger.error(`Archive extraction failed for template ${template.slug}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if template has extracted files
   */
  async hasExtractedFiles(templateId: string): Promise<boolean> {
    const template = await this.machineTemplateModel.findById(templateId);
    if (!template) {
      return false;
    }

    const extractPath = path.join(this.uploadsPath, 'dockers', template.slug);
    return fs.existsSync(extractPath) && fs.readdirSync(extractPath).length > 0;
  }

  /**
   * Get extracted template path
   */
  getExtractedTemplatePath(templateSlug: string): string {
    return path.join(this.uploadsPath, 'dockers', templateSlug);
  }

  /**
   * Get template by ID
   */
  async getTemplateById(templateId: string): Promise<MachineTemplate | null> {
    try {
      const template = await this.machineTemplateModel.findById(templateId);
      return template ? this.transformTemplate(template) : null;
    } catch (error) {
      this.logger.error(`Error getting template by ID ${templateId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Transform MongoDB document to include id field
   */
  private transformTemplate(template: any): any {
    const transformed = template.toObject ? template.toObject() : template;
    transformed.id = transformed._id.toString();
    return transformed;
  }
}