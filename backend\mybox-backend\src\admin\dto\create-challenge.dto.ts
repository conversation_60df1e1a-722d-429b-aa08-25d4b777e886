import { IsNotEmpty, IsString, IsEnum, IsNumber, IsArray, IsOptional, IsBoolean, IsDate, ValidateNested, IsObject, IsUrl } from 'class-validator';
import { Type } from 'class-transformer';
import { ChallengeDifficulty, Flag, ChallengeFile } from '../../schemas/challenge.schema';
import { ApiProperty } from '@nestjs/swagger';

// DTO for Flag
export class FlagDto implements Partial<Flag> {
  @ApiProperty({ description: 'Flag value' })
  @IsNotEmpty()
  @IsString()
  value: string;

  @ApiProperty({ description: 'Whether flag is case sensitive', required: false })
  @IsOptional()
  @IsBoolean()
  isCaseSensitive?: boolean;

  @ApiProperty({ description: 'Points awarded for this flag', required: false })
  @IsOptional()
  @IsNumber()
  points?: number;

  @ApiProperty({ description: 'Flag description', required: false })
  @IsOptional()
  @IsString()
  description?: string;
}

// DTO for ChallengeFile
export class ChallengeFileDto implements Partial<ChallengeFile> {
  @ApiProperty({ description: 'File name' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'File path or URL' })
  @IsNotEmpty()
  @IsString()
  path: string;

  @ApiProperty({ description: 'File size in bytes', required: false })
  @IsOptional()
  @IsNumber()
  size?: number;

  @ApiProperty({ description: 'File MIME type', required: false })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({ description: 'File description', required: false })
  @IsOptional()
  @IsString()
  description?: string;
}

export class CreateChallengeDto {
  @ApiProperty({ description: 'Challenge title' })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ description: 'Challenge description' })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Challenge category (must be a valid category name)'
  })
  @IsString()
  category: string;

  @ApiProperty({ 
    description: 'Challenge difficulty', 
    enum: ['easy', 'medium', 'hard', 'insane'] 
  })
  @IsEnum(['easy', 'medium', 'hard', 'insane'])
  difficulty: ChallengeDifficulty;

  @ApiProperty({ description: 'Challenge points', required: false })
  @IsOptional()
  @IsNumber()
  points?: number;

  @ApiProperty({ description: 'Legacy challenge flag', required: false })
  @IsOptional()
  @IsString()
  flag?: string;

  @ApiProperty({ description: 'Multiple challenge flags', type: [FlagDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FlagDto)
  flags?: FlagDto[];

  @ApiProperty({ description: 'Challenge hints', required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  hints?: string[];
  @ApiProperty({ description: 'Challenge tags', required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: 'Author ID', required: false })
  @IsOptional()
  @IsString()
  authorId?: string;
  
  @ApiProperty({ description: 'Author name', required: false })
  @IsOptional()
  @IsString()
  authorName?: string;

  @ApiProperty({ description: 'Challenge files', type: [ChallengeFileDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChallengeFileDto)
  files?: ChallengeFileDto[];

  @ApiProperty({ description: 'Active status', required: false, default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'Release date', required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  releaseDate?: Date;

  @ApiProperty({ description: 'Expiry date', required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  expiryDate?: Date;

  @ApiProperty({ description: 'Docker image for containerized challenges', required: false })
  @IsOptional()
  @IsString()
  dockerImage?: string;

  @ApiProperty({ description: 'Whether the challenge requires a server', required: false })
  @IsOptional()
  @IsBoolean()
  requiresServer?: boolean;
  @ApiProperty({ description: 'Server configuration for the challenge', required: false })
  @IsOptional()
  @IsObject()
  serverConfig?: object;
}
