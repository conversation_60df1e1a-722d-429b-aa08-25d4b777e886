import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Upload,
  Play,
  Pause,
  Edit,
  Trash2,
  Plus,
  Save,
  X,
  Volume2,
  Users,
  BarChart3,
  Star,
  RefreshCw,
  Download,
  AlertCircle
} from 'lucide-react';
import { NotificationSound, NotificationSoundStats, notificationSoundsService } from '../../services/notification-sounds';

interface CreateSoundModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

function CreateSoundModal({ isOpen, onClose, onSuccess }: CreateSoundModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    displayName: '',
    description: '',
    isDefault: false,
  });
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) {
      setError('Please select an audio file');
      return;
    }

    setUploading(true);
    setError('');

    try {
      await notificationSoundsService.createNotificationSound(formData, file);
      onSuccess();
      onClose();
      setFormData({ name: '', displayName: '', description: '', isDefault: false });
      setFile(null);
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to upload notification sound');
    } finally {
      setUploading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-slate-800 rounded-xl p-6 w-full max-w-md border border-purple-500/30"
      >
        <h3 className="text-xl font-bold text-white mb-4">Add Notification Sound</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Internal Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-purple-400"
              placeholder="notification_sound_1"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Display Name
            </label>
            <input
              type="text"
              value={formData.displayName}
              onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-purple-400"
              placeholder="Notification Sound 1"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Description (Optional)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-purple-400"
              placeholder="A brief description of the sound..."
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Audio File
            </label>
            <input
              type="file"
              accept="audio/*"
              onChange={(e) => setFile(e.target.files?.[0] || null)}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:border-purple-400"
              required
            />
            <p className="text-xs text-slate-400 mt-1">
              Supported formats: MP3, WAV, OGG. Max size: 5MB
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isDefault"
              checked={formData.isDefault}
              onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
              className="w-4 h-4 text-purple-600 bg-slate-700 border-slate-600 rounded focus:ring-purple-500"
            />
            <label htmlFor="isDefault" className="text-sm text-slate-300">
              Set as default sound
            </label>
          </div>

          {error && (
            <div className="flex items-center space-x-2 text-red-400 text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>{error}</span>
            </div>
          )}

          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-slate-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={uploading}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-colors disabled:opacity-50"
            >
              {uploading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Upload className="w-4 h-4" />
              )}
              <span>{uploading ? 'Uploading...' : 'Upload'}</span>
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}

export function NotificationSoundsAdmin() {
  const [sounds, setSounds] = useState<NotificationSound[]>([]);
  const [stats, setStats] = useState<NotificationSoundStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [playingSound, setPlayingSound] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingSound, setEditingSound] = useState<NotificationSound | null>(null);

  const loadData = async () => {
    setLoading(true);
    try {
      const [soundsData, statsData] = await Promise.all([
        notificationSoundsService.getAllNotificationSounds(),
        notificationSoundsService.getNotificationSoundStats(),
      ]);
      setSounds(soundsData);
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load notification sounds data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const handlePlaySound = async (soundId: string) => {
    if (playingSound === soundId) {
      setPlayingSound(null);
      return;
    }

    setPlayingSound(soundId);
    try {
      await notificationSoundsService.playNotificationSound(soundId, 0.7);
    } catch (error) {
      console.error('Failed to play sound:', error);
    } finally {
      setPlayingSound(null);
    }
  };

  const handleDeleteSound = async (soundId: string) => {
    if (!confirm('Are you sure you want to delete this notification sound?')) {
      return;
    }

    try {
      await notificationSoundsService.deleteNotificationSound(soundId);
      await loadData();
    } catch (error) {
      console.error('Failed to delete notification sound:', error);
    }
  };

  const handleSetDefault = async (soundId: string) => {
    try {
      await notificationSoundsService.updateNotificationSound(soundId, { isDefault: true });
      await loadData();
    } catch (error) {
      console.error('Failed to set default sound:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="flex items-center space-x-3 text-purple-200/80">
          <RefreshCw className="w-6 h-6 animate-spin" />
          <span>Loading notification sounds...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Notification Sounds</h2>
          <p className="text-slate-400">Manage notification sounds for the platform</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add Sound</span>
        </button>
      </div>

      {/* Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="glass-card p-6 border border-purple-500/30"
          >
            <div className="flex items-center space-x-3 mb-2">
              <Volume2 className="w-5 h-5 text-purple-400" />
              <span className="text-slate-300">Total Sounds</span>
            </div>
            <p className="text-2xl font-bold text-white">{stats.totalSounds}</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="glass-card p-6 border border-purple-500/30"
          >
            <div className="flex items-center space-x-3 mb-2">
              <Users className="w-5 h-5 text-blue-400" />
              <span className="text-slate-300">Total Users</span>
            </div>
            <p className="text-2xl font-bold text-white">{stats.totalUsers}</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="glass-card p-6 border border-purple-500/30"
          >
            <div className="flex items-center space-x-3 mb-2">
              <BarChart3 className="w-5 h-5 text-green-400" />
              <span className="text-slate-300">Most Popular</span>
            </div>
            <p className="text-lg font-semibold text-white">
              {stats.soundUsageStats.length > 0 
                ? stats.soundUsageStats.reduce((prev, current) => 
                    prev.userCount > current.userCount ? prev : current
                  ).soundName
                : 'None'
              }
            </p>
          </motion.div>
        </div>
      )}

      {/* Sounds List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="glass-card border border-purple-500/30 overflow-hidden"
      >
        <div className="p-6 border-b border-purple-500/20">
          <h3 className="text-xl font-semibold text-white">Notification Sounds</h3>
        </div>

        <div className="divide-y divide-purple-500/10">
          {sounds.map((sound, index) => (
            <motion.div
              key={sound._id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
              className="p-6 hover:bg-purple-500/5 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => handlePlaySound(sound._id)}
                    className="p-2 bg-purple-600/20 text-purple-400 rounded-lg hover:bg-purple-600/30 transition-colors"
                  >
                    {playingSound === sound._id ? (
                      <Pause className="w-4 h-4" />
                    ) : (
                      <Play className="w-4 h-4" />
                    )}
                  </button>

                  <div>
                    <div className="flex items-center space-x-2">
                      <h4 className="font-semibold text-white">{sound.displayName}</h4>
                      {sound.isDefault && (
                        <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded text-xs flex items-center space-x-1">
                          <Star className="w-3 h-3" />
                          <span>Default</span>
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-slate-400">{sound.description || 'No description'}</p>
                    <div className="flex items-center space-x-4 text-xs text-slate-500 mt-1">
                      <span>Duration: {sound.duration}s</span>
                      <span>Size: {(sound.fileSize / 1024).toFixed(1)}KB</span>
                      <span>Used by: {stats?.soundUsageStats.find(s => s.soundName === sound.displayName)?.userCount || 0} users</span>
                      <span>Plays: {sound.usageCount}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {!sound.isDefault && (
                    <button
                      onClick={() => handleSetDefault(sound._id)}
                      className="p-2 text-slate-400 hover:text-yellow-400 transition-colors"
                      title="Set as default"
                    >
                      <Star className="w-4 h-4" />
                    </button>
                  )}

                  <a
                    href={notificationSoundsService.getNotificationSoundFile(sound._id)}
                    download
                    className="p-2 text-slate-400 hover:text-blue-400 transition-colors"
                    title="Download"
                  >
                    <Download className="w-4 h-4" />
                  </a>

                  <button
                    onClick={() => setEditingSound(sound)}
                    className="p-2 text-slate-400 hover:text-purple-400 transition-colors"
                    title="Edit"
                  >
                    <Edit className="w-4 h-4" />
                  </button>

                  <button
                    onClick={() => handleDeleteSound(sound._id)}
                    className="p-2 text-slate-400 hover:text-red-400 transition-colors"
                    title="Delete"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}

          {sounds.length === 0 && (
            <div className="p-12 text-center">
              <Volume2 className="w-12 h-12 text-slate-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-slate-400 mb-2">No notification sounds</h3>
              <p className="text-slate-500">Add your first notification sound to get started.</p>
            </div>
          )}
        </div>
      </motion.div>

      {/* Usage Statistics */}
      {stats && stats.soundUsageStats.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="glass-card p-6 border border-purple-500/30"
        >
          <h3 className="text-xl font-semibold text-white mb-6">Usage Statistics</h3>
          
          <div className="space-y-4">
            {stats.soundUsageStats
              .sort((a, b) => b.userCount - a.userCount)
              .map((stat, index) => (
                <div key={stat.soundName} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-slate-300 w-4">#{index + 1}</span>
                    <span className="text-white">{stat.soundName}</span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-slate-400">
                    <span>{stat.userCount} users</span>
                    <span>{stat.usageCount} plays</span>
                    <div className="w-24 bg-slate-700 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full"
                        style={{
                          width: `${(stat.userCount / stats.totalUsers) * 100}%`
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </motion.div>
      )}

      {/* Create Sound Modal */}
      <AnimatePresence>
        {showCreateModal && (
          <CreateSoundModal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
            onSuccess={loadData}
          />
        )}
      </AnimatePresence>
    </div>
  );
}