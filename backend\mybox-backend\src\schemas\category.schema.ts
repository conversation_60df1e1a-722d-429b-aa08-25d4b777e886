import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Category extends Document {
  @Prop({ required: true, unique: true, minlength: 2, maxlength: 50 })
  name: string;

  @Prop({ required: true, minlength: 2, maxlength: 100 })
  displayName: string;

  @Prop({ maxlength: 500 })
  description?: string;

  @Prop({ required: true })
  color: string; // Hex color code for the category

  @Prop({ required: true })
  icon: string; // Icon name (from Lucide React)

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: 0 })
  sortOrder: number; // For custom ordering

  createdAt: Date;
  updatedAt: Date;
}

export const CategorySchema = SchemaFactory.createForClass(Category);

// Create indexes
CategorySchema.index({ name: 1 }, { unique: true });
CategorySchema.index({ sortOrder: 1 });
CategorySchema.index({ isActive: 1 });
