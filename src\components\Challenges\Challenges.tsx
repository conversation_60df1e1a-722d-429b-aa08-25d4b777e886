import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useApp } from '../../contexts/AppContext';
import { ChallengeCard } from './ChallengeCard';
import { ChallengeFilters } from './ChallengeFilters';
import {
  Search,
  RefreshCw,
  Target,
  Trophy,
  Zap,
  Shield,
  Lock,
  Star,
  Users,
  TrendingUp,
  Filter,
  Grid,
  List,
  Sparkles,
  Crown,
  Award,
  Eye,
  EyeOff,
  ChevronDown,
  X,
  ArrowUp,
  ArrowDown,
  Hexagon,
  Triangle,
  Circle,
  Square,
  Diamond,
  Layers,
  Gauge,
  Activity,
  Calendar,
  Clock,
  Flame,
  BarChart3,
  Hash,
  Code,
  Database,
  Server,
  Wifi,
  Bug,
  Key,
  Globe,
  Terminal,
  HardDrive,
  Network,
  Flag
} from 'lucide-react';
import './Challenges.css';

// Dynamic category icons mapping
const ICON_MAP: { [key: string]: any } = {
  Shield, Lock, Zap, RefreshCw, Search, Star, Target, Trophy, Users, TrendingUp,
  Filter, Grid, <PERSON>, Sparkles, Crown, Award, Eye, EyeOff, ChevronDown, X,
  ArrowUp, ArrowDown, Hexagon, Triangle, Circle, Square, Diamond,
  Layers, Gauge, Activity, Calendar, Clock, Flame, BarChart3,
  Hash, Code, Database, Server, Wifi, Bug, Key, Globe, Terminal, HardDrive, Network, Flag
};

const DIFFICULTY_SHAPES = {
  easy: Circle,
  medium: Triangle,
  hard: Square,
  insane: Diamond
};

export function Challenges() {
  const { state, fetchChallenges } = useApp();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [showSolved, setShowSolved] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [animationKey, setAnimationKey] = useState(0);
  const [groupByCategory, setGroupByCategory] = useState(true);
  const [showStats, setShowStats] = useState(true);

  const categories = state.categories || [];
  const challenges = state.challenges || [];

  React.useEffect(() => {
    setAnimationKey(prev => prev + 1);
  }, [selectedCategory, selectedDifficulty, showSolved]);

  // Helper function to get category icon
  const getCategoryIcon = (iconName: string) => {
    return ICON_MAP[iconName] || Target;
  };

  // Show loading state if categories are not loaded yet
  if (categories.length === 0 && challenges.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  // Helper function to get category style
  const getCategoryStyle = (categoryName: string) => {
    const category = categories.find(cat => cat.name === categoryName);
    if (category) {
      return {
        backgroundColor: `${category.color}20`,
        color: category.color,
        borderColor: `${category.color}40`
      };
    }
    return {
      backgroundColor: '#6B728020',
      color: '#9CA3AF',
      borderColor: '#6B728040'
    };
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await fetchChallenges();
    } catch (error) {
      console.error('Failed to refresh challenges:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const filteredChallenges = useMemo(() => {
    return challenges.filter(challenge => {
      const matchesCategory = selectedCategory === 'all' || challenge.category === selectedCategory;
      const matchesDifficulty = selectedDifficulty === 'all' || challenge.difficulty === selectedDifficulty;
      const matchesSolved = showSolved || !challenge.isSolved;
      const matchesSearch = searchTerm === '' ||
        challenge.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        challenge.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        challenge.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      return matchesCategory && matchesDifficulty && matchesSolved && matchesSearch;
    });
  }, [challenges, selectedCategory, selectedDifficulty, showSolved, searchTerm]);

  // Group challenges by category, sorted by category sort order
  const groupedChallenges = useMemo(() => {
    if (!groupByCategory) {
      return [{ category: null, challenges: filteredChallenges }];
    }

    // Get sorted categories
    const sortedCategories = [...categories].sort((a, b) => a.sortOrder - b.sortOrder);

    return sortedCategories.map(category => ({
      category,
      challenges: filteredChallenges.filter(challenge => challenge.category === category.name)
    })).filter(group => group.challenges.length > 0);
  }, [filteredChallenges, categories, groupByCategory]);

  const stats = useMemo(() => {
    const total = challenges.length;
    const solved = challenges.filter(c => c.isSolved).length;
    const totalPoints = challenges.reduce((sum, c) => sum + (c.isSolved ? c.points : 0), 0);
    const maxPoints = challenges.reduce((sum, c) => sum + c.points, 0);
    const completionRate = total > 0 ? Math.round((solved / total) * 100) : 0;

    // Category breakdown using dynamic categories
    const categoryStats = categories.map(category => {
      const categoryTotal = challenges.filter(c => c.category === category.name).length;
      const categorySolved = challenges.filter(c => c.category === category.name && c.isSolved).length;

      return {
        category: category.name,
        displayName: category.displayName,
        total: categoryTotal,
        solved: categorySolved,
        percentage: categoryTotal > 0 ? Math.round((categorySolved / categoryTotal) * 100) : 0,
        color: category.color,
        icon: category.icon
      };
    });

    // Difficulty breakdown
    const difficultyStats = Object.keys(DIFFICULTY_SHAPES).map(difficulty => {
      const difficultyTotal = challenges.filter(c => c.difficulty === difficulty).length;
      const difficultySolved = challenges.filter(c => c.difficulty === difficulty && c.isSolved).length;
      return {
        difficulty,
        total: difficultyTotal,
        solved: difficultySolved,
        percentage: difficultyTotal > 0 ? Math.round((difficultySolved / difficultyTotal) * 100) : 0
      };
    });
    
    return { total, solved, totalPoints, maxPoints, completionRate, categoryStats, difficultyStats };
  }, [state.challenges]);

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-pink-900/20"></div>
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-purple-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 1, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Floating geometric shapes */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <motion.div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full morph-shape animate-float" />
        <motion.div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '2s' }} />
        <motion.div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-pink-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '4s' }} />
        <motion.div className="absolute bottom-40 right-1/3 w-28 h-28 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '6s' }} />
      </div>

      <div className="relative z-10 space-y-12 p-8">
        {/* Hero Header */}
        <motion.div 
          className="text-center space-y-6"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          <div className="flex items-center justify-center space-x-4">
            <motion.div 
              className="relative p-4 glass-card rounded-2xl"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Target className="w-12 h-12 text-purple-400" />
              <motion.div
                className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center"
                animate={{ rotate: 360 }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <Sparkles className="w-3 h-3 text-white" />
              </motion.div>
            </motion.div>
            <motion.h1 
              className="text-6xl font-black bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              Challenge Hub
            </motion.h1>
          </div>
          <motion.p 
            className="text-xl text-purple-200/80 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            Dive deep into cybersecurity challenges with interactive problem-solving and real-time progress tracking
          </motion.p>
          
          {/* Floating Stats Preview */}
          <motion.div 
            className="flex justify-center space-x-8 mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
          >
            {[
              { label: 'Solved', value: stats.solved, icon: Trophy },
              { label: 'Total', value: stats.total, icon: Target },
              { label: 'Points', value: stats.totalPoints, icon: Star },
              { label: 'Progress', value: `${stats.completionRate}%`, icon: TrendingUp },
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                whileHover={{ scale: 1.1, y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 + index * 0.1, duration: 0.5 }}
              >
                <div className="w-16 h-16 mx-auto mb-2 glass-card rounded-full flex items-center justify-center">
                  <stat.icon className="w-8 h-8 text-purple-400" />
                </div>
                <div className="text-2xl font-bold text-purple-200">{stat.value}</div>
                <div className="text-sm text-purple-300/70">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Enhanced Search and Controls */}
        <motion.div
          className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.8 }}
        >
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-purple-500/10 to-pink-500/10 rounded-full blur-3xl"></div>
          
          <div className="relative z-10 space-y-6">
            {/* Search Bar */}
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-purple-400" />
                <motion.input
                  type="text"
                  placeholder="Search challenges by name, description, or tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 bg-purple-900/20 border border-purple-500/30 rounded-2xl text-white placeholder-purple-300/50 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 backdrop-blur-sm transition-all duration-300"
                  whileFocus={{ scale: 1.02 }}
                />
                {searchTerm && (
                  <motion.button
                    onClick={() => setSearchTerm('')}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 p-1 hover:bg-purple-500/20 rounded-full transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <X className="w-4 h-4 text-purple-400" />
                  </motion.button>
                )}
              </div>
              
              <motion.button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center space-x-2 px-6 py-4 rounded-2xl font-medium transition-all duration-300 ${
                  showFilters 
                    ? 'bg-purple-500/20 text-purple-300 border border-purple-500/30' 
                    : 'bg-purple-900/20 text-purple-200/80 border border-purple-500/20 hover:bg-purple-500/10'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Filter className="w-5 h-5" />
                <span>Filters</span>
                <motion.div
                  animate={{ rotate: showFilters ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <ChevronDown className="w-4 h-4" />
                </motion.div>
              </motion.button>

              <motion.button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="flex items-center space-x-2 px-6 py-4 bg-blue-600/20 text-blue-400 border border-blue-600/30 rounded-2xl hover:bg-blue-600/30 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  animate={{ rotate: isRefreshing ? 360 : 0 }}
                  transition={{ duration: 1, repeat: isRefreshing ? Infinity : 0, ease: "linear" }}
                >
                  <RefreshCw className="w-5 h-5" />
                </motion.div>
                <span className="hidden sm:inline">Refresh</span>
              </motion.button>

              <div className="flex items-center space-x-4">
                {/* View Mode Toggle */}
                <div className="flex items-center space-x-2">
                  <motion.button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg transition-all duration-300 ${
                      viewMode === 'grid'
                        ? 'bg-purple-500/20 text-purple-400'
                        : 'text-purple-200/60 hover:text-purple-400'
                    }`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Grid className="w-4 h-4" />
                  </motion.button>
                  <motion.button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg transition-all duration-300 ${
                      viewMode === 'list'
                        ? 'bg-purple-500/20 text-purple-400'
                        : 'text-purple-200/60 hover:text-purple-400'
                    }`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <List className="w-4 h-4" />
                  </motion.button>
                </div>

                {/* Group Toggle */}
                <motion.button
                  onClick={() => setGroupByCategory(!groupByCategory)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-300 ${
                    groupByCategory
                      ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/30'
                      : 'bg-gray-500/20 text-gray-400 border border-gray-500/30 hover:bg-emerald-500/10'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Layers className="w-4 h-4" />
                  <span className="text-sm">Group</span>
                </motion.button>

                {/* Stats Toggle */}
                <motion.button
                  onClick={() => setShowStats(!showStats)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-300 ${
                    showStats
                      ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                      : 'bg-gray-500/20 text-gray-400 border border-gray-500/30 hover:bg-blue-500/10'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <BarChart3 className="w-4 h-4" />
                  <span className="text-sm">Stats</span>
                </motion.button>
              </div>
            </div>

            {/* Expandable Filters */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="border-t border-purple-500/20 pt-6"
                >
                  <ChallengeFilters
                    categories={categories}
                    selectedCategory={selectedCategory}
                    selectedDifficulty={selectedDifficulty}
                    showSolved={showSolved}
                    onCategoryChange={setSelectedCategory}
                    onDifficultyChange={setSelectedDifficulty}
                    onShowSolvedChange={setShowSolved}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>

        {/* Enhanced Analytics Dashboard */}
        <AnimatePresence>
          {showStats && (
            <motion.div
              className="grid grid-cols-1 xl:grid-cols-3 gap-8"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
          {/* Category Overview */}
          <motion.div
            className="xl:col-span-2 glass-card-dark p-8 rounded-3xl relative overflow-hidden"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1.4, duration: 0.8 }}
          >
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-3xl"></div>
            <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
              <Layers className="w-8 h-8" />
              <span>Category Mastery</span>
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
              {stats.categoryStats.map((category, index) => {
                const IconComponent = getCategoryIcon(category.icon);

                return (
                  <motion.div
                    key={category.category}
                    className="relative p-6 glass-card rounded-2xl cursor-pointer group"
                    whileHover={{ scale: 1.05, y: -5 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setSelectedCategory(selectedCategory === category.category ? 'all' : category.category)}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.6 + index * 0.1 }}
                  >
                    <div
                      className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300 rounded-2xl"
                      style={{ backgroundColor: category.color }}
                    ></div>
                    <div className="relative z-10 text-center space-y-3">
                      <div
                        className="w-12 h-12 mx-auto rounded-xl flex items-center justify-center"
                        style={{ backgroundColor: category.color }}
                      >
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-purple-200">{category.displayName}</h4>
                      <div className="text-sm text-purple-300/70">
                        {category.solved}/{category.total} solved
                      </div>
                      <div className="w-full bg-purple-900/30 rounded-full h-2">
                        <motion.div
                          className="h-2 rounded-full"
                          style={{ backgroundColor: category.color }}
                          initial={{ width: 0 }}
                          animate={{ width: `${category.percentage}%` }}
                          transition={{ delay: 1.8 + index * 0.1, duration: 1 }}
                        />
                      </div>
                      <div className="text-xs font-medium text-purple-300">{category.percentage}%</div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Difficulty Breakdown */}
          <motion.div
            className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1.5, duration: 0.8 }}
          >
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-purple-500/20 to-pink-500/20 rounded-full blur-3xl"></div>
            <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
              <Gauge className="w-8 h-8" />
              <span>Difficulty</span>
            </h3>
            <div className="space-y-6">
              {stats.difficultyStats.map((difficulty, index) => {
                const ShapeComponent = DIFFICULTY_SHAPES[difficulty.difficulty];
                const colors = {
                  easy: 'from-green-500 to-emerald-500',
                  medium: 'from-yellow-500 to-orange-500',
                  hard: 'from-red-500 to-pink-500',
                  insane: 'from-purple-500 to-indigo-500'
                };
                
                return (
                  <motion.div
                    key={difficulty.difficulty}
                    className="flex items-center justify-between p-4 glass-card rounded-xl cursor-pointer group"
                    whileHover={{ scale: 1.02, x: 5 }}
                    onClick={() => setSelectedDifficulty(selectedDifficulty === difficulty.difficulty ? 'all' : difficulty.difficulty)}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.7 + index * 0.1 }}
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 bg-gradient-to-r ${colors[difficulty.difficulty]} rounded-lg`}>
                        <ShapeComponent className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="font-medium text-purple-200 capitalize">{difficulty.difficulty}</h4>
                        <div className="text-sm text-purple-300/70">
                          {difficulty.solved}/{difficulty.total}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-purple-300">{difficulty.percentage}%</div>
                      <div className="w-16 bg-purple-900/30 rounded-full h-2 mt-1">
                        <motion.div
                          className={`bg-gradient-to-r ${colors[difficulty.difficulty]} h-2 rounded-full`}
                          initial={{ width: 0 }}
                          animate={{ width: `${difficulty.percentage}%` }}
                          transition={{ delay: 1.9 + index * 0.1, duration: 0.8 }}
                        />
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Challenges Display */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.8, duration: 0.8 }}
        >
          <div className="flex items-center justify-between mb-8">
            <motion.h2 
              className="text-3xl font-bold text-purple-300 flex items-center space-x-3"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 2, duration: 0.6 }}
            >
              <Target className="w-8 h-8" />
              <span>{filteredChallenges.length} Challenge{filteredChallenges.length !== 1 ? 's' : ''}</span>
            </motion.h2>
            
            <motion.div
              className="flex items-center space-x-2 text-sm text-purple-300/70"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 2.1, duration: 0.6 }}
            >
              <Activity className="w-4 h-4" />
              <span>Live challenges</span>
              <motion.div
                className="w-2 h-2 bg-green-400 rounded-full"
                animate={{ scale: [1, 1.5, 1], opacity: [1, 0.5, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </motion.div>
          </div>

          <AnimatePresence mode="wait">
            {filteredChallenges.length > 0 ? (
              <motion.div
                key={`challenges-${animationKey}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-12"
              >
                {groupByCategory ? (
                  // Grouped by category view
                  groupedChallenges.map((group, groupIndex) => (
                    <motion.div
                      key={group.category?.id || 'ungrouped'}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: groupIndex * 0.1, duration: 0.5 }}
                      className="space-y-6"
                    >
                      {/* Category Header */}
                      {group.category && (
                        <div className="flex items-center space-x-4 mb-8">
                          <div
                            className="p-3 rounded-xl border"
                            style={getCategoryStyle(group.category.name)}
                          >
                            {React.createElement(getCategoryIcon(group.category.icon), {
                              className: "w-6 h-6"
                            })}
                          </div>
                          <div>
                            <h2 className="text-2xl font-bold text-white">
                              {group.category.displayName}
                            </h2>
                            <p className="text-gray-400">
                              {group.challenges.length} challenge{group.challenges.length !== 1 ? 's' : ''}
                              {group.category.description && ` • ${group.category.description}`}
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Challenges Grid */}
                      <div className={
                        viewMode === 'grid'
                          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                          : 'space-y-4'
                      }>
                        {group.challenges.map((challenge, index) => (
                          <motion.div
                            key={challenge.id}
                            initial={{ opacity: 0, y: 20, scale: 0.9 }}
                            animate={{ opacity: 1, y: 0, scale: 1 }}
                            transition={{
                              delay: (groupIndex * 0.1) + (index * 0.05),
                              duration: 0.4,
                              type: "spring",
                              stiffness: 100
                            }}
                            onHoverStart={() => setHoveredCard(challenge.id)}
                            onHoverEnd={() => setHoveredCard(null)}
                            className="challenge-card-wrapper"
                          >
                            <ChallengeCard challenge={challenge} />
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  ))
                ) : (
                  // Regular ungrouped view
                  <div className={
                    viewMode === 'grid'
                      ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
                      : 'space-y-4'
                  }>
                    {filteredChallenges.map((challenge, index) => (
                      <motion.div
                        key={challenge.id}
                        initial={{ opacity: 0, y: 20, scale: 0.9 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{
                          delay: index * 0.05,
                          duration: 0.4,
                          type: "spring",
                          stiffness: 100
                        }}
                        onHoverStart={() => setHoveredCard(challenge.id)}
                        onHoverEnd={() => setHoveredCard(null)}
                        className="challenge-card-wrapper"
                      >
                        <ChallengeCard challenge={challenge} />
                      </motion.div>
                    ))}
                  </div>
                )}
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-20"
              >
                <motion.div 
                  className="w-32 h-32 mx-auto mb-8 glass-card rounded-full flex items-center justify-center"
                  animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{ 
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <Search className="w-16 h-16 text-purple-400" />
                </motion.div>
                
                <h3 className="text-2xl font-bold text-purple-300 mb-4">No challenges found</h3>
                <p className="text-purple-200/60 mb-8 max-w-md mx-auto">
                  Try adjusting your filters or search terms to discover more challenges
                </p>
                
                <motion.button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('all');
                    setSelectedDifficulty('all');
                    setShowSolved(true);
                  }}
                  className="px-8 py-4 bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-400 border border-purple-600/30 rounded-2xl hover:from-purple-600/30 hover:to-pink-600/30 transition-all duration-300 font-medium"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="flex items-center space-x-2">
                    <RefreshCw className="w-5 h-5" />
                    <span>Clear All Filters</span>
                  </div>
                </motion.button>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      {/* Gradient overlay for depth */}
      <div className="fixed inset-0 bg-gradient-to-t from-purple-900/20 via-transparent to-purple-900/20 pointer-events-none" />
    </div>
  );
}