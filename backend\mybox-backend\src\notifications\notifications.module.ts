import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MulterModule } from '@nestjs/platform-express';
import { NotificationsController } from './notifications.controller';
import { NotificationsService } from './notifications.service';
import { NotificationSoundsService } from './notification-sounds.service';
import { NotificationsGateway } from './notifications.gateway';
import { Notification, NotificationSchema } from '../schemas/notification.schema';
import { NotificationSound, NotificationSoundSchema } from '../schemas/notification-sound.schema';
import { UserNotificationSettings, UserNotificationSettingsSchema } from '../schemas/user-notification-settings.schema';
import { User, UserSchema } from '../schemas/user.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Notification.name, schema: NotificationSchema },
      { name: NotificationSound.name, schema: NotificationSoundSchema },
      { name: UserNotificationSettings.name, schema: UserNotificationSettingsSchema },
      { name: User.name, schema: UserSchema },
    ]),
    MulterModule.register({
      dest: './uploads/notification-sounds',
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  ],
  controllers: [NotificationsController],
  providers: [NotificationsService, NotificationSoundsService, NotificationsGateway],
  exports: [NotificationsService, NotificationSoundsService, NotificationsGateway],
})
export class NotificationsModule {}