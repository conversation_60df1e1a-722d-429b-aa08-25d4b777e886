import { Controller, Get, Param, Query, ParseIntPipe, UseGuards } from '@nestjs/common';
import { LeaderboardService } from './leaderboard.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import {
  LeaderboardQueryDto,
  UserLeaderboardResponseDto,
  TeamLeaderboardResponseDto,
  UserLeaderboardEntryDto,
  TeamLeaderboardEntryDto,
  UserRankingDto,
  TeamRankingDto
} from './dto/leaderboard.dto';

@Controller('leaderboard')
@UseGuards(JwtAuthGuard)
export class LeaderboardController {
  constructor(private readonly leaderboardService: LeaderboardService) {}

  @Get()
  async getUserLeaderboard(@Query() query: LeaderboardQueryDto): Promise<UserLeaderboardResponseDto> {
    return this.leaderboardService.getUserLeaderboard(query);
  }

  @Get('teams')
  async getTeamLeaderboard(@Query() query: LeaderboardQueryDto): Promise<TeamLeaderboardResponseDto> {
    return this.leaderboardService.getTeamLeaderboard(query);
  }

  @Get('top/:limit')
  async getTopUsers(@Param('limit', ParseIntPipe) limit: number): Promise<UserLeaderboardEntryDto[]> {
    // Limit the maximum number to prevent abuse
    const maxLimit = Math.min(limit, 100);
    return this.leaderboardService.getTopUsers(maxLimit);
  }

  @Get('teams/top/:limit')
  async getTopTeams(@Param('limit', ParseIntPipe) limit: number): Promise<TeamLeaderboardEntryDto[]> {
    // Limit the maximum number to prevent abuse
    const maxLimit = Math.min(limit, 100);
    return this.leaderboardService.getTopTeams(maxLimit);
  }

  @Get('user/:id')
  async getUserRanking(@Param('id') userId: string): Promise<UserRankingDto> {
    return this.leaderboardService.getUserRanking(userId);
  }

  @Get('team/:id')
  async getTeamRanking(@Param('id') teamId: string): Promise<TeamRankingDto> {
    return this.leaderboardService.getTeamRanking(teamId);
  }
}
