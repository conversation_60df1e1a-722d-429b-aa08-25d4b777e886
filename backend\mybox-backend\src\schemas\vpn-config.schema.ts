import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type VPNConfigDocument = VPNConfig & Document;

@Schema({ timestamps: true })
export class VPNConfig {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true, unique: true })
  userId: Types.ObjectId;

  @Prop({ required: true, unique: true })
  publicKey: string;

  @Prop({ required: true }) // Will be encrypted before storage
  privateKey: string;

  @Prop({ required: true, unique: true })
  ipAddress: string; // User's VPN IP (e.g., ********)

  @Prop({ required: true, unique: true })
  subnet: string; // User's allocated subnet (e.g., ********/24)

  @Prop({ type: [String], default: [] })
  allowedIPs: string[]; // IPs this user can access

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop()
  lastConnected?: Date;

  @Prop()
  lastDisconnected?: Date;

  @Prop({ default: 0 })
  totalConnections: number;

  @Prop({ default: 0 })
  totalBytesTransferred: number;

  @Prop()
  serverEndpoint?: string; // WireGuard server endpoint

  @Prop({ default: 51820 })
  serverPort: number;

  @Prop()
  dns?: string; // DNS servers for VPN clients

  @Prop({ default: 1420 })
  mtu: number;

  @Prop()
  presharedKey?: string; // Optional pre-shared key for additional security

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>; // Additional configuration data
}

export const VPNConfigSchema = SchemaFactory.createForClass(VPNConfig);

// Indexes for performance
VPNConfigSchema.index({ userId: 1 });
VPNConfigSchema.index({ ipAddress: 1 });
VPNConfigSchema.index({ subnet: 1 });
VPNConfigSchema.index({ isActive: 1 });
VPNConfigSchema.index({ createdAt: -1 });
