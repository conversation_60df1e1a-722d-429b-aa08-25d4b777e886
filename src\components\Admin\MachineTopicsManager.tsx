import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Upload,
  Image as ImageIcon,
  Flag,
  Crown,
  User,
  Globe,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { MachineTemplate } from '../../services/machines';
import machineService from '../../services/machines';
import { getImageUrl } from '../../utils/imageUtils';

interface Topic {
  title: string;
  description: string;
  imageUrl?: string;
  order: number;
}

interface MachineFlag {
  name: string;
  value: string;
  type: 'root' | 'user' | 'www-data' | 'custom';
  points: number;
  description?: string;
  isRequired: boolean;
}

interface MachineTopicsManagerProps {
  machine: MachineTemplate;
  onUpdate: (updatedMachine: Partial<MachineTemplate>) => void;
  onClose: () => void;
}

export function MachineTopicsManager({ machine, onUpdate, onClose }: MachineTopicsManagerProps) {
  const [activeTab, setActiveTab] = useState<'topics' | 'flags'>('topics');
  const [topics, setTopics] = useState<Topic[]>(machine.topics || []);
  const [flags, setFlags] = useState<MachineFlag[]>(machine.flags || []);
  const [editingTopic, setEditingTopic] = useState<number | null>(null);
  const [editingFlag, setEditingFlag] = useState<number | null>(null);
  const [newTopic, setNewTopic] = useState<Topic>({ title: '', description: '', order: 0 });
  const [newFlag, setNewFlag] = useState<MachineFlag>({ 
    name: '', 
    value: '', 
    type: 'custom', 
    points: 10, 
    description: '', 
    isRequired: false 
  });
  const [showAddTopic, setShowAddTopic] = useState(false);
  const [showAddFlag, setShowAddFlag] = useState(false);

  const flagTypeIcons = {
    root: Crown,
    user: User,
    'www-data': Globe,
    custom: Flag
  };

  const flagTypeColors = {
    root: 'text-red-400 bg-red-500/20',
    user: 'text-blue-400 bg-blue-500/20',
    'www-data': 'text-green-400 bg-green-500/20',
    custom: 'text-purple-400 bg-purple-500/20'
  };

  const addTopic = () => {
    if (newTopic.title.trim() && newTopic.description.trim()) {
      const updatedTopics = [...topics, { ...newTopic, order: topics.length }];
      setTopics(updatedTopics);
      setNewTopic({ title: '', description: '', order: 0 });
      setShowAddTopic(false);
    }
  };

  const addFlag = () => {
    if (newFlag.name.trim() && newFlag.value.trim()) {
      const updatedFlags = [...flags, { ...newFlag }];
      setFlags(updatedFlags);
      setNewFlag({ 
        name: '', 
        value: '', 
        type: 'custom', 
        points: 10, 
        description: '', 
        isRequired: false 
      });
      setShowAddFlag(false);
    }
  };

  const deleteTopic = (index: number) => {
    const updatedTopics = topics.filter((_, i) => i !== index);
    setTopics(updatedTopics);
  };

  const deleteFlag = (index: number) => {
    const updatedFlags = flags.filter((_, i) => i !== index);
    setFlags(updatedFlags);
  };

  const updateTopic = (index: number, updatedTopic: Topic) => {
    const updatedTopics = [...topics];
    updatedTopics[index] = updatedTopic;
    setTopics(updatedTopics);
    setEditingTopic(null);
  };

  const updateFlag = (index: number, updatedFlag: MachineFlag) => {
    const updatedFlags = [...flags];
    updatedFlags[index] = updatedFlag;
    setFlags(updatedFlags);
    setEditingFlag(null);
  };

  const handleSave = () => {
    // Validate that at least one flag is required
    const hasRequiredFlag = flags.some(flag => flag.isRequired);
    if (flags.length > 0 && !hasRequiredFlag) {
      alert('At least one flag must be marked as required');
      return;
    }

    onUpdate({
      topics: topics.sort((a, b) => a.order - b.order),
      flags
    });
    onClose();
  };

  const handleImageUpload = async (file: File, topicIndex?: number) => {
    try {
      const result = await machineService.uploadTopicImage(file);

      if (topicIndex !== undefined) {
        const updatedTopics = [...topics];
        updatedTopics[topicIndex].imageUrl = result.imageUrl;
        setTopics(updatedTopics);
      } else {
        setNewTopic({ ...newTopic, imageUrl: result.imageUrl });
      }
    } catch (error) {
      console.error('Error uploading topic image:', error);
      alert('Failed to upload image. Please try again.');
    }
  };

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-white">Manage Machine Content</h2>
              <p className="text-gray-400 mt-1">{machine.name}</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex space-x-4 mt-4">
            <button
              onClick={() => setActiveTab('topics')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'topics'
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              Topics & Hints
            </button>
            <button
              onClick={() => setActiveTab('flags')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'flags'
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              Flags & Scoring
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {activeTab === 'topics' && (
            <div className="space-y-4">
              {/* Add Topic Button */}
              <button
                onClick={() => setShowAddTopic(true)}
                className="w-full p-4 border-2 border-dashed border-gray-600 rounded-lg text-gray-400 hover:text-white hover:border-purple-500 transition-colors flex items-center justify-center space-x-2"
              >
                <Plus className="w-5 h-5" />
                <span>Add New Topic</span>
              </button>

              {/* Add Topic Form */}
              <AnimatePresence>
                {showAddTopic && (
                  <motion.div
                    className="bg-gray-700 p-4 rounded-lg"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                  >
                    <div className="space-y-4">
                      <input
                        type="text"
                        placeholder="Topic title"
                        value={newTopic.title}
                        onChange={(e) => setNewTopic({ ...newTopic, title: e.target.value })}
                        className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
                      />
                      <textarea
                        placeholder="Topic description"
                        value={newTopic.description}
                        onChange={(e) => setNewTopic({ ...newTopic, description: e.target.value })}
                        rows={3}
                        className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none resize-none"
                      />
                      
                      {/* Image Upload */}
                      <div className="flex items-center space-x-4">
                        <label className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-500 rounded-lg cursor-pointer transition-colors">
                          <Upload className="w-4 h-4" />
                          <span>Upload Image</span>
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => e.target.files?.[0] && handleImageUpload(e.target.files[0])}
                            className="hidden"
                          />
                        </label>
                        {newTopic.imageUrl && (
                          <div className="flex items-center space-x-2 text-green-400">
                            <CheckCircle className="w-4 h-4" />
                            <span className="text-sm">Image uploaded</span>
                          </div>
                        )}
                      </div>

                      <div className="flex space-x-2">
                        <button
                          onClick={addTopic}
                          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center space-x-2"
                        >
                          <Save className="w-4 h-4" />
                          <span>Save Topic</span>
                        </button>
                        <button
                          onClick={() => setShowAddTopic(false)}
                          className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Topics List */}
              <div className="space-y-4">
                {topics.map((topic, index) => (
                  <motion.div
                    key={index}
                    className="bg-gray-700 p-4 rounded-lg"
                    layout
                  >
                    {editingTopic === index ? (
                      <div className="space-y-4">
                        <input
                          type="text"
                          value={topic.title}
                          onChange={(e) => updateTopic(index, { ...topic, title: e.target.value })}
                          className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                        />
                        <textarea
                          value={topic.description}
                          onChange={(e) => updateTopic(index, { ...topic, description: e.target.value })}
                          rows={3}
                          className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-purple-500 focus:outline-none resize-none"
                        />
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setEditingTopic(null)}
                            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                          >
                            Save
                          </button>
                          <button
                            onClick={() => setEditingTopic(null)}
                            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            {topic.imageUrl && (
                              <img
                                src={getImageUrl(topic.imageUrl)}
                                alt={topic.title}
                                className="w-12 h-12 object-cover rounded-lg"
                              />
                            )}
                            <div>
                              <h3 className="text-lg font-semibold text-white">{topic.title}</h3>
                              <p className="text-gray-300 mt-1">{topic.description}</p>
                            </div>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setEditingTopic(index)}
                            className="p-2 text-gray-400 hover:text-white hover:bg-gray-600 rounded-lg transition-colors"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => deleteTopic(index)}
                            className="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-600 rounded-lg transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'flags' && (
            <div className="space-y-4">
              {/* Add Flag Button */}
              <button
                onClick={() => setShowAddFlag(true)}
                className="w-full p-4 border-2 border-dashed border-gray-600 rounded-lg text-gray-400 hover:text-white hover:border-purple-500 transition-colors flex items-center justify-center space-x-2"
              >
                <Plus className="w-5 h-5" />
                <span>Add New Flag</span>
              </button>

              {/* Validation Warning */}
              {flags.length > 0 && !flags.some(flag => flag.isRequired) && (
                <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4 flex items-center space-x-3">
                  <AlertCircle className="w-5 h-5 text-yellow-400" />
                  <p className="text-yellow-300">At least one flag must be marked as required</p>
                </div>
              )}

              {/* Add Flag Form */}
              <AnimatePresence>
                {showAddFlag && (
                  <motion.div
                    className="bg-gray-700 p-4 rounded-lg"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <input
                        type="text"
                        placeholder="Flag name (e.g., user.txt)"
                        value={newFlag.name}
                        onChange={(e) => setNewFlag({ ...newFlag, name: e.target.value })}
                        className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
                      />
                      <input
                        type="text"
                        placeholder="Flag value"
                        value={newFlag.value}
                        onChange={(e) => setNewFlag({ ...newFlag, value: e.target.value })}
                        className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
                      />
                      <select
                        value={newFlag.type}
                        onChange={(e) => setNewFlag({ ...newFlag, type: e.target.value as any })}
                        className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-purple-500 focus:outline-none"
                      >
                        <option value="custom">Custom</option>
                        <option value="user">User Flag</option>
                        <option value="root">Root Flag</option>
                        <option value="www-data">WWW-Data Flag</option>
                      </select>
                      <input
                        type="number"
                        placeholder="Points"
                        value={newFlag.points}
                        onChange={(e) => setNewFlag({ ...newFlag, points: parseInt(e.target.value) || 0 })}
                        className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
                      />
                      <textarea
                        placeholder="Flag description (optional)"
                        value={newFlag.description}
                        onChange={(e) => setNewFlag({ ...newFlag, description: e.target.value })}
                        className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none resize-none md:col-span-2"
                        rows={2}
                      />
                      <div className="flex items-center space-x-2 md:col-span-2">
                        <input
                          type="checkbox"
                          id="required"
                          checked={newFlag.isRequired}
                          onChange={(e) => setNewFlag({ ...newFlag, isRequired: e.target.checked })}
                          className="w-4 h-4 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500"
                        />
                        <label htmlFor="required" className="text-white">
                          Required flag (at least one flag must be required)
                        </label>
                      </div>
                    </div>

                    <div className="flex space-x-2 mt-4">
                      <button
                        onClick={addFlag}
                        className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center space-x-2"
                      >
                        <Save className="w-4 h-4" />
                        <span>Save Flag</span>
                      </button>
                      <button
                        onClick={() => setShowAddFlag(false)}
                        className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Flags List */}
              <div className="space-y-4">
                {flags.map((flag, index) => {
                  const IconComponent = flagTypeIcons[flag.type];
                  return (
                    <motion.div
                      key={index}
                      className="bg-gray-700 p-4 rounded-lg"
                      layout
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className={`p-2 rounded-lg ${flagTypeColors[flag.type]}`}>
                            <IconComponent className="w-5 h-5" />
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <h3 className="text-lg font-semibold text-white">{flag.name}</h3>
                              {flag.isRequired && (
                                <span className="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full">
                                  Required
                                </span>
                              )}
                            </div>
                            <p className="text-gray-300 text-sm">{flag.description || 'No description'}</p>
                            <div className="flex items-center space-x-4 mt-1">
                              <span className="text-purple-400 font-medium">{flag.points} points</span>
                              <span className="text-gray-400 text-sm capitalize">{flag.type} flag</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setEditingFlag(index)}
                            className="p-2 text-gray-400 hover:text-white hover:bg-gray-600 rounded-lg transition-colors"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => deleteFlag(index)}
                            className="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-600 rounded-lg transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-700 flex justify-end space-x-4">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center space-x-2"
          >
            <Save className="w-4 h-4" />
            <span>Save Changes</span>
          </button>
        </div>
      </motion.div>
    </motion.div>
  );
}
