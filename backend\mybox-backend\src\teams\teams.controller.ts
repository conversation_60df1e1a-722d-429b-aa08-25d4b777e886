import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { TeamsService } from './teams.service';
import { 
  CreateTeamDto, 
  UpdateTeamDto, 
  JoinTeamDto, 
  TeamResponseDto, 
  TeamStatsDto,
  TransferCaptainshipDto,
  TeamInvitationResponseDto,
  TeamSolveDto,
  TeamMemberDto,
  TeamLeaderboardEntryDto,
  InviteMemberDto
} from './dto/teams.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TeamsEnabledGuard } from '../guards/teams-enabled.guard';

@ApiTags('teams')
@Controller('teams')
@UseGuards(JwtAuthGuard, TeamsEnabledGuard)
@ApiBearerAuth()
export class TeamsController {
  constructor(private readonly teamsService: TeamsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new team' })
  @ApiResponse({ status: 201, description: 'Team created successfully', type: TeamResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'Team name already exists or user already in team' })
  async create(@Request() req: any, @Body() createTeamDto: CreateTeamDto): Promise<TeamResponseDto> {
    return this.teamsService.create(req.user.userId, createTeamDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all teams with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'isPublic', required: false, type: Boolean, description: 'Filter by public/private teams' })
  @ApiResponse({ status: 200, description: 'Teams retrieved successfully' })
  async findAll(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Query('isPublic') isPublic?: string,
  ) {
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const isPublicBool = isPublic !== undefined ? isPublic === 'true' : undefined;
    
    return this.teamsService.findAll(pageNum, limitNum, isPublicBool);
  }

  @Get('my-team')
  @ApiOperation({ summary: 'Get current user\'s team' })
  @ApiResponse({ status: 200, description: 'User team retrieved successfully', type: TeamResponseDto })
  @ApiResponse({ status: 404, description: 'User not in any team' })
  async getMyTeam(@Request() req: any): Promise<TeamResponseDto | null> {
    return this.teamsService.findByUser(req.user.userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get team by ID' })
  @ApiResponse({ status: 200, description: 'Team retrieved successfully', type: TeamResponseDto })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async findOne(@Param('id') id: string): Promise<TeamResponseDto> {
    return this.teamsService.findById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update team (captain only)' })
  @ApiResponse({ status: 200, description: 'Team updated successfully', type: TeamResponseDto })
  @ApiResponse({ status: 403, description: 'Only team captain can update team' })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async update(
    @Param('id') id: string,
    @Request() req: any,
    @Body() updateTeamDto: UpdateTeamDto,
  ): Promise<TeamResponseDto> {
    return this.teamsService.update(id, req.user.userId, updateTeamDto);
  }

  @Post('join')
  @ApiOperation({ summary: 'Join a team' })
  @ApiResponse({ status: 200, description: 'Successfully joined team', type: TeamResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'User already in team or team is full' })
  async joinTeam(@Request() req: any, @Body() joinTeamDto: JoinTeamDto): Promise<TeamResponseDto> {
    return this.teamsService.joinTeam(req.user.userId, joinTeamDto);
  }

  @Post('leave')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Leave current team' })
  @ApiResponse({ status: 204, description: 'Successfully left team' })
  @ApiResponse({ status: 404, description: 'User not in any team' })
  async leaveTeam(@Request() req: any): Promise<void> {
    return this.teamsService.leaveTeam(req.user.userId);
  }

  @Delete(':id/members/:userId')
  @ApiOperation({ summary: 'Remove member from team (captain only)' })
  @ApiResponse({ status: 200, description: 'Member removed successfully', type: TeamResponseDto })
  @ApiResponse({ status: 403, description: 'Only team captain can remove members' })
  @ApiResponse({ status: 404, description: 'Team or member not found' })
  async removeMember(
    @Param('id') teamId: string,
    @Param('userId') memberUserId: string,
    @Request() req: any,
  ): Promise<TeamResponseDto> {
    return this.teamsService.removeMember(teamId, req.user.userId, memberUserId);
  }

  @Get(':id/stats')
  @ApiOperation({ summary: 'Get team statistics' })
  @ApiResponse({ status: 200, description: 'Team stats retrieved successfully', type: TeamStatsDto })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async getTeamStats(@Param('id') id: string): Promise<TeamStatsDto> {
    return this.teamsService.getTeamStats(id);
  }

  @Put(':id/regenerate-invite')
  @ApiOperation({ summary: 'Regenerate team invite code (captain only)' })
  @ApiResponse({ status: 200, description: 'Invite code regenerated successfully' })
  @ApiResponse({ status: 403, description: 'Only team captain can regenerate invite code' })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async regenerateInviteCode(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<{ inviteCode: string }> {
    return this.teamsService.regenerateInviteCode(id, req.user.userId);
  }
  @Post(':id/cleanup-duplicates')
  @ApiOperation({ summary: 'Clean up duplicate team member entries' })
  @ApiResponse({ status: 200, description: 'Duplicate members cleaned up successfully' })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async cleanupDuplicateMembers(@Param('id') teamId: string): Promise<{ message: string }> {
    await this.teamsService.cleanupDuplicateMembers(teamId);
    return { message: 'Duplicate members cleaned up successfully' };
  }
  @Post(':id/transfer/:userId')
  @ApiOperation({ summary: 'Transfer team captainship (captain only)' })
  @ApiResponse({ status: 200, description: 'Captainship transferred successfully', type: TeamResponseDto })
  @ApiResponse({ status: 403, description: 'Only team captain can transfer captainship' })
  @ApiResponse({ status: 404, description: 'Team or user not found' })
  @ApiResponse({ status: 400, description: 'Cannot transfer to user not in team' })
  async transferCaptainship(
    @Param('id') teamId: string,
    @Param('userId') newCaptainId: string,
    @Request() req: any,
  ): Promise<TeamResponseDto> {
    const transferDto: TransferCaptainshipDto = { newCaptainId };
    return this.teamsService.transferCaptainship(teamId, req.user.userId, transferDto);
  }

  @Get(':id/invitations')
  @ApiOperation({ summary: 'Get team invitations (captain only)' })
  @ApiResponse({ status: 200, description: 'Team invitations retrieved successfully', type: [TeamInvitationResponseDto] })
  @ApiResponse({ status: 403, description: 'Only team captain can view invitations' })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async getTeamInvitations(
    @Param('id') teamId: string,
    @Request() req: any,
  ): Promise<TeamInvitationResponseDto[]> {
    return this.teamsService.getTeamInvitations(teamId, req.user.userId);
  }
  @Post(':id/invite')
  @ApiOperation({ summary: 'Invite user to team (captain only)' })
  @ApiResponse({ status: 201, description: 'Invitation sent successfully', type: TeamInvitationResponseDto })
  @ApiResponse({ status: 403, description: 'Only team captain can send invitations' })
  @ApiResponse({ status: 404, description: 'Team or user not found' })
  @ApiResponse({ status: 409, description: 'User already invited or in team' })
  async inviteMember(
    @Param('id') teamId: string,
    @Request() req: any,
    @Body() body: { username: string },
  ): Promise<TeamInvitationResponseDto> {
    const inviteDto: InviteMemberDto = { username: body.username };
    return this.teamsService.inviteMember(teamId, req.user.userId, inviteDto);
  }

  @Get(':id/solves')
  @ApiOperation({ summary: 'Get team challenge solves' })
  @ApiResponse({ status: 200, description: 'Team solves retrieved successfully', type: [TeamSolveDto] })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async getTeamSolves(@Param('id') teamId: string): Promise<TeamSolveDto[]> {
    return this.teamsService.getTeamSolves(teamId);
  }

  @Get(':id/members')
  @ApiOperation({ summary: 'Get team members' })
  @ApiResponse({ status: 200, description: 'Team members retrieved successfully', type: [TeamMemberDto] })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async getTeamMembers(@Param('id') teamId: string): Promise<TeamMemberDto[]> {
    return this.teamsService.getTeamMembers(teamId);
  }
  @Get('leaderboard')
  @ApiOperation({ summary: 'Get team leaderboard' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20)' })
  @ApiResponse({ status: 200, description: 'Team leaderboard retrieved successfully' })
  async getTeamLeaderboard(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
  ): Promise<{ teams: TeamLeaderboardEntryDto[]; total: number; page: number; limit: number }> {
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    return this.teamsService.getTeamLeaderboard(pageNum, limitNum);
  }

  @Put('invitations/:id/accept')
  @ApiOperation({ summary: 'Accept team invitation' })
  @ApiResponse({ status: 200, description: 'Invitation accepted successfully', type: TeamResponseDto })
  @ApiResponse({ status: 404, description: 'Invitation not found' })
  @ApiResponse({ status: 400, description: 'Invitation expired or already processed' })
  @ApiResponse({ status: 409, description: 'User already in a team or team is full' })
  async acceptInvitation(
    @Param('id') invitationId: string,
    @Request() req: any,
  ): Promise<TeamResponseDto> {
    return this.teamsService.acceptInvitation(invitationId, req.user.userId);
  }

  @Put('invitations/:id/decline')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Decline team invitation' })
  @ApiResponse({ status: 204, description: 'Invitation declined successfully' })
  @ApiResponse({ status: 404, description: 'Invitation not found' })
  @ApiResponse({ status: 400, description: 'Invitation expired or already processed' })
  async declineInvitation(
    @Param('id') invitationId: string,
    @Request() req: any,
  ): Promise<void> {
    return this.teamsService.declineInvitation(invitationId, req.user.userId);
  }

  @Delete('invitations/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Cancel team invitation (captain only)' })
  @ApiResponse({ status: 204, description: 'Invitation cancelled successfully' })
  @ApiResponse({ status: 403, description: 'Only team captain can cancel invitations' })
  @ApiResponse({ status: 404, description: 'Invitation not found' })
  async cancelInvitation(
    @Param('id') invitationId: string,
    @Request() req: any,
  ): Promise<void> {
    return this.teamsService.cancelInvitation(invitationId, req.user.userId);
  }
}