import React, { useState } from 'react';
import { useApp } from '../../contexts/AppContext';
import { 
  User, 
  Mail, 
  Calendar, 
  Trophy, 
  Target,
  Key,
  Edit,
  Save,
  X,
  Eye,
  EyeOff
} from 'lucide-react';

export function Profile() {
  const { state, updateProfile } = useApp();
  const [isEditing, setIsEditing] = useState(false);
  const [showApiToken, setShowApiToken] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    username: state.auth.user?.username || '',
    email: state.auth.user?.email || '',
    avatar: state.auth.user?.avatar || ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      await updateProfile(formData);
      setIsEditing(false);
    } catch (error) {
      console.error('Profile update error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      username: state.auth.user?.username || '',
      email: state.auth.user?.email || '',
      avatar: state.auth.user?.avatar || ''
    });
    setIsEditing(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const generateNewToken = async () => {
    const newToken = 'htb_api_token_' + Math.random().toString(36).substring(2, 15);
    await updateProfile({ apiToken: newToken });
  };

  const user = state.auth.user;
  if (!user) return null;

  const solvedChallenges = state.challenges.filter(c => c.isSolved);
  const totalPoints = solvedChallenges.reduce((sum, c) => sum + c.points, 0);

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">Profile Settings</h1>
        <p className="text-slate-400">
          Manage your account and preferences
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">Profile Information</h2>
              {!isEditing && (
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-emerald-600/20 text-emerald-400 rounded-lg hover:bg-emerald-600/30 transition-colors"
                >
                  <Edit className="w-4 h-4" />
                  <span>Edit</span>
                </button>
              )}
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="flex items-center space-x-6">
                <div className="flex-shrink-0">
                  <img
                    src={user.avatar || `https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1`}
                    alt={user.username}
                    className="w-20 h-20 rounded-full object-cover"
                  />
                </div>
                {isEditing && (
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Avatar URL
                    </label>
                    <input
                      type="url"
                      name="avatar"
                      value={formData.avatar}
                      onChange={handleChange}
                      className="w-full px-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                      placeholder="Enter avatar URL"
                    />
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Username
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      className="w-full px-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                      required
                    />
                  ) : (
                    <div className="flex items-center space-x-2 px-4 py-2 bg-slate-800/50 rounded-lg">
                      <User className="w-4 h-4 text-slate-400" />
                      <span className="text-white">{user.username}</span>
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Email
                  </label>
                  {isEditing ? (
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400"
                      required
                    />
                  ) : (
                    <div className="flex items-center space-x-2 px-4 py-2 bg-slate-800/50 rounded-lg">
                      <Mail className="w-4 h-4 text-slate-400" />
                      <span className="text-white">{user.email}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2 px-4 py-2 bg-slate-800/50 rounded-lg">
                <Calendar className="w-4 h-4 text-slate-400" />
                <span className="text-slate-300">Joined {user.joinedDate}</span>
              </div>

              {isEditing && (
                <div className="flex space-x-4">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="flex items-center space-x-2 px-6 py-2 bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors"
                  >
                    {isLoading ? (
                      <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                    ) : (
                      <Save className="w-4 h-4" />
                    )}
                    <span>Save Changes</span>
                  </button>
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="flex items-center space-x-2 px-6 py-2 bg-slate-700 hover:bg-slate-600 text-white font-medium rounded-lg transition-colors"
                  >
                    <X className="w-4 h-4" />
                    <span>Cancel</span>
                  </button>
                </div>
              )}
            </form>
          </div>

          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <Key className="w-6 h-6 text-orange-400" />
              <h2 className="text-xl font-semibold text-white">API Token</h2>
            </div>

            <div className="space-y-4">
              <p className="text-sm text-slate-400">
                Use this token to authenticate with the MyBox API for automated scripts and tools.
              </p>
              
              <div className="flex items-center space-x-2">
                <div className="flex-1 px-4 py-2 bg-slate-800/50 border border-slate-700 rounded-lg font-mono text-sm">
                  {showApiToken ? user.apiToken : '•'.repeat(user.apiToken?.length || 0)}
                </div>
                <button
                  onClick={() => setShowApiToken(!showApiToken)}
                  className="p-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors"
                >
                  {showApiToken ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>

              <button
                onClick={generateNewToken}
                className="px-4 py-2 bg-orange-600/20 text-orange-400 border border-orange-600/30 rounded-lg hover:bg-orange-600/30 transition-colors text-sm font-medium"
              >
                Generate New Token
              </button>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <Trophy className="w-6 h-6 text-emerald-400" />
              <h2 className="text-xl font-semibold text-white">Statistics</h2>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Total Score</span>
                <span className="text-xl font-bold text-emerald-400">{user.score}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Global Rank</span>
                <span className="text-xl font-bold text-purple-400">#{user.rank}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Challenges Solved</span>
                <span className="text-xl font-bold text-blue-400">{solvedChallenges.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Points from Challenges</span>
                <span className="text-xl font-bold text-orange-400">{totalPoints}</span>
              </div>
            </div>
          </div>

          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <Target className="w-6 h-6 text-purple-400" />
              <h2 className="text-xl font-semibold text-white">Recent Achievements</h2>
            </div>

            <div className="space-y-3">
              {solvedChallenges.slice(0, 3).map((challenge) => (
                <div key={challenge.id} className="flex items-center space-x-3 p-3 bg-slate-800/50 rounded-lg">
                  <div className="flex-shrink-0 w-8 h-8 bg-emerald-500/20 rounded-full flex items-center justify-center">
                    <Trophy className="w-4 h-4 text-emerald-400" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-white">{challenge.title}</p>
                    <p className="text-xs text-slate-400">+{challenge.points} points</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}