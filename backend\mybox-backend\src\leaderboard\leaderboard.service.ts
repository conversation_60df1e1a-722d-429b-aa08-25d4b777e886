import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { User } from '../schemas/user.schema';
import { Team } from '../schemas/team.schema';
import { ChallengeSubmission } from '../schemas/challenge-submission.schema';
import { TeamChallengeSolve } from '../schemas/team-challenge-solve.schema';
import {
  LeaderboardQueryDto,
  UserLeaderboardEntryDto,
  TeamLeaderboardEntryDto,
  UserLeaderboardResponseDto,
  TeamLeaderboardResponseDto,
  UserRankingDto,
  TeamRankingDto
} from './dto/leaderboard.dto';

@Injectable()
export class LeaderboardService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Team.name) private teamModel: Model<Team>,
    @InjectModel(ChallengeSubmission.name) private challengeSubmissionModel: Model<ChallengeSubmission>,
    @InjectModel(TeamChallengeSolve.name) private teamChallengeSolveModel: Model<TeamChallengeSolve>,
  ) {}

  async getUserLeaderboard(query: LeaderboardQueryDto): Promise<UserLeaderboardResponseDto> {
    const { page = 1, limit = 20 } = query;
    const skip = (page - 1) * limit;

    // Get users with their stats
    const [users, total] = await Promise.all([
      this.userModel.aggregate([
        {
          $lookup: {
            from: 'challengesubmissions',
            localField: '_id',
            foreignField: 'userId',
            as: 'submissions'
          }
        },
        {
          $addFields: {
            solvedChallenges: {
              $size: {
                $filter: {
                  input: '$submissions',
                  cond: { $eq: ['$$this.isCorrect', true] }
                }
              }
            },
            totalSubmissions: { $size: '$submissions' },
            lastSolved: {
              $max: {
                $map: {
                  input: {
                    $filter: {
                      input: '$submissions',
                      cond: { $eq: ['$$this.isCorrect', true] }
                    }
                  },
                  as: 'submission',
                  in: '$$submission.submittedAt'
                }
              }
            }
          }
        },
        { $sort: { score: -1, lastSolved: 1, _id: 1 } },
        { $skip: skip },
        { $limit: limit },
        {
          $project: {
            _id: 1,
            username: 1,
            email: 1,
            avatar: 1,
            score: 1,
            solvedChallenges: 1,
            totalSubmissions: 1,
            lastSolved: 1
          }
        }
      ]),
      this.userModel.countDocuments()
    ]);

    // Add ranks to users
    const usersWithRank: UserLeaderboardEntryDto[] = users.map((user, index) => ({
      userId: user._id.toString(),
      username: user.username,
      email: user.email,
      avatar: user.avatar,
      score: user.score || 0,
      rank: skip + index + 1,
      solvedChallenges: user.solvedChallenges || 0,
      lastSolved: user.lastSolved,
      totalSubmissions: user.totalSubmissions || 0
    }));

    return {
      users: usersWithRank,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  async getTeamLeaderboard(query: LeaderboardQueryDto): Promise<TeamLeaderboardResponseDto> {
    const { page = 1, limit = 20 } = query;
    const skip = (page - 1) * limit;

    // Get teams with their stats
    const [teams, total] = await Promise.all([
      this.teamModel.aggregate([
        {
          $match: { 'members.status': 'active' }
        },
        {
          $lookup: {
            from: 'teamchallengesolves',
            localField: '_id',
            foreignField: 'teamId',
            as: 'solves'
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'captainId',
            foreignField: '_id',
            as: 'captain'
          }
        },
        {
          $addFields: {
            solvedChallenges: { $size: '$solves' },
            lastSolved: { $max: '$solves.solvedAt' },
            memberCount: {
              $size: {
                $filter: {
                  input: '$members',
                  cond: { $eq: ['$$this.status', 'active'] }
                }
              }
            }
          }
        },
        { $sort: { teamScore: -1, lastSolved: 1, _id: 1 } },
        { $skip: skip },
        { $limit: limit },
        {
          $project: {
            _id: 1,
            name: 1,
            teamScore: 1,
            memberCount: 1,
            solvedChallenges: 1,
            lastSolved: 1,
            captain: { $arrayElemAt: ['$captain', 0] }
          }
        }
      ]),
      this.teamModel.countDocuments({ 'members.status': 'active' })
    ]);

    // Add ranks to teams
    const teamsWithRank: TeamLeaderboardEntryDto[] = teams.map((team, index) => ({
      teamId: team._id.toString(),
      teamName: team.name,
      teamScore: team.teamScore || 0,
      rank: skip + index + 1,
      memberCount: team.memberCount,
      solvedChallenges: team.solvedChallenges || 0,
      lastSolved: team.lastSolved,
      captain: {
        id: team.captain._id.toString(),
        username: team.captain.username
      }
    }));

    return {
      teams: teamsWithRank,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  async getTopUsers(limit: number): Promise<UserLeaderboardEntryDto[]> {
    const users = await this.userModel.aggregate([
      {
        $lookup: {
          from: 'challengesubmissions',
          localField: '_id',
          foreignField: 'userId',
          as: 'submissions'
        }
      },
      {
        $addFields: {
          solvedChallenges: {
            $size: {
              $filter: {
                input: '$submissions',
                cond: { $eq: ['$$this.isCorrect', true] }
              }
            }
          },
          totalSubmissions: { $size: '$submissions' },
          lastSolved: {
            $max: {
              $map: {
                input: {
                  $filter: {
                    input: '$submissions',
                    cond: { $eq: ['$$this.isCorrect', true] }
                  }
                },
                as: 'submission',
                in: '$$submission.submittedAt'
              }
            }
          }
        }
      },
      { $sort: { score: -1, lastSolved: 1, _id: 1 } },
      { $limit: limit },
      {
        $project: {
          _id: 1,
          username: 1,
          email: 1,
          avatar: 1,
          score: 1,
          solvedChallenges: 1,
          totalSubmissions: 1,
          lastSolved: 1
        }
      }
    ]);

    return users.map((user, index) => ({
      userId: user._id.toString(),
      username: user.username,
      email: user.email,
      avatar: user.avatar,
      score: user.score || 0,
      rank: index + 1,
      solvedChallenges: user.solvedChallenges || 0,
      lastSolved: user.lastSolved,
      totalSubmissions: user.totalSubmissions || 0
    }));
  }

  async getTopTeams(limit: number): Promise<TeamLeaderboardEntryDto[]> {
    const teams = await this.teamModel.aggregate([
      {
        $match: { 'members.status': 'active' }
      },
      {
        $lookup: {
          from: 'teamchallengesolves',
          localField: '_id',
          foreignField: 'teamId',
          as: 'solves'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'captainId',
          foreignField: '_id',
          as: 'captain'
        }
      },
      {
        $addFields: {
          solvedChallenges: { $size: '$solves' },
          lastSolved: { $max: '$solves.solvedAt' },
          memberCount: {
            $size: {
              $filter: {
                input: '$members',
                cond: { $eq: ['$$this.status', 'active'] }
              }
            }
          }
        }
      },
      { $sort: { teamScore: -1, lastSolved: 1, _id: 1 } },
      { $limit: limit },
      {
        $project: {
          _id: 1,
          name: 1,
          teamScore: 1,
          memberCount: 1,
          solvedChallenges: 1,
          lastSolved: 1,
          captain: { $arrayElemAt: ['$captain', 0] }
        }
      }
    ]);

    return teams.map((team, index) => ({
      teamId: team._id.toString(),
      teamName: team.name,
      teamScore: team.teamScore || 0,
      rank: index + 1,
      memberCount: team.memberCount,
      solvedChallenges: team.solvedChallenges || 0,
      lastSolved: team.lastSolved,
      captain: {
        id: team.captain._id.toString(),
        username: team.captain.username
      }
    }));
  }

  async getUserRanking(userId: string): Promise<UserRankingDto> {
    if (!Types.ObjectId.isValid(userId)) {
      throw new NotFoundException('Invalid user ID');
    }

    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get user's stats
    const userStats = await this.userModel.aggregate([
      { $match: { _id: new Types.ObjectId(userId) } },
      {
        $lookup: {
          from: 'challengesubmissions',
          localField: '_id',
          foreignField: 'userId',
          as: 'submissions'
        }
      },
      {
        $addFields: {
          solvedChallenges: {
            $size: {
              $filter: {
                input: '$submissions',
                cond: { $eq: ['$$this.isCorrect', true] }
              }
            }
          },
          lastSolved: {
            $max: {
              $map: {
                input: {
                  $filter: {
                    input: '$submissions',
                    cond: { $eq: ['$$this.isCorrect', true] }
                  }
                },
                as: 'submission',
                in: '$$submission.submittedAt'
              }
            }
          }
        }
      }
    ]);

    if (!userStats.length) {
      throw new NotFoundException('User stats not found');
    }

    const userStat = userStats[0];

    // Calculate rank
    const rank = await this.userModel.countDocuments({
      $or: [
        { score: { $gt: user.score } },
        { 
          score: user.score, 
          _id: { $lt: new Types.ObjectId(userId) }
        }
      ]
    }) + 1;

    // Calculate percentile
    const totalUsers = await this.userModel.countDocuments();
    const percentile = totalUsers > 1 ? Math.round(((totalUsers - rank + 1) / totalUsers) * 100) : 100;    return {
      userId: (user._id as any).toString(),
      username: user.username,
      score: user.score || 0,
      rank,
      solvedChallenges: userStat.solvedChallenges || 0,
      lastSolved: userStat.lastSolved,
      percentile
    };
  }

  async getTeamRanking(teamId: string): Promise<TeamRankingDto> {
    if (!Types.ObjectId.isValid(teamId)) {
      throw new NotFoundException('Invalid team ID');
    }

    const team = await this.teamModel.findById(teamId).populate('captainId', 'username');
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    // Get team's stats
    const teamStats = await this.teamModel.aggregate([
      { $match: { _id: new Types.ObjectId(teamId) } },
      {
        $lookup: {
          from: 'teamchallengesolves',
          localField: '_id',
          foreignField: 'teamId',
          as: 'solves'
        }
      },
      {
        $addFields: {
          solvedChallenges: { $size: '$solves' },
          lastSolved: { $max: '$solves.solvedAt' },
          memberCount: {
            $size: {
              $filter: {
                input: '$members',
                cond: { $eq: ['$$this.status', 'active'] }
              }
            }
          }
        }
      }
    ]);

    if (!teamStats.length) {
      throw new NotFoundException('Team stats not found');
    }

    const teamStat = teamStats[0];

    // Calculate rank
    const rank = await this.teamModel.countDocuments({
      'members.status': 'active',
      $or: [
        { teamScore: { $gt: team.teamScore } },
        { 
          teamScore: team.teamScore, 
          _id: { $lt: new Types.ObjectId(teamId) }
        }
      ]
    }) + 1;

    // Calculate percentile
    const totalTeams = await this.teamModel.countDocuments({ 'members.status': 'active' });
    const percentile = totalTeams > 1 ? Math.round(((totalTeams - rank + 1) / totalTeams) * 100) : 100;    return {
      teamId: (team._id as any).toString(),
      teamName: team.name,
      teamScore: team.teamScore || 0,
      rank,
      memberCount: teamStat.memberCount,
      solvedChallenges: teamStat.solvedChallenges || 0,
      lastSolved: teamStat.lastSolved,
      percentile
    };
  }
}
