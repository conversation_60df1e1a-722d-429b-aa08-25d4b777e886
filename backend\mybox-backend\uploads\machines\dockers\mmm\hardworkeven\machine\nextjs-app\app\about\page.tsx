import { Award, Target, Heart, Users } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

export default function About() {
  const values = [
    {
      icon: <Target className="h-8 w-8" />,
      title: "Excellence",
      description: "We strive for excellence in everything we do, from our equipment to our service."
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: "Passion",
      description: "Our passion for fitness drives us to help you achieve your personal best."
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "Community",
      description: "We believe in building a supportive community where everyone feels welcome."
    },
    {
      icon: <Award className="h-8 w-8" />,
      title: "Results",
      description: "We're committed to helping you achieve real, lasting results."
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl font-bold mb-6">About Hardwork Gym</h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              More than just a gym - we're a community dedicated to transforming lives through fitness.
            </p>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Our Story</h2>
              <p className="text-lg text-gray-600 mb-6">
                Founded in 2015, Hardwork Gym was born from a simple belief: everyone deserves access 
                to top-quality fitness facilities and expert guidance. What started as a small neighborhood 
                gym has grown into a thriving community of fitness enthusiasts.
              </p>
              <p className="text-lg text-gray-600 mb-6">
                Our founders, Sarah and Mike Johnson, were passionate athletes who noticed a gap in 
                the fitness industry. They wanted to create a space that combined state-of-the-art 
                equipment with a welcoming, non-intimidating environment.
              </p>
              <p className="text-lg text-gray-600">
                Today, we're proud to serve over 2,000 members and have helped countless individuals 
                achieve their fitness goals, from weight loss and muscle building to athletic performance 
                and general wellness.
              </p>
            </div>
            <div className="relative">
              <img 
                src="https://images.pexels.com/photos/1552242/pexels-photo-1552242.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop"
                alt="Gym interior"
                className="rounded-lg shadow-lg w-full h-96 object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Values</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              These core values guide everything we do and shape the experience we create for our members.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="border-0 shadow-lg text-center">
                <CardContent className="p-8">
                  <div className="bg-black text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                    {value.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {value.title}
                  </h3>
                  <p className="text-gray-600">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">Our Mission</h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            To empower individuals to achieve their full potential through fitness, providing world-class 
            facilities, expert guidance, and a supportive community that inspires transformation and 
            promotes lasting wellness.
          </p>
        </div>
      </section>
    </div>
  );
}