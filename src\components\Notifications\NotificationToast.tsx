import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Bell, Trophy, MessageSquare, AlertCircle, Users, Info, Crown, Zap, Server } from 'lucide-react';
import { Notification } from '../../services/notifications';

interface NotificationToastProps {
  notification: Notification;
  onClose: () => void;
  autoClose?: boolean;
  duration?: number;
}

const typeIcons = {
  admin_message: MessageSquare,
  first_blood: Trophy,
  challenge_solved: Trophy,
  new_challenge: Zap,
  new_machine: Server,
  machine_first_blood: Trophy,
  system_announcement: AlertCircle,
  team_invitation: Users,
  competition_update: Info
};

const typeColors = {
  admin_message: {
    bg: 'from-purple-500/90 to-purple-600/90',
    border: 'border-purple-400/50',
    icon: 'text-purple-200'
  },
  first_blood: {
    bg: 'from-yellow-500/90 to-orange-500/90',
    border: 'border-yellow-400/50',
    icon: 'text-yellow-200'
  },
  challenge_solved: {
    bg: 'from-green-500/90 to-emerald-500/90',
    border: 'border-green-400/50',
    icon: 'text-green-200'
  },
  new_challenge: {
    bg: 'from-emerald-500/90 to-teal-500/90',
    border: 'border-emerald-400/50',
    icon: 'text-emerald-200'
  },
  new_machine: {
    bg: 'from-blue-500/90 to-indigo-500/90',
    border: 'border-blue-400/50',
    icon: 'text-blue-200'
  },
  machine_first_blood: {
    bg: 'from-yellow-500/90 to-orange-500/90',
    border: 'border-yellow-400/50',
    icon: 'text-yellow-200'
  },
  system_announcement: {
    bg: 'from-blue-500/90 to-cyan-500/90',
    border: 'border-blue-400/50',
    icon: 'text-blue-200'
  },
  team_invitation: {
    bg: 'from-pink-500/90 to-rose-500/90',
    border: 'border-pink-400/50',
    icon: 'text-pink-200'
  },
  competition_update: {
    bg: 'from-indigo-500/90 to-purple-500/90',
    border: 'border-indigo-400/50',
    icon: 'text-indigo-200'
  }
};

export function NotificationToast({ 
  notification, 
  onClose, 
  autoClose = true, 
  duration = 5000 
}: NotificationToastProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [progress, setProgress] = useState(100);

  const TypeIcon = typeIcons[notification.type] || Bell;
  const colors = typeColors[notification.type] || typeColors.system_announcement;

  useEffect(() => {
    if (!autoClose) return;

    const startTime = Date.now();
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const remaining = Math.max(0, duration - elapsed);
      const progressPercent = (remaining / duration) * 100;
      
      setProgress(progressPercent);
      
      if (remaining <= 0) {
        setIsVisible(false);
        setTimeout(onClose, 300); // Wait for exit animation
        clearInterval(interval);
      }
    }, 50);

    return () => clearInterval(interval);
  }, [autoClose, duration, onClose]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300);
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, x: 400, scale: 0.8 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          exit={{ opacity: 0, x: 400, scale: 0.8 }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 30,
            duration: 0.3
          }}
          className={`relative w-96 max-w-sm bg-gradient-to-r ${colors.bg} backdrop-blur-xl border ${colors.border} rounded-xl shadow-2xl overflow-hidden`}
          style={{ zIndex: 9999 }}
        >
          {/* Special effects for first blood */}
          {(notification.type === 'first_blood' || notification.type === 'machine_first_blood') && (
            <>
              {/* Golden glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 animate-pulse" />
              
              {/* Floating particles */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {[...Array(8)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 bg-yellow-300 rounded-full"
                    initial={{ 
                      x: Math.random() * 100 + '%',
                      y: '100%',
                      opacity: 0
                    }}
                    animate={{ 
                      y: '-10%',
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0]
                    }}
                    transition={{ 
                      duration: 2,
                      delay: i * 0.2,
                      repeat: Infinity,
                      repeatDelay: 3
                    }}
                  />
                ))}
              </div>
              
              {/* Crown icon for first blood */}
              <div className="absolute top-2 right-12">
                <Crown className="w-5 h-5 text-yellow-300 animate-bounce" />
              </div>
            </>
          )}

          {/* Special effects for new challenge */}
          {notification.type === 'new_challenge' && (
            <>
              {/* Green glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/20 to-teal-400/20 animate-pulse" />
              
              {/* Electric sparks */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-0.5 h-4 bg-emerald-300 rounded-full"
                    initial={{ 
                      x: Math.random() * 100 + '%',
                      y: Math.random() * 100 + '%',
                      opacity: 0,
                      rotate: Math.random() * 360
                    }}
                    animate={{ 
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0]
                    }}
                    transition={{ 
                      duration: 1.5,
                      delay: i * 0.3,
                      repeat: Infinity,
                      repeatDelay: 2
                    }}
                  />
                ))}
              </div>
              
              {/* Lightning bolt for new challenge */}
              <div className="absolute top-2 right-12">
                <Zap className="w-5 h-5 text-emerald-300 animate-pulse" />
              </div>
            </>
          )}

          {/* Special effects for new machine */}
          {notification.type === 'new_machine' && (
            <>
              {/* Blue glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 animate-pulse" />
              
              {/* Digital particles */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 bg-blue-300 rounded-full"
                    initial={{ 
                      x: Math.random() * 100 + '%',
                      y: Math.random() * 100 + '%',
                      opacity: 0
                    }}
                    animate={{ 
                      opacity: [0, 1, 0],
                      scale: [0, 1.5, 0]
                    }}
                    transition={{ 
                      duration: 2,
                      delay: i * 0.4,
                      repeat: Infinity,
                      repeatDelay: 2.5
                    }}
                  />
                ))}
              </div>
              
              {/* Server icon for new machine */}
              <div className="absolute top-2 right-12">
                <Server className="w-5 h-5 text-blue-300 animate-pulse" />
              </div>
            </>
          )}

          {/* Progress bar */}
          {autoClose && (
            <div className="absolute top-0 left-0 right-0 h-1 bg-black/20">
              <motion.div
                className="h-full bg-white/40"
                initial={{ width: '100%' }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.1, ease: 'linear' }}
              />
            </div>
          )}

          {/* Close button */}
          <button
            onClick={handleClose}
            className="absolute top-3 right-3 p-1 rounded-full bg-black/20 hover:bg-black/40 transition-colors z-10"
          >
            <X className="w-4 h-4 text-white" />
          </button>

          {/* Content */}
          <div className="p-4 pt-6">
            <div className="flex items-start space-x-3">
              {/* Icon */}
              <div className={`p-2 rounded-lg bg-black/20 ${colors.icon} flex-shrink-0`}>
                <TypeIcon className="w-5 h-5" />
              </div>

              {/* Text content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-semibold text-white text-sm truncate">
                    {notification.title}
                  </h4>
                  <span className="text-xs text-white/70 ml-2 flex-shrink-0">
                    {formatTimeAgo(notification.createdAt)}
                  </span>
                </div>

                <p className="text-white/90 text-sm leading-relaxed mb-2 line-clamp-2">
                  {notification.message}
                </p>

                {/* Metadata */}
                {(notification.metadata.challengeName || notification.metadata.machineName || notification.metadata.challengePoints || notification.metadata.machinePoints || notification.metadata.challengeDifficulty || notification.metadata.machineDifficulty || notification.metadata.challengeCategory || notification.metadata.machineOs || notification.metadata.points || notification.metadata.username) && (
                  <div className="flex flex-wrap items-center gap-2 text-xs">
                    {/* Challenge/Machine Name */}
                    {(notification.metadata.challengeName || notification.metadata.machineName) && (
                      <div className="flex items-center space-x-1 bg-black/20 px-2 py-1 rounded-full">
                        <Trophy className="w-3 h-3" />
                        <span className="text-white/80">{notification.metadata.challengeName || notification.metadata.machineName}</span>
                      </div>
                    )}
                    
                    {/* Difficulty */}
                    {(notification.metadata.challengeDifficulty || notification.metadata.machineDifficulty) && (
                      <div className="flex items-center space-x-1 bg-black/20 px-2 py-1 rounded-full">
                        <span className="text-white/80 font-medium">{(notification.metadata.challengeDifficulty || notification.metadata.machineDifficulty).toUpperCase()}</span>
                      </div>
                    )}
                    
                    {/* Category/OS */}
                    {(notification.metadata.challengeCategory || notification.metadata.machineOs) && (
                      <div className="flex items-center space-x-1 bg-black/20 px-2 py-1 rounded-full">
                        <span className="text-white/80">{notification.metadata.challengeCategory || notification.metadata.machineOs}</span>
                      </div>
                    )}
                    
                    {/* Points */}
                    {(notification.metadata.points || notification.metadata.challengePoints || notification.metadata.machinePoints) && (
                      <div className="flex items-center space-x-1 bg-black/20 px-2 py-1 rounded-full">
                        <span className="text-white/80 font-medium">+{notification.metadata.points || notification.metadata.challengePoints || notification.metadata.machinePoints} pts</span>
                      </div>
                    )}
                    
                    {/* Username */}
                    {notification.metadata.username && (
                      <div className="flex items-center space-x-1 bg-black/20 px-2 py-1 rounded-full">
                        <Users className="w-3 h-3" />
                        <span className="text-white/80">{notification.metadata.username}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Hover effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/5 to-white/0 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
        </motion.div>
      )}
    </AnimatePresence>
  );
}