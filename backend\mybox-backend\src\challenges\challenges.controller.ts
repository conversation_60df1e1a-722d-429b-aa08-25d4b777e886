import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Query,
  HttpStatus,
  HttpException,
  ValidationPipe
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ChallengesService } from './challenges.service';
import { PublicCreateChallengeDto, PublicUpdateChallengeDto, FlagSubmissionDto, ChallengePaginationDto } from './dto/challenges.dto';
import { Roles } from '../auth/roles.decorator';
import { RolesGuard } from '../auth/roles.guard';
import { GetUser } from '../users/get-user.decorator';

@Controller('challenges')
export class ChallengesController {
  constructor(private readonly challengesService: ChallengesService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  async getAllChallenges(@Query(new ValidationPipe({ transform: true })) query: ChallengePaginationDto, @GetUser() user: any) {
    return this.challengesService.getAllChallenges(query, user);
  }
  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async createChallenge(@Body() createChallengeDto: PublicCreateChallengeDto, @GetUser() user: any) {
    return this.challengesService.createChallenge(createChallengeDto, user._id);
  }

  @Post('submit-flag')
  @UseGuards(JwtAuthGuard)
  async submitFlag(@Body() flagSubmissionDto: FlagSubmissionDto, @GetUser() user: any) {
    try {
      return await this.challengesService.submitFlag(flagSubmissionDto, user);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('categories')
  @UseGuards(JwtAuthGuard)
  async getCategories() {
    return this.challengesService.getCategories();
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard)
  async getChallengeStats(@GetUser() user: any) {
    return this.challengesService.getChallengeStats(user);
  }

  @Get('solved')
  @UseGuards(JwtAuthGuard)
  async getSolvedChallenges(@GetUser() user: any) {
    return this.challengesService.getSolvedChallenges(user);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async getChallengeById(@Param('id') id: string, @GetUser() user: any) {
    const challenge = await this.challengesService.getChallengeById(id, user);
    if (!challenge) {
      throw new HttpException('Challenge not found', HttpStatus.NOT_FOUND);
    }
    return challenge;
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async updateChallenge(@Param('id') id: string, @Body() updateChallengeDto: PublicUpdateChallengeDto) {
    const challenge = await this.challengesService.updateChallenge(id, updateChallengeDto);
    if (!challenge) {
      throw new HttpException('Challenge not found', HttpStatus.NOT_FOUND);
    }
    return challenge;
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async deleteChallenge(@Param('id') id: string) {
    const result = await this.challengesService.deleteChallenge(id);
    if (!result) {
      throw new HttpException('Challenge not found', HttpStatus.NOT_FOUND);
    }
    return { message: 'Challenge deleted successfully' };
  }
}
