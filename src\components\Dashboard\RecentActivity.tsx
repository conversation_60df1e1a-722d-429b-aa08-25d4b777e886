import React, { useState, useEffect } from 'react';
import { Trophy, Flag, Play, Clock, Zap, Star, Award, Users, ChevronLeft, ChevronRight } from 'lucide-react';
import { dashboardStatsService, ActivityItem, UserActivityStats } from '../../services/dashboardStats';

const getActivityIcon = (type: ActivityItem['type']) => {
  switch (type) {
    case 'challenge_solved':
      return <Flag className="w-5 h-5 text-purple-400" />;
    case 'vm_started':
      return <Play className="w-5 h-5 text-blue-400" />;
    case 'rank_up':
      return <Trophy className="w-5 h-5 text-pink-400" />;
    case 'first_blood':
      return <Award className="w-5 h-5 text-orange-400" />;
    case 'team_joined':
      return <Users className="w-5 h-5 text-green-400" />;
    default:
      return <Star className="w-5 h-5 text-purple-400" />;
  }
};

const getActivityColor = (type: ActivityItem['type']) => {
  switch (type) {
    case 'challenge_solved':
      return 'from-purple-500/20 to-purple-600/20 border-purple-500/30';
    case 'vm_started':
      return 'from-blue-500/20 to-blue-600/20 border-blue-500/30';
    case 'rank_up':
      return 'from-pink-500/20 to-pink-600/20 border-pink-500/30';
    case 'first_blood':
      return 'from-orange-500/20 to-orange-600/20 border-orange-500/30';
    case 'team_joined':
      return 'from-green-500/20 to-green-600/20 border-green-500/30';
    default:
      return 'from-gray-500/20 to-gray-600/20 border-gray-500/30';
  }
};

export function RecentActivity() {
  const [mounted, setMounted] = useState(false);
  const [activityStats, setActivityStats] = useState<UserActivityStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const ITEMS_PER_PAGE = 4;

  useEffect(() => {
    setMounted(true);
    loadUserActivity();
  }, []);

  const loadUserActivity = async () => {
    try {
      // Fetch more activities to enable pagination
      const stats = await dashboardStatsService.getUserActivity(50);
      setActivityStats(stats);
    } catch (error) {
      console.error('Failed to load user activity:', error);
      // Fallback to empty activity
      setActivityStats({
        recentActivity: [],
        totalActivities: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const totalPages = activityStats ? Math.ceil(activityStats.recentActivity.length / ITEMS_PER_PAGE) : 0;
  const currentActivities = activityStats?.recentActivity.slice(
    currentPage * ITEMS_PER_PAGE,
    (currentPage + 1) * ITEMS_PER_PAGE
  ) || [];

  const handlePrevPage = () => {
    if (currentPage > 0 && !isTransitioning) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentPage(currentPage - 1);
        setIsTransitioning(false);
      }, 150);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages - 1 && !isTransitioning) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentPage(currentPage + 1);
        setIsTransitioning(false);
      }, 150);
    }
  };

  if (loading) {
    return (
      <div className="glass-card-dark backdrop-blur-xl rounded-2xl border border-purple-500/30 p-6 neon-border">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500/30 border-t-purple-400"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`glass-card-dark backdrop-blur-xl rounded-2xl border border-purple-500/30 p-6 neon-border ${mounted ? 'animate-slide-in-left' : 'opacity-0'}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl">
            <Clock className="w-6 h-6 text-purple-400" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-white">Recent Activity</h3>
            <p className="text-sm text-purple-200/70">
              Your latest achievements ({activityStats?.totalActivities || 0} total)
            </p>
          </div>
        </div>
        
        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePrevPage}
              disabled={currentPage === 0 || isTransitioning}
              className={`p-2 rounded-lg transition-all duration-200 ${
                currentPage === 0 || isTransitioning
                  ? 'bg-slate-700/50 text-slate-500 cursor-not-allowed'
                  : 'bg-purple-600/20 text-purple-300 hover:bg-purple-600/40 hover:text-purple-200'
              }`}
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            
            <div className="flex items-center space-x-1">
              {[...Array(totalPages)].map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    if (!isTransitioning) {
                      setIsTransitioning(true);
                      setTimeout(() => {
                        setCurrentPage(index);
                        setIsTransitioning(false);
                      }, 150);
                    }
                  }}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    index === currentPage
                      ? 'bg-purple-400'
                      : 'bg-purple-400/30 hover:bg-purple-400/60'
                  }`}
                />
              ))}
            </div>
            
            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages - 1 || isTransitioning}
              className={`p-2 rounded-lg transition-all duration-200 ${
                currentPage === totalPages - 1 || isTransitioning
                  ? 'bg-slate-700/50 text-slate-500 cursor-not-allowed'
                  : 'bg-purple-600/20 text-purple-300 hover:bg-purple-600/40 hover:text-purple-200'
              }`}
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Activity Content */}
      <div className={`transition-all duration-300 ${isTransitioning ? 'opacity-50 scale-95' : 'opacity-100 scale-100'}`}>
        <div className="space-y-4 min-h-[320px]">
          {currentActivities.length > 0 ? (
            currentActivities.map((activity, index) => (
              <div 
                key={`${activity.id}-${currentPage}`}
                className={`group relative overflow-hidden rounded-xl bg-gradient-to-r ${getActivityColor(activity.type)} border p-4 hover-lift shimmer-effect ${
                  mounted && !isTransitioning ? 'animate-slide-in-up' : 'opacity-0'
                }`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 p-3 bg-gradient-to-r from-purple-900/50 to-purple-800/50 rounded-xl border border-purple-500/30">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-white truncate group-hover:text-purple-200 transition-colors">
                        {activity.title}
                      </h4>
                      {activity.points && activity.points > 0 && (
                        <div className="flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-full">
                          <Star className="w-3 h-3 text-yellow-400" />
                          <span className="text-xs font-bold text-yellow-400">+{activity.points}</span>
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-purple-200/80 mb-2">{activity.description}</p>
                    <div className="flex items-center space-x-2">
                      <div className="w-1 h-1 bg-purple-400 rounded-full animate-pulse" />
                      <span className="text-xs text-purple-300/70">{activity.timestamp}</span>
                    </div>
                  </div>
                </div>

                {/* Animated border */}
                <div className="absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-r from-purple-500/0 via-purple-500/50 to-purple-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            ))
          ) : (
            <div className="text-center py-12">
              <div className="p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl mb-4 inline-block">
                <Clock className="w-12 h-12 text-purple-400/50" />
              </div>
              <h4 className="text-lg font-semibold text-purple-200/70 mb-2">No Recent Activity</h4>
              <p className="text-sm text-purple-300/50">
                Start solving challenges or launching machines to see your activity here!
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Page Indicator and Refresh Button */}
      <div className="mt-6 flex items-center justify-between">
        {totalPages > 1 && (
          <div className="text-sm text-purple-300/70">
            Page {currentPage + 1} of {totalPages}
          </div>
        )}
        
        <button 
          onClick={loadUserActivity}
          disabled={loading}
          className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed ml-auto"
        >
          <span>{loading ? 'Loading...' : 'Refresh Activity'}</span>
          <Zap className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-purple-400/40 rounded-full animate-float"
            style={{
              left: `${10 + i * 20}%`,
              top: `${10 + i * 15}%`,
              animationDelay: `${i * 0.8}s`,
              animationDuration: `${4 + i}s`
            }}
          />
        ))}
      </div>
    </div>
  );
}