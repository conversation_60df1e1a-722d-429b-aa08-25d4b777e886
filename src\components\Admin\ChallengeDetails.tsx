import { useState, useEffect } from 'react';
import { AdminService, AdminChallengeListItem } from '../../services/admin';
import { ArrowLeft, Flag, Eye, EyeOff, Download, Calendar, User, Tag, FileText, Server, AlertCircle } from 'lucide-react';

interface ChallengeDetailsProps {
  challengeId: string;
  onBack: () => void;
}

export function ChallengeDetails({ challengeId, onBack }: ChallengeDetailsProps) {
  const [challenge, setChallenge] = useState<AdminChallengeListItem | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showFlags, setShowFlags] = useState<boolean>(false);

  useEffect(() => {
    fetchChallengeDetails();
  }, [challengeId]);

  const fetchChallengeDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      const challengeData = await AdminService.getChallenge(challengeId);
      setChallenge(challengeData);
    } catch (err) {
      setError((err as Error)?.message || 'Failed to fetch challenge details');
      console.error('Error fetching challenge details:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'web':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'crypto':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'pwn':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'reverse':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'forensics':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'misc':
        return 'bg-slate-500/20 text-slate-400 border-slate-500/30';
      default:
        return 'bg-slate-500/20 text-slate-400 border-slate-500/30';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'hard':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'insane':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-slate-500/20 text-slate-400 border-slate-500/30';
    }
  };

  const downloadFile = (file: any) => {
    // Create a download link for the file
    const link = document.createElement('a');
    link.href = `${import.meta.env.VITE_API_URL}/uploads/${file.path}`;
    link.download = file.name;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 px-4 py-2 bg-slate-800 hover:bg-slate-700 text-slate-300 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Challenges</span>
          </button>
        </div>
        
        <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-8 animate-pulse">
          <div className="space-y-4">
            <div className="h-8 bg-slate-700 rounded w-1/2"></div>
            <div className="h-4 bg-slate-700 rounded w-3/4"></div>
            <div className="h-4 bg-slate-700 rounded w-1/2"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
              <div className="space-y-4">
                <div className="h-6 bg-slate-700 rounded w-1/3"></div>
                <div className="h-32 bg-slate-700 rounded"></div>
              </div>
              <div className="space-y-4">
                <div className="h-6 bg-slate-700 rounded w-1/3"></div>
                <div className="h-32 bg-slate-700 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !challenge) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 px-4 py-2 bg-slate-800 hover:bg-slate-700 text-slate-300 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Challenges</span>
          </button>
        </div>
        
        <div className="bg-red-900/20 border border-red-800 rounded-xl p-6">
          <div className="flex items-center space-x-3">
            <AlertCircle className="w-6 h-6 text-red-400" />
            <div>
              <h3 className="text-lg font-semibold text-red-400">Error Loading Challenge</h3>
              <p className="text-red-300 mt-1">{error || 'Challenge not found'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 px-4 py-2 bg-slate-800 hover:bg-slate-700 text-slate-300 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Challenges</span>
        </button>
        
        <div className="flex items-center space-x-3">
          <span className={`px-3 py-1 rounded-full text-sm border ${getCategoryColor(challenge.category)}`}>
            {challenge.category}
          </span>
          <span className={`px-3 py-1 rounded-full text-sm border ${getDifficultyColor(challenge.difficulty)}`}>
            {challenge.difficulty}
          </span>
          <span className={`px-3 py-1 rounded-full text-sm ${
            challenge.isActive 
              ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/30' 
              : 'bg-red-500/20 text-red-400 border border-red-500/30'
          }`}>
            {challenge.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Challenge Header */}
      <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-white mb-2">{challenge.title}</h1>
            <p className="text-slate-300 text-lg leading-relaxed">{challenge.description}</p>
          </div>
          <div className="ml-6 text-right">
            <div className="text-2xl font-bold text-emerald-400">{challenge.points}</div>
            <div className="text-sm text-slate-400">points</div>
            <div className="flex items-center space-x-1 text-slate-300 mt-2">
              <Flag className="w-4 h-4 text-blue-400" />
              <span>{challenge.solveCount} solves</span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Flags Section - Admin Only */}
        <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
              <Flag className="w-5 h-5 text-red-400" />
              <span>Flags (Admin Only)</span>
            </h3>
            <button
              onClick={() => setShowFlags(!showFlags)}
              className="flex items-center space-x-2 px-3 py-1 bg-slate-700 hover:bg-slate-600 text-slate-300 rounded-lg transition-colors"
            >
              {showFlags ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              <span>{showFlags ? 'Hide' : 'Show'}</span>
            </button>
          </div>
          
          {showFlags ? (
            <div className="space-y-3">
              {/* Legacy single flag */}
              {challenge.flag && (
                <div className="p-3 bg-slate-900/50 border border-slate-600 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-400">Legacy Flag:</span>
                    <span className="text-emerald-400 font-mono">{challenge.flag}</span>
                  </div>
                </div>
              )}
              
              {/* Multiple flags */}
              {challenge.flags && challenge.flags.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-slate-300">Multiple Flags:</h4>
                  {challenge.flags.map((flag, index) => (
                    <div key={index} className="p-3 bg-slate-900/50 border border-slate-600 rounded-lg">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-emerald-400 font-mono">{flag.value}</span>
                          <span className="text-emerald-400 text-sm font-medium">{flag.points} pts</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-slate-400">{flag.description}</span>
                          {flag.isCaseSensitive && (
                            <span className="text-amber-400 text-xs">Case sensitive</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {!challenge.flag && (!challenge.flags || challenge.flags.length === 0) && (
                <div className="text-center py-4 text-slate-400">
                  No flags configured for this challenge
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-slate-400">
              <Flag className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>Click "Show" to reveal flags</p>
            </div>
          )}
        </div>

        {/* Challenge Information */}
        <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <FileText className="w-5 h-5 text-blue-400" />
            <span>Challenge Information</span>
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-slate-400">Author:</span>
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4 text-slate-400" />
                <span className="text-white">{challenge.authorName || 'Unknown'}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-slate-400">Created:</span>
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-slate-400" />
                <span className="text-white">{formatDate(challenge.createdAt)}</span>
              </div>
            </div>
            
            {challenge.releaseDate && (
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Release Date:</span>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-slate-400" />
                  <span className="text-white">{formatDate(challenge.releaseDate)}</span>
                </div>
              </div>
            )}
            
            {challenge.expiryDate && (
              <div className="flex items-center justify-between">
                <span className="text-slate-400">Expiry Date:</span>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-slate-400" />
                  <span className="text-white">{formatDate(challenge.expiryDate)}</span>
                </div>
              </div>
            )}
            
            {challenge.firstBlood && (
              <div className="flex items-center justify-between">
                <span className="text-slate-400">First Blood:</span>
                <div className="flex items-center space-x-2">
                  <Flag className="w-4 h-4 text-red-400" />
                  <span className="text-white">{challenge.firstBlood.username}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Hints */}
        {challenge.hints && challenge.hints.length > 0 && (
          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Hints</h3>
            <div className="space-y-2">
              {challenge.hints.map((hint, index) => (
                <div key={index} className="p-3 bg-slate-900/50 border border-slate-600 rounded-lg">
                  <span className="text-slate-300">{hint}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Tags */}
        {challenge.tags && challenge.tags.length > 0 && (
          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Tag className="w-5 h-5 text-emerald-400" />
              <span>Tags</span>
            </h3>
            <div className="flex flex-wrap gap-2">
              {challenge.tags.map((tag, index) => (
                <span key={index} className="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm">
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Files */}
        {challenge.files && challenge.files.length > 0 && (
          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Challenge Files</h3>
            <div className="space-y-2">
              {challenge.files.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-slate-900/50 border border-slate-600 rounded-lg">
                  <div>
                    <div className="text-white font-medium">{file.name}</div>
                    {file.description && (
                      <div className="text-slate-400 text-sm">{file.description}</div>
                    )}                    <div className="text-slate-500 text-xs">
                      {file.type} • {((file.size || 0) / 1024).toFixed(1)} KB
                    </div>
                  </div>
                  <button
                    onClick={() => downloadFile(file)}
                    className="flex items-center space-x-2 px-3 py-1 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg transition-colors"
                  >
                    <Download className="w-4 h-4" />
                    <span>Download</span>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Server Configuration */}
        {challenge.requiresServer && (
          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Server className="w-5 h-5 text-purple-400" />
              <span>Server Configuration</span>
            </h3>
            <div className="space-y-3">
              {challenge.dockerImage && (
                <div>
                  <span className="text-slate-400 text-sm">Docker Image:</span>
                  <div className="mt-1 p-2 bg-slate-900/50 border border-slate-600 rounded-lg">
                    <code className="text-emerald-400 font-mono text-sm">{challenge.dockerImage}</code>
                  </div>
                </div>
              )}
              {challenge.serverConfig && (
                <div>
                  <span className="text-slate-400 text-sm">Configuration:</span>
                  <div className="mt-1 p-3 bg-slate-900/50 border border-slate-600 rounded-lg">
                    <pre className="text-emerald-400 font-mono text-sm overflow-x-auto">
                      {JSON.stringify(challenge.serverConfig, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
