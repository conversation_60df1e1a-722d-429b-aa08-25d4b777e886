import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Query,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { DashboardService } from './dashboard.service';
import { DashboardStatsService } from './dashboard-stats.service';
import {
  CreateDashboardConfigDto,
  UpdateDashboardConfigDto,
  CreateSponsorDto,
  UpdateSponsorDto,
} from './dto/dashboard-config.dto';

@Controller('dashboard')
@UseGuards(JwtAuthGuard)
export class DashboardController {
  constructor(
    private readonly dashboardService: DashboardService,
    private readonly dashboardStatsService: DashboardStatsService,
  ) {}

  @Get('config')
  async getDashboardConfig() {
    return await this.dashboardService.getDashboardConfig();
  }

  @Put('config')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async updateDashboardConfig(
    @Body() updateDto: UpdateDashboardConfigDto,
    @Request() req,
  ) {
    const config = await this.dashboardService.updateDashboardConfig(updateDto, req.user.userId);
    return {
      message: 'Dashboard configuration updated successfully',
      config,
    };
  }

  @Post('config/cover-image')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @UseInterceptors(FileInterceptor('file', {
    storage: memoryStorage(),
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, callback) => {
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
      if (allowedMimeTypes.includes(file.mimetype)) {
        callback(null, true);
      } else {
        callback(new Error('Invalid file type. Only JPEG, PNG, WebP, and GIF files are allowed.'), false);
      }
    },
  }))
  async uploadCoverImage(
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('Image file is required');
    }

    const result = await this.dashboardService.uploadCoverImage(file, req.user.userId);
    return {
      message: 'Cover image uploaded successfully',
      ...result,
    };
  }

  // Statistics endpoints
  @Get('platform-stats')
  async getPlatformStats() {
    return await this.dashboardStatsService.getDashboardStats();
  }

  @Get('user-activity')
  async getUserActivity(
    @Request() req,
    @Query('limit') limit?: string,
  ) {
    const activityLimit = limit ? parseInt(limit, 10) : 20;
    return await this.dashboardStatsService.getUserActivityStats(req.user.userId, activityLimit);
  }

  // Sponsor Management
  @Post('sponsors/upload-temp-logo')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @UseInterceptors(FileInterceptor('file', {
    storage: memoryStorage(),
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, callback) => {
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'];
      if (allowedMimeTypes.includes(file.mimetype)) {
        callback(null, true);
      } else {
        callback(new Error('Invalid file type. Only JPEG, PNG, WebP, and SVG files are allowed.'), false);
      }
    },
  }))
  async uploadTempSponsorLogo(
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('Logo file is required');
    }

    const result = await this.dashboardService.uploadTempSponsorLogo(file, req.user.userId);
    return {
      message: 'Logo uploaded successfully',
      ...result,
    };
  }

  @Post('sponsors')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async addSponsor(
    @Body() createSponsorDto: CreateSponsorDto,
    @Request() req,
  ) {
    const config = await this.dashboardService.addSponsor(createSponsorDto, req.user.userId);
    return {
      message: 'Sponsor added successfully',
      config,
    };
  }

  @Put('sponsors/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async updateSponsor(
    @Param('id') id: string,
    @Body() updateSponsorDto: Omit<UpdateSponsorDto, 'id'>,
    @Request() req,
  ) {
    const config = await this.dashboardService.updateSponsor(
      { ...updateSponsorDto, id },
      req.user.userId,
    );
    return {
      message: 'Sponsor updated successfully',
      config,
    };
  }

  @Delete('sponsors/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async deleteSponsor(
    @Param('id') id: string,
    @Request() req,
  ) {
    const config = await this.dashboardService.deleteSponsor(id, req.user.userId);
    return {
      message: 'Sponsor deleted successfully',
      config,
    };
  }

  @Put('sponsors/reorder')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async reorderSponsors(
    @Body() { sponsorIds }: { sponsorIds: string[] },
    @Request() req,
  ) {
    const config = await this.dashboardService.reorderSponsors(sponsorIds, req.user.userId);
    return {
      message: 'Sponsors reordered successfully',
      config,
    };
  }

  @Post('sponsors/:id/logo')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @UseInterceptors(FileInterceptor('file', {
    storage: memoryStorage(),
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, callback) => {
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'];
      if (allowedMimeTypes.includes(file.mimetype)) {
        callback(null, true);
      } else {
        callback(new Error('Invalid file type. Only JPEG, PNG, WebP, and SVG files are allowed.'), false);
      }
    },
  }))
  async uploadSponsorLogo(
    @Param('id') id: string,
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('Logo file is required');
    }

    const result = await this.dashboardService.uploadSponsorLogo(id, file, req.user.userId);
    return {
      message: 'Sponsor logo uploaded successfully',
      ...result,
    };
  }

  @Get('stats')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async getDashboardStats() {
    return await this.dashboardService.getDashboardStats();
  }
}