import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { MachineTemplate } from '../../schemas/machine-template.schema';
import { MachineInstance } from '../../schemas/machine-instance.schema';
import { MachineSubmission } from '../../schemas/machine-submission.schema';
import { DockerMachineService, ResourceStats } from './docker-machine.service';
import { User } from '../../schemas/user.schema';
import { Team } from '../../schemas/team.schema';

export interface MachineInstanceDto {
  id: string;
  template: {
    id: string;
    name: string;
    difficulty: string;
    category: string;
    os: string;
  };
  status: string;
  instanceIP: string;
  exposedPorts: Array<{
    internal: number;
    external: number;
    protocol: string;
  }>;
  accessToken: string;
  startedAt: Date;
  expiresAt: Date;
  timeRemaining: number; // minutes
  isTeamShared: boolean;
  resourceUsage?: ResourceStats;
}

export interface CreateInstanceDto {
  templateId: string;
  teamId?: string;
  extendedRuntime?: number;
}

export interface ExtendInstanceDto {
  additionalMinutes: number;
}

@Injectable()
export class MachineInstanceService {
  private readonly logger = new Logger(MachineInstanceService.name);

  constructor(
    @InjectModel(MachineTemplate.name) private machineTemplateModel: Model<MachineTemplate>,
    @InjectModel(MachineInstance.name) private machineInstanceModel: Model<MachineInstance>,
    @InjectModel(MachineSubmission.name) private machineSubmissionModel: Model<MachineSubmission>,
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Team.name) private teamModel: Model<Team>,
    private dockerMachineService: DockerMachineService,
  ) {}

  /**
   * Get all available machine templates
   */
  async getAvailableTemplates(
    userId: string,
    filters: {
      category?: string;
      difficulty?: string;
      search?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{ templates: MachineTemplate[]; total: number; hasMore: boolean }> {
    const { category, difficulty, search, page = 1, limit = 20 } = filters;
    
    const query: any = { isActive: true };
    
    // Apply filters
    if (category) {
      query.category = category;
    }
    
    if (difficulty) {
      query.difficulty = difficulty;
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ];
    }

    // Check release date
    query.releaseDate = { $lte: new Date() };

    const skip = (page - 1) * limit;
    
    const [templates, total] = await Promise.all([
      this.machineTemplateModel
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('authorId', 'username')
        .exec(),
      this.machineTemplateModel.countDocuments(query),
    ]);

    // Add user-specific information
    for (const template of templates) {
      // Check if user has solved this machine
      const userSubmission = await this.machineSubmissionModel.findOne({
        templateId: (template as any)._id,
        userId,
        isCorrect: true,
      });

      (template as any).isSolved = !!userSubmission;
      
      // Get current active instances count
      const activeInstances = await this.machineInstanceModel.countDocuments({
        templateId: (template as any)._id,
        status: { $in: ['starting', 'running'] }
      });
      
      (template as any).activeInstances = activeInstances;
    }

    return {
      templates,
      total,
      hasMore: skip + templates.length < total,
    };
  }

  /**
   * Get machine template details
   */
  async getTemplateDetails(templateId: string, userId: string): Promise<MachineTemplate & { userStats: any }> {
    const template = await this.machineTemplateModel
      .findOne({ _id: templateId, isActive: true })
      .populate('authorId', 'username')
      .exec();

    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    // Get user-specific stats
    const userSubmissions = await this.machineSubmissionModel.find({
      templateId,
      userId,
    });

    const solvedFlags = userSubmissions.filter(sub => sub.isCorrect).map(sub => sub.flagName);
    const totalPoints = userSubmissions
      .filter(sub => sub.isCorrect)
      .reduce((sum, sub) => sum + sub.pointsAwarded, 0);

    const userStats = {
      solvedFlags,
      totalPoints,
      totalSubmissions: userSubmissions.length,
      isSolved: solvedFlags.length === template.flags.length && template.flags.length > 0,
    };

    return { ...template.toObject(), userStats };
  }

  /**
   * Create a new machine instance
   */
  async createInstance(userId: string, createDto: CreateInstanceDto): Promise<MachineInstanceDto> {
    this.logger.log(`Creating instance for user ${userId}, template ${createDto.templateId}`);    // Validate team membership if teamId provided
    if (createDto.teamId) {
      const team = await this.teamModel.findById(createDto.teamId);
      if (!team) {
        throw new BadRequestException('Team not found');
      }

      const isMember = team.members.some(member => 
        member.userId.toString() === userId || team.captainId.toString() === userId
      );

      if (!isMember) {
        throw new BadRequestException('User is not a member of this team');
      }
    }

    // Create instance using Docker service
    const instance = await this.dockerMachineService.spawnMachine(
      createDto.templateId,
      userId,
      createDto.teamId,
      { extendedRuntime: createDto.extendedRuntime }
    );

    return this.formatInstanceDto(instance);
  }

  /**
   * Get user's active instances
   */
  async getUserInstances(userId: string): Promise<MachineInstanceDto[]> {
    const instances = await this.machineInstanceModel
      .find({
        ownerId: userId,
        status: { $in: ['starting', 'running', 'stopping'] }
      })
      .populate('templateId')
      .sort({ createdAt: -1 })
      .exec();

    const instanceDtos: MachineInstanceDto[] = [];
    
    for (const instance of instances) {
      const dto = this.formatInstanceDto(instance);
      
      // Get real-time stats if running
      if (instance.status === 'running' && instance.containerId) {
        try {
          dto.resourceUsage = await this.dockerMachineService.getInstanceStats(
            (instance as any)._id.toString(),
            userId
          );
        } catch (error) {
          this.logger.warn(`Failed to get stats for instance ${(instance as any)._id}: ${error.message}`);
        }
      }
      
      instanceDtos.push(dto);
    }

    return instanceDtos;
  }

  /**
   * Get team instances (for team members)
   */
  async getTeamInstances(userId: string, teamId: string): Promise<MachineInstanceDto[]> {
    // Verify user is team member
    const team = await this.teamModel.findById(teamId);    if (!team) {
      throw new NotFoundException('Team not found');
    }

    const isMember = team.members.some(member => 
      member.userId.toString() === userId || team.captainId.toString() === userId
    );

    if (!isMember) {
      throw new BadRequestException('User is not a member of this team');
    }

    const instances = await this.machineInstanceModel
      .find({
        teamId,
        isTeamShared: true,
        status: { $in: ['starting', 'running', 'stopping'] }
      })
      .populate('templateId')
      .populate('ownerId', 'username')
      .sort({ createdAt: -1 })
      .exec();

    return instances.map(instance => this.formatInstanceDto(instance));
  }

  /**
   * Get instance details
   */
  async getInstanceDetails(instanceId: string, userId: string): Promise<MachineInstanceDto> {
    const instance = await this.machineInstanceModel
      .findOne({
        _id: instanceId,
        $or: [
          { ownerId: userId },
          { teamId: { $exists: true }, isTeamShared: true }
        ]
      })
      .populate('templateId')
      .exec();

    if (!instance) {
      throw new NotFoundException('Machine instance not found or unauthorized');
    }

    // If it's a team instance, verify team membership
    if (instance.teamId && instance.ownerId.toString() !== userId) {
      const team = await this.teamModel.findById(instance.teamId);
      const isMember = team?.members.some(member => 
        member.userId.toString() === userId || team.captainId.toString() === userId
      );

      if (!isMember) {
        throw new BadRequestException('User is not authorized to access this instance');
      }
    }

    const dto = this.formatInstanceDto(instance);

    // Get real-time stats if running
    if (instance.status === 'running' && instance.containerId) {
      try {
        // For team instances, only owner can get detailed stats
        const canGetStats = instance.ownerId.toString() === userId;
        if (canGetStats) {
          dto.resourceUsage = await this.dockerMachineService.getInstanceStats(instanceId, userId);
        }
      } catch (error) {
        this.logger.warn(`Failed to get stats for instance ${instanceId}: ${error.message}`);
      }
    }

    return dto;
  }

  /**
   * Terminate an instance
   */
  async terminateInstance(instanceId: string, userId: string): Promise<void> {
    const instance = await this.machineInstanceModel.findOne({
      _id: instanceId,
      ownerId: userId,
    });

    if (!instance) {
      throw new NotFoundException('Machine instance not found or unauthorized');
    }

    await this.dockerMachineService.terminateMachine(instanceId, userId);
  }

  /**
   * Restart an instance
   */
  async restartInstance(instanceId: string, userId: string): Promise<void> {
    const instance = await this.machineInstanceModel.findOne({
      _id: instanceId,
      ownerId: userId,
    });

    if (!instance) {
      throw new NotFoundException('Machine instance not found or unauthorized');
    }

    await this.dockerMachineService.restartMachine(instanceId, userId);
  }

  /**
   * Extend instance runtime
   */
  async extendInstance(instanceId: string, userId: string, extendDto: ExtendInstanceDto): Promise<void> {
    const instance = await this.machineInstanceModel.findOne({
      _id: instanceId,
      ownerId: userId,
    });

    if (!instance) {
      throw new NotFoundException('Machine instance not found or unauthorized');
    }

    await this.dockerMachineService.extendMachine(instanceId, userId, extendDto.additionalMinutes);
  }

  /**
   * Share instance with team
   */
  async shareInstanceWithTeam(instanceId: string, userId: string, teamId: string): Promise<void> {
    const instance = await this.machineInstanceModel.findOne({
      _id: instanceId,
      ownerId: userId,
    });

    if (!instance) {
      throw new NotFoundException('Machine instance not found or unauthorized');
    }

    // Verify user is team member
    const team = await this.teamModel.findById(teamId);
    if (!team) {
      throw new NotFoundException('Team not found');
    }

    const isMember = team.members.some(member => 
      member.userId.toString() === userId || team.captainId.toString() === userId
    );

    if (!isMember) {
      throw new BadRequestException('User is not a member of this team');
    }

    // Update instance to be team shared
    instance.teamId = new Types.ObjectId(teamId);
    instance.isTeamShared = true;
    await instance.save();

    this.logger.log(`Instance ${instanceId} shared with team ${teamId}`);
  }

  /**
   * Get instance connection information
   */
  async getConnectionInfo(instanceId: string, userId: string): Promise<{
    instanceIP: string;
    ports: Array<{
      internal: number;
      external: number;
      protocol: string;
      url?: string;
    }>;
    accessToken: string;
    sshCommand?: string;
    rdpInfo?: any;
  }> {
    const instance = await this.machineInstanceModel
      .findOne({
        _id: instanceId,
        $or: [
          { ownerId: userId },
          { teamId: { $exists: true }, isTeamShared: true }
        ]
      })
      .populate('templateId')
      .exec();

    if (!instance) {
      throw new NotFoundException('Machine instance not found or unauthorized');
    }

    if (instance.status !== 'running') {
      throw new BadRequestException('Machine instance is not running');
    }

    const template = instance.templateId as unknown as MachineTemplate;
    const connectionInfo: any = {
      instanceIP: instance.instanceIP,
      ports: instance.exposedPorts.map(port => ({
        ...port,
        url: this.generatePortUrl(port, instance.instanceIP),
      })),
      accessToken: instance.accessToken,
    };

    // Generate connection commands based on OS and ports
    if (template.os === 'linux') {
      const sshPort = instance.exposedPorts.find(p => p.internal === 22);
      if (sshPort) {
        connectionInfo.sshCommand = `ssh -p ${sshPort.external} user@${instance.instanceIP}`;
      }
    } else if (template.os === 'windows') {
      const rdpPort = instance.exposedPorts.find(p => p.internal === 3389);
      if (rdpPort) {
        connectionInfo.rdpInfo = {
          host: instance.instanceIP,
          port: rdpPort.external,
          username: 'Administrator',
        };
      }
    }

    return connectionInfo;
  }

  /**
   * Get machine statistics for admin/monitoring
   */
  async getMachineStatistics(): Promise<{
    totalTemplates: number;
    activeInstances: number;
    totalInstances: number;
    resourceUsage: {
      totalCPU: number;
      totalMemory: number;
      totalNetwork: number;
    };
    popularMachines: Array<{
      template: MachineTemplate;
      instanceCount: number;
      solveCount: number;
    }>;
  }> {
    const [
      totalTemplates,
      activeInstances,
      totalInstances,
      popularMachinesData
    ] = await Promise.all([
      this.machineTemplateModel.countDocuments({ isActive: true }),
      this.machineInstanceModel.countDocuments({ status: { $in: ['starting', 'running'] } }),
      this.machineInstanceModel.countDocuments(),
      this.machineInstanceModel.aggregate([
        {
          $group: {
            _id: '$templateId',
            instanceCount: { $sum: 1 },
          }
        },
        { $sort: { instanceCount: -1 } },
        { $limit: 10 },
        {
          $lookup: {
            from: 'machinetemplates',
            localField: '_id',
            foreignField: '_id',
            as: 'template'
          }
        },
        { $unwind: '$template' }
      ])
    ]);

    // Calculate resource usage
    const runningInstances = await this.machineInstanceModel.find({
      status: 'running',
      cpuUsage: { $exists: true }
    });

    const resourceUsage = runningInstances.reduce(
      (acc, instance) => ({
        totalCPU: acc.totalCPU + (instance.cpuUsage || 0),
        totalMemory: acc.totalMemory + (instance.memoryUsage || 0),
        totalNetwork: acc.totalNetwork + (instance.networkUsage || 0),
      }),
      { totalCPU: 0, totalMemory: 0, totalNetwork: 0 }
    );

    // Add solve counts to popular machines
    const popularMachines = await Promise.all(
      popularMachinesData.map(async (data) => ({
        template: data.template,
        instanceCount: data.instanceCount,
        solveCount: await this.machineSubmissionModel.countDocuments({
          templateId: data._id,
          isCorrect: true,
        }),
      }))
    );

    return {
      totalTemplates,
      activeInstances,
      totalInstances,
      resourceUsage,
      popularMachines,
    };
  }  /**
   * Format instance for DTO
   */
  private formatInstanceDto(instance: MachineInstance): MachineInstanceDto {
    const template = instance.templateId as unknown as MachineTemplate;
    const timeRemaining = Math.max(
      0,
      Math.floor((instance.expiresAt.getTime() - Date.now()) / (60 * 1000))
    );

    return {
      id: (instance as any)._id?.toString() || 'unknown',
      template: template ? {
        id: (template as any)._id?.toString() || 'unknown',
        name: template.name || 'Unknown',
        difficulty: template.difficulty || 'easy',
        category: template.category || 'misc',
        os: template.os || 'linux',
      } : {
        id: 'unknown',
        name: 'Unknown Template',
        difficulty: 'easy',
        category: 'misc',
        os: 'linux',
      },
      status: instance.status,
      instanceIP: instance.instanceIP,
      exposedPorts: instance.exposedPorts || [],
      accessToken: instance.accessToken,
      startedAt: instance.startedAt,
      expiresAt: instance.expiresAt,
      timeRemaining,
      isTeamShared: instance.isTeamShared,
    };
  }

  /**
   * Generate URL for web-accessible ports
   */
  private generatePortUrl(port: any, instanceIP: string): string | undefined {
    // Common web ports
    const webPorts = [80, 443, 8080, 8443, 3000, 4000, 5000, 8000, 9000];
    
    if (webPorts.includes(port.internal)) {
      const protocol = [443, 8443].includes(port.internal) ? 'https' : 'http';
      return `${protocol}://${instanceIP}:${port.external}`;
    }
    
    return undefined;
  }
}
