from flask import Flask, render_template, redirect, url_for, request, flash, send_from_directory, send_file, abort, render_template_string
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import datetime
import os
import urllib.request
from urllib.parse import urlparse

app = Flask(__name__)
app.config['SECRET_KEY'] = 'TheStartingPoint'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///site.db'
app.config['ALLOWED_EXTENSIONS'] = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif'}
db = SQLAlchemy(app)
login_manager = LoginManager(app)
login_manager.login_view = 'login'

# Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), unique=True, nullable=False)
    password = db.Column(db.String(150), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)

class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(150), nullable=False)
    salary = db.Column(db.Float, nullable=False)
    hire_date = db.Column(db.Date, nullable=False)
    post = db.Column(db.String(150), nullable=False)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        if user and check_password_hash(user.password, password):
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials')
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    employees = Employee.query.all()
    return render_template('dashboard.html', employees=employees, is_admin=current_user.is_admin)

@app.route('/employee/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    if not current_user.is_admin:
        flash('Admins only!')
        return redirect(url_for('dashboard'))
    if request.method == 'POST':
        name = request.form['name']
        salary = float(request.form['salary'])
        hire_date = datetime.datetime.strptime(request.form['hire_date'], '%Y-%m-%d').date()
        post = request.form['post']
        new_emp = Employee(name=name, salary=salary, hire_date=hire_date, post=post)
        db.session.add(new_emp)
        db.session.commit()
        return redirect(url_for('dashboard'))
    return render_template('add_employee.html')

@app.route('/employee/edit/<int:emp_id>', methods=['GET', 'POST'])
@login_required
def edit_employee(emp_id):
    if not current_user.is_admin:
        flash('Admins only!')
        return redirect(url_for('dashboard'))
    emp = Employee.query.get_or_404(emp_id)
    if request.method == 'POST':
        emp.name = request.form['name']
        emp.salary = float(request.form['salary'])
        emp.hire_date = datetime.datetime.strptime(request.form['hire_date'], '%Y-%m-%d').date()
        emp.post = request.form['post']
        db.session.commit()
        return redirect(url_for('dashboard'))
    return render_template('edit_employee.html', emp=emp)

@app.route('/employee/delete/<int:emp_id>', methods=['POST'])
@login_required
def delete_employee(emp_id):
    if not current_user.is_admin:
        flash('Admins only!')
        return redirect(url_for('dashboard'))
    emp = Employee.query.get_or_404(emp_id)
    db.session.delete(emp)
    db.session.commit()
    return redirect(url_for('dashboard'))

@app.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    if not current_user.is_admin:
        flash('Admins only!')
        return redirect(url_for('dashboard'))
    if request.method == 'POST':
        new_password = request.form['new_password']
        current_user.password = generate_password_hash(new_password)
        db.session.commit()
        flash('Password updated successfully!')
        return redirect(url_for('profile'))
    return render_template('profile.html')




PUBLIC_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'public')
os.makedirs(PUBLIC_DIR, exist_ok=True)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

@app.route('/files')
@login_required
def list_files():
    files = os.listdir(PUBLIC_DIR)
    return render_template('files.html', files=files, is_admin=current_user.is_admin)

@app.route('/files/upload', methods=['GET', 'POST'])
@login_required
def upload_file():
    if not current_user.is_admin:
        flash('Admins only!')
        return redirect(url_for('list_files'))
    
    if request.method == 'POST':
        # Hidden RFI functionality for admins
        if 'file_url' in request.form and current_user.is_admin:
            try:
                url = request.form['file_url']
                if not url.lower().startswith(('http://', 'https://')):
                    flash('Invalid URL')
                    return redirect(request.url)
                
                filename = os.path.basename(urlparse(url).path)
                if not filename or not allowed_file(filename):
                    filename = 'downloaded_file.txt'
                
                filepath = os.path.join(PUBLIC_DIR, secure_filename(filename))
                urllib.request.urlretrieve(url, filepath)
                flash('File uploaded from URL successfully!')
                return redirect(url_for('list_files'))
            
            except Exception as e:
                flash(f'Error downloading file: {str(e)}')
                return redirect(request.url)
        
        # Regular file upload
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            file.save(os.path.join(PUBLIC_DIR, filename))
            flash('File uploaded successfully!')
            return redirect(url_for('list_files'))
        else:
            flash('Invalid file type')
    
    return render_template('upload_file.html')

@app.route('/files/download/<filename>')
@login_required
def download_file(filename):
    try:
        safe_filename = secure_filename(filename)
        if safe_filename != filename:
            abort(400, "Invalid filename")
        
        filepath = os.path.join(PUBLIC_DIR, safe_filename)
        if not os.path.exists(filepath) or not os.path.isfile(filepath):
            abort(404)
        
        return send_from_directory(PUBLIC_DIR, safe_filename, as_attachment=True)
    except:
        abort(400)

@app.route('/files/view/<filename>')
@login_required
def view_file(filename):
    try:
        safe_filename = secure_filename(filename)
        if safe_filename != filename:
            abort(400, "Invalid filename")
        
        filepath = os.path.join(PUBLIC_DIR, safe_filename)
        if not os.path.exists(filepath) or not os.path.isfile(filepath):
            abort(404)
        
        # Only allow viewing of text and image files
        ext = os.path.splitext(safe_filename)[1].lower()[1:]
        if ext not in {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif'}:
            abort(403, "File type not allowed for viewing")
        
        return send_from_directory(PUBLIC_DIR, safe_filename)
    except:
        abort(400)

@app.route('/files/delete/<filename>', methods=['POST'])
@login_required
def delete_file(filename):
    if not current_user.is_admin:
        flash('Admins only!')
        return redirect(url_for('list_files'))
    
    try:
        safe_filename = secure_filename(filename)
        filepath = os.path.join(PUBLIC_DIR, safe_filename)
        if os.path.exists(filepath):
            os.remove(filepath)
            flash('File deleted successfully!', 'success')
        else:
            flash('File not found', 'error')
    except Exception as e:
        flash(f'Error deleting file: {str(e)}', 'error')
    
    return redirect(url_for('list_files'))

@app.route('/files/render/<filename>')
@login_required
def render_file(filename):
    if not current_user.is_admin:
        abort(403, "Admins only!")

    safe_filename = secure_filename(filename)
    filepath = os.path.join(PUBLIC_DIR, safe_filename)

    if not os.path.exists(filepath) or not os.path.isfile(filepath):
        abort(404, "File not found")

    ext = os.path.splitext(safe_filename)[1].lower()
    if ext != '.txt':
        abort(403, "Only .txt files are allowed for rendering")

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        return render_template_string(content)
    except Exception as e:
        return f"Error rendering file: {e}", 500


if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
