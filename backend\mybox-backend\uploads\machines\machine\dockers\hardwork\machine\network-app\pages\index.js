import { useState } from 'react';
import { useRouter } from 'next/router';
import { Frame, ArrowRight } from 'lucide-react';

export default function LoginPage() {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const router = useRouter();

    const handleLogin = async (e) => {
        e.preventDefault();
        setError('');

        if (username !== 'KYBSAdmin' || password !== '6rU73f0rCiL5b33hy33ta7f0uun') {
            setError('Invalid username or password.');
            return;
        }
                
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password }),
        });

        if (response.ok) {
            router.push('/dashboard');
        } else {
            const data = await response.json();
            setError(data.message || 'An unexpected error occurred.');
        }
    };
        
    return (
        <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                {/* Logo Section */}
                <div className="text-center mb-12">
                    <div className="inline-flex items-center justify-center w-20 h-20 bg-white rounded-2xl mb-6 shadow-2xl">
                        <Frame className="w-10 h-10 text-black" />
                    </div>
                    <h1 className="text-4xl font-bold text-white mb-2 tracking-tight">
                        Hardwork Inc
                    </h1>
                    <p className="text-gray-400 text-lg font-light">
                        Authentication required to proceed.
                    </p>
                </div>
                
                {/* Login Form */}
                <div className="bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/10 shadow-2xl">
                    <form onSubmit={handleLogin} className="space-y-6">
                        <div className="space-y-2">
                            <label className="block text-white text-sm font-semibold tracking-wide uppercase">
                                Username
                            </label>
                            <input
                                type="text"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                className="w-full px-6 py-4 text-white bg-black/50 border border-white/20 rounded-2xl focus:ring-2 focus:ring-white/50 focus:border-white/50 outline-none transition-all duration-300 placeholder-gray-400 backdrop-blur-sm hover:bg-black/60"
                                placeholder="Enter username"
                            />
                        </div>
                        
                        <div className="space-y-2">
                            <label className="block text-white text-sm font-semibold tracking-wide uppercase">
                                Password
                            </label>
                            <input
                                type="password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                className="w-full px-6 py-4 text-white bg-black/50 border border-white/20 rounded-2xl focus:ring-2 focus:ring-white/50 focus:border-white/50 outline-none transition-all duration-300 placeholder-gray-400 backdrop-blur-sm hover:bg-black/60"
                                placeholder="Enter password"
                            />
                        </div>
                        
                        {error && (
                            <div className="bg-red-500/10 border border-red-500/20 text-red-400 px-6 py-4 rounded-2xl text-sm font-medium backdrop-blur-sm">
                                {error}
                            </div>
                        )}
                        
                        <button
                            type="submit"
                            className="w-full bg-white text-black py-4 px-6 rounded-2xl font-bold text-lg hover:bg-gray-100 focus:ring-4 focus:ring-white/30 transition-all duration-300 flex items-center justify-center space-x-3 shadow-xl hover:shadow-2xl transform hover:scale-[1.02] active:scale-[0.98]"
                        >
                            <span>Login</span>
                            <ArrowRight className="w-5 h-5" />
                        </button>
                    </form>
                </div>
            </div>
        </div>
    );
    
}