import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Challenge, ChallengeDifficulty, Flag } from '../schemas/challenge.schema';
import { Category } from '../schemas/category.schema';
import { ChallengeSubmission } from '../schemas/challenge-submission.schema';
import { TeamChallengeSolve } from '../schemas/team-challenge-solve.schema';
import { PublicCreateChallengeDto, PublicUpdateChallengeDto, FlagSubmissionDto, ChallengePaginationDto } from './dto/challenges.dto';
import { NotificationsService } from '../notifications/notifications.service';
import { NotificationsGateway } from '../notifications/notifications.gateway';

@Injectable()
export class ChallengesService {
  constructor(
    @InjectModel(Challenge.name) private challengeModel: Model<Challenge>,
    @InjectModel(Category.name) private categoryModel: Model<Category>,
    @InjectModel(ChallengeSubmission.name) private challengeSubmissionModel: Model<ChallengeSubmission>,
    @InjectModel(TeamChallengeSolve.name) private teamChallengeSolveModel: Model<TeamChallengeSolve>,
    @InjectModel('User') private userModel: any,
    @InjectModel('Team') private teamModel: any,
    private notificationsService: NotificationsService,
    private notificationsGateway: NotificationsGateway,
  ) {}

  async getAllChallenges(query: ChallengePaginationDto, user: any): Promise<any> {
    const { page = 1, limit = 20, category, difficulty, search, onlySolved, onlyUnsolved } = query;
    
    // Base query - only return active challenges
    const filter: any = { isActive: true };
    
    // Add category and difficulty filters if provided
    if (category) filter.category = category;
    if (difficulty) filter.difficulty = difficulty;
    
    // Add search filter if provided
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $regex: search, $options: 'i' } },
      ];
    }

    // Get solved challenges by the user (always calculate this)
    const submissions = await this.challengeSubmissionModel
      .find({ userId: user._id, isCorrect: true })
      .select('challengeId')
      .lean();
    
    const solvedChallengeIds = submissions.map(sub => sub.challengeId);

    // Get team solve information if user is in a team
    let teamSolveInfo = new Map();
    if (user.teamId) {
      const teamSolves = await this.teamChallengeSolveModel
        .find({ teamId: user.teamId })
        .populate('solvedBy', 'username')
        .lean();
      
      // Get current team members to check if solver is still in team
      const team = await this.teamModel.findById(user.teamId).select('members').lean();
      const currentTeamMemberIds = team?.members
        .filter(m => m.status === 'active')
        .map(m => m.userId.toString()) || [];

      teamSolves.forEach(solve => {
        const solvedByUser = solve.solvedBy as any;
        const solverUserId = solvedByUser._id.toString();
        const isStillInTeam = currentTeamMemberIds.includes(solverUserId);
        
        teamSolveInfo.set(solve.challengeId.toString(), {
          userId: solverUserId,
          username: solvedByUser.username,
          solvedAt: solve.solvedAt,
          isStillInTeam: isStillInTeam
        });
      });
    }
    
    // Apply solved/unsolved filters if requested
    if (onlySolved) {
      filter._id = { $in: solvedChallengeIds };
    } else if (onlyUnsolved) {
      filter._id = { $nin: solvedChallengeIds };
    }
    
    // Pagination logic
    const skip = (page - 1) * limit;
    
    // Get challenges with pagination
    const challenges = await this.challengeModel
      .find(filter)
      .sort({ difficulty: 1, category: 1, title: 1 })
      .skip(skip)
      .limit(limit)
      .select('-flag')
      .lean();
    
    // Get total count of documents matching the filter
    const total = await this.challengeModel.countDocuments(filter);

    // Mark solved challenges and add team solve information
    const challengesWithSolvedStatus = challenges.map(challenge => {
      const challengeIdStr = (challenge._id as any).toString();
      const isSolvedByUser = solvedChallengeIds.some(id =>
        id.toString() === challengeIdStr
      );
      const teamSolve = teamSolveInfo.get(challengeIdStr);

      // Convert ObjectId to string for firstBlood.userId if it exists
      let processedChallenge = { ...challenge } as any;

      if (challenge.firstBlood && challenge.firstBlood.userId) {
        console.log(`🔍 Converting firstBlood userId for challenge ${challenge.title}:`, {
          original: challenge.firstBlood.userId,
          type: typeof challenge.firstBlood.userId,
          converted: challenge.firstBlood.userId.toString()
        });

        processedChallenge.firstBlood = {
          ...challenge.firstBlood,
          userId: challenge.firstBlood.userId.toString()
        };
      }

      // Convert ObjectId to string for flagsFirstBlood userIds if they exist
      if (challenge.flagsFirstBlood && Object.keys(challenge.flagsFirstBlood).length > 0) {
        const convertedFlagsFirstBlood = {} as any;
        Object.entries(challenge.flagsFirstBlood).forEach(([flagIndex, firstBlood]: [string, any]) => {
          convertedFlagsFirstBlood[flagIndex] = {
            ...firstBlood,
            userId: firstBlood.userId.toString()
          };
        });
        processedChallenge.flagsFirstBlood = convertedFlagsFirstBlood;
      }

      // Debug first blood data
      if (processedChallenge.firstBlood || (processedChallenge.flagsFirstBlood && Object.keys(processedChallenge.flagsFirstBlood).length > 0)) {
        console.log(`🩸 Challenge "${processedChallenge.title}" has first blood data:`, {
          firstBlood: processedChallenge.firstBlood,
          flagsFirstBlood: processedChallenge.flagsFirstBlood,
          currentUserId: user._id.toString()
        });
      }

      return {
        ...processedChallenge,
        solved: isSolvedByUser,
        solvedByTeammate: teamSolve && teamSolve.userId !== user._id.toString() ? teamSolve : undefined
      };
    });
    
    return {
      challenges: challengesWithSolvedStatus,
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    };
  }

  async getChallengeById(id: string, user: any): Promise<any> {
    if (!Types.ObjectId.isValid(id)) {
      throw new HttpException('Invalid challenge ID', HttpStatus.BAD_REQUEST);
    }

    const challenge = await this.challengeModel
      .findOne({ _id: id, isActive: true })
      .select('-flag')
      .lean();

    if (!challenge) {
      return null;
    }

    // Check if user has solved this challenge
    const submission = await this.challengeSubmissionModel.findOne({
      userId: user._id,
      challengeId: id,
      isCorrect: true,
    }).lean();

    // Add solved status to challenge
    const challengeWithSolvedStatus = {
      ...challenge,
      solved: !!submission
    };

    return challengeWithSolvedStatus;
  }

  async createChallenge(createChallengeDto: PublicCreateChallengeDto, authorId: string): Promise<Challenge> {
    const challenge = new this.challengeModel({
      ...createChallengeDto,
      authorId,
      solveCount: 0,
    });
    
    const savedChallenge = await challenge.save();
    
    // If challenge is created as active, send notification
    if (savedChallenge.isActive) {
      try {
        console.log(`🎯 Creating new challenge notification for "${savedChallenge.title}"`);
        const notification = await this.notificationsService.createNewChallengeNotification(
          (savedChallenge._id as any).toString(),
          savedChallenge.title,
          savedChallenge.category,
          savedChallenge.difficulty,
          savedChallenge.points
        );
        
        // Broadcast the new challenge notification to all users
        await this.notificationsGateway.broadcastGlobalNotification(notification);
        console.log(`✅ New challenge notification broadcasted successfully`);
      } catch (error) {
        console.error('Failed to create new challenge notification:', error);
      }
    }
    
    return savedChallenge;
  }

  async updateChallenge(id: string, updateChallengeDto: PublicUpdateChallengeDto): Promise<Challenge> {
    if (!Types.ObjectId.isValid(id)) {
      throw new HttpException('Invalid challenge ID', HttpStatus.BAD_REQUEST);
    }
    
    // Get the original challenge to check if status changed
    const originalChallenge = await this.challengeModel.findById(id);
    if (!originalChallenge) {
      throw new HttpException('Challenge not found', HttpStatus.NOT_FOUND);
    }
    
    const updatedChallenge = await this.challengeModel.findByIdAndUpdate(id, updateChallengeDto, { new: true }).exec();
    
    if (!updatedChallenge) {
      throw new HttpException('Challenge not found', HttpStatus.NOT_FOUND);
    }
    
    // If challenge status changed from inactive to active, send notification
    if (!originalChallenge.isActive && updatedChallenge.isActive) {
      try {
        console.log(`🎯 Creating new challenge notification for activated challenge "${updatedChallenge.title}"`);
        const notification = await this.notificationsService.createNewChallengeNotification(
          (updatedChallenge._id as any).toString(),
          updatedChallenge.title,
          updatedChallenge.category,
          updatedChallenge.difficulty,
          updatedChallenge.points
        );
        
        // Broadcast the new challenge notification to all users
        await this.notificationsGateway.broadcastGlobalNotification(notification);
        console.log(`✅ New challenge notification broadcasted successfully`);
      } catch (error) {
        console.error('Failed to create new challenge notification:', error);
      }
    }
    
    return updatedChallenge;
  }

  async deleteChallenge(id: string): Promise<boolean> {
    if (!Types.ObjectId.isValid(id)) {
      throw new HttpException('Invalid challenge ID', HttpStatus.BAD_REQUEST);
    }

    const result = await this.challengeModel.deleteOne({ _id: id }).exec();
    return result.deletedCount > 0;
  }

  async submitFlag(flagSubmissionDto: FlagSubmissionDto, user: any): Promise<any> {
    const { challengeId, flag } = flagSubmissionDto;

    if (!Types.ObjectId.isValid(challengeId)) {
      throw new HttpException('Invalid challenge ID', HttpStatus.BAD_REQUEST);
    }

    // Get the challenge
    const challenge = await this.challengeModel.findById(challengeId);
    if (!challenge) {
      throw new HttpException('Challenge not found', HttpStatus.NOT_FOUND);
    }

    if (!challenge.isActive) {
      throw new HttpException('This challenge is not active', HttpStatus.BAD_REQUEST);
    }

    // Check if user has already solved this challenge
    const existingSolve = await this.challengeSubmissionModel.findOne({
      userId: user._id,
      challengeId,
      isCorrect: true
    });

    if (existingSolve) {
      throw new HttpException('You have already solved this challenge', HttpStatus.BAD_REQUEST);
    }

    // If user is in a team, check if a teammate has already solved this challenge
    if (user.teamId) {
      const teamSolve = await this.teamChallengeSolveModel
        .findOne({ teamId: user.teamId, challengeId })
        .populate('solvedBy', 'username')
        .lean();

      if (teamSolve && (teamSolve.solvedBy as any)._id.toString() !== user._id.toString()) {
        // Check if the teammate who solved it is still in the team
        const team = await this.teamModel.findById(user.teamId).select('members').lean();
        const currentTeamMemberIds = team?.members
          .filter(m => m.status === 'active')
          .map(m => m.userId.toString()) || [];
        
        const solvedByUser = teamSolve.solvedBy as any;
        const solverUserId = solvedByUser._id.toString();
        const isStillInTeam = currentTeamMemberIds.includes(solverUserId);
        
        if (isStillInTeam) {
          throw new HttpException(
            `This challenge has already been solved by your teammate ${solvedByUser.username}`, 
            HttpStatus.BAD_REQUEST
          );
        }
      }
    }

    // Check flag against all possible flags
    let isCorrect = false;
    let matchedFlag: Flag | null = null;
    let flagIndex = -1;
    
    // First check against legacy flag field if it exists
    if (challenge.flag && challenge.flag === flag) {
      isCorrect = true;
      matchedFlag = {
        value: challenge.flag,
        points: challenge.points,
        isCaseSensitive: false,
        description: 'Primary flag'
      };
      // For legacy flags, we use -1 as flagIndex to indicate it's the legacy flag
      flagIndex = -1;
    } 
    // Then check against flags array if it exists
    else if (challenge.flags && challenge.flags.length > 0) {
      for (let i = 0; i < challenge.flags.length; i++) {
        const flagObj = challenge.flags[i];
        const flagToCompare = flagObj.isCaseSensitive ? flag : flag.toLowerCase();
        const valueToCompare = flagObj.isCaseSensitive ? flagObj.value : flagObj.value.toLowerCase();
        
        if (flagToCompare === valueToCompare) {
          isCorrect = true;
          matchedFlag = flagObj;
          flagIndex = i;
          break;
        }
      }
    }
    
    // Create submission record
    const submission = new this.challengeSubmissionModel({
      userId: user._id,
      challengeId,
      flagSubmitted: flag,
      isCorrect,
      pointsAwarded: 0,
      teamPointsAwarded: 0,
      isFirstTeamSolve: false,
      isFirstBlood: false,
      flagIndex
    });

    // If flag is incorrect, just save the submission and return
    if (!isCorrect) {
      await submission.save();
      return { 
        success: false, 
        message: 'Incorrect flag' 
      };
    }

    // Handle team-based logic if user is in a team
    if (user.teamId) {
      const teamResult = await this.handleTeamSubmission(challenge, user, submission);
      return teamResult;
    } else {
      // Handle individual user submission
      return this.handleIndividualSubmission(challenge, user, submission);
    }
  }

  private async handleTeamSubmission(challenge: Challenge, user: any, submission: ChallengeSubmission): Promise<any> {
    // Check if team has already solved this challenge
    const existingTeamSolve = await this.teamChallengeSolveModel.findOne({
      teamId: user.teamId,
      challengeId: challenge._id
    });

    // Check if this is the first solve globally for this flag
    let isFirstBlood = false;

    // If this is a multi-flag challenge, check first blood for the specific flag
    if (challenge.flags && challenge.flags.length > 0 && submission.flagIndex >= 0) {
      // Check if first blood already exists for this specific flag
      const hasFirstBlood = challenge.flagsFirstBlood && challenge.flagsFirstBlood[submission.flagIndex];

      if (hasFirstBlood) {
        console.log(`🩸 First blood already exists for flag ${submission.flagIndex} on challenge "${challenge.title}"`);
      } else {
        // Only check submissions if no first blood record exists
        isFirstBlood = await this.challengeSubmissionModel.countDocuments({
          challengeId: challenge._id,
          flagIndex: submission.flagIndex,
          isCorrect: true
        }) === 0;
        console.log(`🔍 Multi-flag challenge "${challenge.title}" flag ${submission.flagIndex}: isFirstBlood = ${isFirstBlood}`);
      }
    } else {
      // For single-flag challenges, check if first blood already exists
      if (challenge.firstBlood) {
        console.log(`🩸 First blood already exists for challenge "${challenge.title}"`);
      } else {
        // Only check submissions if no first blood record exists
        isFirstBlood = await this.challengeSubmissionModel.countDocuments({
          challengeId: challenge._id,
          isCorrect: true
        }) === 0;
        console.log(`🔍 Single-flag challenge "${challenge.title}": isFirstBlood = ${isFirstBlood}`);
      }
    }
    
    // Determine points based on the matched flag or challenge points
    let flagPoints = challenge.points;
    if (challenge.flags && challenge.flags.length > 0 && submission.flagIndex >= 0) {
      flagPoints = challenge.flags[submission.flagIndex].points || challenge.points;
    }

    if (!existingTeamSolve) {
      // First solve for the team
      submission.pointsAwarded = flagPoints;
      submission.teamPointsAwarded = flagPoints;
      submission.isFirstTeamSolve = true;
      submission.isFirstBlood = isFirstBlood;

      // Create team solve record
      const teamSolve = new this.teamChallengeSolveModel({
        teamId: user.teamId,
        challengeId: challenge._id,
        solvedBy: user._id,
        pointsAwarded: flagPoints,
        isFirstBlood,
        flagIndex: submission.flagIndex
      });
      
      // Update user score
      await this.userModel.findByIdAndUpdate(user._id, {
        $inc: { score: flagPoints }
      });
      
      // Update team score
      await this.teamModel.findByIdAndUpdate(user.teamId, {
        $inc: { teamScore: flagPoints }
      });

      // Update challenge solve count
      await this.challengeModel.findByIdAndUpdate(challenge._id, {
        $inc: { solveCount: 1 }
      });
      
      // If this is first blood, update challenge's firstBlood field
      if (isFirstBlood) {
        const firstBloodData = {
          userId: user._id.toString(), // Convert ObjectId to string for frontend compatibility
          username: user.username,
          timestamp: new Date(),
          teamId: user.teamId,
          teamName: user.teamName
        };
        
        // For multi-flag challenges, store the flag index in the firstBlood record
        if (challenge.flags && challenge.flags.length > 0 && submission.flagIndex >= 0) {
          await this.challengeModel.updateOne(
            { _id: challenge._id },
            {
              $set: {
                [`flagsFirstBlood.${submission.flagIndex}`]: firstBloodData
              }
            }
          );
        } else {
          // For single flag challenges, update the firstBlood field
          await this.challengeModel.updateOne(
            { _id: challenge._id },
            { $set: { firstBlood: firstBloodData } }
          );
        }

        // Create and broadcast first blood notification
        try {
          console.log(`🏆 Creating first blood notification for ${user.username} on challenge "${challenge.title}"`);
          const notification = await this.notificationsService.createFirstBloodNotification(
            (challenge._id as any).toString(),
            challenge.title,
            user.username,
            flagPoints
          );
          
          // Broadcast the first blood notification to all users
          await this.notificationsGateway.broadcastFirstBloodNotification(notification);
          console.log(`✅ First blood notification broadcasted successfully`);
        } catch (error) {
          console.error('Failed to create first blood notification:', error);
        }
      }
      
      // Save all records
      await Promise.all([
        submission.save(),
        teamSolve.save()
      ]);

      return {
        success: true,
        isFirstTeamSolve: true,
        isFirstBlood,
        pointsAwarded: flagPoints,
        message: isFirstBlood ? 'First blood! Congratulations!' : `Correct flag! Your team earned ${flagPoints} points!`
      };
    } else {
      // Team has already solved this challenge
      submission.pointsAwarded = 0;
      submission.teamPointsAwarded = 0;
      submission.isFirstTeamSolve = false;
      submission.isFirstBlood = false;
      
      await submission.save();
      
      return {
        success: true,
        isFirstTeamSolve: false,
        isFirstBlood: false,
        pointsAwarded: 0,
        message: 'Correct flag! No points awarded as your team already solved this challenge.'
      };
    }
  }

  private async handleIndividualSubmission(challenge: Challenge, user: any, submission: ChallengeSubmission): Promise<any> {
    // Check if this is the first solve globally for this flag
    let isFirstBlood = false;

    // If this is a multi-flag challenge, check first blood for the specific flag
    if (challenge.flags && challenge.flags.length > 0 && submission.flagIndex >= 0) {
      // Check if first blood already exists for this specific flag
      const hasFirstBlood = challenge.flagsFirstBlood && challenge.flagsFirstBlood[submission.flagIndex];

      if (hasFirstBlood) {
        console.log(`🩸 [Individual] First blood already exists for flag ${submission.flagIndex} on challenge "${challenge.title}"`);
      } else {
        // Only check submissions if no first blood record exists
        isFirstBlood = await this.challengeSubmissionModel.countDocuments({
          challengeId: challenge._id,
          flagIndex: submission.flagIndex,
          isCorrect: true
        }) === 0;
        console.log(`🔍 [Individual] Multi-flag challenge "${challenge.title}" flag ${submission.flagIndex}: isFirstBlood = ${isFirstBlood}`);
      }
    } else {
      // For single-flag challenges, check if first blood already exists
      if (challenge.firstBlood) {
        console.log(`🩸 [Individual] First blood already exists for challenge "${challenge.title}"`);
      } else {
        // Only check submissions if no first blood record exists
        isFirstBlood = await this.challengeSubmissionModel.countDocuments({
          challengeId: challenge._id,
          isCorrect: true
        }) === 0;
        console.log(`🔍 [Individual] Single-flag challenge "${challenge.title}": isFirstBlood = ${isFirstBlood}`);
      }
    }
    
    // Determine points based on the matched flag or challenge points
    let flagPoints = challenge.points;
    if (challenge.flags && challenge.flags.length > 0 && submission.flagIndex >= 0) {
      flagPoints = challenge.flags[submission.flagIndex].points || challenge.points;
    }
    
    submission.pointsAwarded = flagPoints;
    submission.isFirstBlood = isFirstBlood;
    
    // Update user score
    await this.userModel.findByIdAndUpdate(user._id, {
      $inc: { score: flagPoints }
    });
    
    // Update challenge solve count
    await this.challengeModel.findByIdAndUpdate(challenge._id, {
      $inc: { solveCount: 1 }
    });
    
    // If this is first blood, update challenge's firstBlood field
    if (isFirstBlood) {
      const firstBloodData = {
        userId: user._id.toString(), // Convert ObjectId to string for frontend compatibility
        username: user.username,
        timestamp: new Date(),
        teamId: user.teamId,
        teamName: user.teamName
      };

      // For multi-flag challenges, store the flag index in the firstBlood record
      if (challenge.flags && challenge.flags.length > 0 && submission.flagIndex >= 0) {
        await this.challengeModel.updateOne(
          { _id: challenge._id },
          {
            $set: {
              [`flagsFirstBlood.${submission.flagIndex}`]: firstBloodData
            }
          }
        );
      } else {
        // For single flag challenges, update the firstBlood field
        await this.challengeModel.updateOne(
          { _id: challenge._id },
          { $set: { firstBlood: firstBloodData } }
        );
      }

      // Create and broadcast first blood notification
      try {
        console.log(`🏆 Creating first blood notification for ${user.username} on challenge "${challenge.title}"`);
        const notification = await this.notificationsService.createFirstBloodNotification(
          (challenge._id as any).toString(),
          challenge.title,
          user.username,
          flagPoints
        );
        
        // Broadcast the first blood notification to all users
        await this.notificationsGateway.broadcastFirstBloodNotification(notification);
        console.log(`✅ First blood notification broadcasted successfully`);
      } catch (error) {
        console.error('Failed to create first blood notification:', error);
      }
    }
    
    await submission.save();
    
    return {
      success: true,
      isFirstBlood,
      pointsAwarded: flagPoints,
      message: isFirstBlood ? 'First blood! Congratulations!' : `Correct flag! You earned ${flagPoints} points!`
    };
  }

  async getCategories(): Promise<{ categories: any[], difficulties: ChallengeDifficulty[] }> {
    const categories = await this.categoryModel
      .find({ isActive: true })
      .sort({ sortOrder: 1, name: 1 })
      .select('name displayName color icon')
      .lean();

    return {
      categories: categories.map(cat => ({
        name: cat.name,
        displayName: cat.displayName,
        color: cat.color,
        icon: cat.icon
      })),
      difficulties: ['easy', 'medium', 'hard', 'insane']
    };
  }

  async getChallengeStats(user: any): Promise<any> {
    // Get total challenge count
    const totalChallenges = await this.challengeModel.countDocuments({ isActive: true });
    
    // Get solved challenges count
    const solvedSubmissions = await this.challengeSubmissionModel.find({
      userId: user._id,
      isCorrect: true
    }).select('challengeId').distinct('challengeId');
    
    const solvedCount = solvedSubmissions.length;
    
    // Calculate completion percentage
    const completionPercentage = totalChallenges > 0 
      ? Math.round((solvedCount / totalChallenges) * 100) 
      : 0;
    
    // Get category breakdown
    const categories = ['web', 'crypto', 'pwn', 'reverse', 'forensics', 'misc'];
    const categoryBreakdown = await Promise.all(
      categories.map(async (category) => {
        const totalInCategory = await this.challengeModel.countDocuments({ 
          category, 
          isActive: true 
        });
        
        const solvedInCategory = await this.challengeSubmissionModel.countDocuments({
          userId: user._id,
          isCorrect: true,
          challengeId: {
            $in: await this.challengeModel.find({ category, isActive: true }).distinct('_id')
          }
        });
        
        return {
          category,
          total: totalInCategory,
          solved: solvedInCategory,
          percentage: totalInCategory > 0 ? Math.round((solvedInCategory / totalInCategory) * 100) : 0
        };
      })
    );
    
    // Get difficulty breakdown
    const difficulties = ['easy', 'medium', 'hard', 'insane'];
    const difficultyBreakdown = await Promise.all(
      difficulties.map(async (difficulty) => {
        const totalInDifficulty = await this.challengeModel.countDocuments({ 
          difficulty, 
          isActive: true 
        });
        
        const solvedInDifficulty = await this.challengeSubmissionModel.countDocuments({
          userId: user._id,
          isCorrect: true,
          challengeId: {
            $in: await this.challengeModel.find({ difficulty, isActive: true }).distinct('_id')
          }
        });
        
        return {
          difficulty,
          total: totalInDifficulty,
          solved: solvedInDifficulty,
          percentage: totalInDifficulty > 0 ? Math.round((solvedInDifficulty / totalInDifficulty) * 100) : 0
        };
      })
    );
    
    return {
      totalChallenges,
      solvedChallenges: solvedCount,
      completionPercentage,
      categoryBreakdown,
      difficultyBreakdown
    };
  }

  async getSolvedChallenges(user: any): Promise<any> {
    const submissions = await this.challengeSubmissionModel
      .find({ userId: user._id, isCorrect: true })
      .populate('challengeId', '-flag')
      .sort({ submittedAt: -1 })
      .lean();
    
    return submissions.map(submission => {
      // Check if challengeId exists and is a populated object
      const challenge = submission.challengeId as any;
      if (!challenge || typeof challenge !== 'object') {
        return null;
      }
      
      return {
        challengeId: challenge._id,
        title: challenge.title,
        category: challenge.category,
        difficulty: challenge.difficulty,
        points: submission.pointsAwarded,
        solvedAt: submission.submittedAt,
        isFirstBlood: submission.isFirstBlood
      };
    }).filter(item => item !== null);
  }
}