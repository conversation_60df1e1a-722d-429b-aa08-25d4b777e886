import {
  Controller,
  Get,
  Post,
  Put,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  Logger,
  HttpCode,
  HttpStatus,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import * as path from 'path';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { RolesGuard } from '../../auth/roles.guard';
import { Roles } from '../../auth/roles.decorator';
import { MachineTemplateService } from '../services/machine-template.service';
import { MachineMaintenanceService } from '../services/machine-maintenance.service';
import { DockerMachineService } from '../services/docker-machine.service';
import { ArchiveExtractorService } from '../services/archive-extractor.service';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';

export interface CreateMachineTemplateDto {
  name: string;
  description: string;
  category: 'web' | 'crypto' | 'pwn' | 'reverse' | 'forensics' | 'misc' | 'osint';
  difficulty: 'easy' | 'medium' | 'hard' | 'insane';
  os: 'linux' | 'windows' | 'other';
  machineType: 'docker' | 'ova' | 'qcow2';
  dockerImage?: string;
  exposedPorts: number[];
  requiredRAM: number;
  requiredCPU: number;
  maxInstances: number;
  flags: Array<{
    name: string;
    value: string;
    type: 'root' | 'user' | 'www-data' | 'custom';
    points: number;
    description?: string;
    isRequired: boolean;
  }>;
  topics: Array<{
    title: string;
    description: string;
    imageUrl?: string;
    order: number;
  }>;
  hints: string[];
  walkthrough?: string;
  releaseDate: Date;
  retireDate?: Date;
  tags: string[];
}

@ApiTags('Machine Admin')
@ApiBearerAuth()
@Controller('admin/machines')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin')
export class MachineAdminController {
  private readonly logger = new Logger(MachineAdminController.name);

  constructor(
    private readonly machineTemplateService: MachineTemplateService,
    private readonly machineMaintenanceService: MachineMaintenanceService,
    private readonly dockerMachineService: DockerMachineService,
    private readonly archiveExtractorService: ArchiveExtractorService,
  ) {}

  /**
   * Get all machine templates (admin view)
   */
  @Get()
  @ApiOperation({ summary: 'Get all machine templates (admin)' })
  @ApiResponse({ status: 200, description: 'List of all machine templates' })
  async getAllTemplates(    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('includeInactive') includeInactive?: string,
  ) {
    return this.machineTemplateService.getAllTemplates({
      page: page ? parseInt(page.toString()) : undefined,
      limit: limit ? parseInt(limit.toString()) : undefined,
      includeInactive: includeInactive === 'true',
    });
  }

  /**
   * Create a new machine template
   */
  @Post()
  @ApiOperation({ summary: 'Create a new machine template' })
  @ApiResponse({ status: 201, description: 'Machine template created successfully' })
  @HttpCode(HttpStatus.CREATED)
  async createTemplate(
    @Request() req: any,
    @Body() createDto: CreateMachineTemplateDto
  ) {
    this.logger.log(`Creating machine template: ${createDto.name}`);
    return this.machineTemplateService.createTemplate(req.user.userId, createDto);
  }

  /**
   * Upload machine files (Docker image, OVA, etc.)
   */
  @Post(':id/upload')
  @ApiOperation({ summary: 'Upload machine files' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 200, description: 'File uploaded successfully' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadMachineFile(
    @Param('id') templateId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { fileType: 'dockerfile' | 'ova' | 'vmdk' | 'qcow2' | 'resource' }
  ) {
    this.logger.log(`Uploading file for template ${templateId}: ${file.originalname}`);
    return this.machineTemplateService.uploadFile(templateId, file, body.fileType);
  }

  /**
   * Upload machine template archive (ZIP/7z)
   */
  @Post(':id/upload-archive')
  @ApiOperation({ summary: 'Upload machine template archive (ZIP/7z)' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 200, description: 'Archive uploaded and extracted successfully' })
  @UseInterceptors(FileInterceptor('archive', {
    limits: { fileSize: 500 * 1024 * 1024 }, // 500MB limit
    fileFilter: (req, file, cb) => {
      const allowedTypes = ['.zip', '.7z'];
      const fileExt = path.extname(file.originalname).toLowerCase();
      if (allowedTypes.includes(fileExt)) {
        cb(null, true);
      } else {
        cb(new BadRequestException('Only ZIP and 7z files are allowed'), false);
      }
    }
  }))
  async uploadMachineArchive(
    @Param('id') templateId: string,
    @UploadedFile() file: Express.Multer.File
  ) {
    this.logger.log(`Uploading archive for template ${templateId}: ${file.originalname}`);
    return this.machineTemplateService.uploadMachineArchive(templateId, file);
  }

  /**
   * Build Docker image from uploaded template
   */
  @Post(':id/build-image')
  @ApiOperation({ summary: 'Build Docker image from uploaded template' })
  @ApiResponse({ status: 200, description: 'Image built successfully' })
  async buildMachineImage(@Param('id') templateId: string) {
    const template = await this.machineTemplateService.getTemplateById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    const templatePath = this.machineTemplateService.getExtractedTemplatePath(template.slug);
    const imageName = `rakcha/${template.slug}:latest`;

    if (!(await this.machineTemplateService.hasExtractedFiles(templateId))) {
      throw new BadRequestException('Template files not found. Please upload archive first.');
    }

    try {
      // Build the Docker image
      const buildResult = await this.dockerMachineService.buildFromTemplate(templatePath, imageName);

      // Update the template with the Docker image name
      await this.machineTemplateService.updateTemplate(templateId, {
        dockerImage: imageName
      });

      this.logger.log(`Successfully built and updated template ${templateId} with image ${imageName}`);

      return buildResult;
    } catch (error) {
      this.logger.error(`Failed to build image for template ${templateId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get build logs for machine template
   */
  @Get(':id/build-logs')
  @ApiOperation({ summary: 'Get build logs for machine template' })
  @ApiResponse({ status: 200, description: 'Build logs retrieved' })
  async getBuildLogs(@Param('id') templateId: string) {
    const template = await this.machineTemplateService.getTemplateById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    return this.dockerMachineService.getBuildLogs(template.slug);
  }

  /**
   * Remove built Docker image
   */
  @Delete(':id/image')
  @ApiOperation({ summary: 'Remove built Docker image' })
  @ApiResponse({ status: 200, description: 'Image removed successfully' })
  async removeImage(@Param('id') templateId: string) {
    const template = await this.machineTemplateService.getTemplateById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    await this.dockerMachineService.removeImage(template.slug);
    return { message: 'Image removed successfully' };
  }

  /**
   * Check if Docker image exists for template
   */
  @Get(':id/image-status')
  @ApiOperation({ summary: 'Check if Docker image exists for template' })
  @ApiResponse({ status: 200, description: 'Image status retrieved' })
  async getImageStatus(@Param('id') templateId: string) {
    const template = await this.machineTemplateService.getTemplateById(templateId);
    if (!template) {
      throw new NotFoundException('Machine template not found');
    }

    const imageName = `rakcha/${template.slug}:latest`;
    const exists = await this.dockerMachineService.imageExists(template.slug);
    const hasFiles = await this.machineTemplateService.hasExtractedFiles(templateId);

    // If image exists but template doesn't have dockerImage field, update it
    if (exists && !template.dockerImage) {
      try {
        await this.machineTemplateService.updateTemplate(templateId, {
          dockerImage: imageName
        });
        this.logger.log(`Auto-updated template ${templateId} with existing Docker image ${imageName}`);
      } catch (error) {
        this.logger.warn(`Failed to auto-update template ${templateId}: ${error.message}`);
      }
    }

    return {
      imageExists: exists,
      hasExtractedFiles: hasFiles,
      imageName,
      canBuild: hasFiles && !exists,
      templateHasImage: !!template.dockerImage
    };
  }

  /**
   * Get system status for archive support
   */
  @Get('system-status')
  @ApiOperation({ summary: 'Get system status and archive support information' })
  @ApiResponse({ status: 200, description: 'System status retrieved' })
  async getSystemStatus() {
    return this.archiveExtractorService.getSystemStatus();
  }

  /**
   * Upload topic image
   */
  @Post('topics/upload-image')
  @ApiOperation({ summary: 'Upload topic image' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 200, description: 'Topic image uploaded successfully' })
  @UseInterceptors(FileInterceptor('image', {
    fileFilter: (req, file, callback) => {
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (allowedMimeTypes.includes(file.mimetype)) {
        callback(null, true);
      } else {
        callback(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP files are allowed.'), false);
      }
    },
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
  }))
  async uploadTopicImage(
    @UploadedFile() file: Express.Multer.File
  ) {
    this.logger.log(`Uploading topic image: ${file.originalname}`);
    return this.machineTemplateService.uploadTopicImage(file);
  }

  /**
   * Build Docker image from Dockerfile
   */
  @Post(':id/build')
  @ApiOperation({ summary: 'Build Docker image from Dockerfile' })
  @ApiResponse({ status: 200, description: 'Docker image built successfully' })
  async buildDockerImage(
    @Param('id') templateId: string,
    @Body() body: { dockerfilePath?: string; buildArgs?: Record<string, string> }
  ) {
    this.logger.log(`Building Docker image for template ${templateId}`);
    return this.machineTemplateService.buildDockerImage(templateId, body.dockerfilePath, body.buildArgs);
  }

  /**
   * Update machine template
   */
  @Put(':id')
  @ApiOperation({ summary: 'Update machine template' })
  @ApiResponse({ status: 200, description: 'Machine template updated successfully' })
  async updateTemplate(
    @Param('id') templateId: string,
    @Body() updateDto: Partial<CreateMachineTemplateDto>
  ) {
    this.logger.log(`Updating machine template ${templateId}`);
    return this.machineTemplateService.updateTemplate(templateId, updateDto);
  }

  /**
   * Delete machine template
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete machine template' })
  @ApiResponse({ status: 200, description: 'Machine template deleted successfully' })
  async deleteTemplate(@Param('id') templateId: string) {
    this.logger.log(`Deleting machine template ${templateId}`);
    await this.machineTemplateService.deleteTemplate(templateId);
    return { message: 'Machine template deleted successfully' };
  }

  /**
   * Activate/deactivate machine template
   */
  @Patch(':id/status')
  @ApiOperation({ summary: 'Toggle machine template status' })
  @ApiResponse({ status: 200, description: 'Machine template status updated' })
  async toggleTemplateStatus(
    @Param('id') templateId: string,
    @Body() body: { isActive: boolean }
  ) {
    this.logger.log(`Toggling template ${templateId} status to ${body.isActive}`);
    return this.machineTemplateService.toggleStatus(templateId, body.isActive);
  }

  /**
   * Test machine template (spawn test instance)
   */
  @Post(':id/test')
  @ApiOperation({ summary: 'Test machine template by spawning instance' })
  @ApiResponse({ status: 200, description: 'Test instance created' })
  async testTemplate(
    @Request() req: any,
    @Param('id') templateId: string
  ) {
    this.logger.log(`Testing machine template ${templateId}`);
    return this.machineTemplateService.testTemplate(templateId, req.user.userId);
  }

  /**
   * Get all active instances (admin overview)
   */
  @Get('instances/all')
  @ApiOperation({ summary: 'Get all machine instances (admin)' })
  @ApiResponse({ status: 200, description: 'List of all machine instances' })
  async getAllInstances(
    @Query('status') status?: string,
    @Query('templateId') templateId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.machineTemplateService.getAllInstances({
      status,
      templateId,
      page: page ? parseInt(page.toString()) : undefined,
      limit: limit ? parseInt(limit.toString()) : undefined,
    });
  }

  /**
   * Force terminate any instance (admin)
   */
  @Delete('instances/:id/force')
  @ApiOperation({ summary: 'Force terminate machine instance (admin)' })
  @ApiResponse({ status: 200, description: 'Instance force terminated' })
  async forceTerminateInstance(@Param('id') instanceId: string) {
    this.logger.log(`Force terminating instance ${instanceId}`);
    await this.machineTemplateService.forceTerminateInstance(instanceId);
    return { message: 'Instance force terminated successfully' };
  }

  /**
   * Get maintenance service statistics
   */
  @Get('maintenance/stats')
  @ApiOperation({ summary: 'Get maintenance service statistics' })
  @ApiResponse({ status: 200, description: 'Maintenance statistics' })
  async getMaintenanceStats() {
    return this.machineMaintenanceService.getMaintenanceStats();
  }

  /**
   * Trigger manual cleanup
   */
  @Post('maintenance/cleanup')
  @ApiOperation({ summary: 'Trigger manual maintenance cleanup' })
  @ApiResponse({ status: 200, description: 'Cleanup triggered successfully' })
  async triggerCleanup() {
    this.logger.log('Triggering manual maintenance cleanup');
    
    // Run cleanup tasks manually
    await Promise.all([
      this.machineMaintenanceService.cleanupOrphanedContainers(),
      this.machineMaintenanceService.autoShutdownExpiredInstances(),
      this.machineMaintenanceService.cleanupOldInstances(),
    ]);

    return { message: 'Manual cleanup completed successfully' };
  }

  /**
   * Get system resource usage
   */
  @Get('system/resources')
  @ApiOperation({ summary: 'Get system resource usage' })
  @ApiResponse({ status: 200, description: 'System resource information' })
  async getSystemResources() {
    return this.machineTemplateService.getSystemResources();
  }

  /**
   * Get machine usage analytics
   */
  @Get('analytics/usage')
  @ApiOperation({ summary: 'Get machine usage analytics' })
  @ApiResponse({ status: 200, description: 'Usage analytics data' })
  async getUsageAnalytics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('templateId') templateId?: string,
  ) {
    return this.machineTemplateService.getUsageAnalytics({
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      templateId,
    });
  }

  /**
   * Export machine templates
   */
  @Get('export')
  @ApiOperation({ summary: 'Export machine templates configuration' })
  @ApiResponse({ status: 200, description: 'Machine templates export data' })
  async exportTemplates(@Query('format') format: 'json' | 'yaml' = 'json') {
    return this.machineTemplateService.exportTemplates(format);
  }

  /**
   * Import machine templates
   */
  @Post('import')
  @ApiOperation({ summary: 'Import machine templates configuration' })
  @ApiResponse({ status: 200, description: 'Templates imported successfully' })
  @UseInterceptors(FileInterceptor('file'))  async importTemplates(
    @Request() req: any,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { overwrite?: string }
  ) {
    this.logger.log(`Importing machine templates from file: ${file.originalname}`);
    return this.machineTemplateService.importTemplates(
      req.user.userId,
      file,
      body.overwrite === 'true'
    );
  }
}
