#!/bin/bash

# Create log directory if it doesn't exist
mkdir -p /var/log/openvpn

# Log script execution
{
    echo "=== OpenVPN Up Script ==="
    echo "Date: $(date)"
    echo "Interface: $1"
    echo "MTU: $2"
    echo "Link MTU: $3"
    echo "Local IP: $4"
    echo "Remote IP: $5"
    echo "Init: $6"
    
    # Enable IP forwarding
    echo 1 > /proc/sys/net/ipv4/ip_forward
    echo 1 > /proc/sys/net/ipv6/conf/all/forwarding 2>/dev/null || true
    
    # Flush all rules
    iptables -F
    iptables -t nat -F
    iptables -t mangle -F
    
    # Set default policies
    iptables -P INPUT ACCEPT
    iptables -P FORWARD ACCEPT
    iptables -P OUTPUT ACCEPT
    
    # Allow all traffic on the TUN interface
    iptables -A INPUT -i tun+ -j ACCEPT
    iptables -A FORWARD -i tun+ -j ACCEPT
    iptables -A OUTPUT -o tun+ -j ACCEPT
    
    # Allow established connections
    iptables -A INPUT -m state --state RELATED,ESTABLISHED -j ACCEPT
    iptables -A FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT
    
    # Enable NAT for VPN clients
    iptables -t nat -A POSTROUTING -s ********/24 -o eth0 -j MASQUERADE
    
    # Allow forwarding from VPN to internet
    iptables -A FORWARD -i tun+ -o eth0 -m state --state NEW,RELATED,ESTABLISHED -j ACCEPT
    iptables -A FORWARD -i eth0 -o tun+ -m state --state RELATED,ESTABLISHED -j ACCEPT
    
    # Allow DNS traffic
    iptables -A FORWARD -i tun+ -p udp --dport 53 -j ACCEPT
    iptables -A FORWARD -i tun+ -p tcp --dport 53 -j ACCEPT
    
    # Log current iptables rules
    echo "=== Current iptables rules ==="
    iptables -t nat -L -v -n
    iptables -L -v -n
    
    echo "=== Network interfaces ==="
    ip a
    echo "=== Routes ==="
    ip route
    
    echo "=== System info ==="
    uname -a
    echo "=== OpenVPN version ==="
    openvpn --version
    
} >> /var/log/openvpn/up-script.log 2>&1

exit 0
