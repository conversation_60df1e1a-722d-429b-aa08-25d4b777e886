import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MachineCard } from './MachineCard';
import { MachineFilters } from './MachineFilters';
import { ActiveInstances } from './ActiveInstances';
import { MachineStartModal } from '../Admin/MachineStartModal';
import machineService, { MachineTemplate, MachineInstance } from '../../services/machines';
import './Machines.css';
import { 
  Monitor, 
  AlertCircle, 
  RefreshCw, 
  Search,
  Filter,
  Grid,
  List,
  ChevronDown,
  X,
  Target,
  Trophy,
  Star,
  TrendingUp,
  Layers,
  Gauge,
  Activity,
  Sparkles,
  Server,
  Cpu,
  HardDrive,
  Zap,
  Shield,
  Lock,
  Eye,
  EyeOff,
  Hexagon,
  Triangle,
  Circle,
  Square,
  Diamond
} from 'lucide-react';

interface Filters {
  category: string;
  difficulty: string;
  search: string;
}

const CATEGORY_ICONS: Record<string, React.ComponentType<any>> = {
  web: Shield,
  crypto: Lock,
  pwn: Zap,
  reverse: RefreshCw,
  forensics: Search,
  misc: Star,
  linux: Server,
  windows: Monitor,
  network: Cpu,
  database: HardDrive
};

const CATEGORY_COLORS: Record<string, { bg: string; text: string }> = {
  web: { bg: 'from-blue-500 to-cyan-500', text: 'text-blue-400' },
  crypto: { bg: 'from-purple-500 to-pink-500', text: 'text-purple-400' },
  pwn: { bg: 'from-red-500 to-orange-500', text: 'text-red-400' },
  reverse: { bg: 'from-green-500 to-teal-500', text: 'text-green-400' },
  forensics: { bg: 'from-yellow-500 to-amber-500', text: 'text-yellow-400' },
  misc: { bg: 'from-indigo-500 to-purple-500', text: 'text-indigo-400' },
  linux: { bg: 'from-orange-500 to-red-500', text: 'text-orange-400' },
  windows: { bg: 'from-blue-500 to-indigo-500', text: 'text-blue-400' },
  network: { bg: 'from-cyan-500 to-blue-500', text: 'text-cyan-400' },
  database: { bg: 'from-green-500 to-emerald-500', text: 'text-green-400' }
};

const DIFFICULTY_SHAPES: Record<string, React.ComponentType<any>> = {
  easy: Circle,
  medium: Triangle,
  hard: Square,
  insane: Diamond
};

const DIFFICULTY_COLORS: Record<string, string> = {
  easy: 'from-green-500 to-emerald-500',
  medium: 'from-yellow-500 to-orange-500',
  hard: 'from-red-500 to-pink-500',
  insane: 'from-purple-500 to-indigo-500'
};

export function Machines() {
  const [templates, setTemplates] = useState<MachineTemplate[]>([]);
  const [instances, setInstances] = useState<MachineInstance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<Filters>({
    category: 'all',
    difficulty: 'all',
    search: ''
  });
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [animationKey, setAnimationKey] = useState(0);
  const [showStartModal, setShowStartModal] = useState(false);
  const [startingTemplate, setStartingTemplate] = useState<MachineTemplate | null>(null);

  React.useEffect(() => {
    setAnimationKey(prev => prev + 1);
  }, [filters.category, filters.difficulty]);

  useEffect(() => {
    loadMachines();
    loadInstances();
  }, []);

  const loadMachines = async () => {
    try {
      setLoading(true);
      const data = await machineService.getAvailableTemplates();
      setTemplates(data.templates || []);
    } catch (err) {
      setError('Failed to load machines');
      console.error('Error loading machines:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadInstances = async () => {
    try {
      const data = await machineService.getUserInstances();
      setInstances(data);
    } catch (err) {
      console.error('Error loading instances:', err);
    }
  };

  const handleShowStartModal = (template: MachineTemplate) => {
    setStartingTemplate(template);
    setShowStartModal(true);
  };

  const handleStartInstance = async (machineId: string) => {
    try {
      await machineService.spawnMachine(machineId);
      await loadMachines();
      await loadInstances();
      setShowStartModal(false);
      setStartingTemplate(null);
    } catch (err) {
      console.error('Error starting machine instance:', err);
      throw err;
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([loadMachines(), loadInstances()]);
    } catch (error) {
      console.error('Failed to refresh machines:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const filteredTemplates = useMemo(() => {
    return templates.filter(template => {
      if (!template.isActive) return false;
      const matchesCategory = filters.category === 'all' || template.category === filters.category;
      const matchesDifficulty = filters.difficulty === 'all' || template.difficulty === filters.difficulty;
      const matchesSearch = filters.search === '' || 
        template.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        template.description.toLowerCase().includes(filters.search.toLowerCase());

      return matchesCategory && matchesDifficulty && matchesSearch;
    });
  }, [templates, filters]);

  const stats = useMemo(() => {
    const total = templates.length;
    const active = templates.filter(t => t.isActive).length;
    const running = instances.filter(i => i.status === 'running').length;
    const available = filteredTemplates.length;
    
    // Category breakdown
    const categoryStats = Object.keys(CATEGORY_COLORS).map(category => {
      const categoryTotal = templates.filter(t => t.category === category).length;
      const categoryActive = templates.filter(t => t.category === category && t.isActive).length;
      return {
        category,
        total: categoryTotal,
        active: categoryActive,
        percentage: categoryTotal > 0 ? Math.round((categoryActive / categoryTotal) * 100) : 0
      };
    }).filter(stat => stat.total > 0);

    // Difficulty breakdown
    const difficultyStats = Object.keys(DIFFICULTY_SHAPES).map(difficulty => {
      const difficultyTotal = templates.filter(t => t.difficulty === difficulty).length;
      const difficultyActive = templates.filter(t => t.difficulty === difficulty && t.isActive).length;
      return {
        difficulty,
        total: difficultyTotal,
        active: difficultyActive,
        percentage: difficultyTotal > 0 ? Math.round((difficultyActive / difficultyTotal) * 100) : 0
      };
    }).filter(stat => stat.total > 0);
    
    return { total, active, running, available, categoryStats, difficultyStats };
  }, [templates, instances, filteredTemplates]);

  const runningInstances = instances.filter(instance => instance.status === 'running');
  const categories = ['all', ...Array.from(new Set(templates.map(t => t.category)))];
  const difficulties = ['all', 'easy', 'medium', 'hard', 'insane'];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 text-purple-400 animate-spin mx-auto mb-4" />
          <p className="text-purple-200/80">Loading machines...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-400 text-lg font-medium mb-2">Error Loading Machines</p>
          <p className="text-purple-200/60 mb-4">{error}</p>
          <button
            onClick={loadMachines}
            className="px-4 py-2 bg-purple-600/20 text-purple-400 border border-purple-600/30 rounded-lg hover:bg-purple-600/30 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-pink-900/20"></div>
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-purple-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 1, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Floating geometric shapes */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <motion.div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full morph-shape animate-float" />
        <motion.div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '2s' }} />
        <motion.div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-pink-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '4s' }} />
        <motion.div className="absolute bottom-40 right-1/3 w-28 h-28 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-full morph-shape animate-float" style={{ animationDelay: '6s' }} />
      </div>

      <div className="relative z-10 space-y-12 p-8">
        {/* Hero Header */}
        <motion.div 
          className="text-center space-y-6"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          <div className="flex items-center justify-center space-x-4">
            <motion.div 
              className="relative p-4 glass-card rounded-2xl"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Monitor className="w-12 h-12 text-purple-400" />
              <motion.div
                className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center"
                animate={{ rotate: 360 }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <Sparkles className="w-3 h-3 text-white" />
              </motion.div>
            </motion.div>
            <motion.h1 
              className="text-6xl font-black bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              Machine Lab
            </motion.h1>
          </div>
          <motion.p 
            className="text-xl text-purple-200/80 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            Launch and manage virtual machines for hands-on cybersecurity practice and real-world scenarios
          </motion.p>
          
          {/* Floating Stats Preview */}
          <motion.div 
            className="flex justify-center space-x-8 mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
          >
            {[
              { label: 'Running', value: stats.running, icon: Activity },
              { label: 'Available', value: stats.available, icon: Monitor },
              { label: 'Active', value: stats.active, icon: Server },
              { label: 'Total', value: stats.total, icon: Target },
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                whileHover={{ scale: 1.1, y: -5 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  delay: 0.8 + index * 0.1,
                  duration: 0.5,
                  type: "spring",
                  stiffness: 300
                }}
              >
                <div className="w-16 h-16 mx-auto mb-2 glass-card rounded-full flex items-center justify-center">
                  <stat.icon className="w-8 h-8 text-purple-400" />
                </div>
                <div className="text-2xl font-bold text-purple-200">{stat.value}</div>
                <div className="text-sm text-purple-300/70">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Enhanced Search and Controls */}
        <motion.div
          className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.8 }}
        >
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-purple-500/10 to-pink-500/10 rounded-full blur-3xl"></div>
          
          <div className="relative z-10 space-y-6">
            {/* Search Bar */}
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-purple-400" />
                <motion.input
                  type="text"
                  placeholder="Search machines by name or description..."
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                  className="w-full pl-12 pr-4 py-4 bg-purple-900/20 border border-purple-500/30 rounded-2xl text-white placeholder-purple-300/50 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 backdrop-blur-sm transition-all duration-300"
                  whileFocus={{ scale: 1.02 }}
                />
                {filters.search && (
                  <motion.button
                    onClick={() => setFilters({ ...filters, search: '' })}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 p-1 hover:bg-purple-500/20 rounded-full transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <X className="w-4 h-4 text-purple-400" />
                  </motion.button>
                )}
              </div>
              
              <motion.button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center space-x-2 px-6 py-4 rounded-2xl font-medium transition-all duration-300 ${
                  showFilters 
                    ? 'bg-purple-500/20 text-purple-300 border border-purple-500/30' 
                    : 'bg-purple-900/20 text-purple-200/80 border border-purple-500/20 hover:bg-purple-500/10'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Filter className="w-5 h-5" />
                <span>Filters</span>
                <motion.div
                  animate={{ rotate: showFilters ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <ChevronDown className="w-4 h-4" />
                </motion.div>
              </motion.button>

              <motion.button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="flex items-center space-x-2 px-6 py-4 bg-blue-600/20 text-blue-400 border border-blue-600/30 rounded-2xl hover:bg-blue-600/30 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  animate={{ rotate: isRefreshing ? 360 : 0 }}
                  transition={{ duration: 1, repeat: isRefreshing ? Infinity : 0, ease: "linear" }}
                >
                  <RefreshCw className="w-5 h-5" />
                </motion.div>
                <span className="hidden sm:inline">Refresh</span>
              </motion.button>

              <div className="flex items-center space-x-2">
                <motion.button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    viewMode === 'grid' 
                      ? 'bg-purple-500/20 text-purple-400' 
                      : 'text-purple-200/60 hover:text-purple-400'
                  }`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Grid className="w-4 h-4" />
                </motion.button>
                <motion.button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    viewMode === 'list' 
                      ? 'bg-purple-500/20 text-purple-400' 
                      : 'text-purple-200/60 hover:text-purple-400'
                  }`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <List className="w-4 h-4" />
                </motion.button>
              </div>
            </div>

            {/* Expandable Filters */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="border-t border-purple-500/20 pt-6"
                >
                  <MachineFilters
                    filters={filters}
                    onFiltersChange={setFilters}
                    categories={categories}
                    difficulties={difficulties}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>

        {/* Enhanced Analytics Dashboard */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Category Overview */}
          <motion.div
            className="xl:col-span-2 glass-card-dark p-8 rounded-3xl relative overflow-hidden"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1.4, duration: 0.8 }}
          >
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-3xl"></div>
            <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
              <Layers className="w-8 h-8" />
              <span>Category Overview</span>
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
              {stats.categoryStats.map((category, index) => {
                const IconComponent = CATEGORY_ICONS[category.category] || Server;
                const colors = CATEGORY_COLORS[category.category] || CATEGORY_COLORS.misc;
                
                return (
                  <motion.div
                    key={category.category}
                    className="relative p-6 glass-card rounded-2xl cursor-pointer group"
                    whileHover={{ scale: 1.05, y: -5 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setFilters({ ...filters, category: filters.category === category.category ? 'all' : category.category })}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.6 + index * 0.1 }}
                  >
                    <div className={`absolute inset-0 bg-gradient-to-br ${colors.bg} opacity-5 group-hover:opacity-10 transition-opacity duration-300`}></div>
                    <div className="relative z-10 text-center space-y-3">
                      <div className={`w-12 h-12 mx-auto bg-gradient-to-r ${colors.bg} rounded-xl flex items-center justify-center`}>
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-purple-200 capitalize">{category.category}</h4>
                      <div className="text-sm text-purple-300/70">
                        {category.active}/{category.total} active
                      </div>
                      <div className="w-full bg-purple-900/30 rounded-full h-2">
                        <motion.div
                          className={`bg-gradient-to-r ${colors.bg} h-2 rounded-full`}
                          initial={{ width: 0 }}
                          animate={{ width: `${category.percentage}%` }}
                          transition={{ delay: 1.8 + index * 0.1, duration: 1 }}
                        />
                      </div>
                      <div className="text-xs font-medium text-purple-300">{category.percentage}%</div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Difficulty Breakdown */}
          <motion.div
            className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1.5, duration: 0.8 }}
          >
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-purple-500/20 to-pink-500/20 rounded-full blur-3xl"></div>
            <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
              <Gauge className="w-8 h-8" />
              <span>Difficulty</span>
            </h3>
            <div className="space-y-6">
              {stats.difficultyStats.map((difficulty, index) => {
                const ShapeComponent = DIFFICULTY_SHAPES[difficulty.difficulty] || Circle;
                const colorGradient = DIFFICULTY_COLORS[difficulty.difficulty] || DIFFICULTY_COLORS.easy;
                
                return (
                  <motion.div
                    key={difficulty.difficulty}
                    className="flex items-center justify-between p-4 glass-card rounded-xl cursor-pointer group"
                    whileHover={{ scale: 1.02, x: 5 }}
                    onClick={() => setFilters({ ...filters, difficulty: filters.difficulty === difficulty.difficulty ? 'all' : difficulty.difficulty })}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.7 + index * 0.1 }}
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 bg-gradient-to-r ${colorGradient} rounded-lg`}>
                        <ShapeComponent className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="font-medium text-purple-200 capitalize">{difficulty.difficulty}</h4>
                        <div className="text-sm text-purple-300/70">
                          {difficulty.active}/{difficulty.total}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-purple-300">{difficulty.percentage}%</div>
                      <div className="w-16 bg-purple-900/30 rounded-full h-2 mt-1">
                        <motion.div
                          className={`bg-gradient-to-r ${colorGradient} h-2 rounded-full`}
                          initial={{ width: 0 }}
                          animate={{ width: `${difficulty.percentage}%` }}
                          transition={{ delay: 1.9 + index * 0.1, duration: 0.8 }}
                        />
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </div>

        {/* Active Instances */}
        {runningInstances.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.6, duration: 0.8 }}
          >
            <ActiveInstances 
              instances={runningInstances} 
              templates={templates}
              onRefresh={loadInstances}
            />
          </motion.div>
        )}

        {/* Machines Display */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.8, duration: 0.8 }}
        >
          <div className="flex items-center justify-between mb-8">
            <motion.h2 
              className="text-3xl font-bold text-purple-300 flex items-center space-x-3"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 2, duration: 0.6 }}
            >
              <Monitor className="w-8 h-8" />
              <span>{filteredTemplates.length} Machine{filteredTemplates.length !== 1 ? 's' : ''}</span>
            </motion.h2>
            
            <motion.div
              className="flex items-center space-x-2 text-sm text-purple-300/70"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 2.1, duration: 0.6 }}
            >
              <Activity className="w-4 h-4" />
              <span>Live machines</span>
              <motion.div
                className="w-2 h-2 bg-green-400 rounded-full"
                animate={{ scale: [1, 1.5, 1], opacity: [1, 0.5, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </motion.div>
          </div>

          <AnimatePresence mode="wait">
            {filteredTemplates.length > 0 ? (
              <motion.div
                key={`machines-${animationKey}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className={
                  viewMode === 'grid' 
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8' 
                    : 'space-y-4'
                }
              >
                {filteredTemplates.map((template, index) => {
                  const templateId = template._id || template.id || '';
                  const instance = instances.find(i => i.template?.id === templateId);
                  return (
                    <motion.div
                      key={templateId}
                      initial={{ opacity: 0, y: 20, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      transition={{ 
                        delay: index * 0.05,
                        duration: 0.4,
                        type: "spring",
                        stiffness: 100
                      }}
                      onHoverStart={() => setHoveredCard(templateId)}
                      onHoverEnd={() => setHoveredCard(null)}
                      className="machine-card-wrapper"
                    >
                      <MachineCard
                        key={templateId}
                        templateId={templateId}
                        template={template}
                        vm={{
                          id: instance?.id || templateId,
                          name: template.name,
                          ipAddress: instance?.instanceIP || '',
                          status: instance?.status === 'running' ? 'running' : 'stopped',
                          timeRemaining: instance?.timeRemaining ? instance.timeRemaining * 60 : 0,
                          maxTime: 7200, // Default 2 hours
                          ports: instance?.exposedPorts?.map(p => p.external) || template.exposedPorts || [],
                          os: template.os || 'linux',
                          difficulty: template.difficulty,
                          category: template.category
                        }}
                        onRefresh={() => {
                          loadMachines();
                          loadInstances();
                        }}
                        onShowStartModal={handleShowStartModal}
                      />
                    </motion.div>
                  );
                })}
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-20"
              >
                <motion.div
                  className="w-32 h-32 mx-auto mb-8 glass-card rounded-full flex items-center justify-center"
                  animate={{
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <Monitor className="w-16 h-16 text-purple-400" />
                </motion.div>
                
                <h3 className="text-2xl font-bold text-purple-300 mb-4">No machines found</h3>
                <p className="text-purple-200/60 mb-8 max-w-md mx-auto">
                  Try adjusting your filters or search terms to discover more machines
                </p>
                
                <motion.button
                  onClick={() => {
                    setFilters({
                      category: 'all',
                      difficulty: 'all',
                      search: ''
                    });
                  }}
                  className="px-8 py-4 bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-400 border border-purple-600/30 rounded-2xl hover:from-purple-600/30 hover:to-pink-600/30 transition-all duration-300 font-medium"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="flex items-center space-x-2">
                    <RefreshCw className="w-5 h-5" />
                    <span>Clear All Filters</span>
                  </div>
                </motion.button>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      {/* Gradient overlay for depth */}
      <div className="fixed inset-0 bg-gradient-to-t from-purple-900/20 via-transparent to-purple-900/20 pointer-events-none" />

      {/* Machine Start Modal */}
      {showStartModal && startingTemplate && (
        <MachineStartModal
          machine={startingTemplate}
          isOpen={showStartModal}
          onStart={handleStartInstance}
          onClose={() => {
            setShowStartModal(false);
            setStartingTemplate(null);
          }}
        />
      )}
    </div>
  );
}