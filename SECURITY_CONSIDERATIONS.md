# Security Considerations
## Machine Deployment & VPN Access System

This document outlines critical security considerations for the machine deployment and VPN access system.

## 🔒 THREAT MODEL

### Attack Vectors
1. **Container Escape**: Malicious users attempting to break out of containers
2. **Network Lateral Movement**: Users accessing other users' machines
3. **VPN Compromise**: Unauthorized access to VPN infrastructure
4. **Archive Exploitation**: Malicious files in uploaded archives
5. **Resource Exhaustion**: DoS attacks through resource consumption
6. **Privilege Escalation**: Gaining unauthorized admin access

### Assets to Protect
- Host system integrity
- User data isolation
- VPN infrastructure
- Container runtime security
- Network traffic confidentiality
- Administrative interfaces

## 🛡️ SECURITY CONTROLS

### 1. Container Security

#### Container Isolation
```yaml
# docker-compose.yml security settings
services:
  user-machine:
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-default
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - DAC_OVERRIDE
      - FOWNER
      - SETGID
      - SETUID
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    ulimits:
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
```

#### Resource Limits
```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 2G
      pids: 100
    reservations:
      cpus: '0.5'
      memory: 512M
```

#### Runtime Security
```bash
# AppArmor profile for containers
/usr/bin/docker-default {
  #include <abstractions/base>
  
  deny @{PROC}/* w,
  deny /sys/[^f]** wklx,
  deny /sys/f[^s]** wklx,
  deny /sys/fs/[^c]** wklx,
  deny /sys/fs/c[^g]** wklx,
  deny /sys/fs/cg[^r]** wklx,
  deny /sys/firmware/** rwklx,
  deny /sys/kernel/security/** rwklx,
}
```

### 2. Network Security

#### Network Isolation Rules
```bash
#!/bin/bash
# iptables rules for network isolation

# Drop all forwarding by default
iptables -P FORWARD DROP

# Allow VPN to Docker bridge
iptables -A FORWARD -i wg0 -o docker0 -j ACCEPT
iptables -A FORWARD -i docker0 -o wg0 -m state --state RELATED,ESTABLISHED -j ACCEPT

# Prevent cross-user network access
iptables -A FORWARD -s *********/16 -d *********/16 -j DROP

# Allow user-specific subnets
for user_id in {1..100}; do
  iptables -A FORWARD -s 10.6.0.$((user_id + 1))/32 -d 10.10.${user_id}.0/24 -j ACCEPT
  iptables -A FORWARD -s 10.10.${user_id}.0/24 -d 10.6.0.$((user_id + 1))/32 -j ACCEPT
done

# NAT for outbound traffic
iptables -t nat -A POSTROUTING -s ********/24 -j MASQUERADE
iptables -t nat -A POSTROUTING -s *********/16 -j MASQUERADE
```

#### VPN Security Configuration
```ini
# WireGuard server configuration
[Interface]
PrivateKey = {SERVER_PRIVATE_KEY}
Address = ********/24
ListenPort = 51820
PostUp = iptables -A FORWARD -i %i -j ACCEPT; iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
PostDown = iptables -D FORWARD -i %i -j ACCEPT; iptables -t nat -D POSTROUTING -o eth0 -j MASQUERADE

# Client template with restricted routes
[Peer]
PublicKey = {CLIENT_PUBLIC_KEY}
AllowedIPs = 10.6.0.{CLIENT_ID}/32
PersistentKeepalive = 25
```

### 3. File Upload Security

#### Archive Validation
```typescript
class ArchiveValidator {
  private readonly maxFileSize = 500 * 1024 * 1024; // 500MB
  private readonly maxFiles = 1000;
  private readonly allowedExtensions = ['.zip', '.7z'];
  private readonly dangerousFiles = [
    '.exe', '.bat', '.cmd', '.com', '.scr', '.pif',
    '.vbs', '.js', '.jar', '.sh', '.ps1'
  ];

  async validateArchive(file: Express.Multer.File): Promise<void> {
    // Size check
    if (file.size > this.maxFileSize) {
      throw new BadRequestException('File too large');
    }

    // Extension check
    const ext = path.extname(file.originalname).toLowerCase();
    if (!this.allowedExtensions.includes(ext)) {
      throw new BadRequestException('Invalid file type');
    }

    // Content validation
    await this.scanArchiveContents(file);
  }

  private async scanArchiveContents(file: Express.Multer.File): Promise<void> {
    const tempPath = `/tmp/${crypto.randomUUID()}`;
    await fs.writeFile(tempPath, file.buffer);

    try {
      const entries = await this.listArchiveEntries(tempPath);
      
      // Check file count
      if (entries.length > this.maxFiles) {
        throw new BadRequestException('Too many files in archive');
      }

      // Check for dangerous files
      for (const entry of entries) {
        const ext = path.extname(entry).toLowerCase();
        if (this.dangerousFiles.includes(ext)) {
          throw new BadRequestException(`Dangerous file type: ${ext}`);
        }

        // Check for path traversal
        if (entry.includes('..') || entry.startsWith('/')) {
          throw new BadRequestException('Invalid file path');
        }
      }
    } finally {
      await fs.unlink(tempPath);
    }
  }
}
```

#### Malware Scanning
```typescript
class MalwareScanner {
  async scanFile(filePath: string): Promise<boolean> {
    // Integration with ClamAV or similar
    const result = await exec(`clamscan --no-summary ${filePath}`);
    return !result.stdout.includes('FOUND');
  }

  async scanDirectory(dirPath: string): Promise<boolean> {
    const result = await exec(`clamscan -r --no-summary ${dirPath}`);
    return !result.stdout.includes('FOUND');
  }
}
```

### 4. Authentication & Authorization

#### JWT Token Security
```typescript
@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    
    if (!token) {
      throw new UnauthorizedException();
    }

    try {
      const payload = this.jwtService.verify(token, {
        secret: process.env.JWT_SECRET,
        algorithms: ['HS256'],
        maxAge: '1h' // Short-lived tokens
      });
      
      request.user = payload;
      return true;
    } catch {
      throw new UnauthorizedException();
    }
  }
}
```

#### Role-Based Access Control
```typescript
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    
    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}
```

### 5. Monitoring & Logging

#### Security Event Logging
```typescript
@Injectable()
export class SecurityLogger {
  private readonly logger = new Logger(SecurityLogger.name);

  logSecurityEvent(event: SecurityEvent): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event: event.type,
      userId: event.userId,
      ip: event.ip,
      userAgent: event.userAgent,
      details: event.details,
      severity: event.severity
    };

    this.logger.warn(`SECURITY: ${JSON.stringify(logEntry)}`);
    
    // Send to SIEM if configured
    if (event.severity === 'HIGH') {
      this.sendToSIEM(logEntry);
    }
  }

  private sendToSIEM(logEntry: any): void {
    // Integration with security monitoring system
  }
}
```

#### Intrusion Detection
```typescript
@Injectable()
export class IntrusionDetectionService {
  private readonly suspiciousPatterns = [
    /\.\.\//g,           // Path traversal
    /<script/gi,         // XSS attempts
    /union.*select/gi,   // SQL injection
    /exec\(/gi,          // Code execution
    /eval\(/gi,          // Code evaluation
  ];

  detectSuspiciousActivity(input: string, userId: string): void {
    for (const pattern of this.suspiciousPatterns) {
      if (pattern.test(input)) {
        this.securityLogger.logSecurityEvent({
          type: 'SUSPICIOUS_INPUT',
          userId,
          details: { pattern: pattern.source, input },
          severity: 'HIGH'
        });
        
        throw new BadRequestException('Suspicious input detected');
      }
    }
  }
}
```

## 🔍 SECURITY TESTING

### Penetration Testing Checklist
- [ ] Container escape attempts
- [ ] Network isolation bypass
- [ ] VPN authentication bypass
- [ ] File upload exploitation
- [ ] Privilege escalation
- [ ] DoS attack resilience
- [ ] Data exfiltration prevention

### Automated Security Scanning
```bash
#!/bin/bash
# Security scanning script

# Container vulnerability scanning
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image rakcha/machine-template:latest

# Network security testing
nmap -sS -O -p 1-65535 localhost

# Web application security testing
nikto -h http://localhost:3000

# SSL/TLS configuration testing
testssl.sh --parallel --protocols --server-defaults localhost:443
```

## 🚨 INCIDENT RESPONSE

### Security Incident Playbook
1. **Detection**: Automated alerts and monitoring
2. **Containment**: Isolate affected systems
3. **Eradication**: Remove threats and vulnerabilities
4. **Recovery**: Restore normal operations
5. **Lessons Learned**: Update security measures

### Emergency Procedures
```bash
#!/bin/bash
# Emergency shutdown script

# Stop all user containers
docker stop $(docker ps -q --filter "label=user-container")

# Disable VPN access
systemctl stop wg-quick@wg0

# Block all network traffic
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT DROP

# Alert administrators
echo "SECURITY INCIDENT: System locked down" | mail -s "URGENT: Security Alert" <EMAIL>
```

## 📋 COMPLIANCE REQUIREMENTS

### Data Protection
- Encrypt data at rest and in transit
- Implement data retention policies
- Provide user data deletion capabilities
- Maintain audit logs for compliance

### Access Controls
- Multi-factor authentication for admins
- Regular access reviews
- Principle of least privilege
- Session management and timeout

### Monitoring Requirements
- Real-time security monitoring
- Log retention for 1 year minimum
- Regular security assessments
- Vulnerability management program

## 🔄 SECURITY MAINTENANCE

### Regular Tasks
- [ ] Update container base images
- [ ] Rotate VPN keys quarterly
- [ ] Review and update firewall rules
- [ ] Scan for vulnerabilities monthly
- [ ] Update security policies
- [ ] Conduct security training

### Security Metrics
- Mean time to detect (MTTD)
- Mean time to respond (MTTR)
- Number of security incidents
- Vulnerability remediation time
- User access review completion rate

This security framework provides comprehensive protection for the machine deployment and VPN system while maintaining usability and performance.
