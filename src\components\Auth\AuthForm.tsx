import React, { useState, useEffect } from 'react';
import { useApp } from '../../contexts/AppContext';
import { Shield, Eye, EyeOff, Sparkles, Zap, Star } from 'lucide-react';
import { EmailVerification } from './EmailVerification';

export function AuthForm() {
  const { login, register } = useApp();
  const [isLogin, setIsLogin] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [showEmailVerification, setShowEmailVerification] = useState(false);
  const [registrationEmail, setRegistrationEmail] = useState('');
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: ''
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (isLogin) {
        const result = await login(formData.email, formData.password);

        // Check if email verification is required
        if (result && typeof result === 'object' && 'requiresEmailVerification' in result) {
          setRegistrationEmail(formData.email);
          setShowEmailVerification(true);
        }
      } else {
        const result = await register(formData.username, formData.email, formData.password);

        // Check if email verification is required
        if (result && typeof result === 'object' && 'requiresEmailVerification' in result) {
          setRegistrationEmail(formData.email);
          setShowEmailVerification(true);
        }
      }
    } catch (error) {
      console.error('Auth error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleEmailVerified = (token: string) => {
    // Store the token and redirect to dashboard
    localStorage.setItem('mybox_token', token);
    window.location.reload(); // This will trigger the app to check auth state
  };

  const handleBackToAuth = () => {
    setShowEmailVerification(false);
    setRegistrationEmail('');
  };

  // Show email verification if needed
  if (showEmailVerification) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 animate-gradient flex items-center justify-center p-4 relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className={`absolute w-2 h-2 bg-white/10 rounded-full animate-float-${i % 3 + 1}`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
              }}
            />
          ))}
        </div>

        <EmailVerification
          email={registrationEmail}
          onVerified={handleEmailVerified}
          onBack={handleBackToAuth}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 animate-gradient flex items-center justify-center p-4 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        {[...Array(30)].map((_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 8}s`,
              animationDuration: `${6 + Math.random() * 6}s`
            }}
          />
        ))}
      </div>

      {/* Floating geometric shapes */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-64 h-64 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full morph-shape animate-float blur-xl" />
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full morph-shape animate-float blur-xl" style={{ animationDelay: '3s' }} />
        <div className="absolute top-1/2 left-1/4 w-32 h-32 bg-gradient-to-r from-pink-500/30 to-purple-500/30 rounded-full morph-shape animate-float blur-lg" style={{ animationDelay: '1.5s' }} />
      </div>

      <div className={`w-full max-w-md relative z-10 ${mounted ? 'animate-scale-in' : 'opacity-0'}`}>
        {/* Main auth card */}
        <div className="glass-card-dark rounded-3xl p-8 shadow-2xl border border-purple-500/30 neon-border hover-lift">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6 relative">
              <div className="relative">
                <Shield className="w-16 h-16 text-purple-400 animate-pulse-glow" />
                <Sparkles className="w-6 h-6 text-yellow-400 absolute -top-2 -right-2 animate-bounce" />
                <Zap className="w-4 h-4 text-blue-400 absolute -bottom-1 -left-1 animate-pulse" />
                <Star className="w-3 h-3 text-pink-400 absolute top-1 left-1 animate-spin" style={{ animationDuration: '3s' }} />
              </div>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent neon-text mb-3">
              Rakcha Pentest V2
            </h1>
            <p className="text-purple-200/80 text-lg">
              {isLogin ? 'Welcome back, hacker!' : 'Join the elite community'}
            </p>
            <div className="mt-4 flex justify-center space-x-2">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse"
                  style={{ animationDelay: `${i * 0.2}s` }}
                />
              ))}
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              {isLogin ? (
                <div className="animate-slide-in-left stagger-1">
                  <label htmlFor="email" className="block text-sm font-medium text-purple-200 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full px-4 py-3 glass-card border border-purple-500/30 rounded-xl text-white placeholder-purple-300/50 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300 hover-lift"
                    placeholder="Enter your email"
                    required
                  />
                </div>
              ) : (
                <>
                  <div className="animate-slide-in-left stagger-1">
                    <label htmlFor="username" className="block text-sm font-medium text-purple-200 mb-2">
                      Username
                    </label>
                    <input
                      type="text"
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      className="w-full px-4 py-3 glass-card border border-purple-500/30 rounded-xl text-white placeholder-purple-300/50 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300 hover-lift"
                      placeholder="Enter your username"
                      required
                    />
                  </div>

                  <div className="animate-slide-in-left stagger-2">
                    <label htmlFor="email" className="block text-sm font-medium text-purple-200 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 glass-card border border-purple-500/30 rounded-xl text-white placeholder-purple-300/50 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300 hover-lift"
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                </>
              )}

              <div className="animate-slide-in-left stagger-3">
                <label htmlFor="password" className="block text-sm font-medium text-purple-200 mb-2">
                  Password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    className="w-full px-4 py-3 pr-12 glass-card border border-purple-500/30 rounded-xl text-white placeholder-purple-300/50 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300 hover-lift"
                    placeholder="Enter your password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 text-purple-300 hover:text-purple-200 transition-colors"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-4 px-6 bg-gradient-to-r from-purple-600 via-purple-500 to-pink-500 hover:from-purple-700 hover:via-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-bold rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl animate-pulse-glow animate-slide-in-up stagger-4"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-5 h-5 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                  <span>Processing...</span>
                </div>
              ) : (
                <span className="flex items-center justify-center space-x-2">
                  <span>{isLogin ? 'Sign In' : 'Create Account'}</span>
                  <Zap className="w-5 h-5" />
                </span>
              )}
            </button>
          </form>

          {/* Toggle form type */}
          <div className="mt-6 text-center animate-slide-in-up stagger-5">
            <button
              onClick={() => setIsLogin(!isLogin)}
              className="text-sm text-purple-300 hover:text-purple-200 transition-colors hover:underline"
            >
              {isLogin ? "Don't have an account? Sign up" : "Already have an account? Sign in"}
            </button>
          </div>

          {/* Demo credentials */}
          <div className="mt-8 p-4 glass-card rounded-xl border border-purple-500/20 animate-slide-in-up stagger-6">
            <p className="text-xs text-purple-300 mb-2 font-semibold">🚀 Demo credentials:</p>
            <div className="space-y-1 text-xs">
              <p className="text-purple-200">👤 User: any username/password</p>
              <p className="text-purple-200">🔑 Admin: admin/admin</p>
            </div>
          </div>
        </div>

        {/* Floating action elements */}
        <div className="absolute -top-10 -right-10 w-20 h-20 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-full animate-float blur-lg" />
        <div className="absolute -bottom-10 -left-10 w-16 h-16 bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded-full animate-float blur-lg" style={{ animationDelay: '2s' }} />
      </div>
    </div>
  );
}