import { IsString, IsEnum, IsNumber, IsArray, IsOptional, IsBoolean, IsDate, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { ChallengeDifficulty } from '../../schemas/challenge.schema';
import { ApiProperty } from '@nestjs/swagger';
import { FlagDto, ChallengeFileDto } from './create-challenge.dto';

export class UpdateChallengeDto {
  @ApiProperty({ description: 'Challenge title', required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ description: 'Challenge description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Challenge category (must be a valid category name)',
    required: false
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ 
    description: 'Challenge difficulty', 
    enum: ['easy', 'medium', 'hard', 'insane'],
    required: false
  })
  @IsOptional()
  @IsEnum(['easy', 'medium', 'hard', 'insane'])
  difficulty?: ChallengeDifficulty;

  @ApiProperty({ description: 'Challenge points', required: false })
  @IsOptional()
  @IsNumber()
  points?: number;
  @ApiProperty({ description: 'Legacy challenge flag', required: false })
  @IsOptional()
  @IsString()
  flag?: string;

  @ApiProperty({ description: 'Multiple challenge flags', type: [FlagDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FlagDto)
  flags?: FlagDto[];

  @ApiProperty({ description: 'Challenge hints', required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  hints?: string[];

  @ApiProperty({ description: 'Challenge tags', required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: 'Author ID', required: false })
  @IsOptional()
  @IsString()
  authorId?: string;
  
  @ApiProperty({ description: 'Author name', required: false })
  @IsOptional()
  @IsString()
  authorName?: string;

  @ApiProperty({ description: 'Challenge files', type: [ChallengeFileDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChallengeFileDto)
  files?: ChallengeFileDto[];

  @ApiProperty({ description: 'Active status', required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
  @ApiProperty({ description: 'Release date', required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  releaseDate?: Date;

  @ApiProperty({ description: 'Expiry date', required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  expiryDate?: Date;

  @ApiProperty({ description: 'Docker image for containerized challenges', required: false })
  @IsOptional()
  @IsString()
  dockerImage?: string;

  @ApiProperty({ description: 'Whether the challenge requires a server', required: false })
  @IsOptional()
  @IsBoolean()
  requiresServer?: boolean;

  @ApiProperty({ description: 'Server configuration for the challenge', required: false })
  @IsOptional()
  @IsObject()
  serverConfig?: object;
}
