import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, Star, Zap, Crown } from 'lucide-react';
import { useNotifications } from '../../contexts/NotificationsContext';

export function FirstBloodAnimation() {
  const { showFirstBloodAnimation, setShowFirstBloodAnimation, latestFirstBlood } = useNotifications();
  const [particles, setParticles] = useState<Array<{ id: number; x: number; y: number; delay: number }>>([]);

  useEffect(() => {
    if (showFirstBloodAnimation) {
      // Generate random particles
      const newParticles = Array.from({ length: 20 }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 2,
      }));
      setParticles(newParticles);

      // Auto-hide after 5 seconds
      const timer = setTimeout(() => {
        setShowFirstBloodAnimation(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [showFirstBloodAnimation, setShowFirstBloodAnimation]);

  if (!showFirstBloodAnimation || !latestFirstBlood) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-[99999] pointer-events-none flex items-center justify-center"
        style={{ zIndex: 99999 }}
      >
        {/* Dark overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.8 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        />

        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden">
          {particles.map((particle) => (
            <motion.div
              key={particle.id}
              initial={{ 
                opacity: 0, 
                scale: 0,
                x: `${particle.x}vw`,
                y: `${particle.y}vh`
              }}
              animate={{ 
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
                y: `${particle.y - 20}vh`,
                rotate: [0, 360]
              }}
              transition={{ 
                duration: 3,
                delay: particle.delay,
                ease: "easeOut"
              }}
              className="absolute w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full"
            />
          ))}
        </div>

        {/* Main animation container */}
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          exit={{ scale: 0, rotate: 180 }}
          transition={{ 
            type: "spring",
            stiffness: 200,
            damping: 20,
            duration: 0.8
          }}
          className="relative z-10 text-center"
        >
          {/* Golden glow effect */}
          <motion.div
            animate={{ 
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{ 
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute inset-0 bg-gradient-to-r from-yellow-400/30 via-orange-400/30 to-red-400/30 rounded-full blur-3xl"
          />

          {/* Crown icon with rotation */}
          <motion.div
            animate={{ 
              rotate: [0, 10, -10, 0],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="relative mb-6"
          >
            <div className="w-32 h-32 mx-auto bg-gradient-to-br from-yellow-400 via-orange-400 to-red-400 rounded-full flex items-center justify-center shadow-2xl">
              <Crown className="w-16 h-16 text-white" />
            </div>
            
            {/* Orbiting stars */}
            {[0, 72, 144, 216, 288].map((angle, index) => (
              <motion.div
                key={index}
                animate={{ 
                  rotate: [angle, angle + 360]
                }}
                transition={{ 
                  duration: 4,
                  repeat: Infinity,
                  ease: "linear",
                  delay: index * 0.2
                }}
                className="absolute top-1/2 left-1/2 w-8 h-8"
                style={{ 
                  transformOrigin: '0 0',
                  transform: `translate(-50%, -50%) rotate(${angle}deg) translateY(-80px)`
                }}
              >
                <Star className="w-6 h-6 text-yellow-400 fill-current" />
              </motion.div>
            ))}
          </motion.div>

          {/* First Blood Text */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="mb-4"
          >
            <motion.h1
              animate={{ 
                textShadow: [
                  "0 0 20px rgba(255, 215, 0, 0.8)",
                  "0 0 40px rgba(255, 215, 0, 1)",
                  "0 0 20px rgba(255, 215, 0, 0.8)"
                ]
              }}
              transition={{ 
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-6xl md:text-8xl font-bold bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 bg-clip-text text-transparent"
            >
              FIRST BLOOD!
            </motion.h1>
          </motion.div>

          {/* Achievement details */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="bg-black/50 backdrop-blur-sm border border-yellow-400/30 rounded-2xl p-6 max-w-md mx-auto"
          >
            <div className="flex items-center justify-center space-x-3 mb-3">
              <Trophy className="w-8 h-8 text-yellow-400" />
              <span className="text-2xl font-bold text-white">
                {latestFirstBlood.metadata.username}
              </span>
            </div>
            
            <p className="text-lg text-yellow-200 mb-2">
              scored first blood on
            </p>
            
            <p className="text-xl font-semibold text-white mb-3">
              {latestFirstBlood.metadata.challengeName}
            </p>
            
            <div className="flex items-center justify-center space-x-2">
              <Zap className="w-5 h-5 text-orange-400" />
              <span className="text-lg font-bold text-orange-400">
                {latestFirstBlood.metadata.points} points
              </span>
            </div>
          </motion.div>

          {/* Celebration text */}
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.6 }}
            className="text-xl text-yellow-200 mt-6 font-medium"
          >
            🎉 Congratulations! 🎉
          </motion.p>

          {/* Close button */}
          <motion.button
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2, duration: 0.6 }}
            onClick={() => setShowFirstBloodAnimation(false)}
            className="mt-8 px-6 py-3 bg-gradient-to-r from-yellow-600/20 to-orange-600/20 text-yellow-400 border border-yellow-600/30 rounded-lg hover:bg-gradient-to-r hover:from-yellow-600/30 hover:to-orange-600/30 transition-all duration-300 pointer-events-auto"
          >
            Continue
          </motion.button>
        </motion.div>

        {/* Fireworks effect */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ 
                scale: 0,
                x: "50vw",
                y: "50vh"
              }}
              animate={{ 
                scale: [0, 1, 0],
                x: `${50 + (Math.cos(i * 45 * Math.PI / 180) * 30)}vw`,
                y: `${50 + (Math.sin(i * 45 * Math.PI / 180) * 30)}vh`,
                opacity: [0, 1, 0]
              }}
              transition={{ 
                duration: 2,
                delay: 1 + (i * 0.1),
                ease: "easeOut"
              }}
              className="absolute w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full"
            />
          ))}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}