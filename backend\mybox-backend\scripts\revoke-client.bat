@echo off
if "%~1"=="" (
    echo Usage: %~n0 ^<username^>
    exit /b 1
)

set USERNAME=%~1
set EASY_RSA=C:\Program Files\OpenVPN\easy-rsa
set CONFIG_PATH=C:\Program Files\OpenVPN\config

:: Revoke client certificate
echo Revoking client certificate for %USERNAME%...
cd /d "%EASY_RSA%"
call vars.bat
call easyrsa revoke %USERNAME%
call easyrsa gen-crl

:: Remove client config file
del /f /q "%CONFIG_PATH%\%USERNAME%.ovpn"

:: Reload OpenVPN to apply changes
net stop openvpnservice
net start openvpnservice

echo VPN access for %USERNAME% has been revoked
