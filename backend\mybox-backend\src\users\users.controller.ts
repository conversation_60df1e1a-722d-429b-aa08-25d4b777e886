import { 
  Controller, 
  Get, 
  Put, 
  Body, 
  Request, 
  UseGuards,
  ValidationPipe,
  NotFoundException 
} from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { IsOptional, IsString, IsEmail, MinLength, MaxLength } from 'class-validator';

export class UpdateProfileDto {
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  username?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  avatarUrl?: string;

  @IsOptional()
  @IsString()
  country?: string;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  bio?: string;
}

@ApiTags('users')
@ApiBearerAuth()
@Controller('users')
export class UsersController {
  constructor(private usersService: UsersService) {}

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  async getProfile(@Request() req) {
    const user = await this.usersService.findById(req.user.userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const { passwordHash, _id, ...userProfile } = user.toObject();
    return {
      ...userProfile,
      id: _id.toString()
    };
  }
  @UseGuards(JwtAuthGuard)
  @Put('profile')
  async updateProfile(
    @Request() req,
    @Body(ValidationPipe) updateProfileDto: UpdateProfileDto
  ) {
    const user = await this.usersService.updateProfile(req.user.userId, updateProfileDto);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const { passwordHash, _id, ...userProfile } = user.toObject();
    return {
      ...userProfile,
      id: _id.toString()
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put('regenerate-api-token')
  async regenerateApiToken(@Request() req) {
    const user = await this.usersService.regenerateApiToken(req.user.userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return { apiToken: user.apiToken };
  }
}
