import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MachineInstance } from '../../schemas/machine-instance.schema';
import { MachineOperation } from '../../schemas/machine-operation.schema';
import { DockerMachineService } from './docker-machine.service';

@Injectable()
export class MachineMaintenanceService {
  private readonly logger = new Logger(MachineMaintenanceService.name);

  constructor(
    @InjectModel(MachineInstance.name) private machineInstanceModel: Model<MachineInstance>,
    @InjectModel(MachineOperation.name) private machineOperationModel: Model<MachineOperation>,
    private dockerMachineService: DockerMachineService,
  ) {}

  /**
   * Process scheduled machine operations (auto-shutdown, cleanup, etc.)
   * Runs every minute
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async processScheduledOperations() {
    this.logger.debug('Processing scheduled machine operations...');

    try {
      // Find pending operations that are due
      const dueOperations = await this.machineOperationModel
        .find({
          status: 'pending',
          executeAt: { $lte: new Date() },
        })
        .populate('instanceId')
        .exec();

      this.logger.log(`Found ${dueOperations.length} due operations to process`);

      for (const operation of dueOperations) {
        try {
          await this.executeOperation(operation);
        } catch (error) {
          this.logger.error(`Failed to execute operation ${operation._id}: ${error.message}`);
          
          // Mark operation as failed
          operation.status = 'failed';
          operation.lastError = error.message;
          operation.attempts++;
          await operation.save();
        }
      }
    } catch (error) {
      this.logger.error(`Error processing scheduled operations: ${error.message}`);
    }
  }

  /**
   * Auto-shutdown expired instances
   * Runs every 5 minutes
   */
  @Cron('*/5 * * * *') // Every 5 minutes
  async autoShutdownExpiredInstances() {
    this.logger.debug('Checking for expired machine instances...');

    try {
      const expiredInstances = await this.machineInstanceModel.find({
        status: { $in: ['running', 'starting'] },
        expiresAt: { $lte: new Date() },
      });

      this.logger.log(`Found ${expiredInstances.length} expired instances to shutdown`);

      for (const instance of expiredInstances) {
        try {
          this.logger.log(`Auto-shutting down expired instance ${instance._id}`);
          
          await this.dockerMachineService.terminateMachine(
            instance._id.toString(),
            instance.ownerId.toString()
          );

          this.logger.log(`Successfully shutdown expired instance ${instance._id}`);
        } catch (error) {
          this.logger.error(`Failed to shutdown expired instance ${instance._id}: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error during auto-shutdown: ${error.message}`);
    }
  }

  /**
   * Cleanup orphaned containers
   * Runs every 30 minutes
   */
  @Cron('*/30 * * * *') // Every 30 minutes
  async cleanupOrphanedContainers() {
    this.logger.debug('Cleaning up orphaned containers...');

    try {
      await this.dockerMachineService.cleanupOrphanedContainers();
      this.logger.log('Orphaned container cleanup completed');
    } catch (error) {
      this.logger.error(`Error during orphaned container cleanup: ${error.message}`);
    }
  }

  /**
   * Update instance statistics
   * Runs every 2 minutes
   */
  @Cron('*/2 * * * *') // Every 2 minutes
  async updateInstanceStats() {
    this.logger.debug('Updating instance statistics...');

    try {
      const runningInstances = await this.machineInstanceModel.find({
        status: 'running',
        containerId: { $exists: true, $ne: null },
      });

      let updated = 0;
      for (const instance of runningInstances) {
        try {
          await this.dockerMachineService.getInstanceStats(
            instance._id.toString(),
            instance.ownerId.toString()
          );
          updated++;
        } catch (error) {
          // Instance might be stopped or have issues
          this.logger.warn(`Failed to update stats for instance ${instance._id}: ${error.message}`);
        }
      }

      this.logger.debug(`Updated statistics for ${updated} instances`);
    } catch (error) {
      this.logger.error(`Error updating instance statistics: ${error.message}`);
    }
  }

  /**
   * Send expiration warnings to users
   * Runs every 10 minutes
   */
  @Cron('*/10 * * * *') // Every 10 minutes
  async sendExpirationWarnings() {
    this.logger.debug('Checking for instances near expiration...');

    try {
      const warningThreshold = 30; // 30 minutes before expiration
      const warningTime = new Date(Date.now() + warningThreshold * 60 * 1000);

      const nearExpirationInstances = await this.machineInstanceModel
        .find({
          status: 'running',
          expiresAt: { $lte: warningTime, $gt: new Date() },
          // Only warn if we haven't warned recently
          lastWarningAt: { 
            $not: { 
              $gte: new Date(Date.now() - 20 * 60 * 1000) // Last warning more than 20 min ago
            } 
          },
        })
        .populate('ownerId', 'username email')
        .populate('templateId', 'name');

      this.logger.log(`Found ${nearExpirationInstances.length} instances near expiration`);

      for (const instance of nearExpirationInstances) {
        try {
          // TODO: Implement notification system (email, websocket, etc.)
          this.logger.log(`Warning: Instance ${instance._id} expires soon for user ${instance.ownerId}`);

          // Update last warning time
          instance.lastWarningAt = new Date();
          await instance.save();
        } catch (error) {
          this.logger.error(`Failed to send warning for instance ${instance._id}: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error sending expiration warnings: ${error.message}`);
    }
  }

  /**
   * Clean up old stopped instances
   * Runs daily at 2 AM
   */
  @Cron('0 2 * * *') // Daily at 2 AM
  async cleanupOldInstances() {
    this.logger.log('Cleaning up old stopped instances...');

    try {
      const retentionDays = 7; // Keep stopped instances for 7 days
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);

      const oldInstances = await this.machineInstanceModel.find({
        status: 'stopped',
        stoppedAt: { $lt: cutoffDate },
      });

      this.logger.log(`Found ${oldInstances.length} old instances to clean up`);

      for (const instance of oldInstances) {
        try {
          // Remove the instance record
          await this.machineInstanceModel.deleteOne({ _id: instance._id });
          
          // Cancel any pending operations for this instance
          await this.machineOperationModel.updateMany(
            { instanceId: instance._id, status: 'pending' },
            { status: 'cancelled' }
          );

          this.logger.log(`Cleaned up old instance ${instance._id}`);
        } catch (error) {
          this.logger.error(`Failed to cleanup instance ${instance._id}: ${error.message}`);
        }
      }

      this.logger.log(`Cleanup completed. Removed ${oldInstances.length} old instances`);
    } catch (error) {
      this.logger.error(`Error during old instance cleanup: ${error.message}`);
    }
  }

  /**
   * Monitor resource usage and alert on high usage
   * Runs every 5 minutes
   */
  @Cron('*/5 * * * *') // Every 5 minutes
  async monitorResourceUsage() {
    this.logger.debug('Monitoring resource usage...');

    try {
      const runningInstances = await this.machineInstanceModel.find({
        status: 'running',
        cpuUsage: { $exists: true },
        memoryUsage: { $exists: true },
      });

      // Check for high resource usage
      const highCpuInstances = runningInstances.filter(i => (i.cpuUsage || 0) > 90);
      const highMemoryInstances = runningInstances.filter(i => (i.memoryUsage || 0) > 1024); // 1GB

      if (highCpuInstances.length > 0) {
        this.logger.warn(`${highCpuInstances.length} instances with high CPU usage detected`);
      }

      if (highMemoryInstances.length > 0) {
        this.logger.warn(`${highMemoryInstances.length} instances with high memory usage detected`);
      }

      // Log overall statistics
      const totalCpu = runningInstances.reduce((sum, i) => sum + (i.cpuUsage || 0), 0);
      const totalMemory = runningInstances.reduce((sum, i) => sum + (i.memoryUsage || 0), 0);
      const avgCpu = runningInstances.length > 0 ? totalCpu / runningInstances.length : 0;
      const avgMemory = runningInstances.length > 0 ? totalMemory / runningInstances.length : 0;

      this.logger.debug(`Resource usage - Instances: ${runningInstances.length}, Avg CPU: ${avgCpu.toFixed(2)}%, Avg Memory: ${avgMemory.toFixed(2)}MB`);

    } catch (error) {
      this.logger.error(`Error monitoring resource usage: ${error.message}`);
    }
  }

  /**
   * Execute a scheduled operation
   */
  private async executeOperation(operation: MachineOperation): Promise<void> {
    this.logger.log(`Executing ${operation.operationType} operation for instance ${operation.instanceId}`);

    operation.status = 'executing';    operation.attempts++;
    await this.machineOperationModel.findByIdAndUpdate((operation as any)._id, {
      attempts: operation.attempts,
      lastAttemptAt: new Date()
    });

    const instance = operation.instanceId as unknown as MachineInstance;
    
    switch (operation.operationType) {
      case 'stop':
        await this.dockerMachineService.terminateMachine(
          (instance as any)._id.toString(),
          (instance as any).ownerId.toString()
        );
        break;

      case 'restart':
        await this.dockerMachineService.restartMachine(
          (instance as any)._id.toString(),
          instance.ownerId.toString()
        );
        break;

      case 'cleanup':
        // Custom cleanup logic
        await this.performInstanceCleanup(instance);
        break;

      default:
        throw new Error(`Unknown operation type: ${operation.operationType}`);
    }    operation.status = 'completed';
    await this.machineOperationModel.findByIdAndUpdate((operation as any)._id, {
      status: 'completed',
      completedAt: new Date()
    });

    this.logger.log(`Successfully executed ${operation.operationType} operation for instance ${operation.instanceId}`);
  }

  /**
   * Perform custom cleanup for an instance
   */  private async performInstanceCleanup(instance: MachineInstance): Promise<void> {
    // Custom cleanup logic - remove files, reset state, etc.
    this.logger.log(`Performing cleanup for instance ${(instance as any)._id}`);
    
    // Add specific cleanup tasks here
    // For example: clean temporary files, reset databases, etc.
  }

  /**
   * Get maintenance service statistics
   */
  async getMaintenanceStats(): Promise<{
    scheduledOperations: number;
    runningInstances: number;
    expiredInstances: number;
    resourceUsage: {
      totalCpu: number;
      totalMemory: number;
      instanceCount: number;
    };
  }> {
    const [
      scheduledOperations,
      runningInstances,
      expiredInstances
    ] = await Promise.all([
      this.machineOperationModel.countDocuments({ status: 'pending' }),
      this.machineInstanceModel.find({ status: 'running' }),
      this.machineInstanceModel.countDocuments({
        status: 'running',
        expiresAt: { $lte: new Date() }
      }),
    ]);

    const resourceUsage = runningInstances.reduce(
      (acc, instance) => ({
        totalCpu: acc.totalCpu + (instance.cpuUsage || 0),
        totalMemory: acc.totalMemory + (instance.memoryUsage || 0),
        instanceCount: acc.instanceCount + 1,
      }),
      { totalCpu: 0, totalMemory: 0, instanceCount: 0 }
    );

    return {
      scheduledOperations: scheduledOperations,
      runningInstances: runningInstances.length,
      expiredInstances,
      resourceUsage,
    };
  }
}
