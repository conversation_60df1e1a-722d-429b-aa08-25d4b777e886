import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AdminSettings } from '../schemas/admin-settings.schema';

@Injectable()
export class TeamsEnabledGuard implements CanActivate {
  constructor(
    @InjectModel(AdminSettings.name) private adminSettingsModel: Model<AdminSettings>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const settings = await this.adminSettingsModel.findOne({ configId: 'default' });
      
      if (!settings || !settings.enableTeams) {
        throw new ForbiddenException({
          error: 'Teams Disabled',
          message: 'Team functionality is currently disabled by the administrator.',
          teamsEnabled: false
        });
      }

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      
      console.error('Teams enabled guard error:', error);
      // If we can't check settings, deny access for safety
      throw new ForbiddenException({
        error: 'Configuration Error',
        message: 'Unable to verify team settings. Please try again later.',
        teamsEnabled: false
      });
    }
  }
}