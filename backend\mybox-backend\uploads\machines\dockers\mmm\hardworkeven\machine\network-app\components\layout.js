import { useRouter } from 'next/router';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { 
    BarChart2, HardDrive, Settings, HelpCircle, Users, LogOut, Frame,
    ChevronLeft, ChevronRight, Bell, Search, User, Moon, Menu,
    Shield, Activity, Database, Network, Globe, Lock
} from 'lucide-react';

const IconWrapper = ({ children }) => <span className="mr-3">{children}</span>;

const NavLink = ({ href, icon, label, isCollapsed, badge = null }) => {
    const router = useRouter();
    const isActive = router.pathname === href;
    
    return (
        <Link href={href} legacyBehavior>
            <a className={`group flex items-center w-full px-4 py-3 text-left font-medium transition-all duration-300 rounded-xl mb-1 relative overflow-hidden ${
                isActive 
                    ? 'bg-white text-black shadow-lg transform scale-[1.02]' 
                    : 'text-gray-400 hover:bg-gray-900/50 hover:text-white hover:transform hover:translate-x-1'
            }`}>
                <div className="flex items-center relative z-10">
                    {icon}
                    {!isCollapsed && (
                        <span className="transition-opacity duration-300">
                            {label}
                        </span>
                    )}
                </div>
                {badge && !isCollapsed && (
                    <span className="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                        {badge}
                    </span>
                )}
                {isActive && (
                    <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-20"></div>
                )}
                <div className="absolute left-0 top-0 h-full w-1 bg-white transform scale-y-0 group-hover:scale-y-100 transition-transform duration-300 origin-top"></div>
            </a>
        </Link>
    );
};

const TopBar = ({ isCollapsed, toggleSidebar }) => {
    const [currentTime, setCurrentTime] = useState(new Date());
    const [notifications] = useState(3);

    useEffect(() => {
        const timer = setInterval(() => setCurrentTime(new Date()), 1000);
        return () => clearInterval(timer);
    }, []);

    return (
        <div className="h-16 bg-black/50 backdrop-blur-sm border-b border-gray-800/50 flex items-center justify-between px-6 sticky top-0 z-40">
            <div className="flex items-center space-x-4">
                <button
                    onClick={toggleSidebar}
                    className="p-2 rounded-lg hover:bg-gray-800/50 transition-colors"
                >
                    <Menu size={20} className="text-gray-400" />
                </button>
                <div className="relative">
                    <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                        type="text"
                        placeholder="Search..."
                        className="bg-gray-900/50 border border-gray-700/50 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white/20 focus:border-white/30 transition-all w-64"
                    />
                </div>
            </div>
            
            <div className="flex items-center space-x-4">
                <div className="text-right">
                    <div className="text-white text-sm font-mono">
                        {currentTime.toLocaleTimeString()}
                    </div>
                    <div className="text-gray-400 text-xs">
                        {currentTime.toLocaleDateString()}
                    </div>
                </div>
                
                <div className="relative">
                    <button className="p-2 rounded-lg hover:bg-gray-800/50 transition-colors relative">
                        <Bell size={20} className="text-gray-400" />
                        {notifications > 0 && (
                            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                {notifications}
                            </span>
                        )}
                    </button>
                </div>
                
                <div className="flex items-center space-x-2 pl-4 border-l border-gray-700/50">
                    <div className="w-8 h-8 bg-gradient-to-br from-white to-gray-300 rounded-full flex items-center justify-center">
                        <User size={16} className="text-black" />
                    </div>
                    <div className="text-right">
                        <div className="text-white text-sm">KYBSAdmin</div>
                        <div className="text-gray-400 text-xs">Administrator</div>
                    </div>
                </div>
            </div>
        </div>
    );
};

const StatusIndicator = ({ label, status, isCollapsed }) => {
    const statusColors = {
        online: 'bg-green-400',
        warning: 'bg-yellow-400',
        offline: 'bg-red-400'
    };

    if (isCollapsed) {
        return (
            <div className="flex justify-center mb-2">
                <div className={`w-2 h-2 rounded-full ${statusColors[status]} animate-pulse`}></div>
            </div>
        );
    }

    return (
        <div className="flex items-center justify-between px-4 py-2 mb-2">
            <span className="text-xs text-gray-400 uppercase tracking-wide">{label}</span>
            <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${statusColors[status]} animate-pulse`}></div>
                <span className="text-xs text-white capitalize">{status}</span>
            </div>
        </div>
    );
};

const Sidebar = () => {
    const router = useRouter();
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [systemStatus] = useState({
        security: 'online',
        network: 'online',
        database: 'warning',
        services: 'online'
    });

    const handleLogout = async () => {
        await fetch('/api/logout');
        router.push('/');
    };

    const toggleCollapse = () => {
        setIsCollapsed(!isCollapsed);
    };

    return (
        <div className={`${isCollapsed ? 'w-20' : 'w-72'} bg-gradient-to-b from-black to-gray-900 text-white p-5 flex flex-col h-screen fixed top-0 left-0 border-r border-gray-800/50 backdrop-blur-sm transition-all duration-300 z-50`}>
            {/* Header */}
            <div className="flex items-center justify-between mb-8 pt-2">
                <div className="flex items-center">
                    <div className="p-2 bg-gradient-to-br from-white to-gray-300 rounded-xl mr-3">
                        <Frame className="text-black" size={24} />
                    </div>
                    {!isCollapsed && (
                        <div className="transition-opacity duration-300">
                            <h1 className="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                                Hardwork Inc
                            </h1>
                            <p className="text-xs text-gray-400">Security Operations</p>
                        </div>
                    )}
                </div>
                <button
                    onClick={toggleCollapse}
                    className="p-1.5 rounded-lg hover:bg-gray-800/50 transition-colors"
                >
                    {isCollapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
                </button>
            </div>

            {/* System Status */}
            <div className="mb-6 p-3 bg-gray-900/30 rounded-xl border border-gray-800/30">
                <StatusIndicator label="Security" status={systemStatus.security} isCollapsed={isCollapsed} />
                <StatusIndicator label="Network" status={systemStatus.network} isCollapsed={isCollapsed} />
                <StatusIndicator label="Database" status={systemStatus.database} isCollapsed={isCollapsed} />
                <StatusIndicator label="Services" status={systemStatus.services} isCollapsed={isCollapsed} />
            </div>

            {/* Navigation */}
            <nav className="flex-grow space-y-1">
                <div className={`${isCollapsed ? 'text-center' : ''} mb-4`}>
                    {!isCollapsed && (
                        <h3 className="text-xs text-gray-400 uppercase tracking-wider font-semibold mb-3 px-4">
                            Main Menu
                        </h3>
                    )}
                </div>
                
                <NavLink 
                    href="/dashboard" 
                    icon={<IconWrapper><BarChart2 size={20} /></IconWrapper>} 
                    label="Dashboard" 
                    isCollapsed={isCollapsed}
                />
                <NavLink 
                    href="/saved" 
                    icon={<IconWrapper><HardDrive size={20} /></IconWrapper>} 
                    label="Saved Items" 
                    isCollapsed={isCollapsed}
                />
                <NavLink 
                    href="/settings" 
                    icon={<IconWrapper><Settings size={20} /></IconWrapper>} 
                    label="Settings" 
                    isCollapsed={isCollapsed}
                />
                <NavLink 
                    href="/faq" 
                    icon={<IconWrapper><HelpCircle size={20} /></IconWrapper>} 
                    label="FAQ" 
                    isCollapsed={isCollapsed}
                />
                <NavLink 
                    href="/user-management" 
                    icon={<IconWrapper><Users size={20} /></IconWrapper>} 
                    label="Users" 
                    isCollapsed={isCollapsed}
                    badge="2"
                />
            </nav>

            {/* Bottom Section */}
            <div className="mt-auto space-y-2">
                {!isCollapsed && (
                    <div className="p-3 bg-gray-900/30 rounded-xl border border-gray-800/30 mb-4">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-xs text-gray-400">System Load</span>
                            <span className="text-xs text-white">34%</span>
                        </div>
                        <div className="w-full bg-gray-800 rounded-full h-1.5">
                            <div className="bg-white h-1.5 rounded-full transition-all duration-300" style={{width: '34%'}}></div>
                        </div>
                    </div>
                )}
                
                <button 
                    onClick={handleLogout} 
                    className="group flex items-center w-full px-4 py-3 text-left font-medium transition-all duration-300 rounded-xl text-gray-400 hover:bg-red-900/20 hover:text-red-300 hover:transform hover:translate-x-1"
                >
                    <IconWrapper><LogOut size={20} /></IconWrapper>
                    {!isCollapsed && (
                        <span className="transition-opacity duration-300">Logout</span>
                    )}
                    <div className="absolute left-0 top-0 h-full w-1 bg-red-400 transform scale-y-0 group-hover:scale-y-100 transition-transform duration-300 origin-top"></div>
                </button>
            </div>
        </div>
    );
};

export default function Layout({ children }) {
    const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

    const toggleSidebar = () => {
        setSidebarCollapsed(!sidebarCollapsed);
    };

    return (
        <div className="font-sans bg-gradient-to-br from-black via-gray-900 to-black text-white min-h-screen">
            <Sidebar />
            <div className={`${sidebarCollapsed ? 'ml-20' : 'ml-72'} transition-all duration-300`}>
                <TopBar isCollapsed={sidebarCollapsed} toggleSidebar={toggleSidebar} />
                <main className="p-8 min-h-screen bg-gradient-to-br from-transparent via-gray-900/10 to-transparent">
                    <div className="max-w-full mx-auto">
                        {children}
                    </div>
                </main>
            </div>
            
            {/* Background Pattern */}
            <div className="fixed inset-0 pointer-events-none opacity-5">
                <div className="absolute inset-0" style={{
                    backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
                    backgroundSize: '20px 20px'
                }}></div>
            </div>
        </div>
    );
}