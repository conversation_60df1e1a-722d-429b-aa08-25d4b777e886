import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import * as Docker from 'dockerode';
import { exec } from 'child_process';
import { promisify } from 'util';

import { MachineNetwork, MachineNetworkDocument, ExposedPort } from '../../schemas/machine-network.schema';
import { VPNConfig, VPNConfigDocument } from '../../schemas/vpn-config.schema';

const execAsync = promisify(exec);

export interface NetworkConfig {
  networkName: string;
  subnet: string;
  gateway: string;
  ipRange: string;
}

export interface MachineNetworkConfig {
  machineId: string;
  userId: string;
  templateId: string;
  internalIP: string;
  exposedPorts: ExposedPort[];
  networkName: string;
}

@Injectable()
export class NetworkIsolationService {
  private readonly logger = new Logger(NetworkIsolationService.name);
  private readonly docker: Docker;

  constructor(
    @InjectModel(MachineNetwork.name) private machineNetworkModel: Model<MachineNetworkDocument>,
    @InjectModel(VPNConfig.name) private vpnConfigModel: Model<VPNConfigDocument>,
    private configService: ConfigService,
  ) {
    this.docker = new Docker({
      socketPath: this.configService.get('DOCKER_SOCKET_PATH', '/var/run/docker.sock'),
    });
  }

  /**
   * Create isolated Docker network for a user
   */
  async createUserNetwork(userId: string): Promise<NetworkConfig> {
    try {
      // Get user's VPN configuration to determine subnet
      const vpnConfig = await this.vpnConfigModel.findOne({ userId: new Types.ObjectId(userId) });
      if (!vpnConfig) {
        throw new BadRequestException('User must have VPN configuration before creating network');
      }

      const networkName = `mybox-user-${userId.substring(0, 8)}`;
      const subnet = vpnConfig.subnet; // e.g., ********/24
      const gateway = vpnConfig.ipAddress; // e.g., ******** (user's VPN IP)
      
      // Calculate IP range for Docker containers (e.g., *********-**********)
      const subnetBase = subnet.split('/')[0].split('.').slice(0, 3).join('.');
      const ipRange = `${subnetBase}.10/28`; // Containers get IPs from .10 to .25

      // Check if network already exists
      const existingNetworks = await this.docker.listNetworks();
      const networkExists = existingNetworks.some(net => net.Name === networkName);

      if (networkExists) {
        this.logger.log(`Network ${networkName} already exists for user ${userId}`);
        return { networkName, subnet, gateway, ipRange };
      }

      // Create Docker network
      const network = await this.docker.createNetwork({
        Name: networkName,
        Driver: 'bridge',
        IPAM: {
          Config: [{
            Subnet: subnet,
            Gateway: gateway,
            IPRange: ipRange
          }]
        },
        Options: {
          'com.docker.network.bridge.name': `br-${networkName}`,
          'com.docker.network.driver.mtu': '1420' // Match WireGuard MTU
        },
        Labels: {
          'mybox.user.id': userId,
          'mybox.network.type': 'user-isolated',
          'mybox.created.at': new Date().toISOString()
        }
      });

      this.logger.log(`Created isolated network ${networkName} for user ${userId} with subnet ${subnet}`);

      // Set up iptables rules for network isolation
      await this.setupNetworkIsolation(networkName, subnet);

      return { networkName, subnet, gateway, ipRange };
    } catch (error) {
      this.logger.error(`Failed to create user network for ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Connect machine to user's isolated network
   */
  async connectMachineToNetwork(config: MachineNetworkConfig): Promise<MachineNetworkDocument> {
    try {
      const { machineId, userId, templateId, exposedPorts } = config;

      // Ensure user network exists
      const networkConfig = await this.createUserNetwork(userId);

      // Get next available IP in the user's subnet
      const internalIP = await this.getNextAvailableIP(userId, networkConfig.subnet);

      // Create machine network record
      const machineNetwork = new this.machineNetworkModel({
        machineId: new Types.ObjectId(machineId),
        userId: new Types.ObjectId(userId),
        templateId: new Types.ObjectId(templateId),
        networkName: networkConfig.networkName,
        internalIP,
        subnetCIDR: networkConfig.subnet,
        exposedPorts,
        vpnAccessible: true,
        allowedSourceIPs: [networkConfig.gateway], // Allow access from user's VPN IP
      });

      await machineNetwork.save();

      this.logger.log(`Connected machine ${machineId} to network ${networkConfig.networkName} with IP ${internalIP}`);

      return machineNetwork;
    } catch (error) {
      this.logger.error(`Failed to connect machine to network: ${error.message}`);
      throw error;
    }
  }

  /**
   * Disconnect machine from network
   */
  async disconnectMachineFromNetwork(machineId: string): Promise<void> {
    try {
      const machineNetwork = await this.machineNetworkModel.findOne({ 
        machineId: new Types.ObjectId(machineId) 
      });

      if (!machineNetwork) {
        this.logger.warn(`No network configuration found for machine ${machineId}`);
        return;
      }

      // Remove from database
      await this.machineNetworkModel.deleteOne({ machineId: new Types.ObjectId(machineId) });

      this.logger.log(`Disconnected machine ${machineId} from network ${machineNetwork.networkName}`);
    } catch (error) {
      this.logger.error(`Failed to disconnect machine from network: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get next available IP address in user's subnet
   */
  private async getNextAvailableIP(userId: string, subnet: string): Promise<string> {
    const subnetBase = subnet.split('/')[0].split('.').slice(0, 3).join('.');
    
    // Get all used IPs in this subnet
    const usedNetworks = await this.machineNetworkModel.find({
      userId: new Types.ObjectId(userId),
      subnetCIDR: subnet
    }, 'internalIP').exec();

    const usedIPs = new Set(usedNetworks.map(net => net.internalIP));

    // Find next available IP (starting from .10, .1 is reserved for VPN gateway)
    for (let i = 10; i <= 254; i++) {
      const ip = `${subnetBase}.${i}`;
      if (!usedIPs.has(ip)) {
        return ip;
      }
    }

    throw new BadRequestException('No available IP addresses in user subnet');
  }

  /**
   * Set up iptables rules for network isolation
   */
  private async setupNetworkIsolation(networkName: string, subnet: string): Promise<void> {
    try {
      const bridgeName = `br-${networkName}`;
      
      // Rules to isolate this network from other networks
      const rules = [
        // Allow traffic within the same subnet
        `iptables -I DOCKER-USER -s ${subnet} -d ${subnet} -j ACCEPT`,
        
        // Block traffic to other mybox networks
        `iptables -I DOCKER-USER -s ${subnet} -d 10.0.0.0/8 -j DROP`,
        
        // Allow traffic to internet (optional, can be configured)
        `iptables -I DOCKER-USER -s ${subnet} -d 0.0.0.0/0 -j ACCEPT`,
        
        // Allow established connections back
        `iptables -I DOCKER-USER -d ${subnet} -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT`
      ];

      for (const rule of rules) {
        try {
          await execAsync(rule);
          this.logger.debug(`Applied iptables rule: ${rule}`);
        } catch (error) {
          this.logger.warn(`Failed to apply iptables rule: ${rule} - ${error.message}`);
        }
      }

      this.logger.log(`Set up network isolation for ${networkName} (${subnet})`);
    } catch (error) {
      this.logger.error(`Failed to set up network isolation: ${error.message}`);
      // Don't throw error as network can still function without perfect isolation
    }
  }

  /**
   * Remove user network and cleanup
   */
  async removeUserNetwork(userId: string): Promise<void> {
    try {
      const networkName = `mybox-user-${userId.substring(0, 8)}`;

      // Remove all machine networks for this user
      await this.machineNetworkModel.deleteMany({ userId: new Types.ObjectId(userId) });

      // Remove Docker network
      try {
        const network = this.docker.getNetwork(networkName);
        await network.remove();
        this.logger.log(`Removed Docker network ${networkName}`);
      } catch (error) {
        if (error.statusCode !== 404) {
          this.logger.warn(`Failed to remove Docker network ${networkName}: ${error.message}`);
        }
      }

      // Cleanup iptables rules
      await this.cleanupNetworkIsolation(networkName);

      this.logger.log(`Cleaned up network for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to remove user network: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cleanup iptables rules for a network
   */
  private async cleanupNetworkIsolation(networkName: string): Promise<void> {
    try {
      // This is a simplified cleanup - in production, you'd want more sophisticated rule management
      const { stdout } = await execAsync('iptables -L DOCKER-USER --line-numbers -n');
      
      // Find and remove rules related to this network
      const lines = stdout.split('\n');
      for (const line of lines) {
        if (line.includes(networkName)) {
          // Extract line number and remove rule
          const lineNumber = line.split(' ')[0];
          if (lineNumber && !isNaN(parseInt(lineNumber))) {
            try {
              await execAsync(`iptables -D DOCKER-USER ${lineNumber}`);
            } catch (error) {
              // Ignore errors during cleanup
            }
          }
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to cleanup iptables rules: ${error.message}`);
    }
  }

  /**
   * Get network information for a machine
   */
  async getMachineNetwork(machineId: string): Promise<MachineNetworkDocument | null> {
    return this.machineNetworkModel.findOne({ 
      machineId: new Types.ObjectId(machineId) 
    }).exec();
  }

  /**
   * Get all networks for a user
   */
  async getUserNetworks(userId: string): Promise<MachineNetworkDocument[]> {
    return this.machineNetworkModel.find({ 
      userId: new Types.ObjectId(userId) 
    }).exec();
  }

  /**
   * Get network statistics
   */
  async getNetworkStats(): Promise<{
    totalNetworks: number;
    activeConnections: number;
    totalMachines: number;
  }> {
    const totalMachines = await this.machineNetworkModel.countDocuments();
    const activeConnections = await this.machineNetworkModel.countDocuments({ status: 'running' });
    
    // Count unique networks
    const networks = await this.machineNetworkModel.distinct('networkName');
    const totalNetworks = networks.length;

    return {
      totalNetworks,
      activeConnections,
      totalMachines
    };
  }
}
