import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type MachineNetworkDocument = MachineNetwork & Document;

export interface ExposedPort {
  internal: number;
  external?: number; // Optional external port for direct access
  protocol: 'tcp' | 'udp';
  description?: string;
}

export interface NetworkStats {
  bytesReceived: number;
  bytesSent: number;
  packetsReceived: number;
  packetsSent: number;
  lastUpdated: Date;
}

@Schema({ timestamps: true })
export class MachineNetwork {
  @Prop({ type: Types.ObjectId, ref: 'MachineInstance', required: true })
  machineId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'MachineTemplate', required: true })
  templateId: Types.ObjectId;

  @Prop({ required: true })
  networkName: string; // Docker network name (e.g., mybox-user-12345678)

  @Prop({ required: true })
  internalIP: string; // Machine's IP within the user's subnet (e.g., *********)

  @Prop({ required: true })
  subnetCIDR: string; // User's subnet (e.g., ********/24)

  @Prop({ type: [Object], default: [] })
  exposedPorts: ExposedPort[];

  @Prop({ default: true })
  vpnAccessible: boolean; // Whether machine is accessible via VPN

  @Prop({ default: false })
  publicAccessible: boolean; // Whether machine has public internet access

  @Prop({ type: [String], default: [] })
  allowedSourceIPs: string[]; // IPs allowed to access this machine

  @Prop({ type: Object, default: { bytesReceived: 0, bytesSent: 0, packetsReceived: 0, packetsSent: 0, lastUpdated: new Date() } })
  networkStats: NetworkStats;

  @Prop({ default: 'running' })
  status: 'running' | 'stopped' | 'paused' | 'error';

  @Prop()
  containerNetworkId?: string; // Docker container network ID

  @Prop()
  bridgeInterface?: string; // Network bridge interface name

  @Prop({ type: [String], default: [] })
  dnsServers: string[]; // Custom DNS servers for this machine

  @Prop({ type: Object, default: {} })
  firewallRules: Record<string, any>; // Custom firewall rules

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop()
  lastAccessedAt?: Date;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>; // Additional network configuration
}

export const MachineNetworkSchema = SchemaFactory.createForClass(MachineNetwork);

// Indexes for performance
MachineNetworkSchema.index({ machineId: 1 });
MachineNetworkSchema.index({ userId: 1 });
MachineNetworkSchema.index({ templateId: 1 });
MachineNetworkSchema.index({ networkName: 1 });
MachineNetworkSchema.index({ internalIP: 1 });
MachineNetworkSchema.index({ status: 1 });
MachineNetworkSchema.index({ createdAt: -1 });
