/* Custom scrollbar for statistics page */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(139, 92, 246, 0.1);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(139, 92, 246, 0.1);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #8B5CF6, #EC4899);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #A78BFA, #F472B6);
}

/* Neon glow effects */
.neon-glow {
  box-shadow: 
    0 0 5px rgba(139, 92, 246, 0.5),
    0 0 10px rgba(139, 92, 246, 0.3),
    0 0 15px rgba(139, 92, 246, 0.2),
    0 0 20px rgba(139, 92, 246, 0.1);
}

.neon-glow-pink {
  box-shadow: 
    0 0 5px rgba(236, 72, 153, 0.5),
    0 0 10px rgba(236, 72, 153, 0.3),
    0 0 15px rgba(236, 72, 153, 0.2),
    0 0 20px rgba(236, 72, 153, 0.1);
}

.neon-glow-blue {
  box-shadow: 
    0 0 5px rgba(6, 182, 212, 0.5),
    0 0 10px rgba(6, 182, 212, 0.3),
    0 0 15px rgba(6, 182, 212, 0.2),
    0 0 20px rgba(6, 182, 212, 0.1);
}

.neon-glow-green {
  box-shadow: 
    0 0 5px rgba(16, 185, 129, 0.5),
    0 0 10px rgba(16, 185, 129, 0.3),
    0 0 15px rgba(16, 185, 129, 0.2),
    0 0 20px rgba(16, 185, 129, 0.1);
}

.neon-glow-yellow {
  box-shadow: 
    0 0 5px rgba(245, 158, 11, 0.5),
    0 0 10px rgba(245, 158, 11, 0.3),
    0 0 15px rgba(245, 158, 11, 0.2),
    0 0 20px rgba(245, 158, 11, 0.1);
}

/* Floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Pulse animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text {
  background: linear-gradient(-45deg, #8B5CF6, #EC4899, #06B6D4, #10B981);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Chart container enhancements */
.chart-container {
  position: relative;
  overflow: hidden;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 70%);
  pointer-events: none;
  z-index: 1;
}

/* Hover effects for cards */
.stat-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.stat-card:hover::before {
  left: 100%;
}

/* Loading spinner enhancement */
.loading-spinner {
  position: relative;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-top: 2px solid #8B5CF6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .chart-container {
    height: 300px !important;
  }
  
  .stat-card {
    padding: 1rem;
  }
}

/* Dark mode enhancements */
.glass-card-dark {
  backdrop-filter: blur(20px);
  background: rgba(139, 92, 246, 0.05);
  border: 1px solid rgba(139, 92, 246, 0.2);
  transition: all 0.3s ease;
}

.glass-card-dark:hover {
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.3);
  transform: translateY(-2px);
}

/* Tab navigation enhancements */
.tab-button {
  position: relative;
  overflow: hidden;
}

.tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(139, 92, 246, 0.1), rgba(236, 72, 153, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab-button:hover::before {
  opacity: 1;
}

/* Chart tooltip enhancements */
.recharts-tooltip-wrapper {
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

/* Animation delays for staggered effects */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }
.stagger-7 { animation-delay: 0.7s; }
.stagger-8 { animation-delay: 0.8s; }