import Layout from '../components/Layout';
import { Plus, Search, MoreVertical } from 'lucide-react';

const users = [
    { id: 1, name: 'Admin', email: '<EMAIL>', role: 'Administrator', status: 'Active', lastLogin: '2025-06-07 11:58 CET' },
    { id: 1, name: 'Admin', email: '<EMAIL>', role: 'Administrator', status: 'Active', lastLogin: '2025-08-12 16:51 CET' },
    { id: 2, name: 'SOC Analyst', email: '<EMAIL>', role: 'Editor', status: 'Active', lastLogin: '2025-06-07 09:12 CET' },
    { id: 3, name: 'Guest User', email: '<EMAIL>', role: 'Viewer', status: 'Inactive', lastLogin: '2025-04-01 14:30 CET' },
    { id: 4, name: 'Service API', email: '<EMAIL>', role: 'API Access', status: 'Active', lastLogin: 'N/A (Service Account)' },
];

export default function UserManagementPage() {
    return (
        <Layout>
            <div className="flex justify-between items-center mb-8">
                <div>
                    <h1 className="text-4xl font-bold text-white mb-2">User Management</h1>
                    <p className="text-gray-400">Manage all users, roles, and permissions.</p>
                </div>
                <button className="group flex items-center justify-center py-2 px-4 font-semibold text-black bg-white rounded-lg hover:bg-gray-200 transition-all duration-200">
                    <Plus className="mr-2 h-5 w-5" />
                    Add User
                </button>
            </div>
            
            <div className="bg-black border border-gray-800 rounded-lg">
                <div className="p-4 flex justify-between items-center border-b border-gray-800">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
                        <input type="text" placeholder="Search users..." className="bg-gray-900 border border-gray-700 rounded-md pl-10 pr-4 py-2 text-white focus:ring-1 focus:ring-white outline-none"/>
                    </div>
                </div>

                <div className="overflow-x-auto">
                    <table className="w-full text-sm text-left text-gray-300">
                        <thead className="text-xs text-gray-400 uppercase">
                            <tr>
                                <th scope="col" className="px-6 py-3">Name</th>
                                <th scope="col" className="px-6 py-3">Role</th>
                                <th scope="col" className="px-6 py-3">Status</th>
                                <th scope="col" className="px-6 py-3">Last Login</th>
                                <th scope="col" className="px-6 py-3"><span className="sr-only">Actions</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            {users.map(user => (
                                <tr key={user.id} className="border-b border-gray-800 hover:bg-gray-900">
                                    <td className="px-6 py-4">
                                        <div className="font-semibold text-white">{user.name}</div>
                                        <div className="text-gray-400">{user.email}</div>
                                    </td>
                                    <td className="px-6 py-4">{user.role}</td>
                                    <td className="px-6 py-4">
                                        <span className={`px-3 py-1 rounded-full text-xs font-semibold ${user.status === 'Active' ? 'bg-green-900/50 text-green-300' : 'bg-gray-700 text-gray-300'}`}>
                                            {user.status}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 font-mono">{user.lastLogin}</td>
                                    <td className="px-6 py-4 text-right">
                                        <button className="p-2 rounded-full hover:bg-gray-800">
                                            <MoreVertical size={18} />
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </Layout>
    );
}
