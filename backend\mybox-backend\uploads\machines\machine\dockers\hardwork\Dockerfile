FROM debian:stable-slim

RUN apt-get update && apt-get install -y build-essential cron php libapache2-mod-php npm gnupg curl vsftpd samba samba-common net-tools python3 apache2 apache2-utils python3-pip libfuse2 gcc libfontconfig1 libx11-6 libharfbuzz0b libfribidi0 sudo && apt-get clean

# Create FTP directory and secure chroot dir
RUN mkdir -p /var/public/ftp && chmod 755 /var/public/ftp && \
    mkdir -p /var/run/vsftpd/empty

COPY machine/ftpserver/ftp_files/ /var/public/ftp
COPY machine/ftpserver/vsftpd.conf /etc/vsftpd.conf
COPY entrypoint.sh /entrypoint.sh



# Copy Samba configuration and entrypoint script
COPY machine/smbserver/smb.conf /etc/samba/smb.conf


# ---- Install Node.js 18 (required by Next.js) ----
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs

# ---- Setup vulnerable Next.js app ----
# Create working directory for app
WORKDIR /opt/nextjs-app

# Copy app files (you will need to place them locally in nextjs-app/)
COPY machine/nextjs-app/package.json .
COPY machine/nextjs-app/package-lock.json .
RUN npm install

COPY machine/nextjs-app/ .


# Network next-app
RUN mkdir -p /opt/network-app
WORKDIR /opt/network-app
COPY machine/network-app/package.json .
COPY machine/network-app/package-lock.json .
RUN npm install

COPY machine/network-app/ .


# backdoor

COPY machine/apache/index.html /var/www/html/index.html
RUN mkdir -p /var/www/html/./app/project__2025__14-2/old-versions/commits_10/version1-config/json/js/html/public/frontend/html/js/php/
COPY machine/backdoor/bdob.php /var/www/html/./app/project__2025__14-2/old-versions/commits_10/version1-config/json/js/html/public/frontend/html/js/php/403.php

# apache subdomain config
COPY machine/apache/hardwork.kybs.conf /etc/apache2/sites-available/hardwork.kybs.conf
COPY machine/apache/networkManagementlocalAdministrationP4nel.hardwork.kybs.conf /etc/apache2/sites-available/networkManagementlocalAdministrationP4nel.hardwork.kybs.conf
COPY machine/apache/uploadsSubdomain.hardwork.kybs  /etc/apache2/sites-available/uploadsSubdomain.hardwork.kybs.conf
COPY machine/apache/000-default.conf /etc/apache2/sites-available/000-default.conf

# user creation 
RUN useradd -m -s /bin/bash developer
RUN echo "developer:$(openssl passwd -6 StrongP@ssw0rd123)" | chpasswd -e

# copy flask app and set permission
COPY machine/web-machine/ /home/<USER>/app/
RUN chown -R developer:developer /home/<USER>/app
RUN chmod -R 700 /home/<USER>/app

#flask app requirements
COPY machine/web-machine/requirements.txt /tmp/
RUN pip3 install -r /tmp/requirements.txt --break-system-packages

# local web app
COPY machine/repo/dev-project.pdf /var/mail/dev-project.pdf
COPY machine/apache/ports.conf /etc/apache2/ports.conf
COPY machine/apache/gym-local.conf  /etc/apache2/sites-available/gym-local.conf
RUN chmod +x /entrypoint.sh

#imagemagick installation 
RUN curl -fL https://github.com/ImageMagick/ImageMagick/releases/download/7.1.1-35/ImageMagick-d775d2a-gcc-x86_64.AppImage -o /usr/bin/magick && chmod +x /usr/bin/magick
RUN echo 'developer ALL=(ALL) NOPASSWD: SETENV: /home/<USER>/squashfs-root/AppRun' >> /etc/sudoers
USER developer
RUN cd /home/<USER>
USER root 
RUN chown root:root /home/<USER>/squashfs-root/*
RUN chmod 711 /home/<USER>/squashfs-root/*

#cronjob for imagemagick

COPY setup_imagemagick.sh /etc/setup_imagemagick.sh
RUN chmod +x /etc/setup_imagemagick.sh
RUN echo "*/2 * * * * root /etc/setup_imagemagick.sh" >> /etc/crontab


EXPOSE 21 10000-10100 445 139 9980 80 

ENTRYPOINT ["/entrypoint.sh"]

