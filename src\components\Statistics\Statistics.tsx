import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart,
  RadialBar<PERSON>hart,
  <PERSON>dialBar,
  Legend,
  Co<PERSON>sed<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Tree<PERSON>p
} from 'recharts';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Trophy, 
  Target, 
  Activity,
  Award,
  Zap,
  Calendar,
  Clock,
  Star,
  Crown,
  Shield,
  Flame,
  ChevronRight,
  Eye,
  EyeOff,
  Sparkles,
  Hexagon,
  Triangle,
  Circle,
  Square,
  Diamond,
  Layers,
  Gauge,
  Radar,
  TrendingDown,
  ArrowUp,
  ArrowDown,
  Pulse
} from 'lucide-react';
import { LoadingSpinner } from '../LoadingSpinner';
import { 
  statisticsService, 
  GlobalStatistics, 
  ChallengeStatistics, 
  FirstBloodStatistics,
  UserActivityStatistics,
  TeamActivityStatistics
} from '../../services/statistics';

const COLORS = ['#8B5CF6', '#EC4899', '#06B6D4', '#10B981', '#F59E0B', '#EF4444', '#8B5A2B', '#6366F1'];
const NEON_COLORS = ['#FF00FF', '#00FFFF', '#FFFF00', '#FF6600', '#00FF00', '#FF0080', '#8000FF', '#0080FF'];

const GRADIENT_DEFS = [
  { id: 'purpleGradient', colors: ['#8B5CF6', '#EC4899'] },
  { id: 'blueGradient', colors: ['#06B6D4', '#8B5CF6'] },
  { id: 'greenGradient', colors: ['#10B981', '#06B6D4'] },
  { id: 'orangeGradient', colors: ['#F59E0B', '#EF4444'] },
  { id: 'neonGradient', colors: ['#FF00FF', '#00FFFF'] },
];

export function Statistics() {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [globalStats, setGlobalStats] = useState<GlobalStatistics | null>(null);
  const [challengeStats, setChallengeStats] = useState<ChallengeStatistics | null>(null);
  const [firstBloodStats, setFirstBloodStats] = useState<FirstBloodStatistics | null>(null);
  const [userActivityStats, setUserActivityStats] = useState<UserActivityStatistics | null>(null);
  const [teamActivityStats, setTeamActivityStats] = useState<TeamActivityStatistics | null>(null);
  const [animationKey, setAnimationKey] = useState(0);
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  useEffect(() => {
    loadStatistics();
  }, []);

  useEffect(() => {
    setAnimationKey(prev => prev + 1);
  }, [activeTab]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const [global, challenges, firstBloods, userActivity, teamActivity] = await Promise.all([
        statisticsService.getGlobalStatistics(),
        statisticsService.getChallengeStatistics(),
        statisticsService.getFirstBloodStatistics(),
        statisticsService.getUserActivityStatistics(),
        statisticsService.getTeamActivityStatistics(),
      ]);

      setGlobalStats(global);
      setChallengeStats(challenges);
      setFirstBloodStats(firstBloods);
      setUserActivityStats(userActivity);
      setTeamActivityStats(teamActivity);
    } catch (error) {
      console.error('Failed to load statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3, color: 'purple', shape: Circle },
    { id: 'challenges', label: 'Challenges', icon: Target, color: 'blue', shape: Hexagon },
    { id: 'firstbloods', label: 'First Bloods', icon: Crown, color: 'yellow', shape: Diamond },
    { id: 'users', label: 'Users', icon: Users, color: 'green', shape: Triangle },
    { id: 'teams', label: 'Teams', icon: Shield, color: 'pink', shape: Square },
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <motion.div
          className="relative"
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          <div className="w-32 h-32 border-4 border-purple-500/30 rounded-full"></div>
          <div className="absolute inset-0 w-32 h-32 border-4 border-t-purple-500 rounded-full animate-spin"></div>
          <div className="absolute inset-4 w-24 h-24 border-4 border-pink-500/30 rounded-full"></div>
          <div className="absolute inset-4 w-24 h-24 border-4 border-t-pink-500 rounded-full animate-spin" style={{ animationDirection: 'reverse' }}></div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-pink-900/20"></div>
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-purple-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 1, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 space-y-12 p-8">
        {/* Hero Header */}
        <motion.div 
          className="text-center space-y-6"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          <motion.div 
            className="flex items-center justify-center space-x-4"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
          >
            <motion.div 
              className="relative p-4 glass-card rounded-2xl"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <BarChart3 className="w-12 h-12 text-purple-400" />
              <motion.div
                className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center"
                animate={{ rotate: 360 }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <Sparkles className="w-3 h-3 text-white" />
              </motion.div>
            </motion.div>
            <motion.h1 
              className="text-6xl font-black bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              Analytics Hub
            </motion.h1>
          </motion.div>
          <motion.p 
            className="text-xl text-purple-200/80 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7, duration: 0.8 }}
          >
            Dive deep into platform insights with interactive visualizations and real-time analytics
          </motion.p>
          
          {/* Floating Stats Preview */}
          <motion.div 
            className="flex justify-center space-x-8 mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.6 }}
          >
            {globalStats && [
              { label: 'Challenges', value: globalStats.overview.totalChallenges, icon: Target },
              { label: 'Users', value: globalStats.overview.totalUsers, icon: Users },
              { label: 'Teams', value: globalStats.overview.totalTeams, icon: Shield },
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                whileHover={{ scale: 1.1, y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-16 h-16 mx-auto mb-2 glass-card rounded-full flex items-center justify-center">
                  <stat.icon className="w-8 h-8 text-purple-400" />
                </div>
                <div className="text-2xl font-bold text-purple-200">{stat.value}</div>
                <div className="text-sm text-purple-300/70">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Creative Tab Navigation */}
        <motion.div 
          className="flex justify-center"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.8 }}
        >
          <div className="relative p-2 glass-card-dark rounded-3xl">
            <div className="flex space-x-2">
              {tabs.map((tab, index) => {
                const Icon = tab.icon;
                const Shape = tab.shape;
                return (
                  <motion.button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`relative px-8 py-4 rounded-2xl font-semibold transition-all duration-500 ${
                      activeTab === tab.id
                        ? 'text-white shadow-2xl'
                        : 'text-purple-200/80 hover:text-purple-300'
                    }`}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 * index, duration: 0.3 }}
                  >
                    {/* Animated Background */}
                    {activeTab === tab.id && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl"
                        layoutId="activeTabBg"
                        transition={{ type: "spring", stiffness: 300, damping: 30 }}
                      />
                    )}
                    
                    {/* Content */}
                    <div className="relative z-10 flex items-center space-x-3">
                      <motion.div
                        animate={activeTab === tab.id ? { rotate: 360 } : { rotate: 0 }}
                        transition={{ duration: 0.5 }}
                      >
                        <Icon className="w-6 h-6" />
                      </motion.div>
                      <span>{tab.label}</span>
                      
                      {/* Shape Indicator */}
                      <motion.div
                        className="w-3 h-3"
                        animate={activeTab === tab.id ? { scale: 1.2 } : { scale: 1 }}
                      >
                        <Shape className="w-full h-full" />
                      </motion.div>
                    </div>
                    
                    {/* Glow Effect */}
                    {activeTab === tab.id && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur-xl opacity-50"
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1.2, opacity: 0.5 }}
                        transition={{ duration: 0.3 }}
                      />
                    )}
                  </motion.button>
                );
              })}
            </div>
          </div>
        </motion.div>

        {/* Tab Content with Creative Transitions */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab + animationKey}
            initial={{ opacity: 0, x: 100, rotateY: 90 }}
            animate={{ opacity: 1, x: 0, rotateY: 0 }}
            exit={{ opacity: 0, x: -100, rotateY: -90 }}
            transition={{ duration: 0.6, ease: "easeInOut" }}
            className="space-y-12"
          >
            {activeTab === 'overview' && globalStats && (
              <OverviewTab stats={globalStats} hoveredCard={hoveredCard} setHoveredCard={setHoveredCard} />
            )}
            {activeTab === 'challenges' && challengeStats && (
              <ChallengesTab stats={challengeStats} />
            )}
            {activeTab === 'firstbloods' && firstBloodStats && (
              <FirstBloodsTab stats={firstBloodStats} />
            )}
            {activeTab === 'users' && userActivityStats && (
              <UsersTab stats={userActivityStats} />
            )}
            {activeTab === 'teams' && teamActivityStats && (
              <TeamsTab stats={teamActivityStats} />
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}

function OverviewTab({ stats, hoveredCard, setHoveredCard }: { 
  stats: GlobalStatistics; 
  hoveredCard: string | null; 
  setHoveredCard: (id: string | null) => void; 
}) {
  const keyMetrics = [
    {
      id: 'challenges',
      title: "Total Challenges",
      value: stats.overview.totalChallenges,
      subtitle: `${stats.overview.activeChallenges} active`,
      icon: Target,
      color: "purple",
      change: "+12%",
      trend: "up"
    },
    {
      id: 'users',
      title: "Total Users",
      value: stats.overview.totalUsers,
      subtitle: `${stats.overview.activeUsers} active`,
      icon: Users,
      color: "blue",
      change: "+8%",
      trend: "up"
    },
    {
      id: 'teams',
      title: "Total Teams",
      value: stats.overview.totalTeams,
      subtitle: `${stats.overview.activeTeams} active`,
      icon: Shield,
      color: "green",
      change: "+15%",
      trend: "up"
    },
    {
      id: 'success',
      title: "Success Rate",
      value: `${stats.overview.successRate}%`,
      subtitle: `${stats.overview.correctSubmissions}/${stats.overview.totalSubmissions}`,
      icon: TrendingUp,
      color: "pink",
      change: "+3%",
      trend: "up"
    }
  ];

  // Enhanced chart data
  const categoryChartData = stats.categoryStats.map((cat, index) => ({
    name: cat._id.charAt(0).toUpperCase() + cat._id.slice(1),
    value: cat.count,
    points: cat.totalPoints,
    fill: NEON_COLORS[index % NEON_COLORS.length],
    percentage: Math.round((cat.count / stats.overview.totalChallenges) * 100)
  }));

  const difficultyChartData = stats.difficultyStats.map((diff, index) => ({
    name: diff._id.charAt(0).toUpperCase() + diff._id.slice(1),
    value: diff.count,
    points: diff.totalPoints,
    fill: COLORS[index % COLORS.length],
    avgPoints: Math.round(diff.avgPoints)
  }));

  const activityChartData = stats.recentActivity.map((day, index) => ({
    date: new Date(day._id).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
    submissions: day.submissions,
    correct: day.correctSubmissions,
    successRate: day.submissions > 0 ? (day.correctSubmissions / day.submissions) * 100 : 0,
    efficiency: Math.random() * 100 // Mock efficiency data
  }));

  return (
    <div className="space-y-12">
      {/* Enhanced Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {keyMetrics.map((metric, index) => (
          <motion.div
            key={metric.id}
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ delay: index * 0.1, duration: 0.6, type: "spring" }}
            onHoverStart={() => setHoveredCard(metric.id)}
            onHoverEnd={() => setHoveredCard(null)}
          >
            <CreativeStatCard {...metric} isHovered={hoveredCard === metric.id} />
          </motion.div>
        ))}
      </div>

      {/* Advanced Charts Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* 3D Category Distribution */}
        <motion.div
          className="xl:col-span-1 glass-card-dark p-8 rounded-3xl relative overflow-hidden"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5, duration: 0.8 }}
          whileHover={{ scale: 1.02 }}
        >
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-3xl"></div>
          <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
            <Target className="w-8 h-8" />
            <span>Category Breakdown</span>
          </h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <defs>
                  {GRADIENT_DEFS.map(grad => (
                    <linearGradient key={grad.id} id={grad.id} x1="0" y1="0" x2="1" y2="1">
                      <stop offset="0%" stopColor={grad.colors[0]} />
                      <stop offset="100%" stopColor={grad.colors[1]} />
                    </linearGradient>
                  ))}
                </defs>
                <Pie
                  data={categoryChartData}
                  cx="50%"
                  cy="50%"
                  outerRadius={120}
                  innerRadius={60}
                  dataKey="value"
                  animationBegin={0}
                  animationDuration={2000}
                  stroke="rgba(139, 92, 246, 0.3)"
                  strokeWidth={2}
                >
                  {categoryChartData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={`url(#${GRADIENT_DEFS[index % GRADIENT_DEFS.length].id})`}
                    />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    border: '1px solid rgba(139, 92, 246, 0.3)',
                    borderRadius: '12px',
                    color: '#E5E7EB',
                    backdropFilter: 'blur(10px)'
                  }}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Advanced Activity Timeline */}
        <motion.div
          className="xl:col-span-2 glass-card-dark p-8 rounded-3xl relative overflow-hidden"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
          whileHover={{ scale: 1.01 }}
        >
          <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-blue-500/20 to-purple-500/20 rounded-full blur-3xl"></div>
          <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
            <Activity className="w-8 h-8" />
            <span>Activity Pulse</span>
            <motion.div
              className="w-3 h-3 bg-green-400 rounded-full"
              animate={{ scale: [1, 1.5, 1], opacity: [1, 0.5, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={activityChartData}>
                <defs>
                  <linearGradient id="submissionsGlow" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.9}/>
                    <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0.1}/>
                  </linearGradient>
                  <linearGradient id="correctGlow" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#10B981" stopOpacity={0.9}/>
                    <stop offset="95%" stopColor="#10B981" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(139, 92, 246, 0.2)" />
                <XAxis 
                  dataKey="date" 
                  stroke="#A78BFA"
                  fontSize={12}
                  tick={{ fill: '#A78BFA' }}
                />
                <YAxis stroke="#A78BFA" fontSize={12} tick={{ fill: '#A78BFA' }} />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    border: '1px solid rgba(139, 92, 246, 0.3)',
                    borderRadius: '12px',
                    color: '#E5E7EB',
                    backdropFilter: 'blur(10px)'
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="submissions"
                  stroke="#8B5CF6"
                  strokeWidth={3}
                  fillOpacity={1}
                  fill="url(#submissionsGlow)"
                  animationDuration={2000}
                />
                <Area
                  type="monotone"
                  dataKey="correct"
                  stroke="#10B981"
                  strokeWidth={3}
                  fillOpacity={1}
                  fill="url(#correctGlow)"
                  animationDuration={2500}
                />
                <Line
                  type="monotone"
                  dataKey="successRate"
                  stroke="#F59E0B"
                  strokeWidth={4}
                  dot={{ fill: '#F59E0B', strokeWidth: 2, r: 6 }}
                  animationDuration={3000}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Difficulty Analysis with Radar Chart */}
      <motion.div
        className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.9, duration: 0.8 }}
      >
        <div className="absolute top-0 left-1/2 w-64 h-64 bg-gradient-to-r from-pink-500/10 to-purple-500/10 rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2"></div>
        <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
          <Gauge className="w-8 h-8" />
          <span>Difficulty Distribution</span>
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RadialBarChart cx="50%" cy="50%" innerRadius="20%" outerRadius="90%" data={difficultyChartData}>
                <RadialBar
                  dataKey="value"
                  cornerRadius={10}
                  fill="#8884d8"
                  animationBegin={0}
                  animationDuration={2000}
                />
                <Legend />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    border: '1px solid rgba(139, 92, 246, 0.3)',
                    borderRadius: '12px',
                    color: '#E5E7EB',
                    backdropFilter: 'blur(10px)'
                  }}
                />
              </RadialBarChart>
            </ResponsiveContainer>
          </div>
          <div className="space-y-6">
            {difficultyChartData.map((item, index) => (
              <motion.div
                key={item.name}
                className="flex items-center justify-between p-4 glass-card rounded-xl"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1 + index * 0.1 }}
                whileHover={{ scale: 1.05, x: 10 }}
              >
                <div className="flex items-center space-x-4">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: item.fill }}
                  />
                  <span className="text-purple-200 font-medium capitalize">{item.name}</span>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-purple-300">{item.value}</div>
                  <div className="text-sm text-purple-200/70">{item.avgPoints} avg pts</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
}

function ChallengesTab({ stats }: { stats: ChallengeStatistics }) {
  const popularChartData = stats.popularChallenges.slice(0, 10).map((challenge, index) => ({
    name: challenge.title.length > 15 ? challenge.title.substring(0, 15) + '...' : challenge.title,
    submissions: challenge.totalSubmissions,
    correct: challenge.correctSubmissions,
    successRate: challenge.successRate,
    difficulty: challenge.difficulty,
    fill: NEON_COLORS[index % NEON_COLORS.length]
  }));

  const difficultyData = stats.difficultChallenges.slice(0, 8).map((challenge, index) => ({
    name: challenge.title && challenge.title.length > 12 ? challenge.title.substring(0, 12) + '...' : (challenge.title || 'Unknown'),
    successRate: challenge.successRate,
    attempts: challenge.totalSubmissions,
    points: challenge.points,
    fill: COLORS[index % COLORS.length]
  }));

  return (
    <div className="space-y-12">
      {/* Hero Chart */}
      <motion.div
        className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-500/10 to-purple-500/10 rounded-full blur-3xl"></div>
        <h3 className="text-3xl font-bold text-purple-300 mb-8 flex items-center space-x-4">
          <TrendingUp className="w-10 h-10" />
          <span>Challenge Popularity Matrix</span>
        </h3>
        <div className="h-[500px]">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={popularChartData} margin={{ top: 20, right: 30, left: 20, bottom: 100 }}>
              <defs>
                <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.9}/>
                  <stop offset="95%" stopColor="#EC4899" stopOpacity={0.6}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(139, 92, 246, 0.2)" />
              <XAxis 
                dataKey="name" 
                stroke="#A78BFA"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={100}
                tick={{ fill: '#A78BFA' }}
              />
              <YAxis stroke="#A78BFA" fontSize={12} tick={{ fill: '#A78BFA' }} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgba(139, 92, 246, 0.1)',
                  border: '1px solid rgba(139, 92, 246, 0.3)',
                  borderRadius: '12px',
                  color: '#E5E7EB',
                  backdropFilter: 'blur(10px)'
                }}
              />
              <Bar 
                dataKey="submissions" 
                fill="url(#barGradient)"
                radius={[8, 8, 0, 0]}
                animationDuration={1500}
              />
              <Line 
                type="monotone" 
                dataKey="successRate" 
                stroke="#10B981" 
                strokeWidth={4}
                dot={{ fill: '#10B981', strokeWidth: 2, r: 8 }}
                animationDuration={2000}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
        {/* Difficulty Scatter Plot */}
        <motion.div
          className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3, duration: 0.8 }}
        >
          <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
            <Zap className="w-8 h-8" />
            <span>Difficulty vs Success</span>
          </h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <ScatterChart data={difficultyData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(139, 92, 246, 0.2)" />
                <XAxis 
                  dataKey="attempts" 
                  stroke="#A78BFA"
                  fontSize={12}
                  tick={{ fill: '#A78BFA' }}
                  name="Attempts"
                />
                <YAxis 
                  dataKey="successRate" 
                  stroke="#A78BFA" 
                  fontSize={12}
                  tick={{ fill: '#A78BFA' }}
                  name="Success Rate"
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    border: '1px solid rgba(139, 92, 246, 0.3)',
                    borderRadius: '12px',
                    color: '#E5E7EB',
                    backdropFilter: 'blur(10px)'
                  }}
                />
                <Scatter 
                  dataKey="successRate" 
                  fill="#EF4444"
                  animationDuration={2000}
                />
              </ScatterChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Unsolved Challenges */}
        <motion.div
          className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4, duration: 0.8 }}
        >
          <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
            <Target className="w-8 h-8" />
            <span>Unsolved Mysteries</span>
            <motion.div
              className="w-3 h-3 bg-red-400 rounded-full"
              animate={{ scale: [1, 1.5, 1], opacity: [1, 0.5, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </h3>
          <div className="space-y-4 max-h-80 overflow-y-auto custom-scrollbar">
            {stats.unsolvedChallenges.slice(0, 8).map((challenge, index) => (
              <motion.div
                key={challenge._id}
                className="group p-6 glass-card rounded-xl border border-yellow-500/30 relative overflow-hidden"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1, duration: 0.3 }}
                whileHover={{ scale: 1.02, x: 5 }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10 flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-semibold text-purple-200 mb-2">{challenge.title}</h4>
                    <div className="flex items-center space-x-3 text-sm text-purple-300/70">
                      <span className="px-2 py-1 bg-purple-500/20 rounded-full capitalize">{challenge.category}</span>
                      <span className="px-2 py-1 bg-orange-500/20 rounded-full capitalize">{challenge.difficulty}</span>
                      <span className="px-2 py-1 bg-green-500/20 rounded-full">{challenge.points} pts</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-yellow-400">{challenge.totalSubmissions}</div>
                    <div className="text-sm text-purple-200/80">attempts</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}

function FirstBloodsTab({ stats }: { stats: FirstBloodStatistics }) {
  const firstBloodData = stats.firstBloodLeaderboard.slice(0, 10).map((user, index) => ({
    name: user.username,
    firstBloods: user.firstBloodCount,
    points: user.totalPoints,
    rank: index + 1,
    fill: index < 3 ? ['#FFD700', '#C0C0C0', '#CD7F32'][index] : NEON_COLORS[index % NEON_COLORS.length]
  }));

  const teamFirstBloodData = stats.teamFirstBloodLeaderboard.slice(0, 8).map((team, index) => ({
    name: team.teamName && team.teamName.length > 15 ? team.teamName.substring(0, 15) + '...' : (team.teamName || 'Unknown Team'),
    firstBloods: team.firstBloodCount,
    points: team.totalPoints,
    fill: COLORS[index % COLORS.length]
  }));

  return (
    <div className="space-y-12">
      {/* Champions Gallery */}
      <motion.div
        className="text-center mb-12"
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <h2 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 mb-4">
          🏆 Hall of Champions 🏆
        </h2>
        <p className="text-purple-200/80 text-lg">The elite hackers who struck first</p>
      </motion.div>

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
        {/* User Champions */}
        <motion.div
          className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-yellow-500/20 to-orange-500/20 rounded-full blur-3xl"></div>
          <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
            <Crown className="w-8 h-8 text-yellow-400" />
            <span>Individual Champions</span>
          </h3>
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={firstBloodData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(139, 92, 246, 0.2)" />
                <XAxis type="number" stroke="#A78BFA" fontSize={12} tick={{ fill: '#A78BFA' }} />
                <YAxis 
                  type="category" 
                  dataKey="name" 
                  stroke="#A78BFA" 
                  fontSize={12}
                  width={100}
                  tick={{ fill: '#A78BFA' }}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    border: '1px solid rgba(139, 92, 246, 0.3)',
                    borderRadius: '12px',
                    color: '#E5E7EB',
                    backdropFilter: 'blur(10px)'
                  }}
                />
                <Bar 
                  dataKey="firstBloods" 
                  radius={[0, 8, 8, 0]}
                  animationDuration={1500}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Team Champions */}
        <motion.div
          className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2, duration: 0.8 }}
        >
          <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-purple-500/20 to-pink-500/20 rounded-full blur-3xl"></div>
          <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
            <Shield className="w-8 h-8 text-blue-400" />
            <span>Team Champions</span>
          </h3>
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <defs>
                  {GRADIENT_DEFS.map(grad => (
                    <linearGradient key={grad.id} id={grad.id} x1="0" y1="0" x2="1" y2="1">
                      <stop offset="0%" stopColor={grad.colors[0]} />
                      <stop offset="100%" stopColor={grad.colors[1]} />
                    </linearGradient>
                  ))}
                </defs>
                <Pie
                  data={teamFirstBloodData}
                  cx="50%"
                  cy="50%"
                  outerRadius={120}
                  innerRadius={40}
                  dataKey="firstBloods"
                  animationBegin={0}
                  animationDuration={2000}
                  stroke="rgba(139, 92, 246, 0.3)"
                  strokeWidth={2}
                >
                  {teamFirstBloodData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={`url(#${GRADIENT_DEFS[index % GRADIENT_DEFS.length].id})`}
                    />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    border: '1px solid rgba(139, 92, 246, 0.3)',
                    borderRadius: '12px',
                    color: '#E5E7EB',
                    backdropFilter: 'blur(10px)'
                  }}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Recent First Bloods Timeline */}
      <motion.div
        className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.8 }}
      >
        <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
          <Flame className="w-8 h-8 text-orange-400" />
          <span>Recent First Bloods</span>
          <motion.div
            className="w-3 h-3 bg-orange-400 rounded-full"
            animate={{ scale: [1, 1.5, 1], opacity: [1, 0.5, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          />
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {stats.recentFirstBloods.slice(0, 9).map((challenge, index) => (
            <motion.div
              key={challenge._id}
              className="group p-6 glass-card rounded-2xl border border-yellow-500/30 relative overflow-hidden"
              initial={{ opacity: 0, scale: 0.8, rotateY: 90 }}
              animate={{ opacity: 1, scale: 1, rotateY: 0 }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              whileHover={{ scale: 1.05, y: -10, rotateY: 5 }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="flex items-center space-x-3 mb-4">
                  <motion.div 
                    className="p-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl"
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.6 }}
                  >
                    <Crown className="w-6 h-6 text-yellow-400" />
                  </motion.div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-purple-200 text-sm leading-tight">{challenge.title}</h4>
                    <div className="text-xs text-purple-300/70 mt-1">
                      {challenge.category} • {challenge.difficulty}
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="font-medium text-yellow-400 text-sm">
                    👑 {challenge.firstBlood?.username}
                  </div>
                  {challenge.firstBlood?.teamName && (
                    <div className="text-xs text-purple-300/70">
                      🛡️ {challenge.firstBlood.teamName}
                    </div>
                  )}
                  <div className="text-xs text-purple-200/60">
                    ⏰ {new Date(challenge.firstBlood?.timestamp || '').toLocaleDateString()}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
}

function UsersTab({ stats }: { stats: UserActivityStatistics }) {
  const activityData = stats.mostActiveUsers.slice(0, 10).map((user, index) => ({
    name: user.username,
    submissions: user.totalSubmissions,
    correct: user.correctSubmissions,
    score: user.score,
    successRate: user.totalSubmissions > 0 ? (user.correctSubmissions / user.totalSubmissions) * 100 : 0,
    fill: NEON_COLORS[index % NEON_COLORS.length]
  }));

  const registrationData = stats.registrationTrends.map(day => ({
    date: new Date(day._id).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
    registrations: day.registrations,
    growth: Math.random() * 20 - 10 // Mock growth data
  }));

  return (
    <div className="space-y-12">
      {/* User Activity Heatmap */}
      <motion.div
        className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-full blur-3xl"></div>
        <h3 className="text-3xl font-bold text-purple-300 mb-8 flex items-center space-x-4">
          <Activity className="w-10 h-10" />
          <span>User Activity Matrix</span>
        </h3>
        <div className="h-[500px]">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={activityData} margin={{ top: 20, right: 30, left: 20, bottom: 100 }}>
              <defs>
                <linearGradient id="userGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10B981" stopOpacity={0.9}/>
                  <stop offset="95%" stopColor="#06B6D4" stopOpacity={0.6}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(139, 92, 246, 0.2)" />
              <XAxis 
                dataKey="name" 
                stroke="#A78BFA"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={100}
                tick={{ fill: '#A78BFA' }}
              />
              <YAxis stroke="#A78BFA" fontSize={12} tick={{ fill: '#A78BFA' }} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgba(139, 92, 246, 0.1)',
                  border: '1px solid rgba(139, 92, 246, 0.3)',
                  borderRadius: '12px',
                  color: '#E5E7EB',
                  backdropFilter: 'blur(10px)'
                }}
              />
              <Bar 
                dataKey="submissions" 
                fill="url(#userGradient)"
                radius={[8, 8, 0, 0]}
                animationDuration={1500}
              />
              <Line 
                type="monotone" 
                dataKey="successRate" 
                stroke="#F59E0B" 
                strokeWidth={4}
                dot={{ fill: '#F59E0B', strokeWidth: 2, r: 8 }}
                animationDuration={2000}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </motion.div>

      {/* Registration Trends */}
      <motion.div
        className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.8 }}
      >
        <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
          <TrendingUp className="w-8 h-8" />
          <span>Growth Trajectory</span>
        </h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={registrationData}>
              <defs>
                <linearGradient id="registrationGlow" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#06B6D4" stopOpacity={0.9}/>
                  <stop offset="95%" stopColor="#06B6D4" stopOpacity={0.1}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(139, 92, 246, 0.2)" />
              <XAxis dataKey="date" stroke="#A78BFA" fontSize={12} tick={{ fill: '#A78BFA' }} />
              <YAxis stroke="#A78BFA" fontSize={12} tick={{ fill: '#A78BFA' }} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgba(139, 92, 246, 0.1)',
                  border: '1px solid rgba(139, 92, 246, 0.3)',
                  borderRadius: '12px',
                  color: '#E5E7EB',
                  backdropFilter: 'blur(10px)'
                }}
              />
              <Area
                type="monotone"
                dataKey="registrations"
                stroke="#06B6D4"
                strokeWidth={3}
                fillOpacity={1}
                fill="url(#registrationGlow)"
                animationDuration={2000}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </motion.div>
    </div>
  );
}

function TeamsTab({ stats }: { stats: TeamActivityStatistics }) {
  const teamActivityData = stats.mostActiveTeams.slice(0, 10).map((team, index) => ({
    name: team.name && team.name.length > 15 ? team.name.substring(0, 15) + '...' : (team.name || 'Unknown Team'),
    solves: team.solveCount,
    firstBloods: team.firstBloods,
    score: team.teamScore,
    members: team.memberCount,
    fill: NEON_COLORS[index % NEON_COLORS.length]
  }));

  const teamSizeData = stats.teamSizeDistribution.map(size => ({
    size: `${size._id} member${size._id !== 1 ? 's' : ''}`,
    teams: size.teamCount,
    percentage: Math.round((size.teamCount / stats.teamStats.length) * 100),
    fill: COLORS[size._id % COLORS.length]
  }));

  return (
    <div className="space-y-12">
      {/* Team Performance Matrix */}
      <motion.div
        className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-pink-500/10 to-purple-500/10 rounded-full blur-3xl"></div>
        <h3 className="text-3xl font-bold text-purple-300 mb-8 flex items-center space-x-4">
          <Shield className="w-10 h-10" />
          <span>Team Performance Arena</span>
        </h3>
        <div className="h-[500px]">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={teamActivityData} margin={{ top: 20, right: 30, left: 20, bottom: 100 }}>
              <defs>
                <linearGradient id="teamGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#EC4899" stopOpacity={0.9}/>
                  <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0.6}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(139, 92, 246, 0.2)" />
              <XAxis 
                dataKey="name" 
                stroke="#A78BFA"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={100}
                tick={{ fill: '#A78BFA' }}
              />
              <YAxis stroke="#A78BFA" fontSize={12} tick={{ fill: '#A78BFA' }} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgba(139, 92, 246, 0.1)',
                  border: '1px solid rgba(139, 92, 246, 0.3)',
                  borderRadius: '12px',
                  color: '#E5E7EB',
                  backdropFilter: 'blur(10px)'
                }}
              />
              <Bar 
                dataKey="solves" 
                fill="url(#teamGradient)"
                radius={[8, 8, 0, 0]}
                animationDuration={1500}
              />
              <Bar 
                dataKey="firstBloods" 
                fill="#F59E0B"
                radius={[8, 8, 0, 0]}
                animationDuration={2000}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </motion.div>

      {/* Team Size Distribution */}
      <motion.div
        className="glass-card-dark p-8 rounded-3xl relative overflow-hidden"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.8 }}
      >
        <h3 className="text-2xl font-bold text-purple-300 mb-8 flex items-center space-x-3">
          <Users className="w-8 h-8" />
          <span>Team Composition Analysis</span>
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <defs>
                  {GRADIENT_DEFS.map(grad => (
                    <linearGradient key={grad.id} id={grad.id} x1="0" y1="0" x2="1" y2="1">
                      <stop offset="0%" stopColor={grad.colors[0]} />
                      <stop offset="100%" stopColor={grad.colors[1]} />
                    </linearGradient>
                  ))}
                </defs>
                <Pie
                  data={teamSizeData}
                  cx="50%"
                  cy="50%"
                  outerRadius={120}
                  innerRadius={50}
                  dataKey="teams"
                  animationBegin={0}
                  animationDuration={2000}
                  stroke="rgba(139, 92, 246, 0.3)"
                  strokeWidth={2}
                >
                  {teamSizeData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={`url(#${GRADIENT_DEFS[index % GRADIENT_DEFS.length].id})`}
                    />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    border: '1px solid rgba(139, 92, 246, 0.3)',
                    borderRadius: '12px',
                    color: '#E5E7EB',
                    backdropFilter: 'blur(10px)'
                  }}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="space-y-4">
            {teamSizeData.map((item, index) => (
              <motion.div
                key={item.size}
                className="flex items-center justify-between p-4 glass-card rounded-xl"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 + index * 0.1 }}
                whileHover={{ scale: 1.05, x: 10 }}
              >
                <div className="flex items-center space-x-4">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: item.fill }}
                  />
                  <span className="text-purple-200 font-medium">{item.size}</span>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-purple-300">{item.teams}</div>
                  <div className="text-sm text-purple-200/70">{item.percentage}%</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
}

function CreativeStatCard({ 
  id,
  title, 
  value, 
  subtitle, 
  icon: Icon, 
  color,
  change,
  trend,
  isHovered
}: { 
  id: string;
  title: string; 
  value: string | number; 
  subtitle: string; 
  icon: any; 
  color: string;
  change?: string;
  trend?: string;
  isHovered: boolean;
}) {
  const colorClasses = {
    purple: { bg: 'from-purple-500 to-pink-500', glow: 'shadow-purple-500/25' },
    blue: { bg: 'from-blue-500 to-purple-500', glow: 'shadow-blue-500/25' },
    green: { bg: 'from-green-500 to-blue-500', glow: 'shadow-green-500/25' },
    pink: { bg: 'from-pink-500 to-purple-500', glow: 'shadow-pink-500/25' },
    yellow: { bg: 'from-yellow-500 to-orange-500', glow: 'shadow-yellow-500/25' },
  };

  return (
    <motion.div 
      className={`glass-card-dark p-8 rounded-3xl relative overflow-hidden transition-all duration-500 ${
        isHovered ? `shadow-2xl ${colorClasses[color].glow}` : ''
      }`}
      whileHover={{ scale: 1.05, y: -10 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      {/* Animated background gradient */}
      <motion.div
        className={`absolute inset-0 bg-gradient-to-br ${colorClasses[color].bg} opacity-5`}
        animate={{
          scale: isHovered ? [1, 1.2, 1] : [1, 1.05, 1],
          opacity: isHovered ? [0.05, 0.15, 0.05] : [0.05, 0.08, 0.05],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      {/* Floating particles */}
      {isHovered && [...Array(5)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 h-1 bg-white/30 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -20, 0],
            opacity: [0, 1, 0],
            scale: [0, 1, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: i * 0.2,
          }}
        />
      ))}
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-6">
          <motion.div 
            className={`p-4 bg-gradient-to-r ${colorClasses[color].bg} rounded-2xl`}
            whileHover={{ rotate: 360, scale: 1.1 }}
            transition={{ duration: 0.6 }}
          >
            <Icon className="w-8 h-8 text-white" />
          </motion.div>
          {change && (
            <motion.div 
              className="flex items-center space-x-2 px-3 py-1 bg-green-500/20 rounded-full"
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              {trend === 'up' ? (
                <ArrowUp className="w-4 h-4 text-green-400" />
              ) : (
                <ArrowDown className="w-4 h-4 text-red-400" />
              )}
              <span className="text-green-400 text-sm font-medium">{change}</span>
            </motion.div>
          )}
        </div>
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-purple-300/80 uppercase tracking-wider">{title}</h3>
          <motion.div 
            className="text-4xl font-black text-purple-200"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            {value}
          </motion.div>
          <p className="text-sm text-purple-300/70">{subtitle}</p>
        </div>
      </div>
      
      {/* Corner decoration */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-white/5 to-transparent rounded-bl-3xl"></div>
    </motion.div>
  );
}