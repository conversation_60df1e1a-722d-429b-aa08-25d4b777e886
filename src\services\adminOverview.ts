import { api } from './api';

export interface SystemStats {
  users: {
    total: number;
    active: number;
    topUsers: Array<{
      _id: string;
      username: string;
      score: number;
    }>;
  };
  teams: {
    total: number;
    active: number;
    topTeams: Array<{
      _id: string;
      name: string;
      teamScore: number;
    }>;
  };
  challenges: {
    total: number;
    active: number;
    mostSolved: Array<{
      _id: string;
      title: string;
      category: string;
      difficulty: string;
      solveCount: number;
    }>;
  };
  submissions: {
    total: number;
    correct: number;
    successRate: number;
  };
}

export interface ActivityLog {
  _id: string;
  userId: {
    _id: string;
    username: string;
    email: string;
  };
  challengeId: {
    _id: string;
    title: string;
    category: string;
  };
  submittedFlag: string;
  isCorrect: boolean;
  submittedAt: string;
  createdAt: string;
}

export interface ActivityLogsResponse {
  activities: ActivityLog[];
  total: number;
  page: number;
  pages: number;
  limit: number;
}

export interface MachineStats {
  totalMachines: number;
  runningMachines: number;
  stoppedMachines: number;
  totalInstances: number;
  activeInstances: number;
  utilizationRate: number;
}

export interface DashboardStats {
  totalUsers: number;
  totalChallenges: number;
  activeVMs: number;
  totalSolves: number;
  onlineUsers: number;
  recentActivity: Array<{
    id: string;
    type: string;
    userId: string;
    username: string;
    details: string;
    timestamp: string;
  }>;
  platformStats: {
    totalPoints: number;
    averageScore: number;
    totalTeams: number;
    activeTeams: number;
  };
}

export class AdminOverviewService {
  // Get comprehensive system statistics
  static async getSystemStats(): Promise<SystemStats> {
    const response = await api.get<SystemStats>('/admin/stats');
    return response.data;
  }

  // Get recent activity logs
  static async getActivityLogs(params?: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
  }): Promise<ActivityLogsResponse> {
    const queryParams = new URLSearchParams();
    if (params) {
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortDirection) queryParams.append('sortDirection', params.sortDirection);
    }

    const response = await api.get<ActivityLogsResponse>(`/admin/activity-logs?${queryParams}`);
    return response.data;
  }

  // Get machine statistics
  static async getMachineStats(): Promise<MachineStats> {
    try {
      const response = await api.get<MachineStats>('/virtual-machines/admin/statistics');
      return response.data;
    } catch (error) {
      // Return default values if endpoint doesn't exist or fails
      return {
        totalMachines: 0,
        runningMachines: 0,
        stoppedMachines: 0,
        totalInstances: 0,
        activeInstances: 0,
        utilizationRate: 0,
      };
    }
  }

  // Get dashboard statistics
  static async getDashboardStats(): Promise<DashboardStats> {
    try {
      const response = await api.get<DashboardStats>('/dashboard/platform-stats');
      return response.data;
    } catch (error) {
      // Return default values if endpoint doesn't exist or fails
      return {
        totalUsers: 0,
        totalChallenges: 0,
        activeVMs: 0,
        totalSolves: 0,
        onlineUsers: 0,
        recentActivity: [],
        platformStats: {
          totalPoints: 0,
          averageScore: 0,
          totalTeams: 0,
          activeTeams: 0,
        },
      };
    }
  }

  // Get notification statistics
  static async getNotificationStats(): Promise<{
    connectedUsers: number;
    connectedUsersList: Array<{
      userId: string;
      username: string;
      connectedAt: string;
    }>;
  }> {
    try {
      const response = await api.get<{
        connectedUsers: number;
        connectedUsersList: Array<{
          userId: string;
          username: string;
          connectedAt: string;
        }>;
      }>('/notifications/admin/stats');
      return response.data;
    } catch (error) {
      return {
        connectedUsers: 0,
        connectedUsersList: [],
      };
    }
  }

  // Format activity logs into a more user-friendly format
  static formatActivityLogs(activities: ActivityLog[]): Array<{
    id: string;
    type: 'challenge_solved' | 'flag_submitted';
    user: string;
    timestamp: string;
    details: string;
  }> {
    return activities.map(activity => ({
      id: activity._id,
      type: activity.isCorrect ? 'challenge_solved' : 'flag_submitted',
      user: activity.userId.username,
      timestamp: activity.submittedAt,
      details: activity.isCorrect 
        ? `Solved ${activity.challengeId.title}` 
        : `Submitted flag for ${activity.challengeId.title}`,
    }));
  }

  // Calculate growth percentages (mock implementation - would need historical data)
  static calculateGrowthPercentage(current: number, previous: number): string {
    if (previous === 0) return '+0%';
    const growth = ((current - previous) / previous) * 100;
    const sign = growth >= 0 ? '+' : '';
    return `${sign}${Math.round(growth)}%`;
  }

  // Format time ago
  static formatTimeAgo(timestamp: string): string {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  }
}
