import { api } from './api';

export interface AdminSettings {
  _id: string;
  configId: string;
  allowRegistration: boolean;
  emailVerification: boolean;
  maintenanceMode: boolean;
  enableTeams: boolean;
  maxTeamSize: number;
  autoBackup: boolean;
  backupInterval: number;
  defaultSessionTime: number;
  maxConcurrentVMs: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  minPasswordLength: number;
  requirePasswordComplexity: boolean;
  smtpHost: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  smtpSecure: boolean;
  fromEmail: string;
  maxFileUploadSize: number;
  sessionTimeout: number;
  enableLogging: boolean;
  logLevel: string;
  apiRateLimit: number;
  loginRateLimit: number;
  lastBackup: string;
  totalBackups: number;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateAdminSettingsDto {
  allowRegistration?: boolean;
  emailVerification?: boolean;
  maintenanceMode?: boolean;
  enableTeams?: boolean;
  maxTeamSize?: number;
  autoBackup?: boolean;
  backupInterval?: number;
  defaultSessionTime?: number;
  maxConcurrentVMs?: number;
  maxLoginAttempts?: number;
  lockoutDuration?: number;
  minPasswordLength?: number;
  requirePasswordComplexity?: boolean;
  smtpHost?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpPassword?: string;
  smtpSecure?: boolean;
  fromEmail?: string;
  maxFileUploadSize?: number;
  sessionTimeout?: number;
  enableLogging?: boolean;
  logLevel?: string;
  apiRateLimit?: number;
  loginRateLimit?: number;
}

export interface SystemMaintenanceResult {
  success: boolean;
  message: string;
  data?: any;
}

export interface BackupInfo {
  filename: string;
  path: string;
  timestamp: string;
  size: number;
  sizeFormatted: string;
  createdAt: string;
}

export interface BackupListResult {
  success: boolean;
  message: string;
  backups: BackupInfo[];
}

export interface SystemStats {
  settings: {
    lastBackup: string;
    totalBackups: number;
    maintenanceMode: boolean;
    autoBackup: boolean;
    backupInterval: number;
  };
  storage: {
    uploadsSize: number;
    uploadsCount: number;
    backupsSize: number;
    backupsCount: number;
    totalSize: number;
  };
  system: {
    nodeVersion: string;
    platform: string;
    uptime: number;
    memoryUsage: {
      rss: number;
      heapTotal: number;
      heapUsed: number;
      external: number;
      arrayBuffers: number;
    };
  };
}

class AdminSettingsService {
  async getAdminSettings(): Promise<AdminSettings> {
    const response = await api.get<AdminSettings>('/admin/settings');
    return response.data;
  }

  async updateAdminSettings(settings: UpdateAdminSettingsDto): Promise<AdminSettings> {
    const response = await api.put<AdminSettings>('/admin/settings', settings);
    return response.data;
  }

  async createBackup(): Promise<SystemMaintenanceResult> {
    const response = await api.post<SystemMaintenanceResult>('/admin/settings/backup');
    return response.data;
  }

  async listBackups(): Promise<BackupListResult> {
    const response = await api.get<BackupListResult>('/admin/settings/backups');
    return response.data;
  }

  async downloadBackup(filename: string): Promise<void> {
    const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/settings/backup/${filename}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('mybox_token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to download backup');
    }

    // Create download link
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  async deleteBackup(filename: string): Promise<SystemMaintenanceResult> {
    const response = await api.delete<SystemMaintenanceResult>(`/admin/settings/backup/${filename}`);
    return response.data;
  }

  async restoreBackup(filename: string): Promise<SystemMaintenanceResult> {
    const response = await api.post<SystemMaintenanceResult>(`/admin/settings/backup/${filename}/restore`);
    return response.data;
  }

  async uploadBackup(file: File): Promise<SystemMaintenanceResult> {
    const formData = new FormData();
    formData.append('backup', file);

    const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/settings/backup/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('mybox_token')}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to upload backup');
    }

    return response.json();
  }

  async downloadSystemLogs(): Promise<SystemMaintenanceResult> {
    const response = await api.get<SystemMaintenanceResult>('/admin/settings/logs');
    return response.data;
  }

  async clearCache(): Promise<SystemMaintenanceResult> {
    const response = await api.post<SystemMaintenanceResult>('/admin/settings/clear-cache');
    return response.data;
  }

  async getSystemStats(): Promise<SystemStats> {
    const response = await api.get<SystemStats>('/admin/settings/system-stats');
    return response.data;
  }

  async testEmailSettings(settings: Partial<UpdateAdminSettingsDto>): Promise<SystemMaintenanceResult> {
    const response = await api.post<SystemMaintenanceResult>('/admin/settings/test-email', settings);
    return response.data;
  }

  // Helper methods for formatting
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleString();
  }

  // Validation helpers
  validateEmailSettings(settings: Partial<UpdateAdminSettingsDto>): string[] {
    const errors: string[] = [];
    
    if (settings.smtpHost && !settings.smtpHost.trim()) {
      errors.push('SMTP host is required');
    }
    
    if (settings.smtpPort && (settings.smtpPort < 1 || settings.smtpPort > 65535)) {
      errors.push('SMTP port must be between 1 and 65535');
    }
    
    if (settings.fromEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(settings.fromEmail)) {
      errors.push('Invalid from email format');
    }
    
    return errors;
  }

  validateGeneralSettings(settings: Partial<UpdateAdminSettingsDto>): string[] {
    const errors: string[] = [];
    
    if (settings.maxTeamSize && (settings.maxTeamSize < 1 || settings.maxTeamSize > 20)) {
      errors.push('Max team size must be between 1 and 20');
    }
    
    if (settings.defaultSessionTime && (settings.defaultSessionTime < 1 || settings.defaultSessionTime > 24)) {
      errors.push('Default session time must be between 1 and 24 hours');
    }
    
    if (settings.maxConcurrentVMs && (settings.maxConcurrentVMs < 1 || settings.maxConcurrentVMs > 10)) {
      errors.push('Max concurrent VMs must be between 1 and 10');
    }
    
    if (settings.backupInterval && (settings.backupInterval < 1 || settings.backupInterval > 168)) {
      errors.push('Backup interval must be between 1 and 168 hours');
    }
    
    return errors;
  }
}

export const adminSettingsService = new AdminSettingsService();