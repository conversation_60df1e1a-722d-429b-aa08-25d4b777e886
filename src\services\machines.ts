import { api } from './api';

export interface MachineTemplate {
  id: string;
  _id?: string; // MongoDB ObjectId field
  name: string;
  slug: string;
  description: string;
  category: 'web' | 'crypto' | 'pwn' | 'reverse' | 'forensics' | 'misc' | 'osint';
  difficulty: 'easy' | 'medium' | 'hard' | 'insane';
  os: 'linux' | 'windows' | 'other';
  machineType: 'docker' | 'ova' | 'qcow2';
  dockerImage?: string;
  exposedPorts: number[];
  requiredRAM: number;
  requiredCPU: number;
  maxInstances: number;
  flags: Array<{
    name: string;
    value: string;
    type: 'root' | 'user' | 'www-data' | 'custom';
    points: number;
    description?: string;
    isRequired: boolean;
  }>;
  topics: Array<{
    title: string;
    description: string;
    imageUrl?: string;
    order: number;
  }>;
  hints: string[];
  tags: string[];
  isActive: boolean;
  authorName: string;
  solveCount: number;
  rating: number;
  releaseDate: string;
  retireDate?: string;
  isSolved?: boolean;
  activeInstances?: number;
  userStats?: {
    solvedFlags: string[];
    totalPoints: number;
    totalSubmissions: number;
    isSolved: boolean;
  };
}

export interface MachineInstance {
  id: string;
  template: {
    id: string;
    name: string;
    difficulty: string;
    category: string;
    os: string;
  };
  ownerId: {
    username: string;
    email: string;
  };
  status: 'starting' | 'running' | 'stopping' | 'stopped' | 'error';
  instanceIP: string;
  exposedPorts: Array<{
    internal: number;
    external: number;
    protocol: string;
    url?: string;
  }>;
  accessToken: string;
  startedAt: string;
  expiresAt: string;
  timeRemaining: number; // minutes
  isTeamShared: boolean;
  resourceUsage?: {
    cpuUsage: number;
    memoryUsage: number;
    networkRx: number;
    networkTx: number;
    uptime: number;
  };
}

export interface MachineFlag {
  name: string;
  value: string;
  type: 'root' | 'user' | 'www-data' | 'custom';
  description?: string;
  points: number;
  isRequired: boolean;
  isSolved: boolean;
  solvedAt?: string;
}

export interface FlagSubmissionResult {
  isCorrect: boolean;
  pointsAwarded: number;
  teamPointsAwarded: number;
  isFirstBlood: boolean;
  isFirstTeamSolve: boolean;
  message: string;
  flagInfo?: {
    name: string;
    description?: string;
  };
}

export interface MachineConnectionInfo {
  instanceIP: string;
  ports: Array<{
    internal: number;
    external: number;
    protocol: string;
    url?: string;
  }>;
  accessToken: string;
  sshCommand?: string;
  rdpInfo?: {
    host: string;
    port: number;
    username: string;
  };
}

export interface MachineFilters {
  category?: string;
  difficulty?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface CreateMachineInstanceRequest {
  teamId?: string;
  extendedRuntime?: number;
}

export interface ExtendInstanceRequest {
  additionalMinutes: number;
}

export interface FlagSubmissionRequest {
  flagName: string;
  flag: string;
}

export interface ShareInstanceRequest {
  teamId: string;
}

class MachineService {  /**
   * Get available machine templates
   */  async getAvailableTemplates(filters: MachineFilters = {}): Promise<{ templates: MachineTemplate[]; total: number; hasMore: boolean }> {
    const params = new URLSearchParams();
    
    if (filters.category) params.append('category', filters.category);
    if (filters.difficulty) params.append('difficulty', filters.difficulty);
    if (filters.search) params.append('search', filters.search);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    const response = await api.get<{ templates: MachineTemplate[]; total: number; hasMore: boolean }>(`/machines?${params.toString()}`);
    return response.data;
  }

  /**
   * Get machine template details
   */
  async getTemplateDetails(templateId: string): Promise<MachineTemplate> {
    const response = await api.get<MachineTemplate>(`/machines/${templateId}`);
    return response.data;
  }

  /**
   * Spawn a new machine instance
   */
  async spawnMachine(templateId: string, options: CreateMachineInstanceRequest = {}): Promise<MachineInstance> {
    const response = await api.post<MachineInstance>(`/machines/${templateId}/spawn`, options);
    return response.data;
  }

  /**
   * Get user's active instances
   */
  async getUserInstances(): Promise<MachineInstance[]> {
    const response = await api.get<MachineInstance[]>('/machines/instances/my');
    return response.data;
  }

  /**
   * Get team instances
   */
  async getTeamInstances(teamId: string): Promise<MachineInstance[]> {
    const response = await api.get<MachineInstance[]>(`/machines/teams/${teamId}/instances`);
    return response.data;
  }

  /**
   * Get instance details
   */
  async getInstanceDetails(instanceId: string): Promise<MachineInstance> {
    const response = await api.get<MachineInstance>(`/machines/instances/${instanceId}`);
    return response.data;
  }

  /**
   * Get instance connection information
   */
  async getConnectionInfo(instanceId: string): Promise<MachineConnectionInfo> {
    const response = await api.get<MachineConnectionInfo>(`/machines/instances/${instanceId}/connection`);
    return response.data;
  }

  /**
   * Terminate a machine instance
   */
  async terminateInstance(instanceId: string): Promise<void> {
    await api.delete(`/machines/instances/${instanceId}`);
  }

  /**
   * Restart a machine instance
   */
  async restartInstance(instanceId: string): Promise<void> {
    await api.post(`/machines/instances/${instanceId}/restart`);
  }

  /**
   * Extend machine instance runtime
   */
  async extendInstance(instanceId: string, request: ExtendInstanceRequest): Promise<void> {
    await api.patch(`/machines/instances/${instanceId}/extend`, request);
  }

  /**
   * Share instance with team
   */
  async shareInstanceWithTeam(instanceId: string, request: ShareInstanceRequest): Promise<void> {
    await api.post(`/machines/instances/${instanceId}/share`, request);
  }
  /**
   * Submit a flag for a machine instance
   */
  async submitFlag(instanceId: string, submission: FlagSubmissionRequest): Promise<FlagSubmissionResult> {
    const response = await api.post<FlagSubmissionResult>(`/machines/instances/${instanceId}/submit`, submission);
    return response.data;
  }

  /**
   * Get available flags for an instance
   */
  async getInstanceFlags(instanceId: string): Promise<MachineFlag[]> {
    const response = await api.get<MachineFlag[]>(`/machines/instances/${instanceId}/flags`);
    return response.data;
  }
  /**
   * Get user's submission history
   */
  async getUserSubmissionHistory(options: {
    page?: number;
    limit?: number;
    correctOnly?: boolean;
  } = {}) {
    const params = new URLSearchParams();
    
    if (options.page) params.append('page', options.page.toString());
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.correctOnly) params.append('correctOnly', 'true');

    const response = await api.get(`/machines/submissions/my?${params.toString()}`);
    return response.data;
  }

  /**
   * Get team submission statistics
   */
  async getTeamSubmissionStats(teamId: string) {
    const response = await api.get(`/machines/teams/${teamId}/stats`);
    return response.data;
  }

  /**
   * Get machine solve statistics
   */
  async getMachineSolveStats(templateId: string) {
    const response = await api.get(`/machines/${templateId}/stats`);
    return response.data;
  }

  /**
   * Get global machine leaderboard
   */
  async getGlobalLeaderboard(timeframe: 'daily' | 'weekly' | 'monthly' | 'all' = 'all') {
    const response = await api.get(`/machines/leaderboard/global?timeframe=${timeframe}`);
    return response.data;
  }
  
  /**
   * Get machine management statistics (for admin)
   */
  async getMachineStatistics() {
    const response = await api.get('/machines/admin/statistics');
    return response.data;
  }  /**
   * Get all machine templates (admin only)
   */
  async getAdminTemplates(): Promise<MachineTemplate[]> {
    const response = await api.get<MachineTemplate[]>('/admin/machines');
    return response.data;
  }

  /**
   * Get all active machine instances (admin only)
   */
  async getAdminInstances(): Promise<MachineInstance[]> {
    const response = await api.get<MachineInstance[]>('/admin/machines/instances/all');
    return response.data;
  }

  /**
   * Delete a machine template (admin only)
   */
  async deleteTemplate(templateId: string): Promise<void> {
    await api.delete<void>(`/admin/machines/${templateId}`);
  }

  /**
   * Toggle template active status (admin only)
   */
  async toggleTemplateStatus(templateId: string, isActive: boolean): Promise<void> {
    await api.patch<void>(`/admin/machines/${templateId}/status`, { isActive });
  }

  /**
   * Force terminate a machine instance (admin only)
   */
  async forceTerminateInstance(instanceId: string): Promise<void> {
    await api.delete<void>(`/admin/machines/instances/${instanceId}/force`);
  }

  /**
   * Create a new machine template (admin only)
   */
  async createTemplate(templateData: Partial<MachineTemplate>): Promise<MachineTemplate> {
    const response = await api.post<MachineTemplate>('/admin/machines', templateData);
    return response.data;
  }

  /**
   * Update a machine template (admin only)
   */
  async updateTemplate(templateId: string, templateData: Partial<MachineTemplate>): Promise<MachineTemplate> {
    const response = await api.put<MachineTemplate>(`/admin/machines/${templateId}`, templateData);
    return response.data;
  }

  /**
   * Upload topic image (admin only)
   */
  async uploadTopicImage(file: File): Promise<{ imageUrl: string; filename: string }> {
    const formData = new FormData();
    formData.append('image', file);

    const response = await api.post<{ imageUrl: string; filename: string }>(
      '/admin/machines/topics/upload-image',
      formData
    );
    return response.data;
  }

  /**
   * Helper method to format time remaining
   */
  formatTimeRemaining(minutes: number): string {
    if (minutes <= 0) return 'Expired';
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  }

  /**
   * Helper method to get difficulty color
   */
  getDifficultyColor(difficulty: string): string {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'text-green-400';
      case 'medium':
        return 'text-yellow-400';
      case 'hard':
        return 'text-orange-400';
      case 'insane':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  }

  /**
   * Helper method to get category icon
   */
  getCategoryIcon(category: string): string {
    switch (category.toLowerCase()) {
      case 'web':
        return '🌐';
      case 'crypto':
        return '🔐';
      case 'pwn':
        return '💥';
      case 'reverse':
        return '🔄';
      case 'forensics':
        return '🔍';
      case 'misc':
        return '🎯';
      case 'osint':
        return '🕵️';
      default:
        return '📦';
    }
  }

  /**
   * Helper method to get status color
   */
  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'running':
        return 'text-green-400';
      case 'starting':
        return 'text-yellow-400';
      case 'stopping':
        return 'text-orange-400';
      case 'stopped':
        return 'text-gray-400';
      case 'error':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  }

  /**
   * Upload machine template archive
   */
  async uploadMachineArchive(templateId: string, file: File): Promise<any> {
    const formData = new FormData();
    formData.append('archive', file);

    const response = await api.post(`/admin/machines/${templateId}/upload-archive`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Build Docker image from uploaded template
   */
  async buildMachineImage(templateId: string): Promise<any> {
    const response = await api.post(`/admin/machines/${templateId}/build-image`);
    return response.data;
  }

  /**
   * Get build logs for machine template
   */
  async getBuildLogs(templateId: string): Promise<string[]> {
    const response = await api.get(`/admin/machines/${templateId}/build-logs`);
    return response.data;
  }

  /**
   * Remove built Docker image
   */
  async removeImage(templateId: string): Promise<any> {
    const response = await api.delete(`/admin/machines/${templateId}/image`);
    return response.data;
  }

  /**
   * Check if Docker image exists for template
   */
  async getImageStatus(templateId: string): Promise<{
    imageExists: boolean;
    hasExtractedFiles: boolean;
    imageName: string;
    canBuild: boolean;
  }> {
    const response = await api.get(`/admin/machines/${templateId}/image-status`);
    return response.data;
  }

  /**
   * Get system status for archive support
   */
  async getSystemStatus(): Promise<{
    platform: string;
    zipSupported: boolean;
    sevenZipSupported: boolean;
    installationInstructions?: string;
  }> {
    const response = await api.get('/admin/machines/system-status');
    return response.data;
  }

  /**
   * Helper method to check if instance is near expiration
   */
  isNearExpiration(timeRemaining: number): boolean {
    return timeRemaining > 0 && timeRemaining <= 30; // 30 minutes warning
  }

  /**
   * Helper method to format resource usage
   */
  formatResourceUsage(usage: number, unit: string): string {
    if (usage < 0) return 'N/A';
    return `${usage.toFixed(1)}${unit}`;
  }

  /**
   * Helper method to get OS icon
   */
  getOSIcon(os: string): string {
    switch (os.toLowerCase()) {
      case 'linux':
        return '🐧';
      case 'windows':
        return '🪟';
      default:
        return '💻';
    }
  }
}

export default new MachineService();
