import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PublicSettingsService, PublicSettings } from './public-settings.service';

@ApiTags('public-settings')
@Controller('public-settings')
export class PublicSettingsController {
  constructor(private readonly publicSettingsService: PublicSettingsService) {}

  @Get()
  @ApiOperation({ summary: 'Get public platform settings' })
  @ApiResponse({ status: 200, description: 'Public settings retrieved successfully' })
  async getPublicSettings(): Promise<PublicSettings> {
    return this.publicSettingsService.getPublicSettings();
  }

  @Get('maintenance')
  @ApiOperation({ summary: 'Check if platform is in maintenance mode' })
  @ApiResponse({ status: 200, description: 'Maintenance status retrieved' })
  async getMaintenanceStatus(): Promise<{ maintenanceMode: boolean }> {
    const maintenanceMode = await this.publicSettingsService.isMaintenanceMode();
    return { maintenanceMode };
  }

  @Get('teams')
  @ApiOperation({ summary: 'Check if teams are enabled' })
  @ApiResponse({ status: 200, description: 'Teams status retrieved' })
  async getTeamsStatus(): Promise<{ teamsEnabled: boolean; maxTeamSize: number }> {
    const [teamsEnabled, maxTeamSize] = await Promise.all([
      this.publicSettingsService.areTeamsEnabled(),
      this.publicSettingsService.getMaxTeamSize()
    ]);
    
    return { teamsEnabled, maxTeamSize };
  }
}