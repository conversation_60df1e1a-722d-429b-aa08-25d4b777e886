import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class RegisterDto {
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  username: string;

  @IsEmail()
  email: string;

  @IsString()
  @MinLength(6)
  @MaxLength(100)
  password: string;
}

export class LoginDto {
  @IsEmail()
  email: string;

  @IsString()
  password: string;
}

export class VerifyEmailDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(6)
  @MaxLength(6)
  code: string;
}

export class ResendVerificationDto {
  @IsEmail()
  email: string;
}

export class AuthResponseDto {
  access_token?: string;
  user?: {
    id: string;
    username: string;
    email: string;
    role: string;
    score: number;
    rank: number;
    teamId?: string;
    isEmailVerified: boolean;
  };
  requiresEmailVerification?: boolean;
  message?: string;
}
