import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Users, 
  Activity, 
  Server, 
  Network,
  Download,
  RefreshCw,
  Trash2,
  RotateCcw,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wifi,
  Settings,
  FileText,
  Loader2
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { AdminVpnService, AdminVPNServerStatus, AdminVPNConfig, AdminVPNConnectionLog } from '../../services/adminVpn';

export function VPNManagement() {
  const [isLoading, setIsLoading] = useState(false);
  const [serverStatus, setServerStatus] = useState<AdminVPNServerStatus | null>(null);
  const [vpnConfigs, setVpnConfigs] = useState<AdminVPNConfig[]>([]);
  const [connectionLogs, setConnectionLogs] = useState<AdminVPNConnectionLog[]>([]);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'users' | 'logs' | 'networks'>('overview');
  const [showServerConfig, setShowServerConfig] = useState(false);
  const [serverConfigData, setServerConfigData] = useState<string>('');

  // Load all VPN data
  const loadVPNData = async () => {
    try {
      setIsLoading(true);
      
      const [status, configs, logs] = await Promise.all([
        AdminVpnService.getVPNServerStatus(),
        AdminVpnService.getAllVPNConfigs(),
        AdminVpnService.getAllConnectionLogs(50)
      ]);

      setServerStatus(status);
      setVpnConfigs(configs);
      setConnectionLogs(logs);
    } catch (error: any) {
      console.error('Error loading VPN data:', error);
      toast.error('Failed to load VPN data');
    } finally {
      setIsLoading(false);
    }
  };

  // Reset user VPN configuration
  const handleResetUserVPN = async (userId: string, username?: string) => {
    if (!window.confirm(`Are you sure you want to reset VPN configuration for ${username || 'this user'}?`)) {
      return;
    }

    try {
      const result = await AdminVpnService.resetUserVPNConfig(userId);
      toast.success(result.message);
      await loadVPNData();
    } catch (error: any) {
      console.error('Error resetting user VPN:', error);
      toast.error(error.message || 'Failed to reset user VPN');
    }
  };

  // Revoke user VPN access
  const handleRevokeUserVPN = async (userId: string, username?: string) => {
    if (!window.confirm(`Are you sure you want to revoke VPN access for ${username || 'this user'}? This will disconnect them immediately.`)) {
      return;
    }

    try {
      const result = await AdminVpnService.revokeUserVPNAccess(userId);
      toast.success(result.message);
      await loadVPNData();
    } catch (error: any) {
      console.error('Error revoking user VPN:', error);
      toast.error(error.message || 'Failed to revoke user VPN');
    }
  };

  // Generate server configuration
  const handleGenerateServerConfig = async () => {
    try {
      setIsLoading(true);
      const config = await AdminVpnService.generateServerConfig();
      setServerConfigData(config.serverConfig);
      setShowServerConfig(true);
    } catch (error: any) {
      console.error('Error generating server config:', error);
      toast.error('Failed to generate server configuration');
    } finally {
      setIsLoading(false);
    }
  };

  // Download server configuration
  const handleDownloadServerConfig = () => {
    const blob = new Blob([serverConfigData], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'wg0.conf');
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    toast.success('Server configuration downloaded');
  };

  // Cleanup orphaned networks
  const handleCleanupNetworks = async () => {
    if (!window.confirm('Are you sure you want to cleanup orphaned networks? This will remove unused VPN configurations and networks.')) {
      return;
    }

    try {
      const result = await AdminVpnService.cleanupOrphanedNetworks();
      toast.success(`Cleanup completed: ${result.data.cleanedNetworks} networks, ${result.data.cleanedConfigs} configs`);
      await loadVPNData();
    } catch (error: any) {
      console.error('Error cleaning up networks:', error);
      toast.error('Failed to cleanup networks');
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${diffInDays}d ago`;
  };

  useEffect(() => {
    loadVPNData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadVPNData, 30000);
    return () => clearInterval(interval);
  }, []);

  if (isLoading && !serverStatus) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-400 mx-auto mb-4" />
          <p className="text-slate-400">Loading VPN management data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">VPN Management</h1>
          <p className="text-slate-400 mt-1">Monitor and manage WireGuard VPN infrastructure</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={loadVPNData}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
          <button
            onClick={handleGenerateServerConfig}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            <FileText className="w-4 h-4" />
            <span>Server Config</span>
          </button>
        </div>
      </div>

      {/* Server Status Overview */}
      {serverStatus && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${serverStatus.server.isRunning ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
                <Server className={`w-5 h-5 ${serverStatus.server.isRunning ? 'text-green-400' : 'text-red-400'}`} />
              </div>
              <div>
                <p className="text-sm text-slate-400">Server Status</p>
                <p className={`font-semibold ${serverStatus.server.isRunning ? 'text-green-400' : 'text-red-400'}`}>
                  {serverStatus.server.isRunning ? 'Running' : 'Stopped'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Users className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-slate-400">Total Users</p>
                <p className="text-xl font-semibold text-white">{serverStatus.server.totalUsers}</p>
              </div>
            </div>
          </div>

          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <Wifi className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <p className="text-sm text-slate-400">Active Connections</p>
                <p className="text-xl font-semibold text-white">{serverStatus.server.activeConnections}</p>
              </div>
            </div>
          </div>

          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Network className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-slate-400">Networks</p>
                <p className="text-xl font-semibold text-white">{serverStatus.networks.totalNetworks}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-slate-800">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: Shield },
            { id: 'users', label: 'Users', icon: Users },
            { id: 'logs', label: 'Connection Logs', icon: Activity },
            { id: 'networks', label: 'Networks', icon: Network }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setSelectedTab(id as any)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                selectedTab === id
                  ? 'border-blue-500 text-blue-400'
                  : 'border-transparent text-slate-400 hover:text-slate-300'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {/* Overview Tab */}
        {selectedTab === 'overview' && (
          <div className="space-y-6">
            {/* Server Information */}
            <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Server Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm text-slate-400 mb-1">Server Endpoint</p>
                  <p className="font-mono text-white">{serverStatus?.server.serverEndpoint}:{serverStatus?.server.serverPort}</p>
                </div>
                <div>
                  <p className="text-sm text-slate-400 mb-1">Last Updated</p>
                  <p className="text-white">{serverStatus ? formatTimeAgo(serverStatus.timestamp) : 'Unknown'}</p>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={handleGenerateServerConfig}
                  className="flex items-center space-x-3 p-4 bg-blue-600/20 border border-blue-500/30 rounded-lg hover:bg-blue-600/30 transition-colors"
                >
                  <FileText className="w-5 h-5 text-blue-400" />
                  <div className="text-left">
                    <p className="font-medium text-white">Generate Server Config</p>
                    <p className="text-sm text-slate-400">Create WireGuard server configuration</p>
                  </div>
                </button>

                <button
                  onClick={handleCleanupNetworks}
                  className="flex items-center space-x-3 p-4 bg-orange-600/20 border border-orange-500/30 rounded-lg hover:bg-orange-600/30 transition-colors"
                >
                  <Settings className="w-5 h-5 text-orange-400" />
                  <div className="text-left">
                    <p className="font-medium text-white">Cleanup Networks</p>
                    <p className="text-sm text-slate-400">Remove orphaned configurations</p>
                  </div>
                </button>

                <button
                  onClick={loadVPNData}
                  className="flex items-center space-x-3 p-4 bg-green-600/20 border border-green-500/30 rounded-lg hover:bg-green-600/30 transition-colors"
                >
                  <RefreshCw className="w-5 h-5 text-green-400" />
                  <div className="text-left">
                    <p className="font-medium text-white">Refresh Data</p>
                    <p className="text-sm text-slate-400">Update all VPN information</p>
                  </div>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Users Tab */}
        {selectedTab === 'users' && (
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800">
            <div className="p-6 border-b border-slate-800">
              <h3 className="text-lg font-semibold text-white">VPN Users</h3>
              <p className="text-slate-400 text-sm mt-1">Manage user VPN configurations and access</p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-800">
                    <th className="text-left p-4 text-sm font-medium text-slate-400">User</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">VPN IP</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">Subnet</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">Status</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">Last Connected</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">Connections</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {vpnConfigs.map((config) => (
                    <tr key={config.id} className="border-b border-slate-800/50 hover:bg-slate-800/20">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className={`w-2 h-2 rounded-full ${config.isActive ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                          <span className="font-medium text-white">{config.userId}</span>
                        </div>
                      </td>
                      <td className="p-4 font-mono text-sm text-slate-300">{config.ipAddress}</td>
                      <td className="p-4 font-mono text-sm text-slate-300">{config.subnet}</td>
                      <td className="p-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          config.isActive ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
                        }`}>
                          {config.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="p-4 text-sm text-slate-400">
                        {config.lastConnected ? formatTimeAgo(config.lastConnected) : 'Never'}
                      </td>
                      <td className="p-4 text-sm text-slate-300">{config.totalConnections}</td>
                      <td className="p-4">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleResetUserVPN(config.userId)}
                            className="p-1 text-blue-400 hover:bg-blue-400/10 rounded"
                            title="Reset VPN Configuration"
                          >
                            <RotateCcw className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleRevokeUserVPN(config.userId)}
                            className="p-1 text-red-400 hover:bg-red-400/10 rounded"
                            title="Revoke VPN Access"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {vpnConfigs.length === 0 && (
                <div className="text-center py-12">
                  <Users className="w-12 h-12 text-slate-600 mx-auto mb-4" />
                  <p className="text-slate-400">No VPN users found</p>
                  <p className="text-slate-500 text-sm mt-1">Users will appear here when they generate VPN configurations</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Connection Logs Tab */}
        {selectedTab === 'logs' && (
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800">
            <div className="p-6 border-b border-slate-800">
              <h3 className="text-lg font-semibold text-white">Connection Logs</h3>
              <p className="text-slate-400 text-sm mt-1">Monitor VPN connection activity and events</p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-800">
                    <th className="text-left p-4 text-sm font-medium text-slate-400">User</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">Action</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">VPN IP</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">Client IP</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">Time</th>
                    <th className="text-left p-4 text-sm font-medium text-slate-400">Duration</th>
                  </tr>
                </thead>
                <tbody>
                  {connectionLogs.map((log) => (
                    <tr key={log.id} className="border-b border-slate-800/50 hover:bg-slate-800/20">
                      <td className="p-4">
                        <span className="font-medium text-white">{log.userId}</span>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${AdminVpnService.getConnectionStatusColor(log.action).replace('text-', 'bg-')}`}></div>
                          <span className={`capitalize font-medium ${AdminVpnService.getConnectionStatusColor(log.action)}`}>
                            {log.action}
                          </span>
                        </div>
                      </td>
                      <td className="p-4 font-mono text-sm text-slate-300">{log.vpnIP}</td>
                      <td className="p-4 font-mono text-sm text-slate-300">{log.clientIP}</td>
                      <td className="p-4 text-sm text-slate-400">{formatTimeAgo(log.timestamp)}</td>
                      <td className="p-4 text-sm text-slate-400">
                        {log.connectionStats?.duration ? AdminVpnService.formatDuration(log.connectionStats.duration) : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {connectionLogs.length === 0 && (
                <div className="text-center py-12">
                  <Activity className="w-12 h-12 text-slate-600 mx-auto mb-4" />
                  <p className="text-slate-400">No connection logs found</p>
                  <p className="text-slate-500 text-sm mt-1">Connection activity will appear here</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Networks Tab */}
        {selectedTab === 'networks' && (
          <div className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Network Overview</h3>
            <div className="text-center py-12">
              <Network className="w-12 h-12 text-slate-600 mx-auto mb-4" />
              <p className="text-slate-400">Network management coming soon</p>
              <p className="text-slate-500 text-sm mt-1">Advanced network monitoring and management features</p>
            </div>
          </div>
        )}
      </div>

      {/* Server Config Modal */}
      {showServerConfig && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-slate-900 rounded-xl border border-slate-800 p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">WireGuard Server Configuration</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleDownloadServerConfig}
                  className="flex items-center space-x-2 px-3 py-1.5 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </button>
                <button
                  onClick={() => setShowServerConfig(false)}
                  className="text-slate-400 hover:text-white"
                >
                  <Eye className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="bg-slate-800 rounded-lg p-4">
              <pre className="text-sm text-slate-300 whitespace-pre-wrap font-mono overflow-x-auto">
                {serverConfigData}
              </pre>
            </div>

            <div className="mt-4 text-sm text-slate-400">
              <p>• Save this configuration as <code className="bg-slate-800 px-1 rounded">wg0.conf</code> on your WireGuard server</p>
              <p>• Restart WireGuard service after updating the configuration</p>
              <p>• Make sure to backup your existing configuration before applying changes</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
